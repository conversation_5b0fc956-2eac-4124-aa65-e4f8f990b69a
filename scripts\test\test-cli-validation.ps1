#!/usr/bin/env pwsh
# Simplified CLI Test Script 
# Tests the CLI binary directly and validates the Bash script functionality

param(
    [switch]$Verbose,
    [switch]$SkipBuild,
    [string]$TestFilter = ""
)

# Configuration
$ErrorActionPreference = "Continue"
$ProjectRoot = $PWD
$CLIBinary = Join-Path $ProjectRoot -ChildPath "target" | Join-Path -ChildPath "release" | Join-Path -ChildPath "analyzer_cli.exe"
$BashScript = Join-Path $ProjectRoot -ChildPath "scripts" | Join-Path -ChildPath "codebase_analyzer.sh"
$TestDir = Join-Path $ProjectRoot -ChildPath "test_cli_validation"
$LogFile = Join-Path $TestDir -ChildPath "test_results.log"

# Colors for output
$Colors = @{
    Reset = "`e[0m"
    Red = "`e[91m"
    Green = "`e[92m"
    Yellow = "`e[93m"
    Blue = "`e[94m"
    Magenta = "`e[95m"
    Cyan = "`e[96m"
    White = "`e[97m"
}

# Test result tracking
$TestStats = @{
    Total = 0
    Passed = 0
    Failed = 0
}

function Write-ColoredOutput {
    param([string]$Text, [string]$Color = "White")
    Write-Host "$($Colors[$Color])$Text$($Colors.Reset)"
}

function Write-TestHeader {
    param([string]$Title)
    Write-Host ""
    Write-ColoredOutput "=" * 60 "Cyan"
    Write-ColoredOutput "TEST: $Title" "Cyan"
    Write-ColoredOutput "=" * 60 "Cyan"
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $TestStats.Total++
    
    if ($Passed) {
        $TestStats.Passed++
        Write-ColoredOutput "✅ PASS: $TestName" "Green"
    } else {
        $TestStats.Failed++
        Write-ColoredOutput "❌ FAIL: $TestName" "Red"
        if ($Details) {
            Write-ColoredOutput "   Details: $Details" "Yellow"
        }
    }
    
    # Log result
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $(if($Passed){'PASS'}else{'FAIL'}): $TestName"
    if ($Details) { $logEntry += " - $Details" }
    Add-Content -Path $LogFile -Value $logEntry
}

function Test-FileStructure {
    Write-TestHeader "File Structure Validation"
    
    # Test 1: CLI Binary exists
    $exists = Test-Path $CLIBinary
    Write-TestResult "CLI binary exists" $exists "Path: $CLIBinary"
    
    # Test 2: Bash script exists
    $bashExists = Test-Path $BashScript
    Write-TestResult "Bash script exists" $bashExists "Path: $BashScript"
    
    # Test 3: Bash script is readable
    if ($bashExists) {
        try {
            $content = Get-Content $BashScript -First 10 -ErrorAction Stop
            $readable = $content.Count -gt 0
            Write-TestResult "Bash script is readable" $readable
        } catch {
            Write-TestResult "Bash script is readable" $false $_.Exception.Message
        }
    }
    
    # Test 4: Bash script has proper structure
    if ($bashExists) {
        try {
            $content = Get-Content $BashScript -Raw
            $hasShebang = $content -match "^#!/.*bash"
            $hasUsageFunction = $content -match "show_usage\(\)"
            $hasMainFunction = $content -match "main\(\)"
            
            Write-TestResult "Bash script has shebang" $hasShebang
            Write-TestResult "Bash script has usage function" $hasUsageFunction
            Write-TestResult "Bash script has main function" $hasMainFunction
        } catch {
            Write-TestResult "Bash script structure validation failed" $false $_.Exception.Message
        }
    }
}

function Test-CLIBinary {
    Write-TestHeader "CLI Binary Testing"
    
    if (-not (Test-Path $CLIBinary)) {
        Write-TestResult "CLI binary not found - skipping CLI tests" $false "Path: $CLIBinary"
        return
    }
    
    # Test 1: CLI runs without arguments
    try {
        $result = & $CLIBinary 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI runs without arguments" ($exitCode -ne 0) "Expected non-zero exit code, got: $exitCode"
    } catch {
        Write-TestResult "CLI runs without arguments" $false $_.Exception.Message
    }
    
    # Test 2: CLI help/usage
    try {
        $result = & $CLIBinary --help 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI help command" ($exitCode -eq 0) "Exit code: $exitCode"
    } catch {
        Write-TestResult "CLI help command" $false $_.Exception.Message
    }
    
    # Test 3: CLI analyze command with test directory
    $testPath = Join-Path $TestDir "cli_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "console.log('test');" | Out-File -FilePath (Join-Path $testPath "test.js") -Encoding UTF8
    
    try {
        $result = & $CLIBinary analyze $testPath json 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI analyze command" ($exitCode -eq 0) "Exit code: $exitCode"
        
        # Check if output is valid JSON
        if ($exitCode -eq 0 -and $result) {
            try {
                $jsonResult = $result | ConvertFrom-Json
                Write-TestResult "CLI produces valid JSON" $true
            } catch {
                Write-TestResult "CLI produces valid JSON" $false "JSON parsing failed"
            }
        }
    } catch {
        Write-TestResult "CLI analyze command" $false $_.Exception.Message
    }
    
    # Test 4: CLI with invalid arguments
    try {
        $result = & $CLIBinary invalid-command 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI handles invalid command" ($exitCode -ne 0) "Exit code: $exitCode"
    } catch {
        Write-TestResult "CLI handles invalid command" $false $_.Exception.Message
    }
}

function Test-BashScriptValidation {
    Write-TestHeader "Bash Script Validation"
    
    if (-not (Test-Path $BashScript)) {
        Write-TestResult "Bash script not found - skipping validation" $false
        return
    }
    
    # Test 1: Script syntax validation
    try {
        $content = Get-Content $BashScript -Raw
        
        # Check for common bash syntax elements
        $hasFunctions = $content -match "function\s+\w+\s*\(\)" -or $content -match "\w+\s*\(\)\s*\{"
        $hasVariables = $content -match '\$\w+' -or $content -match '\$\{\w+\}'
        $hasConditionals = $content -match "if\s*\[\[" -or $content -match "if\s*\["
        $hasLoops = $content -match "for\s+\w+\s+in" -or $content -match "while\s*\[\["
        
        Write-TestResult "Bash script has functions" $hasFunctions
        Write-TestResult "Bash script uses variables" $hasVariables
        Write-TestResult "Bash script has conditionals" $hasConditionals
        Write-TestResult "Bash script has loops" $hasLoops
        
    } catch {
        Write-TestResult "Bash script syntax validation failed" $false $_.Exception.Message
    }
    
    # Test 2: Required commands/functions
    try {
        $content = Get-Content $BashScript -Raw
        
        $requiredFunctions = @(
            "show_usage",
            "analyze_directory", 
            "export_analysis",
            "scan_file",
            "run_tests",
            "run_demo",
            "check_health"
        )
        
        foreach ($func in $requiredFunctions) {
            $hasFunction = $content -match "$func\s*\(\)" -or $content -match "function\s+$func"
            Write-TestResult "Bash script has $func function" $hasFunction
        }
        
    } catch {
        Write-TestResult "Bash script function validation failed" $false $_.Exception.Message
    }
}

function Test-ErrorHandling {
    Write-TestHeader "Error Handling Validation"
    
    # Test 1: CLI with missing file
    try {
        $result = & $CLIBinary analyze /nonexistent/path json 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI handles missing directory" ($exitCode -ne 0) "Exit code: $exitCode"
    } catch {
        Write-TestResult "CLI handles missing directory" $false $_.Exception.Message
    }
    
    # Test 2: CLI with invalid format
    $testPath = Join-Path $TestDir "error_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "test" | Out-File -FilePath (Join-Path $testPath "test.txt") -Encoding UTF8
    
    try {
        $result = & $CLIBinary analyze $testPath invalid_format 2>&1
        $exitCode = $LASTEXITCODE
        Write-TestResult "CLI handles invalid format" ($exitCode -ne 0) "Exit code: $exitCode"
    } catch {
        Write-TestResult "CLI handles invalid format" $false $_.Exception.Message
    }
}

function Test-OutputFormats {
    Write-TestHeader "Output Format Testing"
    
    # Create test directory
    $testPath = Join-Path $TestDir "format_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    
    # Create test files
    "console.log('clean');" | Out-File -FilePath (Join-Path $testPath "clean.js") -Encoding UTF8
    "console.log('test');$([char]0x200B)" | Out-File -FilePath (Join-Path $testPath "suspicious.js") -Encoding UTF8 -NoNewline
    
    $formats = @("json", "text")
    
    foreach ($format in $formats) {
        try {
            $result = & $CLIBinary analyze $testPath $format 2>&1
            $exitCode = $LASTEXITCODE
            
            if ($exitCode -eq 0) {
                Write-TestResult "$format format works" $true "Exit code: $exitCode"
                
                # Validate format-specific output
                switch ($format) {
                    "json" {
                        try {
                            $jsonResult = $result | ConvertFrom-Json
                            Write-TestResult "$format output is valid" $true
                        } catch {
                            Write-TestResult "$format output is valid" $false "JSON parsing failed"
                        }
                    }
                    "text" {
                        $hasTextContent = $result -and $result.ToString().Length -gt 0
                        Write-TestResult "$format output is valid" $hasTextContent
                    }
                }
            } else {
                Write-TestResult "$format format works" $false "Exit code: $exitCode"
            }
        } catch {
            Write-TestResult "$format format works" $false $_.Exception.Message
        }
    }
}

function Test-BashScriptComments {
    Write-TestHeader "Bash Script Comments and Documentation"
    
    if (-not (Test-Path $BashScript)) {
        Write-TestResult "Bash script not found - skipping comment validation" $false
        return
    }
    
    try {
        $content = Get-Content $BashScript -Raw
        
        # Test for good documentation practices
        $hasHeaderComment = $content -match "^#.*\n#.*"
        $hasUsageComment = $content -match "#.*[Uu]sage" -or $content -match "#.*[Hh]elp"
        $hasFunctionComments = $content -match "#.*function" -or $content -match "# \w+.*function"
        $hasErrorComments = $content -match "#.*[Ee]rror" -or $content -match "#.*[Ff]ail"
        
        Write-TestResult "Bash script has header comments" $hasHeaderComment
        Write-TestResult "Bash script has usage documentation" $hasUsageComment
        Write-TestResult "Bash script has function documentation" $hasFunctionComments
        Write-TestResult "Bash script has error handling comments" $hasErrorComments
        
        # Count comment density
        $lines = $content -split "\n"
        $commentLines = $lines | Where-Object { $_ -match "^\s*#" }
        $codeLines = $lines | Where-Object { $_ -match "\S" -and $_ -notmatch "^\s*#" }
        
        $commentRatio = if ($codeLines.Count -gt 0) { 
            [math]::Round(($commentLines.Count / $codeLines.Count) * 100, 2) 
        } else { 0 }
        
        Write-TestResult "Bash script has adequate comments" ($commentRatio -ge 10) "Comment ratio: $commentRatio%"
        
    } catch {
        Write-TestResult "Bash script comment validation failed" $false $_.Exception.Message
    }
}

function Initialize-TestEnvironment {
    Write-ColoredOutput "Initializing test environment..." "Yellow"
    
    # Create test directory
    if (Test-Path $TestDir) {
        Remove-Item $TestDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TestDir -Force | Out-Null
    
    # Initialize log file
    "CLI and Bash Script Validation Test Log - $(Get-Date)" | Out-File -FilePath $LogFile -Encoding UTF8
    
    Write-ColoredOutput "Test environment initialized." "Green"
}

function Write-TestSummary {
    Write-Host ""
    Write-ColoredOutput "=" * 60 "Cyan"
    Write-ColoredOutput "TEST SUMMARY" "Cyan"
    Write-ColoredOutput "=" * 60 "Cyan"
    
    Write-ColoredOutput "Total Tests: $($TestStats.Total)" "White"
    Write-ColoredOutput "Passed: $($TestStats.Passed)" "Green"
    Write-ColoredOutput "Failed: $($TestStats.Failed)" "Red"
    
    $successRate = if ($TestStats.Total -gt 0) { 
        [math]::Round(($TestStats.Passed / $TestStats.Total) * 100, 2) 
    } else { 0 }
    
    Write-ColoredOutput "Success Rate: $successRate%" $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" })
    
    Write-ColoredOutput "`nDetailed test log: $LogFile" "Cyan"
    
    # Exit with appropriate code
    if ($TestStats.Failed -eq 0) {
        Write-ColoredOutput "`n🎉 All tests passed!" "Green"
        exit 0
    } else {
        Write-ColoredOutput "`n❌ Some tests failed. Check the log for details." "Red"
        exit 1
    }
}

# Main execution
function Main {
    Write-ColoredOutput "🔍 CLI and Bash Script Validation Test Suite" "Cyan"
    Write-ColoredOutput "CLI Binary: $CLIBinary" "White"
    Write-ColoredOutput "Bash Script: $BashScript" "White"
    
    Initialize-TestEnvironment
    
    # Build CLI if needed
    if (-not $SkipBuild -and -not (Test-Path $CLIBinary)) {
        Write-ColoredOutput "Building CLI binary..." "Yellow"
        try {
            $buildResult = cargo build --release --bin analyzer_cli
            if ($LASTEXITCODE -eq 0) {
                Write-ColoredOutput "✅ CLI binary built successfully" "Green"
            } else {
                Write-ColoredOutput "❌ CLI binary build failed" "Red"
            }
        } catch {
            Write-ColoredOutput "❌ CLI binary build failed: $($_.Exception.Message)" "Red"
        }
    }
    
    $testFunctions = @(
        "Test-FileStructure",
        "Test-CLIBinary",
        "Test-BashScriptValidation",
        "Test-ErrorHandling",
        "Test-OutputFormats",
        "Test-BashScriptComments"
    )
    
    foreach ($testFunction in $testFunctions) {
        if ($TestFilter -and $testFunction -notlike "*$TestFilter*") {
            continue
        }        
        try {
            & $testFunction
        } catch {
            Write-ColoredOutput "❌ Test function $testFunction failed with exception: $($_.Exception.Message)" "Red"
            $TestStats.Failed++
        }
    }
    
    Write-TestSummary
}

# Run the main function
Main
