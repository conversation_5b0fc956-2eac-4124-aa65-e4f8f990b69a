# Comprehensive Debugging Guide

*When things go wrong (and they will), this is your battle-tested guide to fixing them. No panic, just methodical problem-solving.*

---

## Emergency Response Checklist

### Step 1: Quick Health Check (30 seconds)
```powershell
# Run this first - it'll catch 80% of issues
Write-Host "🔍 EMERGENCY HEALTH CHECK" -ForegroundColor Red
Write-Host "=========================" -ForegroundColor Red

# Check if app files exist
$critical_files = @(
    "assets/Bad_Characters.json",
    "assets/Advanced_AI_Patterns.json", 
    "assets/file-types-schema.json",
    "src-tauri/Cargo.toml",
    "package.json"
)

foreach ($file in $critical_files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ MISSING: $file" -ForegroundColor Red
        Write-Host "   🚨 CRITICAL ERROR - App cannot function without this file!" -ForegroundColor Red
    }
}

# Check if build works
Write-Host "`n🔧 Build Check:" -ForegroundColor Yellow
cargo check --quiet 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Rust code compiles" -ForegroundColor Green
} else {
    Write-Host "❌ Build errors detected - run 'cargo check' for details" -ForegroundColor RedC:\Users\<USER>\Documents\TextLexia\WriterJ\WriterJ-Gemini
}
```

### **Step 2: Asset Integrity Check** (1 minute)
```powershell
# Verify JSON files aren't corrupted
Write-Host "`n📄 JSON Integrity Check:" -ForegroundColor Yellow

$json_files = Get-ChildItem "assets/*.json"
foreach ($json in $json_files) {
    try {
        $content = Get-Content $json.FullName -Raw | ConvertFrom-Json
        $size = (Get-Item $json.FullName).Length
        Write-Host "✅ $($json.Name) - $size bytes, valid JSON" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $($json.Name) - CORRUPTED JSON!" -ForegroundColor Red
        Write-Host "   💡 Fix: Restore from git or run assets/fix_json.ps1" -ForegroundColor Yellow
    }
}
```

---

## 🚨 **CRITICAL ISSUE RESOLVED: ES Module Import Crash**

### **Issue**: Application crashes with `TypeError: Failed to resolve module specifier '@tauri-apps/api/webview'`

**Symptoms:**
- App starts successfully but crashes when trying to analyze codebase
- Console shows: `TypeError: Failed to resolve module specifier '@tauri-apps/api/webview'`
- WASM errors in browser console
- Drag & drop functionality fails

**Root Cause:**
ES module imports (`import('@tauri-apps/api/webview')`) don't work in WASM context with Leptos.

**✅ SOLUTION:**
Replace ES module imports with Tauri v2 global API:

```javascript
// ❌ BROKEN: ES Module approach
import('@tauri-apps/api/webview').then(({ getCurrentWebview }) => {
    const webview = getCurrentWebview();
    // ...
});

// ✅ FIXED: Global API approach
if (window.__TAURI__ && window.__TAURI__.webview) {
    try {
        const webview = window.__TAURI__.webview.getCurrentWebview();
        // ...
    } catch (error) {
        console.error('Failed to setup drag drop:', error);
    }
}
```

**Files Fixed:**
- `src/components/codebase/main.rs` (line 69)

**Prevention:**
- Always use `window.__TAURI__` global API in WASM context
- Avoid ES module imports for Tauri APIs in Leptos components
- Add proper error handling for Tauri API calls

---

## Problem Categories & Solutions

### 🔴 Category 1: App Won't Start

#### **Symptom:** Black screen, immediate crash, or "failed to load" errors
```powershell
# Debugging Commands
Write-Host "🔍 Startup Issue Debugging:" -ForegroundColor Cyan

# Check current directory
Write-Host "Current directory: $(Get-Location)" -ForegroundColor White

# Verify Tauri setup  
Write-Host "`nTauri Environment:" -ForegroundColor Yellow
cargo tauri --version 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Tauri CLI not installed!" -ForegroundColor Red
    Write-Host "   💡 Fix: cargo install tauri-cli@2.5.6" -ForegroundColor Yellow
}

# Check for common blocking issues
if (Test-Path "target/") {
    $target_size = (Get-ChildItem "target/" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
    if ($target_size -gt 2000) {
        Write-Host "⚠️ Target directory is huge ($([math]::Round($target_size))MB)" -ForegroundColor Yellow
        Write-Host "   💡 Consider: cargo clean" -ForegroundColor Yellow
    }
}

# Port conflicts
$port_1420 = Get-NetTCPConnection -LocalPort 1420 -ErrorAction SilentlyContinue
if ($port_1420) {
    Write-Host "⚠️ Port 1420 is in use" -ForegroundColor Yellow
    Write-Host "   💡 Kill the process or change port in tauri.config.json" -ForegroundColor Yellow
}
```

**🛠️ Solutions:**
1. **Use the startup script:** `.\dev_startup.ps1`
2. **Manual two-step start:**
   ```powershell
   # Terminal 1
   trunk serve --port 1420
   
   # Terminal 2 (wait for Terminal 1 to show "serving on...")
   cargo tauri dev
   ```
3. **Nuclear option:** `cargo clean && cargo tauri dev`

### 🟠 Category 2: Asset Loading Failures

#### **Symptom:** App starts but scanning doesn't work, "No threats detected" on obviously malicious text

```powershell
# Asset Loading Diagnostic
Write-Host "🔍 Asset Loading Diagnostic:" -ForegroundColor Cyan

# Check asset file paths that the app tries
$asset_paths = @(
    "assets/Bad_Characters.json",
    "./assets/Bad_Characters.json", 
    "../assets/Bad_Characters.json",
    "src-tauri/assets/Bad_Characters.json"
)

Write-Host "`nAsset Path Resolution:" -ForegroundColor Yellow
foreach ($path in $asset_paths) {
    if (Test-Path $path) {
        $size = (Get-Item $path).Length
        Write-Host "✅ Found: $path ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $path" -ForegroundColor Red
    }
}

# Check if assets are included in build
Write-Host "`nBuild Include Check:" -ForegroundColor Yellow
$tauri_config = Get-Content "tauri.config.json" -Raw | ConvertFrom-Json
if ($tauri_config.tauri.bundle.resources) {
    Write-Host "✅ Resources configured in tauri.config.json" -ForegroundColor Green
} else {
    Write-Host "⚠️ No resources configured in tauri.config.json" -ForegroundColor Yellow
}
```

**🛠️ Solutions:**
1. **Verify asset location:** Assets must be in `assets/` folder at project root
2. **Check file permissions:** `icacls assets /grant Everyone:R /T`
3. **Test asset loading:**
   ```rust
   // Add to src-tauri/src/main.rs for debugging
   fn main() {
       println!("Current dir: {:?}", std::env::current_dir());
       println!("Bad chars exist: {}", std::path::Path::new("assets/Bad_Characters.json").exists());
       tauri::Builder::default().run(tauri::generate_context!()).expect("error while running tauri application");
   }
   ```

### 🟡 Category 3: Performance Issues

#### **Symptom:** Slow scanning, high memory usage, UI freezing

```powershell
# Performance Diagnostic
Write-Host "🔍 Performance Diagnostic:" -ForegroundColor Cyan

# Check asset file sizes
Write-Host "`nAsset Size Analysis:" -ForegroundColor Yellow
$assets = Get-ChildItem "assets/*.json"
$total_size = 0
foreach ($asset in $assets) {
    $size = $asset.Length
    $total_size += $size
    $size_kb = [math]::Round($size / 1KB, 1)
    
    # Flag unusually large assets
    if ($size -gt 50KB) {
        Write-Host "⚠️ $($asset.Name): ${size_kb}KB (LARGE)" -ForegroundColor Yellow
    } else {
        Write-Host "✅ $($asset.Name): ${size_kb}KB" -ForegroundColor Green
    }
}

$total_kb = [math]::Round($total_size / 1KB, 1)
Write-Host "📊 Total asset size: ${total_kb}KB" -ForegroundColor White

# Memory usage check
$rust_processes = Get-Process | Where-Object {$_.ProcessName -like "*tauri*" -or $_.ProcessName -like "*bad*character*"}
if ($rust_processes) {
    Write-Host "`nMemory Usage:" -ForegroundColor Yellow
    foreach ($proc in $rust_processes) {
        $memory_mb = [math]::Round($proc.WorkingSet / 1MB, 1)
        if ($memory_mb -gt 500) {
            Write-Host "⚠️ $($proc.ProcessName): ${memory_mb}MB (HIGH)" -ForegroundColor Yellow
        } else {
            Write-Host "✅ $($proc.ProcessName): ${memory_mb}MB" -ForegroundColor Green
        }
    }
}
```

**🛠️ Solutions:**
1. **Build in release mode:** `cargo tauri build` (not `cargo tauri dev`)
2. **Profile memory usage:** Use Windows Task Manager or `cargo build --release` + profiling
3. **Check for memory leaks:** Monitor memory usage during extended scanning

### 🔵 Category 4: Build & Development Issues

#### **Symptom:** Compilation errors, dependency issues, environment problems

```powershell
# Development Environment Check
Write-Host "🔍 Development Environment Check:" -ForegroundColor Cyan

# Rust toolchain
Write-Host "`nRust Environment:" -ForegroundColor Yellow
rustc --version
cargo --version

# Node.js environment  
Write-Host "`nNode.js Environment:" -ForegroundColor Yellow
node --version
npm --version

# Tauri CLI
Write-Host "`nTauri Environment:" -ForegroundColor Yellow
cargo tauri --version

# Check for common issues
Write-Host "`nCommon Issue Check:" -ForegroundColor Yellow

# Windows-specific
if ($env:OS -eq "Windows_NT") {
    # Check for Visual Studio Build Tools
    $vs_installs = Get-ChildItem "C:\Program Files*\Microsoft Visual Studio\*\*\MSBuild\Current\Bin\MSBuild.exe" -ErrorAction SilentlyContinue
    if ($vs_installs) {
        Write-Host "✅ Visual Studio Build Tools found" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Visual Studio Build Tools may be missing" -ForegroundColor Yellow
        Write-Host "   💡 Install Visual Studio Community with C++ workload" -ForegroundColor Yellow
    }
}

# Check Cargo.lock for conflicts
if (Test-Path "Cargo.lock") {
    Write-Host "✅ Cargo.lock exists" -ForegroundColor Green
} else {
    Write-Host "⚠️ No Cargo.lock - run 'cargo check' to generate" -ForegroundColor Yellow
}
```

---

## 🧪 **Advanced Debugging Techniques**

### **🔬 Deep Asset Analysis**
```powershell
# Comprehensive Asset Analysis
function Analyze-AssetIntegrity {
    Write-Host "🔬 Deep Asset Analysis" -ForegroundColor Magenta
    
    $bad_chars = Get-Content "assets/Bad_Characters.json" -Raw | ConvertFrom-Json
    Write-Host "📊 Bad_Characters.json stats:" -ForegroundColor White
    Write-Host "   • Total categories: $($bad_chars.PSObject.Properties.Count)" -ForegroundColor White
    
    if ($bad_chars.forbidden_chars) {
        Write-Host "   • Forbidden chars: $($bad_chars.forbidden_chars.Count)" -ForegroundColor White
    }
    
    if ($bad_chars.invisible_chars) {
        $total_invisible = 0
        foreach ($category in $bad_chars.invisible_chars.PSObject.Properties) {
            $total_invisible += $category.Value.Count
        }
        Write-Host "   • Invisible chars: $total_invisible" -ForegroundColor White
    }
    
    # Validate critical fields exist
    $required_fields = @("forbidden_chars", "invisible_chars", "homograph_chars")
    foreach ($field in $required_fields) {
        if ($bad_chars.$field) {
            Write-Host "   ✅ $field present" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $field MISSING!" -ForegroundColor Red
        }
    }
}

Analyze-AssetIntegrity
```

### **🔍 Runtime Debugging**
```rust
// Add this to src-tauri/src/modules/asset_manager.rs for deep debugging
impl AssetManager {
    pub fn debug_asset_loading(&self) -> Result<String, String> {
        let mut debug_info = String::new();
        
        debug_info.push_str(&format!("Asset Manager Debug Info\n"));
        debug_info.push_str(&format!("========================\n"));
        debug_info.push_str(&format!("Current directory: {:?}\n", std::env::current_dir()));
        
        // Check each asset file
        let assets = vec![
            "assets/Bad_Characters.json",
            "assets/Advanced_AI_Patterns.json", 
            "assets/file-types-schema.json",
            "assets/FileTypesSummary.json"
        ];
        
        for asset in assets {
            let path = std::path::Path::new(asset);
            if path.exists() {
                match std::fs::metadata(path) {
                    Ok(metadata) => {
                        debug_info.push_str(&format!("✅ {}: {} bytes\n", asset, metadata.len()));
                    }
                    Err(e) => {
                        debug_info.push_str(&format!("❌ {}: Error reading metadata: {}\n", asset, e));
                    }
                }
            } else {
                debug_info.push_str(&format!("❌ {}: File not found\n", asset));
            }
        }
        
        Ok(debug_info)
    }
}
```

---

## 📋 **Step-by-Step Troubleshooting Workflow**

### **🎯 The 5-Minute Fix Protocol**

1. **⏰ Minute 1:** Run emergency health check script
2. **⏰ Minute 2:** Check asset integrity  
3. **⏰ Minute 3:** Verify environment (Rust, Node, Tauri CLI versions)
4. **⏰ Minute 4:** Try `cargo clean && cargo tauri dev`
5. **⏰ Minute 5:** Check logs and error messages

### **🕰️ The 15-Minute Deep Dive**

If the 5-minute fix doesn't work:

1. **Backup check:** `git status` - make sure you haven't accidentally deleted files
2. **Dependency check:** `cargo update` - update dependencies
3. **Clean rebuild:** `rm -rf target/ && cargo tauri dev`
4. **Port conflicts:** Check if something else is using port 1420
5. **Permission issues:** Verify file permissions on assets folder
6. **Path issues:** Confirm working directory and asset paths
7. **Memory issues:** Check if system has enough RAM (app needs ~200MB)

### **⏳ The Nuclear Option (30 minutes)**

When everything else fails:

1. **Complete clean:** Delete `target/`, `node_modules/`, `dist/`
2. **Fresh install:** `npm install && cargo tauri dev`
3. **Version check:** Ensure Rust 1.75+, Node 18+, Tauri CLI 2.5+
4. **Asset restore:** `git checkout HEAD -- assets/` (restore from git)
5. **Environment reset:** Restart terminal, clear environment variables
6. **System reboot:** Sometimes Windows needs a restart 🤷‍♂️

---

## 🆘 **When All Else Fails**

### **📞 Getting Help**

1. **Gather debug info:**
   ```powershell
   # Create a debug report
   Write-Host "Debug Report" > debug_report.txt
   rustc --version >> debug_report.txt
   cargo --version >> debug_report.txt  
   node --version >> debug_report.txt
   Get-Location >> debug_report.txt
   ls assets/ >> debug_report.txt
   cargo check 2>> debug_report.txt
   ```

2. **Check common issues:** Look at `docs/project-management/known-issues.md`

3. **GitHub Issues:** Create an issue with your debug report

4. **Community:** Ask in discussions with specific error messages

### **🔧 Developer Mode Debugging**

```powershell
# Enable verbose logging
$env:RUST_LOG = "debug"
$env:TAURI_DEBUG = "1" 
cargo tauri dev

# Or for even more detail
$env:RUST_LOG = "trace"
cargo tauri dev
```

---

## 📊 **Success Metrics**

After fixing an issue, verify success:

- ✅ App starts within 10 seconds
- ✅ Can scan text and detect threats
- ✅ Asset files load (check with test malicious text)
- ✅ Export functionality works
- ✅ No error messages in console
- ✅ Memory usage under 300MB for normal operation

---

*💡 **Remember:** 90% of issues are asset-related or environment-related. Start with the basics before diving into complex debugging. And always commit your changes before trying experimental fixes!*
