# Ticket GUI-1: Simplify Frontend GUI

**Date Created:** 2025-06-12
**Status:** Open

## Description

The current frontend GUI in `src/lib.rs` needs to be simplified. The existing `HomePage` component should be commented out and replaced with a new, cleaner implementation.

## Tasks

-   [ ] Comment out the existing `HomePage` component in `src/lib.rs`.
-   [ ] Implement a new `HomePage` component with a focus on a clean layout.
-   [ ] Include two main tabs: "Text Analysis" and "Codebase Analysis".
-   [ ] Implement basic functionality for text input, analysis triggering, and results display for the "Text Analysis" tab.
-   [ ] Implement basic functionality for folder selection, analysis triggering, and results display for the "Codebase Analysis" tab.
-   [ ] Ensure the large hourglass SVG is styled correctly and does not cause layout issues.
-   [ ] Resolve any port conflicts (e.g., port 1420) to ensure `cargo tauri dev` and `trunk serve` run correctly for testing the GUI.

## Acceptance Criteria

-   The old `HomePage` component is no longer active.
-   A new, simplified `HomePage` is in place.
-   Basic text analysis and codebase analysis can be performed through the new UI.
-   The application runs without IPC errors related to the dev server port.
