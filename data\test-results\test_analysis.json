﻿{
  "analysis_time_ms": 100,
  "export_timestamp": "2025-06-13 01:11:04 UTC-0700",
  "export_version": "1.0",
  "file_details": [
    {
      "analysis_status": "success",
      "encoding": "UTF-8",
      "error_message": null,
      "file_path": "test_data_secure\\zero_width_attack.js",
      "file_size": 1003,
      "file_type": "js",
      "issues": [
        "ZERO WIDTH SPACE (U+200B)",
        "ZERO WIDTH NON-JOINER (U+200C)",
        "ZERO WIDTH JOINER (U+200D)",
        "ZERO WIDTH SPACE (U+200B)",
        "ZERO WIDTH NON-JOINER (U+200C)",
        "ZERO WIDTH JOINER (U+200D)",
        "ZERO WIDTH NON-JOINER (U+200C)"
      ],
      "relative_path": "zero_width_attack.js",
      "suspicious_characters": 7,
      "total_characters": 987
    }
  ],
  "files_with_issues": 1,
  "health_score": 99.29078014184397,
  "tool_version": "Bad Character Scanner v0.2.0",
  "total_files": 1,
  "total_suspicious_chars": 7
}
