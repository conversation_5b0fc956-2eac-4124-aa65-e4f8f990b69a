# PowerShell script to clean invisible characters from Rust/config files
# Tauri v2 + Leptos project character cleanup

Write-Host "=== Invisible Character Cleanup Script for Tauri v2 + Leptos ===" -ForegroundColor Green
Write-Host "Cleaning invisible characters from critical project files..." -ForegroundColor Yellow

# Define the files that were detected to have invisible characters
$affectedFiles = @(
    "src\lib.rs",
    "src\lib_broken_backup.rs", 
    "src\lib_complete.rs",
    "src\lib_complex_backup.rs",
    "src-tauri\src\main_module.rs",
    "src-tauri\src\report_generator.rs",
    "scripts\check-bad-characters.js",
    "scripts\check-tauri-version.js",
    "test_asset_integration.rs",
    "test_backend_functionality.rs", 
    "test_simple_integration.rs",
    "test_updated_integration.rs"
)

$cleanedCount = 0
$backupDir = "backup_before_cleanup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Create backup directory
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
Write-Host "Created backup directory: $backupDir" -ForegroundColor Cyan

foreach ($file in $affectedFiles) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor White
        
        # Create backup
        $backupPath = Join-Path $backupDir (Split-Path $file -Leaf)
        Copy-Item $file $backupPath -Force
        
        # Read content and clean invisible characters
        $content = Get-Content $file -Raw -Encoding UTF8
        if ($content) {
            # Remove various invisible characters:
            # Zero-width space, zero-width non-joiner, zero-width joiner, BOM, etc.
            $cleanContent = $content -replace "[\u200B-\u200D\uFEFF]", ""
            # Remove other problematic whitespace characters
            $cleanContent = $cleanContent -replace "[\u00A0\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]", " "
            
            # Write cleaned content back
            Set-Content $file -Value $cleanContent -Encoding UTF8 -NoNewline
            $cleanedCount++
            Write-Host "  Cleaned and saved" -ForegroundColor Green
        } else {
            Write-Host "  File is empty or could not be read" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  File not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Cleanup Complete ===" -ForegroundColor Green
Write-Host "Files processed: $cleanedCount" -ForegroundColor White
Write-Host "Backups saved to: $backupDir" -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run `"cargo clean; trunk clean`"" -ForegroundColor White
Write-Host "2. Run `"trunk serve --port 1420`"" -ForegroundColor White
Write-Host "3. Check if build succeeds and UI loads" -ForegroundColor White
Write-Host "4. If successful, run `"cargo tauri dev`"" -ForegroundColor White
