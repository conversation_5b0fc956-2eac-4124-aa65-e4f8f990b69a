#!/usr/bin/env pwsh
# Comprehensive Bash Script CLI Testing Framework
# Tests CLI functionality and validates against secure test data

param(
    [switch]$Verbose,
    [string]$TestFilter = ""
)

# Configuration
$ProjectRoot = $PWD
$CLIBinary = Join-Path $ProjectRoot "target\release\analyzer_cli.exe"
$SecureTestData = Join-Path $ProjectRoot "test_data_secure"
$TestOutputDir = Join-Path $ProjectRoot "test_results_cli"
$LogFile = Join-Path $TestOutputDir "cli_test_results.log"

# Test tracking
$TestStats = @{
    Total = 0; Passed = 0; Failed = 0
}

# Colors
$Colors = @{
    Reset = "`e[0m"; Red = "`e[91m"; Green = "`e[92m"; Yellow = "`e[93m"
    Blue = "`e[94m"; Magenta = "`e[95m"; Cyan = "`e[96m"; White = "`e[97m"
}

function Write-ColoredOutput {
    param([string]$Text, [string]$Color = "White")
    Write-Host "$($Colors[$Color])$Text$($Colors.Reset)"
}

function Write-TestResult {
    param([string]$TestName, [bool]$Passed, [string]$Details = "")
    
    $TestStats.Total++
    if ($Passed) { $TestStats.Passed++ } else { $TestStats.Failed++ }
    
    $status = if ($Passed) { "✅ PASS" } else { "❌ FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-ColoredOutput "$status : $TestName" $color
    if ($Details) { Write-ColoredOutput "   Details: $Details" "Yellow" }
      $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    "$timestamp [$status] $TestName $(if($Details){" - $Details"})" | Add-Content $LogFile
}

function Test-CLIBasicFunctionality {
    Write-ColoredOutput "`n🔧 Testing CLI Basic Functionality" "Cyan"
    
    # Test 1: CLI exists and is executable
    $exists = Test-Path $CLIBinary
    Write-TestResult "CLI binary exists" $exists "Path: $CLIBinary"
    
    if (-not $exists) { return }
    
    # Test 2: CLI shows usage when called without args
    try {
        $result = & $CLIBinary 2>&1
        $showsUsage = $result -like "*Usage:*" -and $LASTEXITCODE -ne 0
        Write-TestResult "CLI shows usage without arguments" $showsUsage "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "CLI shows usage without arguments" $false $_.Exception.Message
    }
    
    # Test 3: CLI handles invalid commands gracefully
    try {
        $result = & $CLIBinary invalid_command 2>&1
        $handlesInvalid = $LASTEXITCODE -ne 0
        Write-TestResult "CLI handles invalid commands" $handlesInvalid "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "CLI handles invalid commands" $false $_.Exception.Message
    }
}

function Test-SecurityDetection {
    Write-ColoredOutput "`n🛡️ Testing Security Detection Capabilities" "Cyan"
    
    $testFiles = @(
        @{ Name = "zero_width_attack.js"; ExpectedIssues = 7; Description = "Zero-width characters" },
        @{ Name = "bidi_override_attack.py"; ExpectedIssues = 3; Description = "Bidirectional overrides" },
        @{ Name = "homoglyph_attack.html"; ExpectedIssues = @("Cyrillic", "homoglyph"); Description = "Homoglyph attacks" },
        @{ Name = "mixed_attack_sample.txt"; ExpectedIssues = "multiple"; Description = "Mixed attack vectors" },
        @{ Name = "clean_reference.js"; ExpectedIssues = 0; Description = "Clean file (control)" }
    )
    
    foreach ($testFile in $testFiles) {
        $filePath = Join-Path $SecureTestData $testFile.Name
        
        if (-not (Test-Path $filePath)) {
            Write-TestResult "$($testFile.Name) detection test" $false "File not found: $filePath"
            continue
        }
        
        try {
            $result = & $CLIBinary analyze $filePath json 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                # Parse JSON output (remove debug lines)
                $jsonLines = $result | Where-Object { $_ -match "^\s*[\{\[]" -or $_ -match "^\s*\"" -or $_ -match "^\s*\}" }
                $jsonOutput = $jsonLines -join "`n"
                
                try {
                    $analysis = $jsonOutput | ConvertFrom-Json
                    $actualIssues = $analysis.total_suspicious_chars
                    
                    # Validate detection
                    $detectionCorrect = switch ($testFile.ExpectedIssues) {
                        { $_ -is [int] } { $actualIssues -eq $_ }
                        "multiple" { $actualIssues -gt 5 }
                        default { $actualIssues -gt 0 }
                    }
                    
                    Write-TestResult "$($testFile.Name) detection test" $detectionCorrect "Expected: $($testFile.ExpectedIssues), Found: $actualIssues"
                    
                    # Validate health score
                    $healthScore = $analysis.health_score
                    $healthValid = ($actualIssues -eq 0 -and $healthScore -eq 100) -or ($actualIssues -gt 0 -and $healthScore -lt 100)
                    Write-TestResult "$($testFile.Name) health score accuracy" $healthValid "Health: $healthScore%, Issues: $actualIssues"
                    
                } catch {
                    Write-TestResult "$($testFile.Name) JSON parsing" $false "Failed to parse JSON: $($_.Exception.Message)"
                }
            } else {
                Write-TestResult "$($testFile.Name) analysis execution" $false "CLI exit code: $LASTEXITCODE"
            }
        } catch {
            Write-TestResult "$($testFile.Name) analysis execution" $false $_.Exception.Message
        }
    }
}

function Test-OutputFormats {
    Write-ColoredOutput "`n📄 Testing Output Format Support" "Cyan"
    
    $testFile = Join-Path $SecureTestData "zero_width_attack.js"
    $formats = @("json", "text")
    
    foreach ($format in $formats) {
        try {
            $result = & $CLIBinary analyze $testFile $format 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                # Validate format-specific output
                $formatValid = switch ($format) {
                    "json" {
                        $jsonLines = $result | Where-Object { $_ -match "^\s*[\{\[]" -or $_ -match "^\s*\"" -or $_ -match "^\s*\}" }
                        try { 
                            ($jsonLines -join "`n") | ConvertFrom-Json | Out-Null
                            $true 
                        } catch { $false }
                    }
                    "text" {
                        ($result -join "`n").Length -gt 0
                    }
                    default { $false }
                }
                
                Write-TestResult "$format format output" $formatValid "Exit code: $LASTEXITCODE"
            } else {
                Write-TestResult "$format format output" $false "Exit code: $LASTEXITCODE"
            }
        } catch {
            Write-TestResult "$format format output" $false $_.Exception.Message
        }
    }
}

function Test-ErrorHandling {
    Write-ColoredOutput "`n⚠️ Testing Error Handling" "Cyan"
    
    # Test 1: Non-existent file
    try {
        $result = & $CLIBinary analyze "nonexistent_file.txt" json 2>&1
        $handlesFileNotFound = $LASTEXITCODE -ne 0
        Write-TestResult "Non-existent file handling" $handlesFileNotFound "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "Non-existent file handling" $false $_.Exception.Message
    }
    
    # Test 2: Invalid format
    $testFile = Join-Path $SecureTestData "clean_reference.js"
    try {
        $result = & $CLIBinary analyze $testFile "invalid_format" 2>&1
        $handlesInvalidFormat = $LASTEXITCODE -ne 0
        Write-TestResult "Invalid format handling" $handlesInvalidFormat "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "Invalid format handling" $false $_.Exception.Message
    }
    
    # Test 3: Missing arguments
    try {
        $result = & $CLIBinary analyze 2>&1
        $handlesMissingArgs = $LASTEXITCODE -ne 0
        Write-TestResult "Missing arguments handling" $handlesMissingArgs "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "Missing arguments handling" $false $_.Exception.Message
    }
}

function Test-BashScriptValidation {
    Write-ColoredOutput "`n📜 Testing Bash Script Structure" "Cyan"
    
    $bashScript = Join-Path $ProjectRoot "scripts\codebase_analyzer.sh"
    
    if (-not (Test-Path $bashScript)) {
        Write-TestResult "Bash script exists" $false "Path: $bashScript"
        return
    }
    
    Write-TestResult "Bash script exists" $true
    
    try {
        $content = Get-Content $bashScript -Raw
        
        # Test script structure
        $hasShebang = $content -match "^#!/.*bash"
        Write-TestResult "Bash script has shebang" $hasShebang
        
        $hasUsage = $content -match "show_usage\(\)"
        Write-TestResult "Bash script has usage function" $hasUsage
        
        $hasErrorHandling = $content -match "set -euo pipefail"
        Write-TestResult "Bash script has error handling" $hasErrorHandling
        
        # Test required functions
        $requiredFunctions = @(
            "analyze_directory", "export_analysis", "scan_file", 
            "run_tests", "run_demo", "check_health"
        )
        
        foreach ($func in $requiredFunctions) {
            $hasFunction = $content -match "$func\s*\(\)"
            Write-TestResult "Bash script has $func function" $hasFunction
        }
        
        # Test documentation quality
        $lines = $content -split "`n"
        $commentLines = $lines | Where-Object { $_ -match "^\s*#" }
        $codeLines = $lines | Where-Object { $_ -match "\S" -and $_ -notmatch "^\s*#" }
        
        $commentRatio = if ($codeLines.Count -gt 0) { 
            [math]::Round(($commentLines.Count / $codeLines.Count) * 100, 2) 
        } else { 0 }
        
        Write-TestResult "Bash script documentation quality" ($commentRatio -ge 15) "Comment ratio: $commentRatio%"
        
    } catch {
        Write-TestResult "Bash script validation" $false $_.Exception.Message
    }
}

function Test-RealWorldScenarios {
    Write-ColoredOutput "`n🌍 Testing Real-World Scenarios" "Cyan"
    
    # Test 1: Large file handling
    Write-ColoredOutput "Creating large test file..." "Yellow"
    $largeFile = Join-Path $TestOutputDir "large_test_file.js"
    $largeContent = @"
// Large test file with scattered malicious characters
$(1..1000 | ForEach-Object { "const var$_ = 'clean_content_$_';" }) -join "`n"
const malicious1 = 'text_with​zero_width';  // Zero-width space
$(1..1000 | ForEach-Object { "function func$_() { return 'clean_$_'; }" }) -join "`n"
const malicious2 = 'text‌with‍joiners';  // Zero-width joiners
$(1..1000 | ForEach-Object { "// Clean comment $_" }) -join "`n"
"@
    
    $largeContent | Out-File $largeFile -Encoding UTF8
    
    try {
        $startTime = Get-Date
        $result = & $CLIBinary analyze $largeFile json 2>&1
        $duration = (Get-Date) - $startTime
        
        $performanceOk = $LASTEXITCODE -eq 0 -and $duration.TotalSeconds -lt 30
        Write-TestResult "Large file performance" $performanceOk "Duration: $($duration.TotalSeconds)s, Exit code: $LASTEXITCODE"
        
        if ($LASTEXITCODE -eq 0) {
            $jsonLines = $result | Where-Object { $_ -match "^\s*[\{\[]" -or $_ -match "^\s*\"" -or $_ -match "^\s*\}" }
            try {
                $analysis = ($jsonLines -join "`n") | ConvertFrom-Json
                $detectedIssues = $analysis.total_suspicious_chars
                Write-TestResult "Large file detection accuracy" ($detectedIssues -eq 3) "Detected: $detectedIssues issues"
            } catch {
                Write-TestResult "Large file JSON parsing" $false "JSON parse error"
            }
        }
    } catch {
        Write-TestResult "Large file handling" $false $_.Exception.Message
    }
    
    # Test 2: Multiple file types
    $fileTypes = @(".js", ".py", ".html", ".txt", ".md")
    foreach ($ext in $fileTypes) {
        $testFile = Join-Path $TestOutputDir "test$ext"
        "// Test content with​zero-width space" | Out-File $testFile -Encoding UTF8
        
        try {
            $result = & $CLIBinary analyze $testFile json 2>&1
            $handlesFileType = $LASTEXITCODE -eq 0
            Write-TestResult "$ext file type support" $handlesFileType "Exit code: $LASTEXITCODE"
        } catch {
            Write-TestResult "$ext file type support" $false $_.Exception.Message
        }
    }
}

function Initialize-TestEnvironment {
    Write-ColoredOutput "🔧 Initializing Test Environment" "Yellow"
    
    # Create test output directory
    if (Test-Path $TestOutputDir) { Remove-Item $TestOutputDir -Recurse -Force }
    New-Item -ItemType Directory -Path $TestOutputDir -Force | Out-Null
    
    # Initialize log
    "CLI Testing Framework Log - $(Get-Date)" | Out-File $LogFile -Encoding UTF8
    
    # Verify secure test data exists
    if (-not (Test-Path $SecureTestData)) {
        Write-ColoredOutput "❌ Secure test data not found: $SecureTestData" "Red"
        exit 1
    }
    
    Write-ColoredOutput "✅ Test environment ready" "Green"
}

function Write-TestSummary {
    Write-ColoredOutput "`n📊 TEST SUMMARY" "Cyan"
    Write-ColoredOutput "=" * 50 "Cyan"
    
    Write-ColoredOutput "Total Tests: $($TestStats.Total)" "White"
    Write-ColoredOutput "Passed: $($TestStats.Passed)" "Green"
    Write-ColoredOutput "Failed: $($TestStats.Failed)" "Red"
    
    $successRate = if ($TestStats.Total -gt 0) { 
        [math]::Round(($TestStats.Passed / $TestStats.Total) * 100, 2) 
    } else { 0 }
    
    $rateColor = if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" }
    Write-ColoredOutput "Success Rate: $successRate%" $rateColor
    
    Write-ColoredOutput "`nDetailed log: $LogFile" "Cyan"
    
    if ($TestStats.Failed -eq 0) {
        Write-ColoredOutput "`n🎉 All tests passed! CLI is ready for production." "Green"
        exit 0
    } else {
        Write-ColoredOutput "`n⚠️ Some tests failed. Review the results above." "Yellow"
        exit 1
    }
}

# Main execution
function Main {
    Write-ColoredOutput "🧪 Comprehensive CLI Testing Framework" "Cyan"
    Write-ColoredOutput "Testing against secure malicious character samples" "White"
    
    Initialize-TestEnvironment
    
    $testSuites = @(
        "Test-CLIBasicFunctionality",
        "Test-SecurityDetection", 
        "Test-OutputFormats",
        "Test-ErrorHandling",
        "Test-BashScriptValidation",
        "Test-RealWorldScenarios"
    )
    
    foreach ($testSuite in $testSuites) {
        if ($TestFilter -and $testSuite -notlike "*$TestFilter*") { continue }
        
        try {
            & $testSuite
        } catch {
            Write-ColoredOutput "❌ Test suite $testSuite failed: $($_.Exception.Message)" "Red"
            $TestStats.Failed++
        }
    }
    
    Write-TestSummary
}

Main
