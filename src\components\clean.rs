use leptos::*;
use serde::Serialize;
use wasm_bindgen::prelude::*;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
struct CleanRequest {
    text: String,
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[component]
pub fn CleanComponent() -> impl IntoView {
    let (input_text, set_input_text) = create_signal(String::new());
    let (cleaned_text, set_cleaned_text) = create_signal(None::<String>);
    let (is_cleaning, set_is_cleaning) = create_signal(false);

    let clean_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            return;
        }

        set_is_cleaning.set(true);
        spawn_local(async move {
            let args = CleanRequest { text: text.clone() };
            match serde_wasm_bindgen::to_value(&args) {
                Ok(args_js) => {
                    let result = invoke("clean_text", args_js).await;
                    match serde_wasm_bindgen::from_value::<String>(result) {
                        Ok(clean) => set_cleaned_text.set(Some(clean)),
                        Err(e) => set_cleaned_text.set(Some(format!("Error: {:?}", e))),
                    }
                }
                Err(e) => set_cleaned_text.set(Some(format!("Serialization error: {:?}", e))),
            }
            set_is_cleaning.set(false);
        });
    };

    view! {
        <div class="space-y-4">
            <div>
                <label for="clean-input" class="block text-sm font-medium text-gray-700 mb-2">
                    "Text to Clean"
                </label>
                <textarea
                    id="clean-input"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="6"
                    placeholder="Enter text to clean suspicious characters from..."
                    on:input=move |ev| {
                        set_input_text.set(event_target_value(&ev));
                    }
                    prop:value=input_text
                />
            </div>

            <button
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                on:click=clean_text
                disabled=move || is_cleaning.get() || input_text.get().is_empty()
            >
                {move || if is_cleaning.get() { "Cleaning..." } else { "Clean" }}
            </button>

            {move || {
                cleaned_text
                    .get()
                    .map(|result| {
                        view! {
                            <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                <h3 class="text-lg font-semibold mb-2">"Cleaned Text"</h3>
                                <pre class="whitespace-pre-wrap text-sm font-mono bg-white p-3 border rounded">
                                    {result}
                                </pre>
                            </div>
                        }
                    })
            }}
        </div>
    }
}