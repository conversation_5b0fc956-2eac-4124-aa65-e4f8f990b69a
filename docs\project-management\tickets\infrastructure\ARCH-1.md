# ARCH-1 - Leptos-Tauri PWA Architecture Definition

**Status:** 🟡 In Progress  
**Priority:** Critical  
**Created:** 2025-05-27  
**Updated:** 2025-05-27  
**Assigned To:** @dev  
**Related Issues:** SETUP-1, PWA-1, CORE-1

## Description

Define and implement a comprehensive architecture for a Progressive Web Application built with Leptos that seamlessly transitions to a Tauri v2 desktop application. The architecture follows modern web standards, Rust best practices, and maintains code sharing between web and desktop platforms.

## Acceptance Criteria

- [x] Core project structure documented and implemented
- [x] Leptos-Tauri integration patterns established
- [ ] PWA architecture with offline-first design
- [ ] Shared business logic between web and desktop
- [ ] Performance optimization strategies
- [ ] Security model for both platforms
- [ ] CI/CD pipeline for dual deployment

## Architecture Overview

### Multi-Platform Strategy
```
┌─────────────────────────────────────────────────────────┐
│                    Shared Leptos Core                  │
│  ┌─────────────────┐  ┌─────────────────────────────────┐ │
│  │   Components    │  │        Business Logic          │ │
│  │   - Scanner UI  │  │   - Character Analysis         │ │
│  │   - Layout      │  │   - Validation Rules           │ │
│  │   - Common      │  │   - Data Processing            │ │
│  └─────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
           │                               │
           ▼                               ▼
┌─────────────────────┐         ┌─────────────────────┐
│     PWA Platform    │         │  Tauri Platform     │
│                     │         │                     │
│  - Service Worker   │         │  - Native APIs      │
│  - Web APIs         │         │  - File System      │
│  - Cache First      │         │  - System Tray      │
│  - Install Prompt   │         │  - Menu Integration │
│  - Background Sync  │         │  - Auto Updates     │
└─────────────────────┘         └─────────────────────┘
```

### Technology Stack

#### Frontend (Shared)
- **Framework**: Leptos 0.6+ (Rust/WebAssembly)
- **Build Tool**: Trunk for WASM compilation
- **Styling**: CSS-in-Rust with responsive design
- **State Management**: Leptos Signals (fine-grained reactivity)
- **Routing**: Leptos Router for SPA navigation

#### Backend Adaptation Layer
- **PWA Mode**: Web APIs + Local Storage + IndexedDB
- **Desktop Mode**: Tauri v2 Commands + Native File System
- **Shared**: Common business logic in Rust

#### Build Pipeline
- **Development**: Hot reload with Trunk serve
- **PWA Build**: Optimized WASM bundle + Service Worker
- **Desktop Build**: Tauri bundling with embedded web view
- **Deployment**: Automated CI/CD for both platforms

## Project Structure (Detailed)

```
laptos-tauri-v2/
├── 📁 .github/                    # CI/CD and automation
│   └── workflows/
│       ├── ci.yml               # Test, lint, build
│       ├── pwa-deploy.yml       # PWA deployment
│       ├── tauri-release.yml    # Desktop releases
│       └── dependabot.yml       # Dependency updates
│
├── 📁 docs/                       # Documentation
│   ├── tickets/                 # Development tickets
│   ├── architecture/            # Architecture docs
│   └── api/                    # API documentation
│
├── 📁 src/                        # Leptos frontend (shared)
│   ├── 📁 components/
│   │   ├── 📁 layout/           # App shell components
│   │   │   ├── header.rs        # Top navigation
│   │   │   ├── sidebar.rs       # Side navigation
│   │   │   ├── footer.rs        # Footer with status
│   │   │   └── app_shell.rs     # PWA app shell
│   │   ├── 📁 scanner/          # Core functionality
│   │   │   ├── text_input.rs    # Text input with validation
│   │   │   ├── results_panel.rs # Analysis results
│   │   │   ├── char_details.rs  # Character detail view
│   │   │   └── export_dialog.rs # Results export
│   │   ├── 📁 common/           # Reusable components
│   │   │   ├── button.rs        # Button variations
│   │   │   ├── modal.rs         # Modal dialogs
│   │   │   ├── toast.rs         # Notifications
│   │   │   ├── loading.rs       # Loading states
│   │   │   └── error_boundary.rs # Error handling
│   │   └── 📁 pwa/              # PWA-specific
│   │       ├── install_banner.rs # Install prompt
│   │       ├── offline_notice.rs # Offline indicator
│   │       └── update_toast.rs   # Update notifications
│   │
│   ├── 📁 pages/                  # Application routes
│   │   ├── home.rs              # Main scanner interface
│   │   ├── about.rs             # About/help page
│   │   ├── settings.rs          # User preferences
│   │   └── help.rs              # Documentation
│   │
│   ├── 📁 services/               # Business logic layer
│   │   ├── scanner_service.rs   # Character analysis
│   │   ├── storage_service.rs   # Data persistence
│   │   ├── export_service.rs    # Data export
│   │   └── platform_adapter.rs  # Platform abstraction
│   │
│   ├── 📁 hooks/                  # Custom Leptos hooks
│   │   ├── use_scanner.rs       # Scanner state management
│   │   ├── use_storage.rs       # Local storage
│   │   ├── use_theme.rs         # Theme management
│   │   └── use_platform.rs      # Platform detection
│   │
│   ├── 📁 types/                  # Shared type definitions
│   │   ├── scanner.rs           # Scanner data types
│   │   ├── results.rs           # Analysis results
│   │   └── config.rs            # Configuration types
│   │
│   ├── 📁 utils/                  # Utility functions
│   │   ├── unicode.rs           # Unicode utilities
│   │   ├── validation.rs        # Input validation
│   │   └── formatting.rs        # Display formatting
│   │
│   ├── lib.rs                   # Main app component
│   └── main.rs                  # Entry point
│
├── 📁 src-tauri/                 # Tauri desktop backend
│   ├── 📁 src/
│   │   ├── main.rs              # Tauri app entry
│   │   ├── 📁 commands/         # Tauri commands
│   │   │   ├── scanner.rs       # Scanner commands
│   │   │   ├── file_system.rs   # File operations
│   │   │   └── system.rs        # System integration
│   │   ├── 📁 services/         # Backend services
│   │   │   ├── analysis.rs      # Deep analysis
│   │   │   └── export.rs        # Advanced export
│   │   └── 📁 utils/            # Backend utilities
│   │       ├── error.rs         # Error handling
│   │       └── config.rs        # Configuration
│   ├── Cargo.toml               # Backend dependencies
│   ├── tauri.config.json        # Tauri configuration
│   └── 📁 icons/                # Application icons
│
├── 📁 public/                     # PWA static assets
│   ├── 📁 icons/                # PWA icons (various sizes)
│   ├── manifest.json            # PWA manifest
│   ├── sw.js                    # Service worker
│   └── offline.html             # Offline fallback
│
├── 📁 tests/                      # Test suites
│   ├── 📁 unit/                 # Unit tests
│   ├── 📁 integration/          # Integration tests
│   └── 📁 e2e/                  # End-to-end tests
│
├── index.html                   # HTML entry point
├── Cargo.toml                   # Workspace configuration
├── Trunk.toml                   # Build configuration
├── package.json                 # Development tooling
└── README.md                    # Project documentation
```

## Platform Abstraction Strategy

### Service Layer Pattern
```rust
// Platform-agnostic interface
#[async_trait(?Send)]
pub trait StorageService {
    async fn save_results(&self, results: &ScanResults) -> Result<()>;
    async fn load_history(&self) -> Result<Vec<ScanResults>>;
    async fn export_data(&self, format: ExportFormat) -> Result<Vec<u8>>;
}

// PWA implementation
pub struct WebStorageService {
    // Uses IndexedDB/Local Storage
}

// Tauri implementation  
pub struct TauriStorageService {
    // Uses native file system
}
```

### Build Configuration

#### PWA Build (Trunk)
```toml
# Trunk.toml
[build]
target = "./index.html"
dist = "dist"
public_url = "/"

[serve]
port = 1420
open = false

[watch]
ignore = ["dist", "target", "src-tauri"]

[clean]
dist = "dist"
cargo = true
```

#### Desktop Build (Tauri)
```json
// tauri.config.json
{
  "build": {
    "beforeDevCommand": "trunk serve --port 1420",
    "beforeBuildCommand": "trunk build --release",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "app": {
    "withGlobalTauri": true
  }
}
```

## Development Workflow

### 1. PWA Development
```bash
# Start PWA development
trunk serve --port 1420

# Build PWA for production
trunk build --release
```

### 2. Desktop Development
```bash
# Start Tauri development (includes PWA build)
cargo tauri dev

# Build desktop application
cargo tauri build
```

### 3. Testing Strategy
```bash
# Run all tests
cargo test

# Test PWA functionality
wasm-pack test --headless --firefox

# Test Tauri commands
cargo test --manifest-path src-tauri/Cargo.toml
```

## Performance Considerations

### WASM Optimization
- Code splitting by route
- Lazy loading of heavy components
- Efficient signal updates
- Minimal bundle size

### PWA Performance
- App shell caching
- Background sync
- Offline-first data strategy
- Progressive enhancement

### Desktop Performance
- Native API usage where beneficial
- Efficient IPC communication
- Memory management
- Fast startup times

## Security Model

### PWA Security
- Content Security Policy
- HTTPS enforcement
- Secure origins only
- Input sanitization

### Desktop Security
- Tauri's capability system
- Sandboxed web view
- Controlled native API access
- Secure file operations

---
*Last updated: 2025-05-27*
