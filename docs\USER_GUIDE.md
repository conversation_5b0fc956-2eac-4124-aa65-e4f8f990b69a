# User Guide - Bad Character Scanner by <PERSON><PERSON>

**Your complete guide to using the Bad Character Scanner v0.3.1 - A powerful desktop application for Unicode security analysis and threat detection.**

---

## 🚀 **Quick Start (5 Minutes)**

### **Get Started Instantly**
| Step | Action | Time |
|------|--------|------|
| **1. Download** | Get latest release from GitHub | 30 seconds |
| **2. Install** | Run installer for your platform | 2 minutes |
| **3. Launch** | Open Bad Character Scanner | 10 seconds |
| **4. First Scan** | Drop a file or paste text | 30 seconds |
| **5. View Results** | See analysis and security threats | 1 minute |

### **Choose Your Interface**
| Interface | Best For | Quick Access |
|-----------|----------|--------------|
| **🖥️ Desktop GUI** | General users, interactive analysis | Launch app |
| **⌨️ Command Line** | Developers, automation, CI/CD | `bcs-cli analyze` |
| **📜 Scripts** | Advanced automation, batch processing | `./analyze.sh` |

---

## 🛡️ **What is Bad Character Scanner?**

The Bad Character Scanner (BCS) is a **production-ready desktop application** designed to detect, analyze, and clean suspicious Unicode characters from text files and entire codebases. Built with **Leptos + Tauri v2**, it provides enterprise-grade security analysis with a modern, Apple-inspired user experience.

### **🎯 Security Threats We Detect**
| Threat Type | Description | Risk Level | Examples |
|-------------|-------------|------------|----------|
| **Homograph Attacks** | Visually similar characters from different scripts | 🔴 Critical | е (Cyrillic), α (Greek) |
| **Invisible Characters** | Zero-width spaces, control characters | 🔴 Critical | U+200B, U+200C, U+200D |
| **Bidirectional Attacks** | Manipulating text direction | 🔴 Critical | U+202E, U+202D, U+202C |
| **Script Mixing** | Malicious combination of writing systems | 🟠 Medium | Mixed Latin/Cyrillic |
| **AI Content Detection** | Identify AI-generated text patterns | 🟡 Low | Generic structures |

---

## 🎨 **Desktop Application Guide**

### **Modern Interface Design**
The Bad Character Scanner features an Apple-inspired interface with clean, minimal design and intuitive navigation.

#### **Main Interface Elements**
- **🎯 Two Main Modes**: Text Analysis and Codebase Analysis
- **⚙️ Settings Button**: Always accessible in upper-right corner
- **🌙 Theme Support**: Dark/Light/Auto modes
- **📱 Responsive Design**: Scales beautifully across screen sizes

#### **Text Analysis Mode**
```text
📝 Text Analysis Features:
├── 📋 Paste or type text directly
├── 🔍 Real-time analysis as you type
├── 🎨 Visual highlighting of threats
├── 📊 Instant risk scoring
└── 🧹 One-click text cleaning
```

#### **Codebase Analysis Mode**
```text
📂 Codebase Analysis Features:
├── 🖱️ Drag & drop entire folders
├── 📁 Direct path input with validation
├── 📊 Progress bars with time estimates
├── 🔍 30+ file type support
└── 📈 Comprehensive security reports
```

### **Drag & Drop Experience**
- **Visual Feedback**: Highlighted drop zones with smooth animations
- **Multiple Input Methods**: Drag files, paste paths, or browse
- **Automatic Validation**: Instant path verification and file type detection
- **Seamless Workflow**: Consistent experience across all input methods

### **Enhanced Security Analysis Interface**
The security analysis features a tabbed interface with Apple-inspired design:

#### **📊 Overview Tab**
- **Security Score**: Up to 99% (Enterprise upgrade for 100%)
- **Threat Summary**: Visual breakdown of risk levels
- **Quick Actions**: Export, clean, or share results
- **Enterprise CTA**: Upgrade prompts for advanced features

#### **🔍 Bad Characters Tab**
- **Character Details**: Unicode codes and descriptions
- **Risk Assessment**: Individual threat level for each character
- **Context View**: See characters in their original context
- **Replacement Suggestions**: Safe alternatives for dangerous characters

#### **🎭 Homoglyph Attacks Tab**
- **Lookalike Detection**: Characters that appear similar but aren't
- **Script Analysis**: Mixed writing system detection
- **Phishing Prevention**: Domain spoofing protection
- **Visual Comparison**: Side-by-side character comparison

#### **👁️ Visual Spoofing Tab**
- **Direction Attacks**: Bidirectional text manipulation
- **Hidden Content**: Invisible character detection
- **Layout Manipulation**: Text rendering attacks
- **Steganography**: Hidden data in Unicode sequences

---

## ⚙️ **Settings & Configuration**

### **🔧 Settings Panel Access**
Click the **gear button** in the upper-right corner to access comprehensive settings with tabbed organization.

#### **General Settings**
- **Theme**: Light, Dark, or System preference
- **Auto-save**: Automatically save analysis results
- **Default Format**: Choose preferred export format
- **Language**: Interface language selection

#### **Analysis Settings**
| Setting | Options | Default | Description |
|---------|---------|---------|-------------|
| **Threat Sensitivity** | Low, Medium, High, Custom | Medium | Detection threshold |
| **Character Sets** | Basic, Extended, Comprehensive | Extended | Unicode coverage |
| **Risk Thresholds** | Critical (90+), High (70+), Medium (40+) | Standard | Scoring system |
| **Pattern Matching** | Strict, Balanced, Permissive | Balanced | Analysis strictness |

#### **Performance Settings**
- **Analysis Threads**: CPU core utilization (1-16)
- **Memory Limit**: RAM usage cap (100MB-2GB)
- **Progress Bars**: Show for operations >0.4 seconds
- **Background Processing**: Continue when minimized

#### **Export Settings**
- **File Naming**: Timestamp patterns and custom names
- **Report Sections**: Enable/disable specific analysis parts
- **Compression**: Auto-zip large reports
- **Default Location**: Preferred save directory

---

## 🧹 **Text Cleaning & Export**

### **Cleaning Options**
- **Safe Replacement**: Replace dangerous characters with safe alternatives
- **Preserve Meaning**: Maintain text readability and intent
- **Before/After**: Compare original and cleaned versions
- **Configurable Rules**: Customize cleaning behavior

### **Export Formats**
| Format | Use Case | Contains | Best For |
|--------|----------|----------|----------|
| **JSON** | Developer integration | Complete structured data | APIs, automation |
| **CSV** | Spreadsheet analysis | Summary statistics | Excel, data analysis |
| **HTML** | Visual reports | Styled presentation | Sharing, presentations |
| **Markdown** | Documentation | Formatted text | GitHub, wikis |
| **XML** | System integration | Machine-readable format | Enterprise systems |

---

## 📊 **Understanding Results**

### **Security Score Interpretation**
```text
🎯 Security Score Guide:
├── 95-99%: 🟢 Excellent - Minor issues only
├── 85-94%: 🟡 Good - Some attention needed
├── 70-84%: 🟠 Fair - Multiple issues found
├── 50-69%: 🔴 Poor - Significant threats detected
└── <50%:   ⚫ Critical - Immediate action required
```

### **Threat Level Meanings**
- **🔴 Critical**: Immediate security risk, requires urgent attention
- **🟠 High**: Significant risk, should be addressed soon
- **🟡 Medium**: Moderate risk, review when convenient
- **🟢 Low**: Minor issue, informational only

### **Common Threat Patterns**
```text
🔍 Typical Analysis Results:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📂 Files Analyzed: 121
🔴 Critical Issues: 3 (Bidirectional attacks)
🟡 High Risk: 15 (Homoglyph characters)
🟠 Medium Risk: 8 (Control characters)
🟢 Low Risk: 2 (Unicode variants)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 Overall Health Score: 92/100 ✅
```

---

## ⌨️ **Keyboard Shortcuts**

### **Essential Shortcuts**
```text
🔤 Quick Actions:
├── Ctrl+O - Open File/Folder
├── Ctrl+V - Paste Text for Analysis
├── Ctrl+S - Save Current Report
├── Ctrl+E - Export Results
├── Ctrl+, - Open Settings
├── F5 - Refresh Analysis
└── Esc - Cancel Current Operation
```

### **Navigation Shortcuts**
- **Tab**: Move between interface elements
- **Enter**: Activate buttons and start analysis
- **Space**: Toggle checkboxes and options
- **Arrow Keys**: Navigate through results

---

## 🚨 **Troubleshooting**

### **Common Issues & Solutions**

#### **Analysis Not Starting**
1. Check file permissions and path validity
2. Ensure sufficient disk space for reports
3. Verify file types are supported
4. Try restarting the application

#### **Slow Performance**
1. Reduce analysis thread count in settings
2. Lower memory limit if system is constrained
3. Analyze smaller batches of files
4. Close other resource-intensive applications

#### **Export Problems**
1. Check write permissions for output directory
2. Ensure sufficient disk space
3. Try different export format
4. Verify file isn't locked by another application

### **Getting Help**
- **📖 Documentation**: Check this guide and developer handbook
- **🐛 Issues**: Report bugs on GitHub
- **💬 Community**: Join discussions for questions
- **📧 Support**: Contact support for enterprise users

---

## 🎯 **Best Practices**

### **For Regular Users**
- **Regular Scans**: Analyze important documents periodically
- **Before Sharing**: Check files before sending to others
- **Clean Copies**: Always keep cleaned versions of important files
- **Stay Updated**: Keep the application updated for latest threat detection

### **For Developers**
- **Pre-commit Hooks**: Integrate into your development workflow
- **CI/CD Integration**: Add to build pipelines
- **Code Reviews**: Use for security-focused code reviews
- **Documentation**: Scan README files and documentation

### **For Security Teams**
- **Incident Response**: Use for analyzing suspicious files
- **Compliance**: Regular scans for regulatory requirements
- **Training**: Demonstrate Unicode security threats
- **Monitoring**: Set up automated scanning workflows

---

*Ready to get started? Launch the Bad Character Scanner and drop your first file to begin securing your content!*

**Bad Character Scanner by J.Shoy - 2025**
