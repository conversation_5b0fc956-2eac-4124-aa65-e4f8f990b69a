"""
Homograph Attack Test File
This file contains visually similar characters from different scripts
"""

# These look identical but use different Unicode characters
def process_data():  # Normal ASCII
    return "legitimate"

def рrocess_data():  # Cyrillic 'р' instead of 'p'
    return "malicious"

# These domain names look identical but aren't
legitimate_domain = "google.com"
malicious_domain = "gооgle.com"  # Contains Cyrillic 'о' characters

# More homograph examples
file_рath = "/etc/passwd"  # Cyrillic 'р'
admin_user = "аdmin"       # Cyrillic 'а'

# Mixed script attack
def get_user_іnfo():       # Cyrillic 'і' 
    return "confidential data"

print("Homograph attacks completed")
