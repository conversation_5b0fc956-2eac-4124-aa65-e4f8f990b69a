The "cargo tauri dev" command is the standard way to run a Tauri application in development mode using Rust's Cargo tool for Tauri v2, but it currently has some issues and limitations that affect its usability.

Key points:

The Tauri v2 ecosystem, including the CLI and tooling, is still evolving and has been reported as difficult to learn and poorly documented, with breaking issues and a messy directory structure that complicates development and best practices application.

Users have reported bugs and build failures when running cargo tauri dev with Tauri v2, such as build script panics related to permissions and configuration problems.

There is a known bug where cargo tauri dev does not respect environment variables set in .cargo/config.toml, which can cause unexpected behavior during development.

The official Tauri v2 CLI documentation confirms that cargo tauri dev is the intended command to run the app in development mode, alongside other commands like init, build, bundle, and migrate.

Recent releases of the Tauri CLI have improved error handling and process management for the tauri dev command, but some breaking changes and feature toggles remain.

For advanced CLI features like argument parsing inside Tauri apps, the tauri-plugin-cli crate (version 2.x) is available and maintained, which can complement the development experience but is separate from the cargo tauri dev command itself.

Summary:

While cargo tauri dev is the official and primary command for running Tauri v2 apps in development, it currently suffers from some bugs, incomplete environment variable support, and a steep learning curve due to the evolving nature of Tauri v2 tooling and documentation. Developers often need to rely on community resources, migration assistants, and GitHub examples to navigate these challenges effectively.

If you are starting a new Tauri v2 project or migrating from v1, expect some rough edges with cargo tauri dev, but it remains the correct tool to use for development builds. Keeping the CLI updated and monitoring GitHub issues can help mitigate problems.

In short, "cargo tauri dev" is good and necessary for Tauri v2 terminal commands but currently not without some practical difficulties and bugs.