use serde::{Deserialize, Serialize};
use fancy_regex::Regex;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityThreat {
    pub threat_type: SecurityThreatType,
    pub severity: ThreatSeverity,
    pub position: usize,
    pub length: usize,
    pub matched_content: String,
    pub description: String,
    pub impact: String,
    pub remediation: String,
    pub cve_references: Vec<String>,
    pub context: String,
    pub file_path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum SecurityThreatType {
    CodeInjection,
    CommandInjection,
    SqlInjection,
    XssVulnerability,
    PathTraversal,
    InsecureDeserialization,
    HardcodedCredentials,
    WeakCryptography,
    InformationDisclosure,
    PrivilegeEscalation,
    DenialOfService,
    BufferOverflow,
    RaceCondition,
    AuthenticationBypass,
    SessionFixation,
    CsrfVulnerability,
    ClickJacking,
    OpenRedirect,
    FileInclusion,
    RemoteCodeExecution,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatSeverity {
    Critical,  // Immediate exploitation risk
    High,      // High exploitation probability
    Medium,    // Moderate risk
    Low,       // Low risk but still concerning
}

pub struct SecurityScanner {
    threat_patterns: HashMap<SecurityThreatType, Vec<SecurityRule>>,
    context_analyzers: Vec<ContextAnalyzer>,
}

#[derive(Debug, Clone)]
struct SecurityRule {
    regex: Regex,
    description: String,
    severity: ThreatSeverity,
    impact: String,
    remediation: String,
    cve_refs: Vec<String>,
    file_types: Vec<String>,
}

#[derive(Debug, Clone)]
struct ContextAnalyzer {
    name: String,
    patterns: Vec<Regex>,
    risk_multiplier: f32,
}

impl SecurityScanner {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut scanner = SecurityScanner {
            threat_patterns: HashMap::new(),
            context_analyzers: Vec::new(),
        };
        
        scanner.initialize_threat_patterns()?;
        scanner.initialize_context_analyzers()?;
        Ok(scanner)
    }

    fn initialize_threat_patterns(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Critical Code Injection Patterns
        self.add_security_rule(
            SecurityThreatType::CodeInjection,
            r"eval\s*\(\s*[^)]*(?:req\.body|req\.query|req\.params|process\.argv|location\.search)",
            "Direct eval() with user input - immediate RCE risk",
            ThreatSeverity::Critical,
            "Remote Code Execution, complete system compromise",
            "Never use eval() with user input. Use JSON.parse() for data, or implement a secure expression parser",
            vec!["CVE-2021-44228", "CVE-2019-11358"],
            vec!["js", "ts"],
        )?;

        self.add_security_rule(
            SecurityThreatType::CodeInjection,
            r"Function\s*\(\s*[^)]*(?:req\.body|req\.query|input|userdata)",
            "Dynamic Function constructor with user input",
            ThreatSeverity::Critical,
            "Arbitrary code execution, data theft, system compromise",
            "Use predefined functions and validate inputs strictly",
            vec!["CVE-2020-8203"],
            vec!["js", "ts"],
        )?;        // SQL Injection Patterns
        self.add_security_rule(
            SecurityThreatType::SqlInjection,
            r#"(?i)(SELECT|INSERT|UPDATE|DELETE|DROP)\s+.*\s*\+\s*['"][^'"]*(?:req\.|input|user|param)"#,
            "SQL query with string concatenation and user input",
            ThreatSeverity::Critical,
            "Database compromise, data theft, privilege escalation",
            "Use parameterized queries or prepared statements",
            vec!["CVE-2019-16313", "CVE-2020-13379"],
            vec!["js", "ts", "py", "php", "java", "cs"],
        )?;        self.add_security_rule(
            SecurityThreatType::SqlInjection,
            r#"(?i)query\s*\(\s*['"][^'"]*\s*\+\s*[^'"]*['"]"#,
            "Direct query execution with concatenated user input",
            ThreatSeverity::High,
            "SQL injection, unauthorized data access",
            "Use ORM with parameterized queries",
            vec!["CVE-2021-21234"],
            vec!["*"],
        )?;

        // Command Injection Patterns
        self.add_security_rule(
            SecurityThreatType::CommandInjection,
            r#"(?:exec|system|shell_exec|passthru|popen)\s*\(\s*['"][^'"]*\$\{?(?:req\.|process\.argv|input)"#,
            "Command execution with unsanitized user input",
            ThreatSeverity::Critical,
            "System command execution, server compromise",
            "Sanitize input, use allow-lists, or avoid shell commands entirely",
            vec!["CVE-2021-44832", "CVE-2020-14343"],
            vec!["js", "ts", "py", "php", "sh"],
        )?;

        // XSS Vulnerability Patterns
        self.add_security_rule(
            SecurityThreatType::XssVulnerability,
            r"innerHTML\s*=\s*[^;]*(?:req\.|input|user|param)",
            "Direct innerHTML assignment with user data - XSS risk",
            ThreatSeverity::High,
            "Cross-site scripting, session hijacking, CSRF attacks",
            "Use textContent or sanitize HTML with DOMPurify",
            vec!["CVE-2020-6819", "CVE-2019-8625"],
            vec!["js", "ts", "html"],
        )?;

        self.add_security_rule(
            SecurityThreatType::XssVulnerability,
            r"document\.write\s*\(\s*[^)]*(?:location\.|window\.|req\.)",
            "document.write() with potentially unsafe user input",
            ThreatSeverity::High,
            "Script injection, content manipulation",
            "Use safer DOM manipulation methods",
            vec!["CVE-2018-8174"],
            vec!["js", "ts", "html"],
        )?;

        // Path Traversal Patterns
        self.add_security_rule(
            SecurityThreatType::PathTraversal,
            r"(?:fs\.readFile|fs\.writeFile|open|fopen)\s*\([^)]*(?:req\.|input|user).*\.\./",
            "File operations with path traversal sequences",
            ThreatSeverity::High,
            "Unauthorized file access, information disclosure",
            "Validate and sanitize file paths, use allow-lists",
            vec!["CVE-2021-37137", "CVE-2020-7598"],
            vec!["js", "ts", "py", "php", "java"],
        )?;        // Hardcoded Credentials
        self.add_security_rule(
            SecurityThreatType::HardcodedCredentials,
            r#"(?i)(password|passwd|pwd|secret|token|key|api_key)\s*[:=]\s*['"][a-zA-Z0-9_\-]{8,}['"]"#,
            "Hardcoded credentials or API keys in source code",
            ThreatSeverity::Critical,
            "Unauthorized access, privilege escalation, data breach",
            "Move credentials to environment variables or secure vault",
            vec!["CVE-2019-12384", "CVE-2020-13956"],
            vec!["*"],
        )?;

        // Weak Cryptography
        self.add_security_rule(
            SecurityThreatType::WeakCryptography,
            r"(?i)(md5|sha1|des|rc4|ssl3\.0|tls1\.0)\s*\(",
            "Use of weak or deprecated cryptographic algorithms",
            ThreatSeverity::Medium,
            "Cryptographic weakness, data compromise",
            "Use SHA-256, AES-256, TLS 1.2+ or modern alternatives",
            vec!["CVE-2020-12321", "CVE-2019-13224"],
            vec!["*"],
        )?;

        // Insecure Deserialization
        self.add_security_rule(
            SecurityThreatType::InsecureDeserialization,
            r"(?:pickle\.loads|yaml\.load|unserialize|ObjectInputStream)\s*\([^)]*(?:req\.|input|user)",
            "Insecure deserialization of untrusted data",
            ThreatSeverity::Critical,
            "Remote code execution, object injection attacks",
            "Use safe deserialization methods or validate data structure",
            vec!["CVE-2017-5638", "CVE-2019-12384"],
            vec!["py", "java", "php"],
        )?;

        // Buffer Overflow Patterns (C/C++)
        self.add_security_rule(
            SecurityThreatType::BufferOverflow,
            r"(?:strcpy|strcat|sprintf|gets)\s*\(\s*[^,)]*,\s*[^)]*\)",
            "Unsafe string functions prone to buffer overflow",
            ThreatSeverity::High,
            "Memory corruption, arbitrary code execution",
            "Use safe alternatives: strncpy, strncat, snprintf, fgets",
            vec!["CVE-2021-3156", "CVE-2020-14386"],
            vec!["c", "cpp", "h"],
        )?;

        // Information Disclosure
        self.add_security_rule(
            SecurityThreatType::InformationDisclosure,
            r"(?i)console\.log\s*\([^)]*(?:password|token|secret|key|credit|ssn|social)",
            "Sensitive information logged to console",
            ThreatSeverity::Medium,
            "Information disclosure, credential exposure",
            "Remove debug logs or sanitize sensitive data",
            vec!["CVE-2019-12308"],
            vec!["js", "ts"],
        )?;

        // Open Redirect
        self.add_security_rule(
            SecurityThreatType::OpenRedirect,
            r"(?:location\.href|window\.location|redirect)\s*=\s*[^;]*(?:req\.|input|param)",
            "Open redirect vulnerability with user-controlled URL",
            ThreatSeverity::Medium,
            "Phishing attacks, malicious redirects",
            "Validate redirect URLs against allow-list",
            vec!["CVE-2020-11022"],
            vec!["js", "ts", "php"],
        )?;

        Ok(())
    }

    fn add_security_rule(
        &mut self,
        threat_type: SecurityThreatType,
        regex_str: &str,
        description: &str,
        severity: ThreatSeverity,
        impact: &str,
        remediation: &str,
        cve_refs: Vec<&str>,
        file_types: Vec<&str>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let regex = Regex::new(regex_str)?;
        let rule = SecurityRule {
            regex,
            description: description.to_string(),
            severity,
            impact: impact.to_string(),
            remediation: remediation.to_string(),
            cve_refs: cve_refs.iter().map(|s| s.to_string()).collect(),
            file_types: file_types.iter().map(|s| s.to_string()).collect(),
        };

        self.threat_patterns.entry(threat_type)
            .or_insert_with(Vec::new)
            .push(rule);

        Ok(())
    }

    fn initialize_context_analyzers(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Web application context
        self.context_analyzers.push(ContextAnalyzer {
            name: "Web Application".to_string(),
            patterns: vec![
                Regex::new(r"(?i)(express|flask|django|spring|servlet)")?,
                Regex::new(r"(?i)(http|https|www|api|endpoint)")?,
            ],
            risk_multiplier: 1.5,
        });

        // Database context
        self.context_analyzers.push(ContextAnalyzer {
            name: "Database Operations".to_string(),
            patterns: vec![
                Regex::new(r"(?i)(mysql|postgres|mongodb|redis|oracle)")?,
                Regex::new(r"(?i)(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP)")?,
            ],
            risk_multiplier: 1.8,
        });

        // Authentication context
        self.context_analyzers.push(ContextAnalyzer {
            name: "Authentication System".to_string(),
            patterns: vec![
                Regex::new(r"(?i)(login|auth|session|jwt|oauth|saml)")?,
                Regex::new(r"(?i)(password|credential|token|certificate)")?,
            ],
            risk_multiplier: 2.0,
        });

        Ok(())
    }

    pub fn scan_content(&self, content: &str, file_type: &str, file_path: &str) -> Vec<SecurityThreat> {
        let mut threats = Vec::new();

        for (threat_type, rules) in &self.threat_patterns {
            for rule in rules {
                // Check if rule applies to this file type
                if !rule.file_types.contains(&"*".to_string()) && 
                   !rule.file_types.contains(&file_type.to_string()) {
                    continue;
                }

                // Find all matches
                match rule.regex.find_iter(content) {
                    Ok(iter) => {
                        for mat in iter {
                            let position = mat.start();
                            let matched_content = mat.as_str().to_string();
                            let context = self.extract_context(content, position, 150);
                            
                            // Assess contextual risk
                            let severity = self.assess_contextual_severity(&rule.severity, content);

                            threats.push(SecurityThreat {
                                threat_type: threat_type.clone(),
                                severity,
                                position,
                                length: mat.len(),
                                matched_content,
                                description: rule.description.clone(),
                                impact: rule.impact.clone(),
                                remediation: rule.remediation.clone(),
                                cve_references: rule.cve_refs.clone(),
                                context,
                                file_path: file_path.to_string(),
                            });
                        }
                    }
                    Err(_) => {}
                }
            }
        }

        threats
    }

    fn extract_context(&self, content: &str, position: usize, window: usize) -> String {
        // Convert to character indices to avoid Unicode boundary issues
        let chars: Vec<char> = content.chars().collect();
        let char_position = position.min(chars.len());

        let start = char_position.saturating_sub(window);
        let end = (char_position + window).min(chars.len());

        let context: String = chars[start..end].iter().collect();
        context.replace('\n', "\\n").replace('\r', "\\r")
    }

    fn assess_contextual_severity(&self, base_severity: &ThreatSeverity, content: &str) -> ThreatSeverity {
        let mut risk_multiplier = 1.0;
        let mut detected_contexts = Vec::new();

        for analyzer in &self.context_analyzers {
            for pattern in &analyzer.patterns {
                if pattern.is_match(content) {
                    risk_multiplier *= analyzer.risk_multiplier;
                    // Use the name field to track which contexts were detected
                    if !detected_contexts.contains(&analyzer.name) {
                        detected_contexts.push(analyzer.name.clone());
                    }
                }
            }
        }

        // Log detected contexts for debugging/analysis (in a real implementation,
        // this could be used for detailed reporting)
        if !detected_contexts.is_empty() {
            // The name field is now being used to identify security contexts
            // This could be expanded to include context information in threat reports
            #[cfg(debug_assertions)]
            eprintln!("Security contexts detected: {}", detected_contexts.join(", "));
        }

        // Increase severity based on context
        match (base_severity, risk_multiplier) {
            (ThreatSeverity::Critical, _) => ThreatSeverity::Critical,
            (ThreatSeverity::High, m) if m >= 1.8 => ThreatSeverity::Critical,
            (ThreatSeverity::Medium, m) if m >= 2.0 => ThreatSeverity::Critical,
            (ThreatSeverity::Medium, m) if m >= 1.5 => ThreatSeverity::High,
            (ThreatSeverity::Low, m) if m >= 1.8 => ThreatSeverity::High,
            (ThreatSeverity::Low, m) if m >= 1.3 => ThreatSeverity::Medium,
            (severity, _) => severity.clone(),
        }
    }

    pub fn calculate_security_score(&self, threats: &[SecurityThreat]) -> f32 {
        if threats.is_empty() {
            return 100.0;
        }

        let mut score_deduction = 0.0;
        
        for threat in threats {
            score_deduction += match threat.severity {
                ThreatSeverity::Critical => 25.0,
                ThreatSeverity::High => 15.0,
                ThreatSeverity::Medium => 8.0,
                ThreatSeverity::Low => 3.0,
            };
        }

        (100.0f32 - score_deduction as f32).max(0.0)
    }

    pub fn get_threat_summary(&self, threats: &[SecurityThreat]) -> HashMap<SecurityThreatType, usize> {
        let mut summary = HashMap::new();

        for threat in threats {
            *summary.entry(threat.threat_type.clone()).or_insert(0) += 1;
        }

        summary
    }

    /// Get detected security contexts for a given content
    /// This method uses the name field to provide context information
    pub fn get_detected_contexts(&self, content: &str) -> Vec<String> {
        let mut detected_contexts = Vec::new();

        for analyzer in &self.context_analyzers {
            for pattern in &analyzer.patterns {
                if pattern.is_match(content) {
                    // Use the name field to identify the security context
                    if !detected_contexts.contains(&analyzer.name) {
                        detected_contexts.push(analyzer.name.clone());
                    }
                    break; // Found a match for this analyzer, no need to check other patterns
                }
            }
        }

        detected_contexts
    }

    /// Enhanced threat description that includes detected security contexts
    /// This method demonstrates practical use of the name field
    pub fn get_enhanced_threat_description(&self, threat: &SecurityThreat, content: &str) -> String {
        let contexts = self.get_detected_contexts(content);

        if contexts.is_empty() {
            threat.description.clone()
        } else {
            format!(
                "{} [Detected in: {}]",
                threat.description,
                contexts.join(", ")
            )
        }
    }
}
