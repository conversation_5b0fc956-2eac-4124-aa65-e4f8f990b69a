# Comprehensive Guide: Building and Running the Leptos-Tauri V2 Project

## 1. Introduction

This document provides an extensive guide to understanding the build process, runtime behavior, and potential issues specific to our Leptos-Tauri project (`Laptos_TaurieV2_HelloWorld`). This project uses **Tauri v2** with **Leptos** as the frontend framework.

Our application architecture involves a Leptos frontend compiled to WebAssembly using Trunk, hosted by a Tauri v2 backend. This guide details the correct Tauri v2 setup and Leptos integration best practices.

## 2. Project Evolution and Architecture

### 2.1. Technology Stack

*   **Frontend**: Leptos (Rust/WebAssembly framework)
*   **Build Tool**: Trunk (for WASM compilation)
*   **Backend**: Tauri v2 (Rust)
*   **Development**: Vite integration for enhanced DX

### 2.2. Key Design Decisions

*   **Leptos Choice**: Selected for fine-grained reactivity and performance
*   **Tauri v2**: Chosen for enhanced security model and better performance
*   **Trunk Build**: Standard toolchain for Leptos/WASM applications
*   **TypeScript Integration**: Via Vite for improved development experience

## 3. Core Build & Runtime Configuration (Tauri v2 + Leptos)

### 3.1. Frontend Build (Leptos)

*   **Technology**: Leptos framework, compiled to WASM by Trunk
*   **Entry Point**: `index.html` at project root
*   **Build Commands**:
    *   Development: `trunk serve --port 1420`
    *   Production: `trunk build --release`
*   **Output Directory**: `dist/` at project root

### 3.2. Tauri v2 Backend Integration

The primary configuration file is **`src-tauri/tauri.config.json`**.

*   **Build Configuration**:
    ```json
    {
      "build": {
        "beforeDevCommand": "trunk serve --port 1420",
        "beforeBuildCommand": "trunk build --release",
        "devUrl": "http://localhost:1420",
        "frontendDist": "../dist"
      }
    }
    ```

*   **Leptos-Specific Settings**:
    ```json
    {
      "app": {
        "withGlobalTauri": true
      }
    }
    ```

### 3.3. Development and Build Commands

*   **Integrated Development**:
    *   Command: `cargo tauri dev`
    *   Directory: Run from project root
    *   Action: Starts Trunk server, then launches Tauri window

*   **Production Build**:
    *   Command: `cargo tauri build`
    *   Directory: Run from project root
    *   Action: Builds Leptos app, then packages with Tauri

*   **Frontend Only**: `trunk serve` for frontend development

## 4. Common Issues & Solutions (Leptos + Tauri v2)

### 4.1. Build Configuration Issues

1.  **Trunk Port Mismatch**:
    *   **Symptom**: Tauri opens but shows blank screen
    *   **Cause**: `devUrl` port doesn't match Trunk serve port
    *   **Solution**: Ensure both use port 1420 (or update both consistently)

2.  **WASM Loading Errors**:
    *   **Symptom**: Console errors about WASM module loading
    *   **Cause**: Incorrect public path or CORS issues
    *   **Solution**: Verify Trunk configuration and paths

3.  **Hot Reload Not Working**:
    *   **Symptom**: Changes don't reflect automatically
    *   **Cause**: WebSocket connection issues
    *   **Solution**: Check firewall settings and port availability

### 4.2. Leptos-Specific Issues

1.  **Signal Updates Not Reflecting**:
    *   **Symptom**: UI doesn't update when signals change
    *   **Cause**: Improper signal usage or missing reactive context
    *   **Solution**: Ensure signals are used within reactive contexts

2.  **Tauri API Access**:
    *   **Symptom**: `window.__TAURI__` is undefined
    *   **Cause**: `withGlobalTauri` not enabled
    *   **Solution**: Set `"withGlobalTauri": true` in `tauri.config.json`

### 4.3. Performance Optimization

1.  **Large Bundle Sizes**:
    *   **Solution**: Enable code splitting and tree shaking
    *   **Configuration**: Use `trunk build --release` for optimized builds

2.  **Slow Development Builds**:
    *   **Solution**: Use incremental compilation and caching
    *   **Configuration**: Configure Trunk for faster rebuilds

## 5. Recommended Development Workflow

### 5.1. Development Setup

1.  **Install Dependencies**:
    ```bash
    # Install Rust toolchain
    rustup target add wasm32-unknown-unknown
    
    # Install Trunk
    cargo install trunk
    
    # Install Tauri CLI
    cargo install tauri-cli
    ```

2.  **Start Development**:
    ```bash
    # From project root
    cargo tauri dev
    ```

### 5.2. Project Structure Best Practices

```
laptos-tauri-v2/
├── src/                    # Leptos frontend
│   ├── components/         # Reusable components
│   ├── pages/             # Page components
│   ├── lib.rs             # App root
│   └── main.rs            # Entry point
├── src-tauri/             # Tauri backend
├── Cargo.toml             # Workspace config
└── index.html             # HTML entry
```

### 5.3. Debugging Strategies

1.  **Frontend Debugging**:
    *   Use browser developer tools
    *   Add `web_sys::console::log!` statements
    *   Implement error boundaries

2.  **Backend Debugging**:
    *   Use `println!` for simple debugging
    *   Enable Rust backtrace: `RUST_BACKTRACE=1`
    *   Use IDE debugging for complex issues

## 6. Conclusion

This Leptos-Tauri project provides a modern foundation for building high-performance desktop applications. The key to success lies in:

- Proper configuration of Trunk and Tauri integration
- Understanding Leptos's fine-grained reactivity model
- Following Tauri v2's security best practices
- Implementing proper error handling throughout the stack

By following these guidelines, developers can effectively leverage the power of Rust for both frontend and backend development within the Tauri v2 framework.
