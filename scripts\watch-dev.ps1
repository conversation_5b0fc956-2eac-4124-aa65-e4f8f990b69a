#!/usr/bin/env powershell
# Development watcher - monitors for changes and runs appropriate actions

param(
    [switch]$AutoFix,
    [switch]$RunTests,
    [int]$CheckInterval = 5
)

Write-Host "`n👁️  DEVELOPMENT WATCHER" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "Monitoring for changes every $CheckInterval seconds..." -ForegroundColor Gray
Write-Host "Press Ctrl+C to stop" -ForegroundColor Gray

$project_root = $PSScriptRoot | Split-Path -Parent
$last_check = @{}
$error_count = 0

function Get-FileHash {
    param([string]$Path)
    if (Test-Path $Path) {
        return (Get-FileItem $Path).LastWriteTime.Ticks
    }
    return 0
}

function Check-CompilerErrors {
    Write-Host "`n🔍 Checking for compiler errors..." -ForegroundColor Yellow
    Push-Location $project_root
    $errors = cargo check 2>&1 | Select-String -Pattern "error\[E"
    Pop-Location
    
    if ($errors) {
        Write-Host "❌ Found $($errors.Count) compiler errors!" -ForegroundColor Red
        if ($AutoFix) {
            Write-Host "🔧 Attempting auto-fix..." -ForegroundColor Yellow
            & "$PSScriptRoot\fix-compiler-errors.ps1"
        }
        return $errors.Count
    } else {
        Write-Host "✅ No compiler errors!" -ForegroundColor Green
        return 0
    }
}

function Check-Changes {
    $changed_files = @()
    
    # Monitor key directories
    $watch_patterns = @(
        "src\*.rs",
        "src\components\*.rs",
        "src-tauri\src\*.rs",
        "*.toml",
        "*.json"
    )
    
    foreach ($pattern in $watch_patterns) {
        $files = Get-ChildItem -Path $project_root -Filter $pattern -Recurse -ErrorAction SilentlyContinue |
            Where-Object { $_.FullName -notmatch "target|node_modules|dist" }
        
        foreach ($file in $files) {
            $current_hash = Get-FileHash $file.FullName
            $last_hash = $last_check[$file.FullName]
            
            if ($last_hash -and $current_hash -ne $last_hash) {
                $changed_files += $file
            }
            
            $last_check[$file.FullName] = $current_hash
        }
    }
    
    return $changed_files
}

# Initial check
Write-Host "`n📊 Initial status:" -ForegroundColor Cyan
$error_count = Check-CompilerErrors

# Main monitoring loop
while ($true) {
    Start-Sleep -Seconds $CheckInterval
    
    $changes = Check-Changes
    
    if ($changes) {
        Write-Host "`n📝 Detected changes in:" -ForegroundColor Cyan
        foreach ($file in $changes) {
            Write-Host "   - $($file.Name)" -ForegroundColor Gray
        }
        
        # Check for errors after changes
        $new_error_count = Check-CompilerErrors
        
        if ($new_error_count -ne $error_count) {
            if ($new_error_count -gt $error_count) {
                Write-Host "⚠️  Error count increased: $error_count → $new_error_count" -ForegroundColor Red
            } else {
                Write-Host "✨ Error count decreased: $error_count → $new_error_count" -ForegroundColor Green
            }
            $error_count = $new_error_count
        }
        
        # Run tests if requested
        if ($RunTests -and $error_count -eq 0) {
            Write-Host "`n🧪 Running tests..." -ForegroundColor Yellow
            & "$PSScriptRoot\quick-doctor.ps1"
        }
    }
    
    # Show status line
    Write-Host -NoNewline "`r⏰ $(Get-Date -Format 'HH:mm:ss') | Errors: $error_count | Watching..." -ForegroundColor DarkGray
}