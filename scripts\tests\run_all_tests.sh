#!/bin/bash
# 🧪 Master Test Runner for All Bash Scripts
# Executes all test suites and provides comprehensive reporting

set -euo pipefail

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the test framework
source "$SCRIPT_DIR/test_framework.sh"

# Master test configuration
MASTER_LOG="$TEST_RESULTS_DIR/master_test_run.log"
MASTER_REPORT="$TEST_RESULTS_DIR/master_test_report.json"

# Test suite registry
declare -A TEST_SUITES=(
    ["drag_drop"]="$SCRIPT_DIR/test_drag_drop.sh"
    # Add more test suites here as we create them
    # ["pattern_manager"]="$SCRIPT_DIR/test_pattern_manager.sh"
    # ["batch_processor"]="$SCRIPT_DIR/test_batch_processor.sh"
)

# Initialize master test runner
init_master_runner() {
    init_test_framework
    touch "$MASTER_LOG"
    
    echo "$(date): Master test runner initialized" >> "$MASTER_LOG"
    echo -e "${BOLD}🧪 Master Test Runner Initialized${NC}"
    echo "Available test suites: ${!TEST_SUITES[*]}"
    echo
}

# Run individual test suite
run_individual_suite() {
    local suite_name="$1"
    local suite_script="$2"
    
    echo -e "${BOLD}🚀 Running Test Suite: $suite_name${NC}"
    echo "=================================================="
    
    local start_time=$(date +%s)
    local suite_log="$TEST_RESULTS_DIR/${suite_name}_results.log"
    
    if [[ -f "$suite_script" ]]; then
        chmod +x "$suite_script"
        
        # Run the test suite and capture results
        if "$suite_script" > "$suite_log" 2>&1; then
            local exit_code=0
            echo -e "${GREEN}✅ Test Suite '$suite_name' PASSED${NC}"
        else
            local exit_code=1
            echo -e "${RED}❌ Test Suite '$suite_name' FAILED${NC}"
        fi
        
        # Show summary from the log
        if grep -q "Test Results Summary" "$suite_log"; then
            echo "Summary from $suite_name:"
            grep -A 10 "Test Results Summary" "$suite_log" | head -n 10
        fi
        
    else
        echo -e "${RED}❌ Test suite script not found: $suite_script${NC}"
        local exit_code=1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo -e "${BLUE}⏱️  Suite '$suite_name' completed in ${duration}s${NC}"
    echo "=================================================="
    echo
    
    # Log results
    echo "$(date): Suite '$suite_name' completed with exit code $exit_code in ${duration}s" >> "$MASTER_LOG"
    
    return $exit_code
}

# Run all test suites
run_all_suites() {
    local total_suites=${#TEST_SUITES[@]}
    local passed_suites=0
    local failed_suites=0
    local suite_results=()
    
    echo -e "${BOLD}🎯 Running All Test Suites ($total_suites total)${NC}"
    echo "=============================================="
    echo
    
    for suite_name in "${!TEST_SUITES[@]}"; do
        local suite_script="${TEST_SUITES[$suite_name]}"
        
        if run_individual_suite "$suite_name" "$suite_script"; then
            ((passed_suites++))
            suite_results+=("$suite_name:PASS")
        else
            ((failed_suites++))
            suite_results+=("$suite_name:FAIL")
        fi
    done
    
    # Generate master report
    generate_master_report "$total_suites" "$passed_suites" "$failed_suites" "${suite_results[@]}"
    
    # Show master summary
    show_master_summary "$total_suites" "$passed_suites" "$failed_suites"
    
    # Return success only if all suites passed
    [[ $failed_suites -eq 0 ]]
}

# Run specific test suites
run_specific_suites() {
    local suites_to_run=("$@")
    local total_suites=${#suites_to_run[@]}
    local passed_suites=0
    local failed_suites=0
    
    echo -e "${BOLD}🎯 Running Specific Test Suites ($total_suites selected)${NC}"
    echo "=============================================="
    echo
    
    for suite_name in "${suites_to_run[@]}"; do
        if [[ -n "${TEST_SUITES[$suite_name]:-}" ]]; then
            local suite_script="${TEST_SUITES[$suite_name]}"
            
            if run_individual_suite "$suite_name" "$suite_script"; then
                ((passed_suites++))
            else
                ((failed_suites++))
            fi
        else
            echo -e "${RED}❌ Unknown test suite: $suite_name${NC}"
            echo "Available suites: ${!TEST_SUITES[*]}"
            ((failed_suites++))
        fi
    done
    
    show_master_summary "$total_suites" "$passed_suites" "$failed_suites"
    
    [[ $failed_suites -eq 0 ]]
}

# Generate comprehensive master report
generate_master_report() {
    local total="$1"
    local passed="$2"
    local failed="$3"
    shift 3
    local results=("$@")
    
    cat > "$MASTER_REPORT" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "test_run_type": "master",
    "summary": {
        "total_suites": $total,
        "passed_suites": $passed,
        "failed_suites": $failed,
        "success_rate": $(echo "scale=2; $passed * 100 / $total" | bc -l)
    },
    "suite_results": [
$(printf '        {"suite": "%s", "status": "%s"},\n' $(printf '%s\n' "${results[@]}" | sed 's/:/" "status": "/') | sed '$ s/,$//')
    ],
    "execution_environment": {
        "hostname": "$(hostname)",
        "user": "$(whoami)",
        "pwd": "$(pwd)",
        "bash_version": "$BASH_VERSION"
    },
    "log_files": {
        "master_log": "$MASTER_LOG",
        "results_directory": "$TEST_RESULTS_DIR"
    }
}
EOF
    
    echo -e "${GREEN}📊 Master report generated: $MASTER_REPORT${NC}"
}

# Show master summary
show_master_summary() {
    local total="$1"
    local passed="$2"
    local failed="$3"
    
    echo "=============================================="
    echo -e "${BOLD}📊 Master Test Results Summary${NC}"
    echo "=============================================="
    echo -e "Total Test Suites: ${BOLD}$total${NC}"
    echo -e "Passed:            ${GREEN}$passed${NC}"
    echo -e "Failed:            ${RED}$failed${NC}"
    
    if [[ $total -gt 0 ]]; then
        local success_rate=$((passed * 100 / total))
        echo -e "Success Rate:      ${BOLD}${success_rate}%${NC}"
    fi
    
    echo
    echo -e "Master Log:        ${BLUE}$MASTER_LOG${NC}"
    echo -e "Master Report:     ${BLUE}$MASTER_REPORT${NC}"
    echo -e "Results Directory: ${BLUE}$TEST_RESULTS_DIR${NC}"
    echo
    
    if [[ $failed -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL TEST SUITES PASSED!${NC}"
        return 0
    else
        echo -e "${RED}💥 SOME TEST SUITES FAILED!${NC}"
        echo -e "${YELLOW}Check individual suite logs in: $TEST_RESULTS_DIR${NC}"
        return 1
    fi
}

# List available test suites
list_suites() {
    echo -e "${BOLD}📋 Available Test Suites:${NC}"
    echo "=========================="
    
    for suite_name in "${!TEST_SUITES[@]}"; do
        local suite_script="${TEST_SUITES[$suite_name]}"
        if [[ -f "$suite_script" ]]; then
            echo -e "${GREEN}✅${NC} $suite_name ($suite_script)"
        else
            echo -e "${RED}❌${NC} $suite_name ($suite_script) - MISSING"
        fi
    done
    echo
}

# Check test environment
check_environment() {
    echo -e "${BOLD}🔍 Checking Test Environment${NC}"
    echo "=============================="
    
    # Check required commands
    local required_commands=("bash" "jq" "bc")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if command -v "$cmd" >/dev/null 2>&1; then
            echo -e "${GREEN}✅${NC} $cmd available"
        else
            echo -e "${RED}❌${NC} $cmd missing"
            missing_commands+=("$cmd")
        fi
    done
    
    # Check test scripts
    echo
    echo "Test Scripts Status:"
    for suite_name in "${!TEST_SUITES[@]}"; do
        local suite_script="${TEST_SUITES[$suite_name]}"
        if [[ -f "$suite_script" ]]; then
            if [[ -x "$suite_script" ]]; then
                echo -e "${GREEN}✅${NC} $suite_name (executable)"
            else
                echo -e "${YELLOW}⚠️${NC} $suite_name (not executable, will be fixed)"
                chmod +x "$suite_script" 2>/dev/null || true
            fi
        else
            echo -e "${RED}❌${NC} $suite_name (missing script)"
        fi
    done
    
    echo
    
    if [[ ${#missing_commands[@]} -eq 0 ]]; then
        echo -e "${GREEN}🎉 Environment check passed!${NC}"
        return 0
    else
        echo -e "${RED}💥 Environment check failed!${NC}"
        echo -e "${YELLOW}Missing commands: ${missing_commands[*]}${NC}"
        return 1
    fi
}

# Show help
show_help() {
    echo -e "${BOLD}🧪 Master Test Runner${NC}"
    echo "======================"
    echo
    echo "Usage: $0 [OPTIONS] [SUITES...]"
    echo
    echo "Options:"
    echo "  -h, --help       Show this help message"
    echo "  -l, --list       List available test suites"
    echo "  -a, --all        Run all test suites (default)"
    echo "  -c, --check      Check test environment"
    echo "  -v, --verbose    Enable verbose output"
    echo
    echo "Examples:"
    echo "  $0                           # Run all test suites"
    echo "  $0 --all                     # Run all test suites"
    echo "  $0 drag_drop                 # Run specific test suite"
    echo "  $0 drag_drop pattern_manager # Run multiple specific suites"
    echo "  $0 --list                    # List available suites"
    echo "  $0 --check                   # Check environment"
    echo
    echo "Available test suites: ${!TEST_SUITES[*]}"
}

# Main function
main() {
    local run_all=false
    local list_suites_flag=false
    local check_env_flag=false
    local verbose=false
    local specific_suites=()
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                list_suites_flag=true
                shift
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            -c|--check)
                check_env_flag=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -*)
                echo -e "${RED}❌ Unknown option: $1${NC}"
                show_help
                exit 1
                ;;
            *)
                specific_suites+=("$1")
                shift
                ;;
        esac
    done
    
    # Initialize
    init_master_runner
    
    # Handle specific actions
    if [[ "$check_env_flag" == true ]]; then
        check_environment
        exit $?
    fi
    
    if [[ "$list_suites_flag" == true ]]; then
        list_suites
        exit 0
    fi
    
    # Determine what to run
    if [[ ${#specific_suites[@]} -gt 0 ]]; then
        run_specific_suites "${specific_suites[@]}"
    else
        run_all_suites
    fi
}

# Run main function with all arguments
main "$@"
