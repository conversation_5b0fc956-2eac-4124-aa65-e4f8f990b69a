@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Design System for Bad Character Scanner */
@layer base {
  :root {
    /* NASA-inspired Color Palette (UX-1) */
    --primary-blue: #0066CC;
    --secondary-red: #E03C31;
    --success-green: #28A745;
    --warning-yellow: #FFC107;
    --background-light: #FFFFFF;
    --background-dark: #121212;
    --surface-light: #F8F9FA;
    --surface-dark: #1E1E1E;
    --text-primary: #212529;
    --text-secondary: #6C757D;
    --border-color: #E9ECEF;
    
    /* Spacing System */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-xxl: 48px;
    
    /* Typography */
    --font-primary: 'Roboto', sans-serif;
    --font-code: 'Fira Code', monospace;
    --font-size-base: 16px;
    --font-scale: 1.250;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  * {
    box-sizing: border-box;
  }
  
  body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--surface-light);
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--space-md);
  }
  
  code {
    font-family: var(--font-code);
    background-color: #F3F4F6;
    padding: 2px 4px;
    border-radius: var(--radius-sm);
  }
}

@layer components {
  /* Enhanced Button System */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2;
    @apply inline-flex items-center justify-center cursor-pointer;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
    @apply shadow-md hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  
  .btn-sm {
    @apply px-3 py-1 text-sm;
  }
  
  .btn:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  /* Enhanced Input System */
  .input {
    @apply px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2;
    @apply focus:ring-blue-500 focus:border-blue-500 transition-all duration-200;
    @apply bg-white text-gray-900 placeholder-gray-500;
  }
  
  .input-error {
    @apply border-red-300 bg-red-50 focus:ring-red-500 focus:border-red-500;
  }
  
  .input-success {
    @apply border-green-300 bg-green-50 focus:ring-green-500 focus:border-green-500;
  }
  
  /* Card System */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Status Indicators */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply bg-green-100 text-green-800;
  }
  
  .status-error {
    @apply bg-red-100 text-red-800;
  }
  
  .status-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-info {
    @apply bg-blue-100 text-blue-800;
  }
  
  /* Interface State Animations */
  .interface-state-enter {
    @apply opacity-0 transform scale-95;
  }
  
  .interface-state-enter-active {
    @apply opacity-100 transform scale-100 transition-all duration-300 ease-out;
  }
  
  .interface-state-exit {
    @apply opacity-100 transform scale-100;
  }
  
  .interface-state-exit-active {
    @apply opacity-0 transform scale-95 transition-all duration-200 ease-in;
  }
  
  /* Drag & Drop Zone */
  .drag-zone {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center;
    @apply transition-all duration-200 hover:border-gray-400;
  }
  
  .drag-zone-active {
    @apply border-blue-400 bg-blue-50;
  }
  
  /* Progress Indicators */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }
  
  .progress-fill {
    @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
  }
  
  /* Accessibility Enhancements */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
  
  .focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }
  
  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .btn-primary {
      @apply border-2 border-blue-800;
    }
    
    .input {
      @apply border-2 border-gray-600;
    }
  }
  
  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
  
  /* Dark Mode Support */
  @media (prefers-color-scheme: dark) {
    :root {
      --background-light: #121212;
      --surface-light: #1E1E1E;
      --text-primary: #F8F9FA;
      --text-secondary: #ADB5BD;
      --border-color: #343A40;
    }
    
    .card {
      @apply bg-gray-800 border-gray-700;
    }
    
    .input {
      @apply bg-gray-800 border-gray-600 text-white;
    }
  }
  
  /* Professional Action Cards */
  .action-card {
    @apply bg-gradient-to-r rounded-lg p-6 hover:shadow-md transition-all duration-200;
    @apply cursor-pointer border border-transparent hover:border-opacity-50;
  }
  
  .action-card-primary {
    @apply from-blue-50 to-indigo-50 border-blue-200 hover:border-blue-300;
  }
  
  .action-card-success {
    @apply from-green-50 to-emerald-50 border-green-200 hover:border-green-300;
  }
  
  .action-card-warning {
    @apply from-yellow-50 to-orange-50 border-yellow-200 hover:border-yellow-300;
  }
  
  .action-card-purple {
    @apply from-purple-50 to-pink-50 border-purple-200 hover:border-purple-300;
  }
}

@layer utilities {
  /* Custom utilities for enhanced UX */
  .text-balance {
    text-wrap: balance;
  }
  
  .animation-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animation-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animation-pulse-subtle {
    animation: pulseSubtle 2s infinite;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulseSubtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* File Type Icons and Colors */
.file-type-js { @apply text-yellow-600; }
.file-type-ts { @apply text-blue-600; }
.file-type-py { @apply text-green-600; }
.file-type-rs { @apply text-orange-600; }
.file-type-css { @apply text-purple-600; }
.file-type-html { @apply text-red-600; }
.file-type-json { @apply text-gray-600; }
.file-type-md { @apply text-blue-500; }
.file-type-txt { @apply text-gray-500; }
.file-type-unknown { @apply text-gray-400; }
