# Simple CLI and Bash Script Test

$ErrorActionPreference = "Continue"
$ProjectRoot = $PWD
$CLIBinary = Join-Path $ProjectRoot "target\release\analyzer_cli.exe"
$BashScript = Join-Path $ProjectRoot "scripts\codebase_analyzer.sh"

Write-Host "CLI and Bash Script Validation" -ForegroundColor Cyan
Write-Host "CLI Binary: $CLIBinary" -ForegroundColor White
Write-Host "Bash Script: $BashScript" -ForegroundColor White
Write-Host ""

$totalTests = 0
$passedTests = 0

function Test-Result {
    param($TestName, $Passed, $Details = "")
    $script:totalTests++
    if ($Passed) {
        $script:passedTests++
        Write-Host "PASS: $TestName" -ForegroundColor Green
    } else {
        Write-Host "FAIL: $TestName" -ForegroundColor Red
        if ($Details) {
            Write-Host "   Details: $Details" -ForegroundColor Yellow
        }
    }
}

# Test files exist
Test-Result "CLI binary exists" (Test-Path $CLIBinary)
Test-Result "Bash script exists" (Test-Path $BashScript)

# Test Bash script structure
if (Test-Path $BashScript) {
    $content = Get-Content $BashScript -Raw
    Test-Result "Bash script has shebang" ($content -match "^#!/.*bash")
    Test-Result "Bash script has show_usage function" ($content -match "show_usage")
    Test-Result "Bash script has analyze_directory function" ($content -match "analyze_directory")
    Test-Result "Bash script has error handling" ($content -match "log_error")
    Test-Result "Bash script has verbose output" ($content -match "log_debug")
    
    # Count comments
    $lines = $content -split "\n"
    $commentLines = ($lines | Where-Object { $_ -match "^\s*#" }).Count
    $codeLines = ($lines | Where-Object { $_ -match "\S" -and $_ -notmatch "^\s*#" }).Count
    $commentRatio = if ($codeLines -gt 0) { [math]::Round(($commentLines / $codeLines) * 100, 2) } else { 0 }
    
    Test-Result "Bash script has good documentation" ($commentRatio -ge 15) "Comment ratio: $commentRatio%"
    
    # Check for required functions
    $functions = @("show_usage", "analyze_directory", "export_analysis", "scan_file", "run_tests", "run_demo", "check_health")
    foreach ($func in $functions) {
        $hasFunction = $content -match "$func\s*\(\)"
        Test-Result "Has $func function" $hasFunction
    }
    
    # Check for good practices
    Test-Result "Uses 'set -euo pipefail'" ($content -match "set -euo pipefail")
    Test-Result "Has cleanup function" ($content -match "cleanup\s*\(\)")
    Test-Result "Has argument parsing" ($content -match "parse_args")
    Test-Result "Has help documentation" ($content -match "USAGE")
}

# Test CLI if exists
if (Test-Path $CLIBinary) {
    $testDir = "test_temp_cli"
    New-Item -ItemType Directory -Path $testDir -Force | Out-Null
    $testFile = Join-Path $testDir "test.js"
    "console.log('test');" | Out-File -FilePath $testFile -Encoding UTF8
    
    try {
        $result = & $CLIBinary analyze $testDir json 2>&1
        $exitCode = $LASTEXITCODE
        Test-Result "CLI analyze works" ($exitCode -eq 0) "Exit code: $exitCode"
        
        if ($exitCode -eq 0 -and $result) {
            try {
                $jsonResult = $result | ConvertFrom-Json
                Test-Result "CLI produces valid JSON" $true
            } catch {
                Test-Result "CLI produces valid JSON" $false
            }
        }
        
        $result = & $CLIBinary analyze "nonexistent" json 2>&1
        $exitCode = $LASTEXITCODE
        Test-Result "CLI handles errors" ($exitCode -ne 0)
        
    } catch {
        Test-Result "CLI testing failed" $false
    }
    
    Remove-Item $testDir -Recurse -Force -ErrorAction SilentlyContinue
}

# Summary
Write-Host ""
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } else { "Red" })

Write-Host ""
if ($passedTests -eq $totalTests) {
    Write-Host "All tests passed!" -ForegroundColor Green
} else {
    Write-Host "Some tests failed." -ForegroundColor Red
}
