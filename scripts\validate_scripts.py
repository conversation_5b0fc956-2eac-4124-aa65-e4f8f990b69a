#!/usr/bin/env python3
"""
Script Validation Tool
Validates all current project scripts for syntax and dependencies
"""

import ast
import sys
import subprocess
import json
from pathlib import Path

def validate_python_syntax(script_path):
    """Validate Python syntax of a script"""
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()

        ast.parse(content)
        return True, ""
    except SyntaxError as e:
        return False, f"Syntax error at line {e.lineno}: {e.msg}"
    except Exception as e:
        return False, str(e)

def validate_powershell_syntax(script_path):
    """Validate PowerShell syntax using PowerShell parser"""
    try:
        # Use PowerShell to check syntax
        result = subprocess.run(
            ["powershell", "-Command", f"Get-Content '{script_path}' | Out-Null; $?"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0, result.stderr if result.stderr else ""
    except subprocess.TimeoutExpired:
        return False, "PowerShell validation timed out"
    except FileNotFoundError:
        return True, "PowerShell not available for validation"
    except Exception as e:
        return False, str(e)

def validate_javascript_syntax(script_path):
    """Validate JavaScript syntax using Node.js"""
    try:
        # Use Node.js to check syntax
        result = subprocess.run(
            ["node", "--check", str(script_path)],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0, result.stderr if result.stderr else ""
    except subprocess.TimeoutExpired:
        return False, "Node.js validation timed out"
    except FileNotFoundError:
        return True, "Node.js not available for validation"
    except Exception as e:
        return False, str(e)

def check_script_dependencies(script_path):
    """Check if script has all required dependencies"""
    # Updated dependencies for current scripts
    dependencies = {
        "validate_scripts.py": ["ast", "sys", "subprocess", "json", "pathlib"]
    }

    script_name = script_path.name
    required_deps = dependencies.get(script_name, [])

    missing_deps = []
    for dep in required_deps:
        try:
            __import__(dep)
        except ImportError:
            missing_deps.append(dep)

    return len(missing_deps) == 0, missing_deps

def check_external_tools():
    """Check availability of external tools"""
    tools = {
        "cargo": "Rust package manager",
        "node": "Node.js runtime",
        "powershell": "PowerShell runtime"
    }

    available_tools = {}
    for tool, description in tools.items():
        try:
            result = subprocess.run([tool, "--version"], capture_output=True, timeout=5)
            available_tools[tool] = result.returncode == 0
        except (FileNotFoundError, subprocess.TimeoutExpired):
            available_tools[tool] = False

    return available_tools

def main():
    """Main validation function"""
    print("🔍 Validating Project Scripts")
    print("=" * 50)

    scripts_dir = Path("scripts")

    # Current active scripts to validate
    scripts_to_validate = {
        "validate_scripts.py": "python",
        "check-bad-characters.js": "javascript",
        "check-tauri-version.js": "javascript",
        "start-dev.ps1": "powershell",
        "check-quality.ps1": "powershell",
        "setup-dev-environment.ps1": "powershell",
        "codebase_analyzer.ps1": "powershell",
        "enhanced_analyzer.ps1": "powershell"
    }

    all_valid = True
    validation_results = {}

    # Check external tools first
    print("\n🔧 Checking External Tools...")
    external_tools = check_external_tools()
    for tool, available in external_tools.items():
        status = "✅ Available" if available else "❌ Missing"
        print(f"  {tool}: {status}")
        if not available and tool in ["cargo"]:  # cargo is critical
            all_valid = False

    print(f"\n📄 Validating {len(scripts_to_validate)} Scripts...")

    for script_name, script_type in scripts_to_validate.items():
        script_path = scripts_dir / script_name
        print(f"\n📄 {script_name} ({script_type})...")

        if not script_path.exists():
            print(f"❌ Script not found: {script_path}")
            all_valid = False
            validation_results[script_name] = {"exists": False, "syntax": False, "deps": False}
            continue

        validation_results[script_name] = {"exists": True}

        # Check syntax based on script type
        syntax_valid = True
        syntax_error = ""

        if script_type == "python":
            syntax_valid, syntax_error = validate_python_syntax(script_path)
        elif script_type == "powershell":
            syntax_valid, syntax_error = validate_powershell_syntax(script_path)
        elif script_type == "javascript":
            syntax_valid, syntax_error = validate_javascript_syntax(script_path)

        if syntax_valid:
            print("  ✅ Syntax: Valid")
        else:
            print(f"  ❌ Syntax: {syntax_error}")
            all_valid = False

        validation_results[script_name]["syntax"] = syntax_valid

        # Check dependencies for Python scripts
        if script_type == "python":
            deps_valid, missing_deps = check_script_dependencies(script_path)
            if deps_valid:
                print("  ✅ Dependencies: Available")
            else:
                print(f"  ⚠️ Missing dependencies: {', '.join(missing_deps)}")
            validation_results[script_name]["deps"] = deps_valid
        else:
            validation_results[script_name]["deps"] = True  # Not applicable

    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)

    valid_scripts = sum(1 for result in validation_results.values()
                       if result.get("exists", False) and result.get("syntax", False))
    total_scripts = len(scripts_to_validate)

    print(f"Scripts validated: {valid_scripts}/{total_scripts}")

    if all_valid:
        print("✅ All scripts validated successfully!")
        print("\n🚀 Ready to use project scripts:")
        print("  • Development: .\\scripts\\start-dev.ps1")
        print("  • Quality Check: .\\scripts\\check-quality.ps1")
        print("  • Bad Characters: node scripts\\check-bad-characters.js")
        print("  • Codebase Analysis: .\\scripts\\codebase_analyzer.ps1 analyze <path>")
        print("  • Setup Environment: .\\scripts\\setup-dev-environment.ps1")
    else:
        print("❌ Some scripts have issues")
        print("Please fix the issues before using the scripts")
        print("\n💡 Common fixes:")
        print("  • Install missing tools (cargo, node, powershell)")
        print("  • Check script syntax and fix errors")
        print("  • Ensure all dependencies are available")

    return all_valid

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
