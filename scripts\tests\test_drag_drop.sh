#!/bin/bash
# 🧪 Test Suite for Drag & Drop Simulation
# Tests all functionality of the drag & drop simulation script

set -euo pipefail

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the test framework
source "$SCRIPT_DIR/test_framework.sh"

# Path to the script we're testing
DRAG_DROP_SCRIPT="$SCRIPTS_DIR/test_drag_drop_simulation.sh"

# Test data specific to drag & drop
DD_TEST_DIR="$TEST_DATA_DIR/drag_drop"

# Setup specific test data for drag & drop tests
setup_drag_drop_test_data() {
    mkdir -p "$DD_TEST_DIR"
    
    # Create various test files
    echo "Simple text file" > "$DD_TEST_DIR/simple.txt"
    echo "JavaScript code" > "$DD_TEST_DIR/code.js"
    echo "Python script" > "$DD_TEST_DIR/script.py"
    echo '{"key": "value"}' > "$DD_TEST_DIR/data.json"
    
    # Create files with different sizes
    echo "Small file" > "$DD_TEST_DIR/small.txt"
    yes "Large file content" | head -n 10000 > "$DD_TEST_DIR/large.txt"
    
    # Create subdirectory with files
    mkdir -p "$DD_TEST_DIR/subdir"
    echo "Nested file 1" > "$DD_TEST_DIR/subdir/nested1.txt"
    echo "Nested file 2" > "$DD_TEST_DIR/subdir/nested2.js"
    
    # Create unsupported file types
    echo "Binary-like content" > "$DD_TEST_DIR/unsupported.bin"
    
    # Create empty file
    touch "$DD_TEST_DIR/empty.txt"
    
    # Create file with special characters in name
    echo "Special chars content" > "$DD_TEST_DIR/file with spaces.txt"
    
    echo "✅ Drag & drop test data setup complete"
}

# Test script existence and permissions
test_script_setup() {
    echo "🔍 Testing script setup..."
    
    assert_file_exists "$DRAG_DROP_SCRIPT" "drag_drop_script_exists"
    
    if [[ -f "$DRAG_DROP_SCRIPT" ]]; then
        assert_command_success "test -x '$DRAG_DROP_SCRIPT'" "script_is_executable"
    fi
}

# Test help functionality
test_help_functionality() {
    echo "📖 Testing help functionality..."
    
    assert_command_success "$DRAG_DROP_SCRIPT --help" "help_option_works"
    assert_command_success "$DRAG_DROP_SCRIPT -h" "help_short_option_works"
}

# Test automated tests within the script
test_internal_automated_tests() {
    echo "🔧 Testing internal automated tests..."
    
    assert_command_success "$DRAG_DROP_SCRIPT --test" "internal_tests_pass"
}

# Test single file processing
test_single_file_processing() {
    echo "📄 Testing single file processing..."
    
    local test_file="$DD_TEST_DIR/simple.txt"
    
    # Test valid single file
    assert_command_success "$DRAG_DROP_SCRIPT '$test_file'" "single_file_processing"
    
    # Check if output files were created
    local reports_dir="./reports/drag_drop_results"
    assert_directory_exists "$reports_dir" "reports_directory_created"
    
    # Check for log file
    local log_file="$reports_dir/drag_drop_test.log"
    assert_file_exists "$log_file" "log_file_created"
    
    # Check for analysis result
    local analysis_file="$reports_dir/simple.txt.analysis.json"
    assert_file_exists "$analysis_file" "analysis_file_created"
}

# Test multiple file processing
test_multiple_file_processing() {
    echo "📁 Testing multiple file processing..."
    
    local file1="$DD_TEST_DIR/simple.txt"
    local file2="$DD_TEST_DIR/code.js"
    local file3="$DD_TEST_DIR/data.json"
    
    assert_command_success "$DRAG_DROP_SCRIPT '$file1' '$file2' '$file3'" "multiple_files_processing"
    
    # Check that multiple analysis files were created
    local reports_dir="./reports/drag_drop_results"
    assert_file_exists "$reports_dir/simple.txt.analysis.json" "first_analysis_created"
    assert_file_exists "$reports_dir/code.js.analysis.json" "second_analysis_created"
    assert_file_exists "$reports_dir/data.json.analysis.json" "third_analysis_created"
}

# Test directory processing
test_directory_processing() {
    echo "📂 Testing directory processing..."
    
    local test_dir="$DD_TEST_DIR/subdir"
    
    assert_command_success "$DRAG_DROP_SCRIPT '$test_dir'" "directory_processing"
    
    # Check that nested files were processed
    local reports_dir="./reports/drag_drop_results"
    assert_file_exists "$reports_dir/nested1.txt.analysis.json" "nested_file1_processed"
    assert_file_exists "$reports_dir/nested2.js.analysis.json" "nested_file2_processed"
}

# Test mixed file and directory processing
test_mixed_processing() {
    echo "🔀 Testing mixed file and directory processing..."
    
    local file="$DD_TEST_DIR/simple.txt"
    local directory="$DD_TEST_DIR/subdir"
    
    assert_command_success "$DRAG_DROP_SCRIPT '$file' '$directory'" "mixed_processing"
}

# Test error handling
test_error_handling() {
    echo "❌ Testing error handling..."
    
    # Test non-existent file
    assert_command_failure "$DRAG_DROP_SCRIPT '/nonexistent/file.txt'" "nonexistent_file_handling"
    
    # Test non-existent directory
    assert_command_failure "$DRAG_DROP_SCRIPT '/nonexistent/directory'" "nonexistent_directory_handling"
    
    # Test with no arguments
    assert_command_failure "$DRAG_DROP_SCRIPT" "no_arguments_handling"
}

# Test file validation
test_file_validation() {
    echo "✅ Testing file validation..."
    
    # Test supported file extensions
    local supported_files=(
        "$DD_TEST_DIR/simple.txt"
        "$DD_TEST_DIR/code.js"
        "$DD_TEST_DIR/script.py"
        "$DD_TEST_DIR/data.json"
    )
    
    for file in "${supported_files[@]}"; do
        if [[ -f "$file" ]]; then
            local basename=$(basename "$file")
            assert_command_success "$DRAG_DROP_SCRIPT '$file'" "supported_file_$basename"
        fi
    done
    
    # Test unsupported file extension (should still process but with warnings)
    local unsupported_file="$DD_TEST_DIR/unsupported.bin"
    if [[ -f "$unsupported_file" ]]; then
        # This might fail or succeed depending on implementation
        # We'll just test that it doesn't crash
        "$DRAG_DROP_SCRIPT" "$unsupported_file" >/dev/null 2>&1 || true
        echo "✅ PASS unsupported_file_handling (no crash)"
    fi
}

# Test output file generation
test_output_generation() {
    echo "📊 Testing output file generation..."
    
    # Clean previous results
    rm -rf "./reports/drag_drop_results"
    
    # Process a test file
    local test_file="$DD_TEST_DIR/simple.txt"
    assert_command_success "$DRAG_DROP_SCRIPT '$test_file'" "output_generation_setup"
    
    local reports_dir="./reports/drag_drop_results"
    
    # Check summary report
    assert_file_exists "$reports_dir/drag_drop_summary.json" "summary_report_created"
    
    # Check log file
    assert_file_exists "$reports_dir/drag_drop_test.log" "log_file_created"
    
    # Validate JSON structure in summary report
    if [[ -f "$reports_dir/drag_drop_summary.json" ]]; then
        assert_command_success "jq . '$reports_dir/drag_drop_summary.json' >/dev/null" "summary_json_valid"
    fi
    
    # Validate JSON structure in analysis report
    local analysis_file="$reports_dir/simple.txt.analysis.json"
    if [[ -f "$analysis_file" ]]; then
        assert_command_success "jq . '$analysis_file' >/dev/null" "analysis_json_valid"
    fi
}

# Test performance with larger datasets
test_performance() {
    echo "⚡ Testing performance..."
    
    # Create a performance test directory
    local perf_dir="$DD_TEST_DIR/performance"
    mkdir -p "$perf_dir"
    
    # Create multiple files for batch processing
    for i in {1..10}; do
        echo "Performance test file $i content" > "$perf_dir/perf_$i.txt"
    done
    
    # Measure execution time
    local start_time=$(date +%s)
    assert_command_success "$DRAG_DROP_SCRIPT '$perf_dir'" "performance_test_execution"
    local end_time=$(date +%s)
    
    local duration=$((end_time - start_time))
    echo "✅ Performance test completed in ${duration}s"
    
    # Performance should be reasonable (less than 30 seconds for 10 small files)
    if [[ $duration -lt 30 ]]; then
        echo "✅ PASS performance_within_limits"
        ((PASSED_TESTS++))
    else
        echo "❌ FAIL performance_within_limits (took ${duration}s)"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
}

# Test edge cases
test_edge_cases() {
    echo "🎯 Testing edge cases..."
    
    # Test empty file
    local empty_file="$DD_TEST_DIR/empty.txt"
    assert_command_success "$DRAG_DROP_SCRIPT '$empty_file'" "empty_file_processing"
    
    # Test file with spaces in name
    local space_file="$DD_TEST_DIR/file with spaces.txt"
    assert_command_success "$DRAG_DROP_SCRIPT '$space_file'" "spaces_in_filename"
    
    # Test very large file (if it exists)
    local large_file="$DD_TEST_DIR/large.txt"
    if [[ -f "$large_file" ]]; then
        assert_command_success "$DRAG_DROP_SCRIPT '$large_file'" "large_file_processing"
    fi
}

# Test cleanup functionality
test_cleanup() {
    echo "🧹 Testing cleanup functionality..."
    
    # Create some temporary files first
    assert_command_success "$DRAG_DROP_SCRIPT '$DD_TEST_DIR/simple.txt'" "setup_for_cleanup"
    
    # Test cleanup
    assert_command_success "$DRAG_DROP_SCRIPT --clean" "cleanup_execution"
}

# Main test function that runs all tests
run_drag_drop_tests() {
    echo "🎯 Setting up drag & drop specific test data..."
    setup_drag_drop_test_data
    
    # Run all test functions
    test_script_setup
    test_help_functionality
    test_internal_automated_tests
    test_single_file_processing
    test_multiple_file_processing
    test_directory_processing
    test_mixed_processing
    test_error_handling
    test_file_validation
    test_output_generation
    test_performance
    test_edge_cases
    test_cleanup
    
    echo "🎯 All drag & drop tests completed!"
}

# Integration with the test framework
main() {
    init_test_framework
    
    echo -e "${BOLD}🧪 Drag & Drop Simulation Test Suite${NC}"
    echo "=============================================="
    
    if [[ ! -f "$DRAG_DROP_SCRIPT" ]]; then
        echo -e "${RED}❌ Drag & drop script not found: $DRAG_DROP_SCRIPT${NC}"
        echo "Please ensure the script exists before running tests."
        exit 1
    fi
    
    # Make the script executable if it isn't
    chmod +x "$DRAG_DROP_SCRIPT" 2>/dev/null || true
    
    run_test_suite "Drag & Drop Simulation Tests" "run_drag_drop_tests"
    
    show_test_summary
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
