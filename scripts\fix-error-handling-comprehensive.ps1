#!/usr/bin/env powershell
# Comprehensive fix for error_handling.rs closure issues

Write-Host "`n🔧 COMPREHENSIVE ERROR HANDLING FIX" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$file_path = Join-Path $project_root "src\components\error_handling.rs"

Write-Host "`nBacking up current file..." -ForegroundColor Yellow
$backup_path = "$file_path.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Copy-Item $file_path $backup_path

Write-Host "Reading error_handling.rs..." -ForegroundColor Yellow
$content = Get-Content $file_path -Raw

# Fix 1: Ensure error_manager is cloned before view! macro
Write-Host "`nFix 1: Cloning error_manager before view! macro..." -ForegroundColor Cyan
if ($content -notmatch "let error_manager_clone = error_manager\.clone\(\);\s*\n\s*view!") {
    $content = $content -replace "(let has_more_errors[^}]+\}\);\s*\n)\s*(view!)", '$1
    let error_manager_clone = error_manager.clone();

    $2'
    Write-Host "  ✅ Added clone before view!" -ForegroundColor Green
} else {
    Write-Host "  ✅ Already has clone before view!" -ForegroundColor Green
}

# Fix 2: Ensure the main closure properly clones error_manager
Write-Host "`nFix 2: Fixing main closure..." -ForegroundColor Cyan
# This is the tricky part - we need to ensure the closure at line ~232 uses the right clone
$pattern = '(\{)\s*\n\s*(let error_manager = error_manager\.clone\(\);)\s*\n\s*(move \|\| \{)'
$replacement = '{
                    let error_manager = error_manager.clone();
                    move || {'

$content = $content -replace $pattern, $replacement

# Fix 3: Fix the map closure to properly handle error_manager
Write-Host "`nFix 3: Fixing map closure..." -ForegroundColor Cyan
# Ensure each error in the map has its own error_manager clone
$pattern = 'current_errors\.into_iter\(\)\.map\(\|error\| \{\s*let error_manager = error_manager\.clone\(\);'
$replacement = 'current_errors.into_iter().map(|error| {
                            let error_manager = error_manager.clone();'

$content = $content -replace $pattern, $replacement

# Fix 4: Fix remove button
Write-Host "`nFix 4: Fixing remove button..." -ForegroundColor Cyan
$pattern = 'on:click=move \|_\| \{\s*error_manager\.remove_error\(&error_id\);\s*\}'
$replacement = 'on:click={
                                            let error_manager = error_manager.clone();
                                            let error_id = error_id.clone();
                                            move |_| {
                                                error_manager.remove_error(&error_id);
                                            }
                                        }'

$content = $content -replace $pattern, $replacement

# Fix 5: Fix clear all button to use error_manager_clone
Write-Host "`nFix 5: Fixing clear all button..." -ForegroundColor Cyan
$pattern = 'on:click=\{\s*let error_manager = error_manager\.clone\(\);\s*move \|_\| \{'
$replacement = 'on:click={
                                let em = error_manager_clone.clone();
                                move |_| {'

$content = $content -replace $pattern, $replacement

# Save the fixed content
Write-Host "`nSaving fixed file..." -ForegroundColor Yellow
Set-Content -Path $file_path -Value $content -Encoding UTF8

# Verify the fixes
Write-Host "`n✅ Verifying fixes..." -ForegroundColor Cyan

# Count clones
$clone_count = ([regex]::Matches($content, "error_manager\.clone\(\)")).Count
$error_manager_clone_count = ([regex]::Matches($content, "error_manager_clone")).Count

Write-Host "  Found $clone_count error_manager.clone() calls" -ForegroundColor Gray
Write-Host "  Found $error_manager_clone_count error_manager_clone references" -ForegroundColor Gray

# Test compilation
Write-Host "`n🔨 Testing compilation..." -ForegroundColor Yellow
Push-Location $project_root
$output = cargo check --target wasm32-unknown-unknown 2>&1
$success = $LASTEXITCODE -eq 0
Pop-Location

if ($success) {
    Write-Host "  ✅ Compilation successful!" -ForegroundColor Green
    Write-Host "`n  Backup saved at: $backup_path" -ForegroundColor Gray
} else {
    Write-Host "  ❌ Compilation still failing" -ForegroundColor Red
    Write-Host "`n  Restoring backup..." -ForegroundColor Yellow
    Copy-Item $backup_path $file_path -Force
    
    Write-Host "`n  Manual fix required. The issue is likely:" -ForegroundColor Yellow
    Write-Host "  - A closure is capturing error_manager by move" -ForegroundColor White
    Write-Host "  - Check around lines 232-298 for closure issues" -ForegroundColor White
    Write-Host "  - Ensure all error_manager uses are cloned" -ForegroundColor White
}

Write-Host "`n🦸 Fix attempt complete!" -ForegroundColor Cyan