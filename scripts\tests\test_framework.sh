#!/bin/bash
# 🧪 Test Framework for Bash Scripts
# Provides common testing utilities and standardized test execution

set -euo pipefail

# Colors for test output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Test framework configuration
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$(dirname "$TEST_DIR")"
PROJECT_DIR="$(dirname "$SCRIPTS_DIR")"
TEST_DATA_DIR="$TEST_DIR/test_data"
TEST_RESULTS_DIR="$TEST_DIR/results"
TEST_LOG="$TEST_RESULTS_DIR/test_framework.log"

# Test statistics
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# Initialize test framework
init_test_framework() {
    mkdir -p "$TEST_DATA_DIR"
    mkdir -p "$TEST_RESULTS_DIR"
    touch "$TEST_LOG"
    
    echo "$(date): Test framework initialized" >> "$TEST_LOG"
    echo -e "${BOLD}🧪 Test Framework Initialized${NC}"
    echo "Test Data Directory: $TEST_DATA_DIR"
    echo "Test Results Directory: $TEST_RESULTS_DIR"
    echo "Test Log: $TEST_LOG"
    echo
}

# Logging function for tests
test_log() {
    local level="$1"
    local message="$2"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" >> "$TEST_LOG"
}

# Assert functions for testing
assert_equals() {
    local expected="$1"
    local actual="$2"
    local test_name="${3:-assertion}"
    
    ((TOTAL_TESTS++))
    
    if [[ "$expected" == "$actual" ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: expected='$expected', actual='$actual'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Expected: ${YELLOW}$expected${NC}"
        echo -e "   Actual:   ${YELLOW}$actual${NC}"
        test_log "FAIL" "$test_name: expected='$expected', actual='$actual'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_not_equals() {
    local not_expected="$1"
    local actual="$2"
    local test_name="${3:-assertion}"
    
    ((TOTAL_TESTS++))
    
    if [[ "$not_expected" != "$actual" ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: not_expected='$not_expected', actual='$actual'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Should not equal: ${YELLOW}$not_expected${NC}"
        echo -e "   But got:          ${YELLOW}$actual${NC}"
        test_log "FAIL" "$test_name: should not equal '$not_expected' but got '$actual'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_file_exists() {
    local file_path="$1"
    local test_name="${2:-file_exists}"
    
    ((TOTAL_TESTS++))
    
    if [[ -f "$file_path" ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: file exists at '$file_path'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   File does not exist: ${YELLOW}$file_path${NC}"
        test_log "FAIL" "$test_name: file does not exist at '$file_path'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_file_not_exists() {
    local file_path="$1"
    local test_name="${2:-file_not_exists}"
    
    ((TOTAL_TESTS++))
    
    if [[ ! -f "$file_path" ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: file does not exist at '$file_path'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   File should not exist: ${YELLOW}$file_path${NC}"
        test_log "FAIL" "$test_name: file should not exist at '$file_path'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_directory_exists() {
    local dir_path="$1"
    local test_name="${2:-directory_exists}"
    
    ((TOTAL_TESTS++))
    
    if [[ -d "$dir_path" ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: directory exists at '$dir_path'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Directory does not exist: ${YELLOW}$dir_path${NC}"
        test_log "FAIL" "$test_name: directory does not exist at '$dir_path'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_command_success() {
    local command="$1"
    local test_name="${2:-command_success}"
    
    ((TOTAL_TESTS++))
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: command succeeded: '$command'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Command failed: ${YELLOW}$command${NC}"
        test_log "FAIL" "$test_name: command failed: '$command'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_command_failure() {
    local command="$1"
    local test_name="${2:-command_failure}"
    
    ((TOTAL_TESTS++))
    
    if ! eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: command properly failed: '$command'"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Command should have failed: ${YELLOW}$command${NC}"
        test_log "FAIL" "$test_name: command should have failed: '$command'"
        ((FAILED_TESTS++))
        return 1
    fi
}

assert_contains() {
    local haystack="$1"
    local needle="$2"
    local test_name="${3:-contains}"
    
    ((TOTAL_TESTS++))
    
    if [[ "$haystack" == *"$needle"* ]]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
        test_log "PASS" "$test_name: haystack contains needle"
        ((PASSED_TESTS++))
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
        echo -e "   Haystack: ${YELLOW}$haystack${NC}"
        echo -e "   Needle:   ${YELLOW}$needle${NC}"
        test_log "FAIL" "$test_name: haystack does not contain needle"
        ((FAILED_TESTS++))
        return 1
    fi
}

# Skip a test
skip_test() {
    local test_name="$1"
    local reason="${2:-no reason given}"
    
    ((TOTAL_TESTS++))
    ((SKIPPED_TESTS++))
    
    echo -e "${YELLOW}⏭️  SKIP${NC} $test_name ($reason)"
    test_log "SKIP" "$test_name: $reason"
}

# Test setup and teardown helpers
setup_test_data() {
    echo -e "${BLUE}🔧 Setting up test data...${NC}"
    
    # Create common test files
    echo "Hello, World!" > "$TEST_DATA_DIR/simple.txt"
    echo "Test file with special chars: äöü ñ" > "$TEST_DATA_DIR/unicode.txt"
    echo '{"test": "json", "value": 123}' > "$TEST_DATA_DIR/test.json"
    echo "console.log('Hello from JavaScript');" > "$TEST_DATA_DIR/test.js"
    echo "# Test Markdown File" > "$TEST_DATA_DIR/test.md"
    
    # Create test directory structure
    mkdir -p "$TEST_DATA_DIR/subdir"
    echo "File in subdirectory" > "$TEST_DATA_DIR/subdir/nested.txt"
    
    # Create empty file
    touch "$TEST_DATA_DIR/empty.txt"
    
    # Create large file (for performance testing)
    yes "This is a line in a large file for testing purposes." | head -n 1000 > "$TEST_DATA_DIR/large.txt"
    
    echo -e "${GREEN}✅ Test data setup complete${NC}"
}

cleanup_test_data() {
    echo -e "${BLUE}🧹 Cleaning up test data...${NC}"
    
    if [[ -d "$TEST_DATA_DIR" ]]; then
        rm -rf "$TEST_DATA_DIR"/*
    fi
    
    echo -e "${GREEN}✅ Test data cleanup complete${NC}"
}

# Run a test suite
run_test_suite() {
    local test_suite_name="$1"
    local test_function="$2"
    
    echo -e "${BOLD}🧪 Running Test Suite: $test_suite_name${NC}"
    echo "=================================================="
    
    # Setup
    setup_test_data
    
    # Run the test function
    if declare -f "$test_function" >/dev/null; then
        "$test_function"
    else
        echo -e "${RED}❌ Test function '$test_function' not found${NC}"
        ((FAILED_TESTS++))
    fi
    
    # Cleanup
    cleanup_test_data
    
    echo
}

# Display test results summary
show_test_summary() {
    echo "=================================================="
    echo -e "${BOLD}📊 Test Results Summary${NC}"
    echo "=================================================="
    echo -e "Total Tests:  ${BOLD}$TOTAL_TESTS${NC}"
    echo -e "Passed:       ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed:       ${RED}$FAILED_TESTS${NC}"
    echo -e "Skipped:      ${YELLOW}$SKIPPED_TESTS${NC}"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        echo -e "Success Rate: ${BOLD}${success_rate}%${NC}"
    fi
    
    echo
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        test_log "SUMMARY" "All tests passed: $PASSED_TESTS/$TOTAL_TESTS"
        return 0
    else
        echo -e "${RED}💥 Some tests failed!${NC}"
        test_log "SUMMARY" "Tests failed: $FAILED_TESTS/$TOTAL_TESTS"
        return 1
    fi
}

# Performance testing helper
measure_execution_time() {
    local command="$1"
    local test_name="${2:-performance_test}"
    
    echo -e "${BLUE}⏱️  Measuring execution time for: $test_name${NC}"
    
    local start_time=$(date +%s.%N)
    eval "$command"
    local end_time=$(date +%s.%N)
    
    local duration=$(echo "$end_time - $start_time" | bc -l)
    echo -e "${BLUE}⏱️  Execution time: ${duration}s${NC}"
    
    test_log "PERFORMANCE" "$test_name: ${duration}s"
    echo "$duration"
}

# Compare performance between two commands
compare_performance() {
    local command1="$1"
    local command2="$2"
    local test_name="${3:-performance_comparison}"
    
    echo -e "${BLUE}🏁 Performance comparison: $test_name${NC}"
    
    local time1=$(measure_execution_time "$command1" "Command 1")
    local time2=$(measure_execution_time "$command2" "Command 2")
    
    local improvement=$(echo "scale=2; ($time1 - $time2) / $time1 * 100" | bc -l)
    
    if (( $(echo "$time2 < $time1" | bc -l) )); then
        echo -e "${GREEN}🚀 Command 2 is ${improvement}% faster${NC}"
    else
        echo -e "${YELLOW}⚠️  Command 1 is faster by ${improvement}%${NC}"
    fi
}

# Memory usage testing (Linux/macOS)
measure_memory_usage() {
    local command="$1"
    local test_name="${2:-memory_test}"
    
    if command -v /usr/bin/time >/dev/null 2>&1; then
        echo -e "${BLUE}💾 Measuring memory usage for: $test_name${NC}"
        /usr/bin/time -v eval "$command" 2>&1 | grep "Maximum resident set size" || echo "Memory measurement not available"
    else
        echo -e "${YELLOW}⚠️  Memory measurement not available on this system${NC}"
    fi
}

# Export functions for use in other test scripts
export -f assert_equals assert_not_equals assert_file_exists assert_file_not_exists
export -f assert_directory_exists assert_command_success assert_command_failure
export -f assert_contains skip_test setup_test_data cleanup_test_data
export -f run_test_suite show_test_summary measure_execution_time
export -f compare_performance measure_memory_usage test_log

# Export variables
export TEST_DIR TEST_DATA_DIR TEST_RESULTS_DIR TEST_LOG
export TOTAL_TESTS PASSED_TESTS FAILED_TESTS SKIPPED_TESTS
export RED GREEN YELLOW BLUE BOLD NC

# Main function for standalone execution
main() {
    if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
        init_test_framework
        echo -e "${GREEN}🧪 Test framework is ready!${NC}"
        echo
        echo "Usage examples:"
        echo "  source scripts/tests/test_framework.sh"
        echo "  assert_equals 'expected' 'actual' 'test_name'"
        echo "  assert_file_exists '/path/to/file'"
        echo "  run_test_suite 'My Tests' 'my_test_function'"
        echo "  show_test_summary"
    fi
}

# Initialize if run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
