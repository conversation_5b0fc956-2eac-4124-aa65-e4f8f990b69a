// Cleaning operations module for Bad Character Scanner
// Handles text cleaning and character removal operations

use crate::modules::character_analyzer::CharacterAnalyzer;
use std::path::Path;
use anyhow::Result;

pub struct CleaningOperations {
    analyzer: CharacterAnalyzer,
}

impl CleaningOperations {
    pub fn new() -> Result<Self> {
        Ok(Self {
            analyzer: CharacterAnalyzer::new()?,
        })
    }

    /// Clean text by removing problematic characters
    pub fn clean_text(&self, text: &str) -> String {
        self.analyzer.clean_text(text)
    }

    /// Clean file content and return cleaned text
    pub fn clean_file_content(&self, file_path: &Path) -> Result<String> {
        let content = std::fs::read_to_string(file_path)?;
        Ok(self.clean_text(&content))
    }

    /// Save cleaned content to a new file
    pub fn save_cleaned_file(&self, original_path: &Path, cleaned_content: &str) -> Result<()> {
        let parent = original_path.parent().unwrap_or(Path::new("."));
        let file_name = original_path.file_stem().unwrap_or_default();
        let extension = original_path.extension().unwrap_or_default();
        
        let cleaned_file_name = format!(
            "{}_cleaned.{}",
            file_name.to_string_lossy(),
            extension.to_string_lossy()
        );
        
        let cleaned_path = parent.join(cleaned_file_name);
        std::fs::write(cleaned_path, cleaned_content)?;
        Ok(())
    }
}

/// Convenience function for cleaning text content
pub fn clean_content(text: &str) -> String {
    if let Ok(ops) = CleaningOperations::new() {
        ops.clean_text(text)
    } else {
        // Fallback to basic cleaning if initialization fails
        text.chars()
            .filter(|&c| {
                // Remove common problematic characters
                !matches!(c, '\u{200B}'..='\u{200F}' | '\u{202A}'..='\u{202E}' | '\u{FEFF}')
            })
            .collect()
    }
}
