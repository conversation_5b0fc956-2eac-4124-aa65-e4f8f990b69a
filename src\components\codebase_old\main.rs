use leptos::*;
use crate::components::codebase::{
    state::CodebaseState,
    handlers::{
        setup_progress_listener,
        setup_file_drop_listener,
        select_folder,
        // Drag & drop handlers moved to DropZone component
    },
    ui::{
        EnvironmentIndicator,
        DropZone,
        ProgressBar,
        AnalysisResults,
    },
};
use crate::context::use_analysis_context;

#[component]
pub fn CodebaseComponent() -> impl IntoView {
    let state = CodebaseState::new();

    // Get analysis context for sharing results
    let analysis_context = use_analysis_context();

    // Destructure state for easier access
    let (is_analyzing, set_is_analyzing) = state.is_analyzing;
    let (analysis_result, set_analysis_result) = state.analysis_result;
    let (selected_path, set_selected_path) = state.selected_path;
    let (progress, set_progress) = state.progress;
    
    // Set up progress event listener
    create_effect(move |_| {
        setup_progress_listener(set_progress);
    });

    // Set up file drop event listener
    {
        let analysis_context_clone = analysis_context.clone();
        create_effect(move |_| {
            setup_file_drop_listener(
                set_is_analyzing,
                set_progress,
                set_selected_path,
                set_analysis_result,
                Some(analysis_context_clone.clone()),
            );
        });
    }
    
    // Create event handlers
    let on_select_folder = select_folder(
        set_is_analyzing,
        set_progress,
        set_selected_path,
        set_analysis_result,
        Some(analysis_context.clone()),
    );
    
    // Drag & drop handlers are now created inside the DropZone component
    
    view! {
        <div class="p-8">
            // JavaScript for file drop handling
            <script>
                r#"
                // Setup file drop handling using Tauri v2 Global API
                if (window.__TAURI__ && window.__TAURI__.webview) {
                    try {
                        const webview = window.__TAURI__.webview.getCurrentWebview();
                        webview.onDragDropEvent((event) => {
                            console.log('Drag drop event:', event);
                            if (event.payload.type === 'drop' && event.payload.paths.length > 0) {
                                const path = event.payload.paths[0];
                                console.log('File dropped:', path);
                                // Call the global callback function
                                if (window.handleFileDrop) {
                                    window.handleFileDrop(path);
                                }
                            }
                        });
                    } catch (error) {
                        console.error('Failed to setup drag drop:', error);
                    }
                }
                "#
            </script>

            // Professional header section
            <div class="section-header">
                <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <svg class="section-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="section-title">"Code Base Analysis & Cleaning"</h2>
                    <p class="section-subtitle">"Analyze and clean suspicious characters from your entire codebase"</p>
                </div>
                <button
                    class="ml-auto btn-secondary text-sm px-3 py-2"
                    on:click=move |_| {}
                >
                    <svg class="btn-icon-sm mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    "Back to Analysis"
                </button>
            </div>

            // Environment indicator
            <EnvironmentIndicator />

            // Main content card
            <div class="card p-6 mt-6">
                // Drop zone
                <DropZone
                    on_select_folder=on_select_folder
                    is_analyzing=is_analyzing
                    set_is_analyzing=set_is_analyzing
                    set_progress=set_progress
                    set_selected_path=set_selected_path
                    set_analysis_result=set_analysis_result
                    analysis_context=Some(analysis_context.clone())
                />

                // Selected path display
                {move || {
                    (!selected_path.get().is_empty())
                        .then(|| {
                            view! {
                                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                            <svg class="icon-xs text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-blue-800">"Selected Folder:"</p>
                                            <p class="text-sm font-mono text-blue-700 break-all">{selected_path.get()}</p>
                                        </div>
                                    </div>
                                </div>
                            }
                        })
                }}

                // Progress bar
                {move || is_analyzing.get().then(|| {
                    view! {
                        <div class="mt-6">
                            <ProgressBar progress=progress />
                        </div>
                    }
                })}
            </div>

            // Analysis results
            {move || {
                analysis_result.get().map(|result| {
                    view! {
                        <div class="mt-6">
                            <AnalysisResults result=result />
                        </div>
                    }
                })
            }}
        </div>
    }
}