This file contains MULTIPLE ATTACK VECTORS for comprehensive testing

SECTION 1: Zero-Width Characters
================================
Normal text with hidden ​zero-width space.
Text with zero-width‌joiner attack.
String with zero-width‍non-joiner.
Invisible separator﻿character (ZWNBSP).

SECTION 2: Bidirectional Attacks  
================================
Text with override: ‮hidden reversed text‬ normal text
Embedding attack: normal ‭hidden‬ normal
Isolate attack: text ⁦malicious⁩ text
Strong isolate: normal ⁨hidden⁩ text

SECTION 3: Homoglyphs (Lookalikes)
=================================
Cyrillic in Latin: аdmіn (contains Cyrillic a, i)
Greek in Latin: αpple (Greek alpha)
Mixed script: Gοοgle (Greek omicrons)
Confusing: РayРal (Cyrillic P)

SECTION 4: Control Characters
============================
Text with bell character: Hello World (contains BEL)
Line separator: Line1 Line2 (contains LS)
Paragraph separator: Para1 Para2 (contains PS)

SECTION 5: Format Characters
===========================
Arabic formatting: ‏Right-to-left‎ override
Mongolian vowel separator: ᠠ᠊ᠠ (contains MVS)
Invisible plus: 2＋2 (fullwidth plus)

SECTION 6: High Codepoint Attacks
=================================
Mathematical: 𝐀𝐁𝐂 (Mathematical Bold)
Emoji variants: ︎ (variation selector)
Private use: 󠁢 (tag character)

SECTION 7: Mixed Scripts
=======================
Arabic + Latin: مرحباHello
Hebrew + English: שלוםWorld
Thai + English: สวัสดีHello

CLEAN REFERENCE:
===============
Normal text without any special characters.
Standard ASCII: ABCDEfghijk123456789
Common punctuation: .,;:!?'"()[]{}
Standard whitespace and newlines only.
