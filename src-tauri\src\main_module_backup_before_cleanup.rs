// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// Don't declare modules here, they're declared in lib.rs

use std::collections::HashMap;
use std::time::{SystemTime, Duration};
use std::path::Path;
use std::fs;
use tauri::{Emitter, Manager};
use serde::{Deserialize, Serialize};
use unicode_segmentation::UnicodeSegmentation;
use unicode_width::UnicodeWidthStr;
use encoding_rs::*;
use regex::Regex;
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};
use uuid::Uuid;

// Import report_generator module
use crate::report_generator;

// Import modular components
use crate::modules::{
    data_structures::*,
    asset_manager::AssetManager,
    character_analyzer::CharacterAnalyzer,
    ai_detection::*,
};

impl CharacterAnalyzer {
    pub fn new() -> Result<Self, String> {
        let asset_manager = AssetManager::new()?;
        Ok(Self {
            homograph_db: Self::build_homograph_database(),
            pattern_rules: Self::build_pattern_rules(),
            script_detector: ScriptDetector::new(),
            asset_manager,
        })
    }

    fn build_homograph_database() -> HashMap<char, Vec<char>> {
        let mut db = HashMap::new();
        
        // ==================== LATIN LOOKALIKES ====================
        
        // Basic Latin confusables
        db.insert('а', vec!['a']); // Cyrillic а -> Latin a
        db.insert('е', vec!['e']); // Cyrillic е -> Latin e
        db.insert('о', vec!['o']); // Cyrillic о -> Latin o
        db.insert('р', vec!['p']); // Cyrillic р -> Latin p
        db.insert('с', vec!['c']); // Cyrillic с -> Latin c
        db.insert('у', vec!['y']); // Cyrillic у -> Latin y
        db.insert('х', vec!['x']); // Cyrillic х -> Latin x
        
        // Advanced Cyrillic uppercase confusables
        db.insert('А', vec!['A']); // Cyrillic А -> Latin A
        db.insert('В', vec!['B']); // Cyrillic В -> Latin B
        db.insert('Е', vec!['E']); // Cyrillic Е -> Latin E
        db.insert('З', vec!['3']); // Cyrillic З -> Number 3
        db.insert('К', vec!['K']); // Cyrillic К -> Latin K
        db.insert('М', vec!['M']); // Cyrillic М -> Latin M
        db.insert('Н', vec!['H']); // Cyrillic Н -> Latin H
        db.insert('О', vec!['O']); // Cyrillic О -> Latin O
        db.insert('Р', vec!['P']); // Cyrillic Р -> Latin P
        db.insert('С', vec!['C']); // Cyrillic С -> Latin C
        db.insert('Т', vec!['T']); // Cyrillic Т -> Latin T
        db.insert('У', vec!['Y']); // Cyrillic У -> Latin Y
        db.insert('Х', vec!['X']); // Cyrillic Х -> Latin X
        
        // ==================== GREEK LOOKALIKES ====================
        
        // Greek lowercase confusables
        db.insert('α', vec!['a']); // Greek alpha -> Latin a
        db.insert('ο', vec!['o']); // Greek omicron -> Latin o
        db.insert('ρ', vec!['p']); // Greek rho -> Latin p
        db.insert('υ', vec!['v', 'u']); // Greek upsilon -> Latin v/u
        db.insert('ν', vec!['v']); // Greek nu -> Latin v
        db.insert('κ', vec!['k']); // Greek kappa -> Latin k
        db.insert('η', vec!['n']); // Greek eta -> Latin n
        db.insert('ι', vec!['i']); // Greek iota -> Latin i
        db.insert('χ', vec!['x']); // Greek chi -> Latin x
        db.insert('τ', vec!['t']); // Greek tau -> Latin t
        db.insert('ε', vec!['e']); // Greek epsilon -> Latin e
        db.insert('μ', vec!['u']); // Greek mu -> Latin u
        
        // Greek uppercase confusables
        db.insert('Α', vec!['A']); // Greek capital alpha -> Latin A
        db.insert('Β', vec!['B']); // Greek capital beta -> Latin B
        db.insert('Ε', vec!['E']); // Greek capital epsilon -> Latin E
        db.insert('Ζ', vec!['Z']); // Greek capital zeta -> Latin Z
        db.insert('Η', vec!['H']); // Greek capital eta -> Latin H
        db.insert('Ι', vec!['I']); // Greek capital iota -> Latin I
        db.insert('Κ', vec!['K']); // Greek capital kappa -> Latin K
        db.insert('Μ', vec!['M']); // Greek capital mu -> Latin M
        db.insert('Ν', vec!['N']); // Greek capital nu -> Latin N
        db.insert('Ο', vec!['O']); // Greek capital omicron -> Latin O
        db.insert('Ρ', vec!['P']); // Greek capital rho -> Latin P
        db.insert('Τ', vec!['T']); // Greek capital tau -> Latin T
        db.insert('Υ', vec!['Y']); // Greek capital upsilon -> Latin Y
        db.insert('Χ', vec!['X']); // Greek capital chi -> Latin X
        
        // ==================== ARMENIAN LOOKALIKES ====================
        
        db.insert('Ա', vec!['A']); // Armenian capital Ayb -> Latin A
        db.insert('Բ', vec!['B']); // Armenian capital Ben -> Latin B
        db.insert('Ե', vec!['E']); // Armenian capital Ech -> Latin E
        db.insert('Զ', vec!['Z']); // Armenian capital Za -> Latin Z
        db.insert('Հ', vec!['H']); // Armenian capital Ho -> Latin H
        db.insert('Ի', vec!['I']); // Armenian capital Ini -> Latin I
        db.insert('Ծ', vec!['C']); // Armenian capital Tsa -> Latin C
        db.insert('Կ', vec!['K']); // Armenian capital Ken -> Latin K
        db.insert('Մ', vec!['M']); // Armenian capital Men -> Latin M
        db.insert('Ո', vec!['O']); // Armenian capital Vo -> Latin O
        db.insert('Պ', vec!['P']); // Armenian capital Peh -> Latin P
        db.insert('Ս', vec!['S']); // Armenian capital Seh -> Latin S
        db.insert('Վ', vec!['V']); // Armenian capital Vev -> Latin V
        db.insert('Ր', vec!['R']); // Armenian capital Reh -> Latin R
        
        // ==================== MATHEMATICAL ALPHANUMERIC SYMBOLS ====================
        
        // Mathematical Bold
        db.insert('𝐀', vec!['A']); // Mathematical Bold Capital A -> Latin A
        db.insert('𝐁', vec!['B']); // Mathematical Bold Capital B -> Latin B
        db.insert('𝐂', vec!['C']); // Mathematical Bold Capital C -> Latin C
        db.insert('𝐃', vec!['D']); // Mathematical Bold Capital D -> Latin D
        db.insert('𝐄', vec!['E']); // Mathematical Bold Capital E -> Latin E
        db.insert('𝐅', vec!['F']); // Mathematical Bold Capital F -> Latin F
        db.insert('𝐆', vec!['G']); // Mathematical Bold Capital G -> Latin G
        db.insert('𝐇', vec!['H']); // Mathematical Bold Capital H -> Latin H
        db.insert('𝐈', vec!['I']); // Mathematical Bold Capital I -> Latin I
        db.insert('𝐉', vec!['J']); // Mathematical Bold Capital J -> Latin J
        db.insert('𝐊', vec!['K']); // Mathematical Bold Capital K -> Latin K
        db.insert('𝐋', vec!['L']); // Mathematical Bold Capital L -> Latin L
        db.insert('𝐌', vec!['M']); // Mathematical Bold Capital M -> Latin M
        db.insert('𝐍', vec!['N']); // Mathematical Bold Capital N -> Latin N
        db.insert('𝐎', vec!['O']); // Mathematical Bold Capital O -> Latin O
        db.insert('𝐏', vec!['P']); // Mathematical Bold Capital P -> Latin P
        db.insert('𝐐', vec!['Q']); // Mathematical Bold Capital Q -> Latin Q
        db.insert('𝐑', vec!['R']); // Mathematical Bold Capital R -> Latin R
        db.insert('𝐒', vec!['S']); // Mathematical Bold Capital S -> Latin S
        db.insert('𝐓', vec!['T']); // Mathematical Bold Capital T -> Latin T
        db.insert('𝐔', vec!['U']); // Mathematical Bold Capital U -> Latin U
        db.insert('𝐕', vec!['V']); // Mathematical Bold Capital V -> Latin V
        db.insert('𝐖', vec!['W']); // Mathematical Bold Capital W -> Latin W
        db.insert('𝐗', vec!['X']); // Mathematical Bold Capital X -> Latin X
        db.insert('𝐘', vec!['Y']); // Mathematical Bold Capital Y -> Latin Y
        db.insert('𝐙', vec!['Z']); // Mathematical Bold Capital Z -> Latin Z
        
        // Mathematical Bold lowercase
        db.insert('𝐚', vec!['a']); // Mathematical Bold Small A -> Latin a
        db.insert('𝐛', vec!['b']); // Mathematical Bold Small B -> Latin b
        db.insert('𝐜', vec!['c']); // Mathematical Bold Small C -> Latin c
        db.insert('𝐝', vec!['d']); // Mathematical Bold Small D -> Latin d
        db.insert('𝐞', vec!['e']); // Mathematical Bold Small E -> Latin e
        db.insert('𝐟', vec!['f']); // Mathematical Bold Small F -> Latin f
        db.insert('𝐠', vec!['g']); // Mathematical Bold Small G -> Latin g
        db.insert('𝐡', vec!['h']); // Mathematical Bold Small H -> Latin h
        db.insert('𝐩', vec!['p']); // Mathematical Bold Small P -> Latin p
        db.insert('𝐪', vec!['q']); // Mathematical Bold Small Q -> Latin q
        db.insert('𝐫', vec!['r']); // Mathematical Bold Small R -> Latin r
        db.insert('𝐬', vec!['s']); // Mathematical Bold Small S -> Latin s
        db.insert('𝐭', vec!['t']); // Mathematical Bold Small T -> Latin t
        db.insert('𝐮', vec!['u']); // Mathematical Bold Small U -> Latin u
        db.insert('𝐯', vec!['v']); // Mathematical Bold Small V -> Latin v
        db.insert('𝐰', vec!['w']); // Mathematical Bold Small W -> Latin w
        db.insert('𝐱', vec!['x']); // Mathematical Bold Small X -> Latin x
        db.insert('𝐲', vec!['y']); // Mathematical Bold Small Y -> Latin y
        db.insert('𝐳', vec!['z']); // Mathematical Bold Small Z -> Latin z
        
        // ==================== FULLWIDTH CHARACTERS ====================
        
        // Fullwidth Latin uppercase
        db.insert('Ａ', vec!['A']); // Fullwidth Latin Capital Letter A -> Latin A
        db.insert('Ｂ', vec!['B']); // Fullwidth Latin Capital Letter B -> Latin B
        db.insert('Ｃ', vec!['C']); // Fullwidth Latin Capital Letter C -> Latin C
        db.insert('Ｄ', vec!['D']); // Fullwidth Latin Capital Letter D -> Latin D
        db.insert('Ｅ', vec!['E']); // Fullwidth Latin Capital Letter E -> Latin E
        db.insert('Ｆ', vec!['F']); // Fullwidth Latin Capital Letter F -> Latin F
        db.insert('Ｇ', vec!['G']); // Fullwidth Latin Capital Letter G -> Latin G
        db.insert('Ｈ', vec!['H']); // Fullwidth Latin Capital Letter H -> Latin H
        db.insert('Ｉ', vec!['I']); // Fullwidth Latin Capital Letter I -> Latin I
        db.insert('Ｊ', vec!['J']); // Fullwidth Latin Capital Letter J -> Latin J
        db.insert('Ｋ', vec!['K']); // Fullwidth Latin Capital Letter K -> Latin K
        db.insert('Ｌ', vec!['L']); // Fullwidth Latin Capital Letter L -> Latin L
        db.insert('Ｍ', vec!['M']); // Fullwidth Latin Capital Letter M -> Latin M
        db.insert('Ｎ', vec!['N']); // Fullwidth Latin Capital Letter N -> Latin N
        db.insert('Ｏ', vec!['O']); // Fullwidth Latin Capital Letter O -> Latin O
        db.insert('Ｐ', vec!['P']); // Fullwidth Latin Capital Letter P -> Latin P
        db.insert('Ｑ', vec!['Q']); // Fullwidth Latin Capital Letter Q -> Latin Q
        db.insert('Ｒ', vec!['R']); // Fullwidth Latin Capital Letter R -> Latin R
        db.insert('Ｓ', vec!['S']); // Fullwidth Latin Capital Letter S -> Latin S
        db.insert('Ｔ', vec!['T']); // Fullwidth Latin Capital Letter T -> Latin T
        db.insert('Ｕ', vec!['U']); // Fullwidth Latin Capital Letter U -> Latin U
        db.insert('Ｖ', vec!['V']); // Fullwidth Latin Capital Letter V -> Latin V
        db.insert('Ｗ', vec!['W']); // Fullwidth Latin Capital Letter W -> Latin W
        db.insert('Ｘ', vec!['X']); // Fullwidth Latin Capital Letter X -> Latin X
        db.insert('Ｙ', vec!['Y']); // Fullwidth Latin Capital Letter Y -> Latin Y
        db.insert('Ｚ', vec!['Z']); // Fullwidth Latin Capital Letter Z -> Latin Z
        
        // Fullwidth Latin lowercase
        db.insert('ａ', vec!['a']); // Fullwidth Latin Small Letter A -> Latin a
        db.insert('ｂ', vec!['b']); // Fullwidth Latin Small Letter B -> Latin b
        db.insert('ｃ', vec!['c']); // Fullwidth Latin Small Letter C -> Latin c
        db.insert('ｄ', vec!['d']); // Fullwidth Latin Small Letter D -> Latin d
        db.insert('ｅ', vec!['e']); // Fullwidth Latin Small Letter E -> Latin e
        db.insert('ｆ', vec!['f']); // Fullwidth Latin Small Letter F -> Latin f
        db.insert('ｇ', vec!['g']); // Fullwidth Latin Small Letter G -> Latin g
        db.insert('ｈ', vec!['h']); // Fullwidth Latin Small Letter H -> Latin h
        db.insert('ｉ', vec!['i']); // Fullwidth Latin Small Letter I -> Latin i
        db.insert('ｊ', vec!['j']); // Fullwidth Latin Small Letter J -> Latin j
        db.insert('ｋ', vec!['k']); // Fullwidth Latin Small Letter K -> Latin k
        db.insert('ｌ', vec!['l']); // Fullwidth Latin Small Letter L -> Latin l
        db.insert('ｍ', vec!['m']); // Fullwidth Latin Small Letter M -> Latin m
        db.insert('ｎ', vec!['n']); // Fullwidth Latin Small Letter N -> Latin n
        db.insert('ｏ', vec!['o']); // Fullwidth Latin Small Letter O -> Latin o
        db.insert('ｐ', vec!['p']); // Fullwidth Latin Small Letter P -> Latin p
        db.insert('ｑ', vec!['q']); // Fullwidth Latin Small Letter Q -> Latin q
        db.insert('ｒ', vec!['r']); // Fullwidth Latin Small Letter R -> Latin r
        db.insert('ｓ', vec!['s']); // Fullwidth Latin Small Letter S -> Latin s
        db.insert('ｔ', vec!['t']); // Fullwidth Latin Small Letter T -> Latin t
        db.insert('ｕ', vec!['u']); // Fullwidth Latin Small Letter U -> Latin u
        db.insert('ｖ', vec!['v']); // Fullwidth Latin Small Letter V -> Latin v
        db.insert('ｗ', vec!['w']); // Fullwidth Latin Small Letter W -> Latin w
        db.insert('ｘ', vec!['x']); // Fullwidth Latin Small Letter X -> Latin x
        db.insert('ｙ', vec!['y']); // Fullwidth Latin Small Letter Y -> Latin y
        db.insert('ｚ', vec!['z']); // Fullwidth Latin Small Letter Z -> Latin z
        
        // Fullwidth digits
        db.insert('０', vec!['0']); // Fullwidth Digit Zero -> Digit Zero
        db.insert('１', vec!['1']); // Fullwidth Digit One -> Digit One
        db.insert('２', vec!['2']); // Fullwidth Digit Two -> Digit Two
        db.insert('３', vec!['3']); // Fullwidth Digit Three -> Digit Three
        db.insert('４', vec!['4']); // Fullwidth Digit Four -> Digit Four
        db.insert('５', vec!['5']); // Fullwidth Digit Five -> Digit Five
        db.insert('６', vec!['6']); // Fullwidth Digit Six -> Digit Six
        db.insert('７', vec!['7']); // Fullwidth Digit Seven -> Digit Seven
        db.insert('８', vec!['8']); // Fullwidth Digit Eight -> Digit Eight
        db.insert('９', vec!['9']); // Fullwidth Digit Nine -> Digit Nine
        
        // ==================== DANGEROUS NUMBER LOOKALIKES ====================
        
        // Mathematical Bold Digits
        db.insert('𝟎', vec!['0']); // Mathematical Bold Digit Zero -> Digit Zero
        db.insert('𝟏', vec!['1']); // Mathematical Bold Digit One -> Digit One
        db.insert('𝟐', vec!['2']); // Mathematical Bold Digit Two -> Digit Two
        db.insert('𝟑', vec!['3']); // Mathematical Bold Digit Three -> Digit Three
        db.insert('𝟒', vec!['4']); // Mathematical Bold Digit Four -> Digit Four
        db.insert('𝟓', vec!['5']); // Mathematical Bold Digit Five -> Digit Five
        db.insert('𝟔', vec!['6']); // Mathematical Bold Digit Six -> Digit Six
        db.insert('𝟕', vec!['7']); // Mathematical Bold Digit Seven -> Digit Seven
        db.insert('𝟖', vec!['8']); // Mathematical Bold Digit Eight -> Digit Eight
        db.insert('𝟗', vec!['9']); // Mathematical Bold Digit Nine -> Digit Nine
        
        // Mathematical Double-Struck Digits
        db.insert('𝟘', vec!['0']); // Mathematical Double-Struck Digit Zero -> Digit Zero
        db.insert('𝟙', vec!['1']); // Mathematical Double-Struck Digit One -> Digit One
        db.insert('𝟚', vec!['2']); // Mathematical Double-Struck Digit Two -> Digit Two
        db.insert('𝟛', vec!['3']); // Mathematical Double-Struck Digit Three -> Digit Three
        db.insert('𝟜', vec!['4']); // Mathematical Double-Struck Digit Four -> Digit Four
        db.insert('𝟝', vec!['5']); // Mathematical Double-Struck Digit Five -> Digit Five
        db.insert('𝟞', vec!['6']); // Mathematical Double-Struck Digit Six -> Digit Six
        db.insert('𝟟', vec!['7']); // Mathematical Double-Struck Digit Seven -> Digit Seven
        db.insert('𝟠', vec!['8']); // Mathematical Double-Struck Digit Eight -> Digit Eight
        db.insert('𝟡', vec!['9']); // Mathematical Double-Struck Digit Nine -> Digit Nine
        
        // ==================== PUNCTUATION LOOKALIKES ====================
        
        // Similar punctuation from different scripts
        db.insert('；', vec![';']); // Fullwidth Semicolon -> Semicolon
        db.insert('：', vec![':']); // Fullwidth Colon -> Colon
        db.insert('，', vec![',']); // Fullwidth Comma -> Comma
        db.insert('．', vec!['.']); // Fullwidth Full Stop -> Full Stop
        db.insert('！', vec!['!']); // Fullwidth Exclamation Mark -> Exclamation Mark
        db.insert('？', vec!['?']); // Fullwidth Question Mark -> Question Mark
        db.insert('（', vec!['(']); // Fullwidth Left Parenthesis -> Left Parenthesis
        db.insert('）', vec![')']); // Fullwidth Right Parenthesis -> Right Parenthesis
        
        // ==================== SPECIAL CONFUSABLE COMBINATIONS ====================
        
        // Combining characters that could masquerade as base characters
        db.insert('‌', vec![' ']); // Zero Width Non-Joiner -> Space (approximation)
        db.insert('‍', vec![' ']); // Zero Width Joiner -> Space (approximation)
        db.insert('⁠', vec![' ']); // Word Joiner -> Space (approximation)
        
        db
    }

    fn build_pattern_rules() -> Vec<(String, Regex, String)> {
        let mut rules = Vec::new();
        
        // ==================== ADVANCED HOMOGLYPH ATTACK DETECTION ====================
        
        // Advanced Multi-Script Homoglyph Attacks
        rules.push((
            "Advanced Cyrillic Homoglyph Attack".to_string(),
            Regex::new(r"[аеорсухАЕМНОРСТУХ][a-zA-Z]|[a-zA-Z][аеорсухАЕМНОРСТУХ]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Greek Letter Substitution Attack".to_string(),
            Regex::new(r"[αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ][a-zA-Z]|[a-zA-Z][αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Mathematical Alphanumeric Substitution".to_string(),
            Regex::new(r"[𝐀-𝐙𝐚-𝐳𝟎-𝟗𝔸-ℤ𝕒-𝔃]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Armenian-Latin Homoglyph Mix".to_string(),
            Regex::new(r"[ԱԲԳԴԵԶԷԸԹԺԻԼԽԾԿՀՁՂՃՄՅՆՇՈՉՊՋՌՍՎՏՐՑՒՓՔՕՖ][a-zA-Z]|[a-zA-Z][ԱԲԳԴԵԶԷԸԹԺԻԼԽԾԿՀՁՂՃՄՅՆՇՈՉՊՋՌՍՎՏՐՑՒՓՔՕՖ]").unwrap(),
            "High".to_string(),
        ));
        
        // Complex Mixed Script Patterns
        rules.push((
            "Triple Script Mixing Attack".to_string(),
            Regex::new(r"[\p{Latin}][\p{Cyrillic}][\p{Greek}]|[\p{Greek}][\p{Latin}][\p{Cyrillic}]").unwrap(),
            "Critical".to_string(),
        ));
        
        // ==================== ADVANCED ZERO-WIDTH & INVISIBLE ATTACKS ====================
        
        rules.push((
            "Multiple Zero-Width Characters".to_string(),
            Regex::new(r"[\u{200B}\u{200C}\u{200D}]{3,}").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Zero-Width Joiner Attack Pattern".to_string(),
            Regex::new(r"\u{200D}{2,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Invisible Character Sandwich".to_string(),
            Regex::new(r"[\u{200B}\u{200C}\u{200D}\u{2060}\u{FEFF}][a-zA-Z0-9]+[\u{200B}\u{200C}\u{200D}\u{2060}\u{FEFF}]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Word Joiner Abuse".to_string(),
            Regex::new(r"\u{2060}{2,}").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== BIDIRECTIONAL TEXT ATTACKS ====================
        
        rules.push((
            "Right-to-Left Override Attack".to_string(),
            Regex::new(r"\u{202E}").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Bidirectional Override Sequence".to_string(),
            Regex::new(r"[\u{202A}-\u{202E}]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Nested Bidirectional Controls".to_string(),
            Regex::new(r"[\u{202A}\u{202B}].*[\u{202A}\u{202B}]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Unmatched Directional Formatting".to_string(),
            Regex::new(r"[\u{202A}\u{202B}\u{202D}\u{202E}](?!.*\u{202C})").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== AI WRITING DETECTION PATTERNS ====================
        
        rules.push((
            "AI Generated Code Pattern".to_string(),
            Regex::new(r"(?i)(?:here'?s?\s+(?:a|an|the|your|some)|as\s+an\s+ai|i\s+(?:cannot|can'?t|am\s+unable)|(?:please\s+)?note\s+that|it'?s\s+important\s+to|keep\s+in\s+mind|(?:be\s+)?(?:sure|careful)\s+to|(?:make\s+sure|ensure)\s+(?:that\s+)?you)").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "AI Explanation Pattern".to_string(),
            Regex::new(r"(?i)(?:this\s+(?:code|function|method|implementation)|the\s+above\s+(?:code|example)|(?:in\s+)?this\s+example|(?:here'?s?\s+)?(?:how\s+(?:to|you|it)|what\s+(?:this|it))\s+(?:does|works?))").unwrap(),
            "Low".to_string(),
        ));
        
        rules.push((
            "AI Disclaimer Pattern".to_string(),
            Regex::new(r"(?i)(?:please\s+(?:adapt|modify|adjust|customize)|you\s+(?:may\s+need\s+to|might\s+want\s+to|should)|(?:feel\s+free\s+to|don'?t\s+hesitate\s+to)|depending\s+on\s+your|based\s+on\s+your\s+(?:needs|requirements))").unwrap(),
            "Low".to_string(),
        ));
        
        rules.push((
            "AI Helper Language".to_string(),
            Regex::new(r"(?i)(?:i'?ll?\s+(?:help|assist|show|guide)|let\s+me\s+(?:help|show|explain)|(?:i\s+)?hope\s+this\s+helps|(?:if\s+you\s+(?:have|need)|should\s+you\s+(?:have|need))\s+(?:any\s+)?(?:further\s+)?(?:questions?|help|assistance))").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "AI Template Language".to_string(),
            Regex::new(r"(?i)(?:replace\s+(?:this|these)\s+with|substitute\s+your|insert\s+your\s+(?:own|actual)|(?:add|include)\s+your\s+(?:own|specific)|customize\s+(?:this|these)\s+(?:to|for)\s+your)").unwrap(),
            "Low".to_string(),
        ));
        
        rules.push((
            "AI Code Comment Style".to_string(),
            Regex::new(r"(?i)(?://\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)|#\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)).*(?:replace|modify|customize|adapt|adjust)").unwrap(),
            "Low".to_string(),
        ));
        
        // ==================== ADVANCED UNICODE ATTACK PATTERNS ====================
        
        rules.push((
            "Suspicious Unicode Blocks".to_string(),
            Regex::new(r"[\u{FFF0}-\u{FFFF}]").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Private Use Area Characters".to_string(),
            Regex::new(r"[\u{E000}-\u{F8FF}\u{F0000}-\u{FFFFD}\u{100000}-\u{10FFFD}]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Combining Character Abuse".to_string(),
            Regex::new(r"[\u{0300}-\u{036F}]{5,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Variation Selector Abuse".to_string(),
            Regex::new(r"[\u{FE00}-\u{FE0F}\u{E0100}-\u{E01EF}]{2,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Format Character Injection".to_string(),
            Regex::new(r"[\u{200C}\u{200D}][a-zA-Z0-9][\u{200C}\u{200D}]").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== STEGANOGRAPHY & HIDDEN DATA PATTERNS ====================
        
        rules.push((
            "Hidden Text Pattern".to_string(),
            Regex::new(r"[a-zA-Z][\u{200B}\u{200C}\u{200D}]+[a-zA-Z]").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Steganographic Spacing Pattern".to_string(),
            Regex::new(r"[\u{2000}-\u{200A}]{3,}").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Invisible Character Data Encoding".to_string(),
            Regex::new(r"[\u{200B}\u{200C}\u{200D}\u{2060}]{8,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Font Variation Abuse".to_string(),
            Regex::new(r"[\u{FE00}-\u{FE0F}]{3,}").unwrap(),
            "Medium".to_string(),
        ));
        
        // ==================== ADVANCED AI TEXT DETECTION ====================
        
        rules.push((
            "AI Uncertainty Language".to_string(),
            Regex::new(r"(?i)(?:might\s+(?:be|work|help)|could\s+(?:be|work|try)|perhaps\s+(?:try|consider)|(?:it\s+)?(?:may|might)\s+be\s+(?:worth|helpful)|(?:you\s+)?(?:may|might)\s+(?:want\s+to|need\s+to))").unwrap(),
            "Low".to_string(),
        ));
        
        rules.push((
            "AI Step-by-Step Language".to_string(),
            Regex::new(r"(?i)(?:(?:here\s+are\s+the\s+)?steps?|(?:step\s+)?\d+[.:)]|first[,\s]+(?:you\s+)?(?:need\s+to|should)|next[,\s]+(?:you\s+)?(?:need\s+to|should)|finally[,\s]+(?:you\s+)?(?:need\s+to|should))").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "AI Qualification Language".to_string(),
            Regex::new(r"(?i)(?:(?:please\s+)?(?:note|keep\s+in\s+mind)\s+that|it'?s\s+(?:important\s+to|worth\s+noting)|(?:bear\s+in\s+mind|remember)\s+that|one\s+(?:thing\s+to\s+)?(?:note|remember|consider))").unwrap(),
            "Low".to_string(),
        ));
        
        rules.push((
            "AI Generic Placeholder".to_string(),
            Regex::new(r"(?i)(?:\[(?:your|insert|add|replace)[^\]]*\]|<(?:your|insert|add|replace)[^>]*>|{(?:your|insert|add|replace)[^}]*})").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "AI Code Explanation Pattern".to_string(),
            Regex::new(r"(?i)(?:(?:this\s+)?(?:code|function|method|script|implementation)\s+(?:does|will|should)|(?:the\s+)?(?:above|following)\s+(?:code|example)|what\s+this\s+(?:does|code\s+does))").unwrap(),
            "Low".to_string(),
        ));
        
        // ==================== ADVANCED HOMOGLYPH DETECTION ====================
        
        rules.push((
            "Lookalike Number Substitution".to_string(),
            Regex::new(r"[０-９𝟎-𝟗𝟘-𝟡𝟢-𝟫𝟬-𝟵][0-9]|[0-9][０-９𝟎-𝟗𝟘-𝟡𝟢-𝟫𝟬-𝟵]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Advanced Confusable Detection".to_string(),
            Regex::new(r"[ЗВНРСТУХАЕМОКаеорсух][a-zA-Z]|[a-zA-Z][ЗВНРСТУХАЕМОКаеорсух]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Script Boundary Attack".to_string(),
            Regex::new(r"[\p{Latin}][\p{Cyrillic}][\p{Latin}]|[\p{Cyrillic}][\p{Latin}][\p{Cyrillic}]").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Mathematical Bold/Italic Substitution".to_string(),
            Regex::new(r"[𝐀-𝐝𝑨-𝒙𝒜-𝓩𝔄-𝔷𝕬-𝖟𝖠-𝗓𝗔-𝘇𝘈-𝙕]").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== DOMAIN AND URL ATTACKS ====================
        
        rules.push((
            "Punycode IDN Attack".to_string(),
            Regex::new(r"xn--[a-z0-9-]+\.").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Mixed Script Domain".to_string(),
            Regex::new(r"(?i)(?:https?://)?[a-z]*[а-я]+[a-z]*\.[a-z]{2,}|(?:https?://)?[а-я]*[a-z]+[а-я]*\.[a-z]{2,}").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== CODE INJECTION PATTERNS ====================
        
        rules.push((
            "Zero-Width Code Injection".to_string(),
            Regex::new(r"[\u{200B}\u{200C}\u{200D}](?:function|var|let|const|class|import|export|eval|document|window)").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Invisible Function Call".to_string(),
            Regex::new(r"[a-zA-Z_$][a-zA-Z0-9_$]*[\u{200B}\u{200C}\u{200D}]*\(").unwrap(),
            "High".to_string(),
        ));
        
        // ==================== REMAINING LEGACY PATTERNS ====================
        
        rules.push((
            "Bidirectional Override Sequence".to_string(),
            Regex::new(r"[\u{202A}-\u{202E}]").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Mixed Scripts".to_string(),
            Regex::new(r"[a-zA-Z].*[\u{0400}-\u{04FF}]|[\u{0400}-\u{04FF}].*[a-zA-Z]").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Punycode Pattern".to_string(),
            Regex::new(r"xn--[a-z0-9]+").unwrap(),
            "Medium".to_string(),
        ));
        
        rules
    }

    pub fn analyze_text(&self, text: &str) -> AnalysisResults {
        let start_time = SystemTime::now();
        let id = Uuid::new_v4().to_string();
        let timestamp = Utc::now();
        
        // Create hash of the text
        let mut hasher = Sha256::new();
        hasher.update(text.as_bytes());
        let text_hash = format!("{:x}", hasher.finalize());
        
        let graphemes: Vec<&str> = text.graphemes(true).collect();
        let total_graphemes = graphemes.len();
        let visual_width = text.width();
        
        let mut suspicious_chars = Vec::new();
        let mut char_breakdown = HashMap::new();
        let mut script_breakdown = HashMap::new();
        
        // Analyze each character
        for (pos, ch) in text.char_indices() {
            let char_info = self.analyze_character(ch, pos);
            
            // Update statistics
            let category = char_info.category.clone();
            *char_breakdown.entry(category).or_insert(0) += 1;
            
            // Detect script
            let script = self.script_detector.detect_script(ch);
            *script_breakdown.entry(script).or_insert(0) += 1;
            
            if char_info.is_suspicious {
                suspicious_chars.push(char_info);
            }
        }
        
        // Encoding analysis
        let encoding_info = self.analyze_encoding(text);
        
        // Security analysis
        let security_analysis = self.perform_security_analysis(text, &suspicious_chars);
        
        // Pattern matching
        let patterns_found = self.find_patterns(text);
        
        // Generate recommendations
        let recommendations = self.generate_recommendations(&suspicious_chars, &security_analysis, &patterns_found);
        
        let duration = start_time.elapsed().unwrap_or_default();
        let confidence_score = self.calculate_confidence_score(&suspicious_chars, &patterns_found);
        
        AnalysisResults {
            id,
            timestamp,
            input_text: text.to_string(),
            text_hash,
            total_characters: text.chars().count(),
            total_bytes: text.len(),
            total_graphemes,
            visual_width,
            encoding_info,
            suspicious_characters: suspicious_chars,
            character_breakdown: char_breakdown,
            script_breakdown,
            analysis_duration_ms: duration.as_millis() as u64,
            confidence_score,
            security_analysis,
            patterns_found,
            recommendations,
        }
    }

    fn analyze_character(&self, ch: char, position: usize) -> CharacterInfo {
        let mut suspicion_reasons = Vec::new();
        let mut recommendations = Vec::new();
        
        // Basic character properties
        let codepoint = ch as u32;
        let utf8_bytes = ch.to_string().into_bytes();
        let mut utf16_buffer = [0u16; 2];
        let utf16_units = ch.encode_utf16(&mut utf16_buffer).to_vec();
        
        // Unicode properties
        let category = self.get_unicode_category(ch);
        let unicode_name = self.get_unicode_name(ch);
        let unicode_block = self.get_unicode_block(ch);
        let visual_width = ch.to_string().width();
        let is_combining = unicode_normalization::char::is_combining_mark(ch);
        let is_emoji = self.is_emoji(ch);
        
        // Suspicious character detection
        if ch.is_control() && !matches!(ch, '\n' | '\r' | '\t') {
            suspicion_reasons.push("Control character".to_string());
            recommendations.push("Remove or replace with visible equivalent".to_string());
        }
        
        if matches!(ch, '\u{200B}'..='\u{200D}') {
            suspicion_reasons.push("Zero-width character".to_string());
            recommendations.push("Consider if zero-width character is necessary".to_string());
        }
        
        if self.homograph_db.contains_key(&ch) {
            suspicion_reasons.push("Homograph character".to_string());
            recommendations.push("May be confused with similar-looking characters".to_string());
        }
        
        if matches!(ch, '\u{202A}'..='\u{202E}') {
            suspicion_reasons.push("Bidirectional text control".to_string());
            recommendations.push("Remove unless bidirectional text is intended".to_string());
        }
        
        if codepoint >= 0xFFF0 {
            suspicion_reasons.push("Private use or non-character".to_string());
            recommendations.push("Verify character is valid and necessary".to_string());
        }
        
        let is_suspicious = !suspicion_reasons.is_empty();
        
        CharacterInfo {
            character: ch,
            position,
            unicode_name,
            unicode_block,
            category,
            codepoint,
            utf8_bytes,
            utf16_units,
            is_suspicious,
            suspicion_reasons,
            recommendations,
            visual_width,
            is_combining,
            is_emoji,
        }
    }
    
    fn get_unicode_category(&self, ch: char) -> String {
        match ch {
            c if c.is_alphabetic() => "Letter".to_string(),
            c if c.is_numeric() => "Number".to_string(),
            c if c.is_whitespace() => "Whitespace".to_string(),
            c if c.is_control() => "Control".to_string(),
            c if c.is_ascii_punctuation() => "Punctuation".to_string(),
            _ => "Other".to_string(),
        }
    }
    
    fn get_unicode_name(&self, ch: char) -> String {
        // Simplified Unicode name mapping - in a real implementation, 
        // you'd use a proper Unicode database
        match ch {
            '\u{200B}' => "ZERO WIDTH SPACE".to_string(),
            '\u{200C}' => "ZERO WIDTH NON-JOINER".to_string(),
            '\u{200D}' => "ZERO WIDTH JOINER".to_string(),
            '\u{202A}' => "LEFT-TO-RIGHT EMBEDDING".to_string(),
            '\u{202B}' => "RIGHT-TO-LEFT EMBEDDING".to_string(),
            '\u{202C}' => "POP DIRECTIONAL FORMATTING".to_string(),
            '\u{202D}' => "LEFT-TO-RIGHT OVERRIDE".to_string(),
            '\u{202E}' => "RIGHT-TO-LEFT OVERRIDE".to_string(),
            '\u{FEFF}' => "ZERO WIDTH NO-BREAK SPACE".to_string(),
            c if c.is_ascii() => format!("LATIN {} {}", 
                if c.is_uppercase() { "CAPITAL" } else { "SMALL" },
                format!("LETTER {}", c.to_uppercase())
            ),
            _ => format!("U+{:04X}", ch as u32),
        }
    }
    
    fn get_unicode_block(&self, ch: char) -> String {
        let codepoint = ch as u32;
        match codepoint {
            0x0000..=0x007F => "Basic Latin".to_string(),
            0x0080..=0x00FF => "Latin-1 Supplement".to_string(),
            0x0100..=0x017F => "Latin Extended-A".to_string(),
            0x0180..=0x024F => "Latin Extended-B".to_string(),
            0x0250..=0x02AF => "IPA Extensions".to_string(),
            0x02B0..=0x02FF => "Spacing Modifier Letters".to_string(),
            0x0300..=0x036F => "Combining Diacritical Marks".to_string(),
            0x0370..=0x03FF => "Greek and Coptic".to_string(),
            0x0400..=0x04FF => "Cyrillic".to_string(),
            0x0500..=0x052F => "Cyrillic Supplement".to_string(),
            0x0530..=0x058F => "Armenian".to_string(),
            0x0590..=0x05FF => "Hebrew".to_string(),
            0x0600..=0x06FF => "Arabic".to_string(),
            0x0700..=0x074F => "Syriac".to_string(),
            0x0780..=0x07BF => "Thaana".to_string(),
            0x0900..=0x097F => "Devanagari".to_string(),
            0x1F600..=0x1F64F => "Emoticons".to_string(),
            0x1F300..=0x1F5FF => "Miscellaneous Symbols and Pictographs".to_string(),
            0x1F680..=0x1F6FF => "Transport and Map Symbols".to_string(),
            0x1F700..=0x1F77F => "Alchemical Symbols".to_string(),
            0x1F780..=0x1F7FF => "Geometric Shapes Extended".to_string(),
            0x1F800..=0x1F8FF => "Supplemental Arrows-C".to_string(),
            0x1F900..=0x1F9FF => "Supplemental Symbols and Pictographs".to_string(),
            0x2000..=0x206F => "General Punctuation".to_string(),
            0x20A0..=0x20CF => "Currency Symbols".to_string(),
            0x2100..=0x214F => "Letterlike Symbols".to_string(),
            0x2190..=0x21FF => "Arrows".to_string(),
            0x2200..=0x22FF => "Mathematical Operators".to_string(),
            0x2300..=0x23FF => "Miscellaneous Technical".to_string(),
            0x2400..=0x243F => "Control Pictures".to_string(),
            0x2440..=0x245F => "Optical Character Recognition".to_string(),
            0x2460..=0x24FF => "Enclosed Alphanumerics".to_string(),
            0x2500..=0x257F => "Box Drawing".to_string(),
            0x2580..=0x259F => "Block Elements".to_string(),
            0x25A0..=0x25FF => "Geometric Shapes".to_string(),
            0x2600..=0x26FF => "Miscellaneous Symbols".to_string(),
            0x2700..=0x27BF => "Dingbats".to_string(),
            0x4E00..=0x9FFF => "CJK Unified Ideographs".to_string(),
            0x3040..=0x309F => "Hiragana".to_string(),
            0x30A0..=0x30FF => "Katakana".to_string(),
            0xFFF0..=0xFFFF => "Specials".to_string(),
            _ => "Other".to_string(),
        }
    }
    
    fn is_emoji(&self, ch: char) -> bool {
        let codepoint = ch as u32;
        matches!(codepoint,
            0x1F600..=0x1F64F | // Emoticons
            0x1F300..=0x1F5FF | // Miscellaneous Symbols and Pictographs
            0x1F680..=0x1F6FF | // Transport and Map Symbols
            0x1F700..=0x1F77F | // Alchemical Symbols
            0x1F780..=0x1F7FF | // Geometric Shapes Extended
            0x1F800..=0x1F8FF | // Supplemental Arrows-C
            0x1F900..=0x1F9FF | // Supplemental Symbols and Pictographs
            0x2600..=0x26FF |   // Miscellaneous Symbols
            0x2700..=0x27BF     // Dingbats
        )
    }
    
    fn analyze_encoding(&self, text: &str) -> EncodingInfo {
        let bytes = text.as_bytes();
        let (_decoded, encoding, malformed) = WINDOWS_1252.decode(bytes);
        
        let detected_encoding = if std::str::from_utf8(bytes).is_ok() {
            "UTF-8".to_string()
        } else {
            encoding.name().to_string()
        };
        
        let confidence = if malformed { 0.5 } else { 0.95 };
        let is_valid_utf8 = std::str::from_utf8(bytes).is_ok();
        
        // Check for BOM
        let bom_detected = if bytes.starts_with(&[0xEF, 0xBB, 0xBF]) {
            Some("UTF-8 BOM".to_string())
        } else if bytes.starts_with(&[0xFF, 0xFE]) {
            Some("UTF-16 LE BOM".to_string())
        } else if bytes.starts_with(&[0xFE, 0xFF]) {
            Some("UTF-16 BE BOM".to_string())
        } else {
            None
        };
        
        // Detect line endings
        let line_endings = if text.contains("\r\n") {
            "CRLF (Windows)".to_string()
        } else if text.contains('\n') {
            "LF (Unix)".to_string()
        } else if text.contains('\r') {
            "CR (Classic Mac)".to_string()
        } else {
            "None".to_string()
        };
        
        EncodingInfo {
            detected_encoding,
            confidence,
            is_valid_utf8,
            bom_detected,
            line_endings,
        }
    }
    
    fn perform_security_analysis(&self, text: &str, suspicious_chars: &[CharacterInfo]) -> SecurityAnalysis {
        let mut phishing_indicators = Vec::new();
        let mut homograph_attacks = Vec::new();
        let mut script_mixing = Vec::new();
        let mut steganography_potential = false;
        
        // Check for homograph attacks
        for ch in text.chars() {
            if self.homograph_db.contains_key(&ch) {
                let lookalikes = &self.homograph_db[&ch];
                homograph_attacks.push(format!("Character '{}' (U+{:04X}) looks like {:?}", 
                    ch, ch as u32, lookalikes));
            }
        }
        
        // Check for script mixing
        let scripts = self.script_detector.detect_all_scripts(text);
        if scripts.len() > 1 {
            script_mixing = scripts;
        }
        
        // Check for steganography indicators
        let zero_width_count = text.chars().filter(|&c| matches!(c, '\u{200B}'..='\u{200D}')).count();
        if zero_width_count > 5 {
            steganography_potential = true;
        }
        
        // Check for phishing indicators
        if text.to_lowercase().contains("paypal") || text.to_lowercase().contains("amazon") {
            phishing_indicators.push("Contains common phishing target names".to_string());
        }
        
        if text.chars().any(|c| matches!(c, '\u{202A}'..='\u{202E}')) {
            phishing_indicators.push("Contains bidirectional text overrides".to_string());
        }
        
        // Calculate risk level
        let risk_level = if !homograph_attacks.is_empty() && !script_mixing.is_empty() {
            "Critical".to_string()
        } else if steganography_potential || !phishing_indicators.is_empty() {
            "High".to_string()
        } else if !suspicious_chars.is_empty() {
            "Medium".to_string()
        } else {
            "Low".to_string()
        };
        
        SecurityAnalysis {
            risk_level,
            phishing_indicators,
            homograph_attacks,
            steganography_potential,
            script_mixing,
        }
    }
    
    fn find_patterns(&self, text: &str) -> Vec<PatternMatch> {
        let mut matches = Vec::new();
        
        for (name, regex, severity) in &self.pattern_rules {
            for m in regex.find_iter(text) {
                matches.push(PatternMatch {
                    pattern_name: name.clone(),
                    description: format!("Pattern '{}' detected", name),
                    start_position: m.start(),
                    end_position: m.end(),
                    matched_text: m.as_str().to_string(),
                    severity: severity.clone(),
                });
            }
        }
        
        matches
    }
    
    fn generate_recommendations(&self, suspicious_chars: &[CharacterInfo], 
                               security_analysis: &SecurityAnalysis, 
                               patterns: &[PatternMatch]) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if !suspicious_chars.is_empty() {
            recommendations.push(format!("Found {} suspicious characters that may need review", 
                suspicious_chars.len()));
        }
        
        if !security_analysis.homograph_attacks.is_empty() {
            recommendations.push("Consider replacing lookalike characters with standard equivalents".to_string());
        }
        
        if security_analysis.steganography_potential {
            recommendations.push("High number of zero-width characters detected - check for hidden content".to_string());
        }
        
        if !security_analysis.script_mixing.is_empty() {
            recommendations.push("Multiple scripts detected - verify this is intentional".to_string());
        }
        
        for pattern in patterns {
            if pattern.severity == "Critical" || pattern.severity == "High" {
                recommendations.push(format!("High-risk pattern detected: {}", pattern.pattern_name));
            }
        }
        
        if recommendations.is_empty() {
            recommendations.push("Text appears to be clean and safe".to_string());
        }
        
        recommendations
    }
    
    fn calculate_confidence_score(&self, suspicious_chars: &[CharacterInfo], patterns: &[PatternMatch]) -> f32 {
        let base_score = 1.0;
        let suspicious_penalty = suspicious_chars.len() as f32 * 0.1;
        let pattern_penalty = patterns.iter()
            .map(|p| match p.severity.as_str() {
                "Critical" => 0.3,
                "High" => 0.2,
                "Medium" => 0.1,
                _ => 0.05,
            })
            .sum::<f32>();
        
        (base_score - suspicious_penalty - pattern_penalty).max(0.0).min(1.0)
    }
    
    pub fn clean_text(&self, text: &str) -> String {
        let mut cleaned = String::new();
        
        for ch in text.chars() {
            // Check if character should be removed based on comprehensive bad character list
            if self.should_remove_character(ch) {
                continue;
            }
            
            // Replace homograph characters with their Latin equivalents
            if let Some(replacements) = self.homograph_db.get(&ch) {
                if let Some(&replacement) = replacements.first() {
                    cleaned.push(replacement);
                } else {
                    cleaned.push(ch);
                }
            } else {
                cleaned.push(ch);
            }
        }
        
        // DO NOT normalize whitespace for code files - preserve original formatting
        cleaned
    }
    
    /// Enhanced character removal check using AssetManager data and comprehensive Bad_Characters.json
    fn should_remove_character(&self, ch: char) -> bool {
        let code = ch as u32;
        
        // First check using AssetManager data for severity-based analysis
        if let Some(severity) = self.asset_manager.get_character_severity(code) {
            // Remove characters based on severity levels from Bad_Characters.json
            match severity {
                "extremely_big_problems" | "high_problems" => return true,
                "medium_problems" => return true, // For comprehensive cleaning
                "low_problems" => return true,    // For maximum safety
                _ => {}
            }
        }
        
        // Fallback to hardcoded comprehensive list (for any characters not in JSON)
        
        // Extremely Big Problems - Zero-width and invisible characters
        if matches!(ch, 
            '\u{200B}' |  // Zero Width Space
            '\u{FEFF}' |  // Zero Width No-Break Space / BOM
            '\u{2060}'    // Word Joiner / Zero Width No-Break Space
        ) {
            return true;
        }
        
        // High Problems - Problematic space characters
        if matches!(ch,
            '\u{00A0}' |  // No-Break Space
            '\u{2000}' |  // En Quad
            '\u{2001}' |  // Em Quad
            '\u{2002}' |  // En Space
            '\u{2003}' |  // Em Space
            '\u{2004}' |  // Three-Per-Em Space
            '\u{2005}' |  // Four-Per-Em Space
            '\u{2006}' |  // Six-Per-Em Space
            '\u{2007}' |  // Figure Space
            '\u{2008}' |  // Punctuation Space
            '\u{2009}' |  // Thin Space
            '\u{200A}' |  // Hair Space
            '\u{202F}' |  // Narrow No-Break Space
            '\u{205F}' |  // Medium Mathematical Space
            '\u{3000}'    // Ideographic Space
        ) {
            return true;
        }
        
        // High Problems - Line and paragraph separators
        if matches!(ch,
            '\u{2028}' |  // Line Separator
            '\u{2029}'    // Paragraph Separator
        ) {
            return true;
        }
        
        // High Problems - Bidirectional text controls
        if matches!(ch,
            '\u{200E}' |  // Left-to-Right Mark
            '\u{200F}' |  // Right-to-Left Mark
            '\u{202A}' |  // Left-to-Right Embedding
            '\u{202B}' |  // Right-to-Left Embedding
            '\u{202C}' |  // Pop Directional Formatting
            '\u{202D}' |  // Left-to-Right Override
            '\u{202E}' |  // Right-to-Left Override
            '\u{2066}' |  // Left-to-Right Isolate
            '\u{2067}' |  // Right-to-Left Isolate
            '\u{2068}' |  // First Strong Isolate
            '\u{2069}'    // Pop Directional Isolate
        ) {
            return true;
        }
        
        // High Problems - NEL (Next Line)
        if ch == '\u{0085}' {
            return true;
        }
        
        // Medium Problems - Additional zero-width and invisible characters
        if matches!(ch,
            '\u{00AD}' |  // Soft Hyphen
            '\u{200C}' |  // Zero Width Non-Joiner
            '\u{200D}' |  // Zero Width Joiner
            '\u{FFFC}'    // Object Replacement Character
        ) {
            return true;
        }
        
        // C0 Control Characters (0x00-0x1F) except standard whitespace
        if code <= 0x1F && !matches!(ch, '\n' | '\r' | '\t') {
            return true;
        }
        
        // DEL character
        if ch == '\u{007F}' {
            return true;
        }
        
        // C1 Control Characters (0x80-0x9F)
        if code >= 0x80 && code <= 0x9F {
            return true;
        }
        
        // Low Problems - Mathematical notation characters
        if matches!(ch,
            '\u{2061}' |  // Function Application
            '\u{2062}' |  // Invisible Times
            '\u{2063}' |  // Invisible Separator
            '\u{2064}'    // Invisible Plus
        ) {
            return true;
        }
        
        // Variation Selectors (FE00-FE0F)
        if code >= 0xFE00 && code <= 0xFE0F {
            return true;
        }
        
        // Interlinear annotation characters
        if matches!(ch,
            '\u{FFF9}' |  // Interlinear Annotation Anchor
            '\u{FFFA}' |  // Interlinear Annotation Separator
            '\u{FFFB}'    // Interlinear Annotation Terminator
        ) {
            return true;
        }
        
        // Replacement Character (usually indicates corruption)
        if ch == '\u{FFFD}' {
            return true;
        }
        
        // Additional problematic characters not in Bad_Characters.json
        
        // Noncharacter code points (should never appear in text)
        if ch == '\u{FFFF}' {
            return true;
        }
        
        // Kaithi script characters (often problematic in mixed text)
        if ch == '\u{110BD}' {  // Kaithi Number Sign
            return true;
        }
        
        // Mongolian script characters (can cause rendering issues)
        if code >= 0x1BCA0 && code <= 0x1BCA3 {  // Mongolian cluster characters
            return true;
        }
        
        // Musical Symbol characters (notation marks, not for regular text)
        if code >= 0x1D173 && code <= 0x1D17A {  // Musical Symbol characters
            return true;
        }
        
        // Tag characters (language/script tags, invisible formatting)
        if matches!(ch,
            '\u{E0000}' |  // Language Tag
            '\u{E0001}' |  // Language Tag
            '\u{E0020}' |  // Tag Space
            '\u{E007F}'    // Cancel Tag
        ) {
            return true;
        }
        
        false
    }
}

// Script detection helper
#[derive(Debug, Clone)]
pub struct ScriptDetector;

impl ScriptDetector {
    pub fn new() -> Self {
        Self
    }

    pub fn detect_script(&self, ch: char) -> String {
        let codepoint = ch as u32;
        match codepoint {
            0x0000..=0x007F => "Basic Latin".to_string(),
            0x0080..=0x00FF => "Latin-1".to_string(),
            0x0100..=0x017F => "Latin Extended-A".to_string(),
            0x0180..=0x024F => "Latin Extended-B".to_string(),
            0x0400..=0x04FF => "Cyrillic".to_string(),
            0x0370..=0x03FF => "Greek".to_string(),
            0x0590..=0x05FF => "Hebrew".to_string(),
            0x0600..=0x06FF => "Arabic".to_string(),
            0x4E00..=0x9FFF => "CJK Unified Ideographs".to_string(),
            0x3040..=0x309F => "Hiragana".to_string(),
            0x30A0..=0x30FF => "Katakana".to_string(),
            _ => "Other".to_string(),
        }
    }

    pub fn detect_all_scripts(&self, text: &str) -> Vec<String> {
        let mut scripts = std::collections::HashSet::new();
        for ch in text.chars() {
            scripts.insert(self.detect_script(ch));
        }
        scripts.into_iter().collect()
    }
}

// Tauri command handlers
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
pub async fn analyze_characters(text: String) -> Result<AnalysisResults, String> {
    println!("Laptos_Tauri Backend: Received text for analysis in analyze_characters (first 100 chars): \"{:_>100}\"", text);
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);

    // Debug: Print the JSON being sent to the frontend
    match serde_json::to_string_pretty(&results) {
        Ok(json_string) => {
            println!("Laptos_Tauri Backend: Sending AnalysisResults JSON from analyze_characters:\n{}", json_string);
        }
        Err(e) => {
            eprintln!("Laptos_Tauri Backend: Error serializing AnalysisResults for logging in analyze_characters: {}", e);
        }
    }

    Ok(results)
}

#[tauri::command]
async fn analyze_file(request: FileAnalysisRequest) -> Result<AnalysisResults, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&request.content);
    Ok(results)
}

#[tauri::command]
pub async fn batch_analyze(texts: Vec<String>) -> Result<Vec<AnalysisResults>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut results = Vec::new();
    
    for text in texts {
        let analysis = analyzer.analyze_text(&text);
        results.push(analysis);
    }
    
    Ok(results)
}

#[tauri::command]
pub async fn export_analysis(results: AnalysisResults, format: String) -> Result<String, String> {
    match format.as_str() {
        "json" => {
            serde_json::to_string_pretty(&results)
                .map_err(|e| format!("Failed to serialize to JSON: {}", e))
        },
        "csv" => {
            let mut csv_content = String::new();
            csv_content.push_str("Position,Character,Unicode,Category,Suspicious,Reasons\n");
            
            for char_info in &results.suspicious_characters {
                csv_content.push_str(&format!(
                    "{},{},U+{:04X},{},{},{}\n",
                    char_info.position,
                    char_info.character,
                    char_info.codepoint,
                    char_info.category,
                    char_info.is_suspicious,
                    char_info.suspicion_reasons.join("; ")
                ));
            }
            
            Ok(csv_content)
        },
        "txt" => {
            let mut report = String::new();
            report.push_str(&format!("Character Analysis Report\n"));
            report.push_str(&format!("Generated: {}\n", results.timestamp));
            report.push_str(&format!("Analysis ID: {}\n\n", results.id));
            
            report.push_str(&format!("Summary:\n"));
            report.push_str(&format!("- Total Characters: {}\n", results.total_characters));
            report.push_str(&format!("- Total Bytes: {}\n", results.total_bytes));
            report.push_str(&format!("- Visual Width: {}\n", results.visual_width));
            report.push_str(&format!("- Suspicious Characters: {}\n", results.suspicious_characters.len()));
            report.push_str(&format!("- Risk Level: {}\n\n", results.security_analysis.risk_level));
            
            if !results.suspicious_characters.is_empty() {
                report.push_str("Suspicious Characters:\n");
                for char_info in &results.suspicious_characters {
                    report.push_str(&format!(
                        "- Position {}: '{}' (U+{:04X}) - {}\n",
                        char_info.position,
                        char_info.character,
                        char_info.codepoint,
                        char_info.suspicion_reasons.join(", ")
                    ));
                }
                report.push_str("\n");
            }
            
            if !results.recommendations.is_empty() {
                report.push_str("Recommendations:\n");
                for rec in &results.recommendations {
                    report.push_str(&format!("- {}\n", rec));
                }
            }
            
            Ok(report)
        },
        _ => Err("Unsupported export format".to_string()),
    }
}

#[tauri::command]
pub async fn get_character_details(character: char) -> Result<CharacterInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let char_info = analyzer.analyze_character(character, 0);
    Ok(char_info)
}

#[tauri::command]
pub async fn detect_encoding(bytes: Vec<u8>) -> Result<EncodingInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    
    // Try to convert bytes to string
    let text = match std::str::from_utf8(&bytes) {
        Ok(s) => s.to_string(),
        Err(_) => {
            // Try different encodings
            let (decoded, _encoding, _) = WINDOWS_1252.decode(&bytes);
            decoded.to_string()
        }
    };
    
    let encoding_info = analyzer.analyze_encoding(&text);
    Ok(encoding_info)
}

#[tauri::command]
pub async fn check_homographs(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut homographs = Vec::new();
    
    for ch in text.chars() {
        if analyzer.homograph_db.contains_key(&ch) {
            let lookalikes = &analyzer.homograph_db[&ch];
            homographs.push(format!("'{}' (U+{:04X}) looks like: {:?}", 
                ch, ch as u32, lookalikes));
        }
    }
    
    Ok(homographs)
}

#[tauri::command]
pub async fn normalize_text(text: String, form: String) -> Result<String, String> {
    use unicode_normalization::UnicodeNormalization;
    
    let normalized = match form.as_str() {
        "nfc" => text.nfc().collect::<String>(),
        "nfd" => text.nfd().collect::<String>(),
        "nfkc" => text.nfkc().collect::<String>(),
        "nfkd" => text.nfkd().collect::<String>(),
        _ => return Err("Invalid normalization form. Use: nfc, nfd, nfkc, nfkd".to_string()),
    };
    
    Ok(normalized)
}

#[tauri::command]
pub async fn get_script_info(text: String) -> Result<HashMap<String, usize>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut script_counts = HashMap::new();
    
    for ch in text.chars() {
        let script = analyzer.script_detector.detect_script(ch);
        *script_counts.entry(script).or_insert(0) += 1;
    }
    
    Ok(script_counts)
}

#[derive(Serialize, Deserialize)]
struct CleaningChange {
    start: usize,
    end: usize,
    original: String,
    cleaned: String,
    change_type: String,
}

#[derive(Serialize, Deserialize)]
pub struct CleaningResult {
    original: String,
    cleaned: String,
    changes: Vec<CleaningChange>,
    stats: HashMap<String, u32>,
}

#[tauri::command]
pub async fn clean_text(text: String, options: HashMap<String, bool>) -> Result<String, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut cleaned = text;
    
    // Remove zero-width characters (comprehensive set)
    if *options.get("remove_zero_width").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !matches!(c, 
                '\u{200B}' |  // Zero Width Space
                '\u{FEFF}' |  // Zero Width No-Break Space / BOM
                '\u{2060}'    // Word Joiner / Zero Width No-Break Space
                '\u{200C}' |  // Zero Width Non-Joiner
                '\u{200D}'    // Zero Width Joiner
            ))
            .collect();
    }
    
    // Remove control characters (comprehensive set)
    if *options.get("remove_control").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| {
                let code = c as u32;
                // Keep standard whitespace, remove other control characters
                !((code <= 0x1F && !matches!(c, '\n' | '\r' | '\t')) ||
                  c == '\u{007F}' ||
                  (code >= 0x80 && code <= 0x9F) ||
                  c == '\u{0085}')
            })
            .collect();
    }
    
    // Remove bidirectional overrides (comprehensive set)
    if *options.get("remove_bidi").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !matches!(c,
                '\u{200E}' |  // Left-to-Right Mark
                '\u{200F}' |  // Right-to-Left Mark
                '\u{202A}' |  // Left-to-Right Embedding
                '\u{202B}' |  // Right-to-Left Embedding
                '\u{202C}' |  // Pop Directional Formatting
                '\u{202D}' |  // Left-to-Right Override
                '\u{202E}' |  // Right-to-Left Override
                '\u{2066}' |  // Left-to-Right Isolate
                '\u{2067}' |  // Right-to-Left Isolate
                '\u{2068}' |  // First Strong Isolate
                '\u{2069}'    // Pop Directional Isolate
            ))
            .collect();
    }
    
    // Comprehensive cleaning using all bad characters from Bad_Characters.json
    if *options.get("comprehensive_clean").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !analyzer.should_remove_character(c))
            .collect();
    }
    
    // Normalize whitespace
    if *options.get("normalize_whitespace").unwrap_or(&false) {
        cleaned = cleaned.split_whitespace().collect::<Vec<_>>().join(" ");
    }
    
    Ok(cleaned)
}

#[tauri::command]
pub async fn clean_text_detailed(text: String, options: HashMap<String, bool>) -> Result<CleaningResult, String> {
    let original = text.clone();
    let mut changes = Vec::new();
    let mut stats = HashMap::new();
    let mut current_text = text;
    let char_offset = 0;
    
    let analyzer = CharacterAnalyzer::new()?;
    
    // Track each cleaning operation with changes using comprehensive character removal
    if *options.get("remove_zero_width").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            // Use comprehensive zero-width character detection
            if matches!(ch, 
                '\u{200B}' |  // Zero Width Space
                '\u{FEFF}' |  // Zero Width No-Break Space / BOM
                '\u{2060}' |  // Word Joiner / Zero Width No-Break Space
                '\u{200C}' |  // Zero Width Non-Joiner
                '\u{200D}'    // Zero Width Joiner
            ) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: ch.to_string(),
                    cleaned: String::new(),
                    change_type: "zero_width_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("zero_width_removed".to_string(), removed_count);
    }
    
    if *options.get("remove_control").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            let code = ch as u32;
            // Comprehensive control character removal
            let is_control_to_remove = 
                // C0 Control Characters (0x00-0x1F) except standard whitespace
                (code <= 0x1F && !matches!(ch, '\n' | '\r' | '\t')) ||
                // DEL character
                ch == '\u{007F}' ||
                // C1 Control Characters (0x80-0x9F)
                (code >= 0x80 && code <= 0x9F) ||
                // NEL (Next Line)
                ch == '\u{0085}';
                
            if is_control_to_remove {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "control_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("control_removed".to_string(), removed_count);
    }
    
    if *options.get("remove_bidi").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            // Comprehensive bidirectional control characters
            if matches!(ch,
                '\u{200E}' |  // Left-to-Right Mark
                '\u{200F}' |  // Right-to-Left Mark
                '\u{202A}' |  // Left-to-Right Embedding
                '\u{202B}' |  // Right-to-Left Embedding
                '\u{202C}' |  // Pop Directional Formatting
                '\u{202D}' |  // Left-to-Right Override
                '\u{202E}' |  // Right-to-Left Override
                '\u{2066}' |  // Left-to-Right Isolate
                '\u{2067}' |  // Right-to-Left Isolate
                '\u{2068}' |  // First Strong Isolate
                '\u{2069}'    // Pop Directional Isolate
            ) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "bidi_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("bidi_removed".to_string(), removed_count);
    }
    
    // Add comprehensive cleaning option that removes ALL problematic characters
    if *options.get("comprehensive_clean").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            if analyzer.should_remove_character(ch) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "comprehensive_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("comprehensive_removed".to_string(), removed_count);
    }
    
    if *options.get("normalize_whitespace").unwrap_or(&false) {
        let normalized = current_text.split_whitespace().collect::<Vec<_>>().join(" ");
        if normalized != current_text {
            changes.push(CleaningChange {
                start: 0,
                end: current_text.len(),
                original: current_text.clone(),
                cleaned: normalized.clone(),
                change_type: "whitespace_normalized".to_string(),
            });
            stats.insert("whitespace_normalized".to_string(), 1);
        }
        current_text = normalized;
    }
    
    Ok(CleaningResult {
        original,
        cleaned: current_text,
        changes,
        stats,
    })
}

#[tauri::command]
pub async fn generate_report(results: AnalysisResults) -> Result<String, String> {
    let mut report = String::new();
    
    // HTML report
    report.push_str("<!DOCTYPE html>\n<html>\n<head>\n");
    report.push_str("<title>Character Analysis Report</title>\n");
    report.push_str("<style>\n");
    report.push_str("body { font-family: Arial, sans-serif; margin: 20px; }\n");
    report.push_str(".header { background: #f5f5f5; padding: 15px; border-radius: 5px; }\n");
    report.push_str(".section { margin: 20px 0; }\n");
    report.push_str(".suspicious { color: #d32f2f; font-weight: bold; }\n");
    report.push_str(".safe { color: #388e3c; }\n");
    report.push_str("table { border-collapse: collapse; width: 100%; }\n");
    report.push_str("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
    report.push_str("th { background-color: #f2f2f2; }\n");
    report.push_str("</style>\n</head>\n<body>\n");
    
    report.push_str(&format!("<div class='header'>\n"));
    report.push_str(&format!("<h1>Character Analysis Report</h1>\n"));
    report.push_str(&format!("<p><strong>Analysis ID:</strong> {}</p>\n", results.id));
    report.push_str(&format!("<p><strong>Generated:</strong> {}</p>\n", results.timestamp));
    report.push_str(&format!("<p><strong>Risk Level:</strong> <span class='{}'>{}</span></p>\n", 
        if results.security_analysis.risk_level == "Low" { "safe" } else { "suspicious" },
        results.security_analysis.risk_level));
    report.push_str("</div>\n");
    
    report.push_str(&format!("<div class='section'>\n"));
    report.push_str(&format!("<h2>Summary</h2>\n"));
    report.push_str(&format!("<ul>\n"));
    report.push_str(&format!("<li>Total Characters: {}</li>\n", results.total_characters));
    report.push_str(&format!("<li>Total Bytes: {}</li>\n", results.total_bytes));
    report.push_str(&format!("<li>Visual Width: {}</li>\n", results.visual_width));
    report.push_str(&format!("<li>Suspicious Characters: {}</li>\n", results.suspicious_characters.len()));
    report.push_str(&format!("<li>Confidence Score: {:.2}%</li>\n", results.confidence_score * 100.0));
    report.push_str(&format!("</ul>\n"));
    report.push_str("</div>\n");
    
    if !results.suspicious_characters.is_empty() {
        report.push_str(&format!("<div class='section'>\n"));
        report.push_str(&format!("<h2>Suspicious Characters</h2>\n"));
        report.push_str("<table>\n");
        report.push_str("<tr><th>Position</th><th>Character</th><th>Unicode</th><th>Category</th><th>Reasons</th></tr>\n");
        
        for char_info in &results.suspicious_characters {
            report.push_str(&format!(
                "<tr><td>{}</td><td>{}</td><td>U+{:04X}</td><td>{}</td><td>{}</td></tr>\n",
                char_info.position,
                char_info.character,
                char_info.codepoint,
                char_info.category,
                char_info.suspicion_reasons.join(", ")
            ));
        }
        
        report.push_str("</table>\n</div>\n");
    }
    
    if !results.recommendations.is_empty() {
        report.push_str(&format!("<div class='section'>\n"));
        report.push_str(&format!("<h2>Recommendations</h2>\n"));
        report.push_str("<ul>\n");
        for rec in &results.recommendations {
            report.push_str(&format!("<li>{}</li>\n", rec));
        }
        report.push_str("</ul>\n</div>\n");
    }
    
    report.push_str("</body>\n</html>");
    
    Ok(report)
}

#[tauri::command]
async fn analyze_text(text: String) -> Result<AnalysisResults, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results)
}

#[tauri::command]
async fn get_security_analysis(text: String) -> Result<SecurityAnalysis, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results.security_analysis)
}

#[tauri::command]
async fn get_pattern_analysis(text: String) -> Result<Vec<PatternMatch>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results.patterns_found)
}

#[tauri::command]
async fn get_encoding_analysis(text: String) -> Result<EncodingInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let encoding_info = analyzer.analyze_encoding(&text);
    Ok(encoding_info)
}

#[tauri::command]
async fn detect_mixed_scripts(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let scripts = analyzer.script_detector.detect_all_scripts(&text);
    Ok(scripts)
}

#[tauri::command]
async fn analyze_homoglyphs(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut homographs = Vec::new();
    
    for ch in text.chars() {
        if analyzer.homograph_db.contains_key(&ch) {
            let lookalikes = &analyzer.homograph_db[&ch];
            homographs.push(format!("'{}' (U+{:04X}) looks like: {:?}", 
                ch, ch as u32, lookalikes));
        }
    }
    
    Ok(homographs)
}

#[tauri::command]
async fn detect_suspicious_patterns(text: String) -> Result<Vec<PatternMatch>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let patterns = analyzer.find_patterns(&text);
    Ok(patterns)
}

// Data structures for AI detection results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDetectionMatch {
    pub pattern_name: String,
    pub description: String,
    pub severity: String,
    pub confidence: f64,
    pub start_position: usize,
    pub end_position: usize,
    pub matched_text: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDetectionResult {
    pub overall_confidence: f64,
    pub ai_likelihood: String,
    pub patterns_detected: usize,
    pub detected_patterns: Vec<AIDetectionMatch>,
    pub analysis_summary: String,
}

#[tauri::command]
pub async fn detect_ai_content(content: String) -> Result<AIDetectionResult, String> {
    let asset_manager = AssetManager::new()?;
    
    if !asset_manager.has_ai_patterns() {
        return Ok(AIDetectionResult {
            overall_confidence: 0.0,
            ai_likelihood: "Unknown".to_string(),
            patterns_detected: 0,
            detected_patterns: Vec::new(),
            analysis_summary: "AI patterns not available for analysis".to_string(),
        });
    }
    
    let mut detected_patterns = Vec::new();
    let mut total_confidence = 0.0;
    let mut pattern_count = 0;
    
    // Analyze different AI pattern categories
    
    // 1. Code injection patterns
    if let Some(injection_patterns) = asset_manager.get_code_injection_patterns() {
        for pattern in &injection_patterns.patterns {
            if let Ok(regex) = regex::Regex::new(&pattern.pattern) {
                for mat in regex.find_iter(&content) {
                    let confidence = pattern.confidence_score.unwrap_or(0.5);
                    total_confidence += confidence;
                    pattern_count += 1;
                    
                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "AI code injection pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "medium".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 2. Advanced homoglyph patterns
    if let Some(homoglyph_patterns) = asset_manager.get_advanced_homoglyph_patterns() {
        for pattern in &homoglyph_patterns.patterns {
            if let Ok(regex) = regex::Regex::new(&pattern.pattern) {
                for mat in regex.find_iter(&content) {
                    let confidence = pattern.confidence_score.unwrap_or(0.6);
                    total_confidence += confidence;
                    pattern_count += 1;
                    
                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Advanced homoglyph pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "high".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 3. Steganography patterns
    if let Some(stego_patterns) = asset_manager.get_steganography_patterns() {
        for pattern in &stego_patterns.patterns {
            if let Ok(regex) = regex::Regex::new(&pattern.pattern) {
                for mat in regex.find_iter(&content) {
                    let confidence = pattern.confidence_score.unwrap_or(0.7);
                    total_confidence += confidence;
                    pattern_count += 1;
                    
                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Steganography pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "high".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 4. Bidirectional attack patterns
    if let Some(bidi_patterns) = asset_manager.get_bidirectional_attack_patterns() {
        for pattern in &bidi_patterns.patterns {
            if let Ok(regex) = regex::Regex::new(&pattern.pattern) {
                for mat in regex.find_iter(&content) {
                    let confidence = pattern.confidence_score.unwrap_or(0.8);
                    total_confidence += confidence;
                    pattern_count += 1;
                    
                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Bidirectional attack pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "critical".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 5. AI code patterns
    if let Some(code_patterns) = asset_manager.get_ai_code_patterns() {
        for pattern in &code_patterns.patterns {
            if let Ok(regex) = regex::Regex::new(&pattern.pattern) {
                for mat in regex.find_iter(&content) {
                    let confidence = pattern.confidence_score.unwrap_or(0.4);
                    total_confidence += confidence;
                    pattern_count += 1;
                    
                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "AI code pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "low".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // Calculate overall confidence and likelihood
    let overall_confidence = if pattern_count > 0 {
        total_confidence / pattern_count as f64
    } else {
        0.0
    };
    
    let ai_likelihood = match overall_confidence {
        c if c >= 0.8 => "Very High",
        c if c >= 0.6 => "High",
        c if c >= 0.4 => "Medium", 
        c if c >= 0.2 => "Low",
        _ => "Very Low",
    }.to_string();
    
    let analysis_summary = if pattern_count == 0 {
        "No AI-generated patterns detected in the content.".to_string()
    } else {
        format!(
            "Detected {} AI patterns with an overall confidence of {:.1}%. Analysis suggests {} likelihood of AI-generated content.",
            pattern_count,
            overall_confidence * 100.0,
            ai_likelihood.to_lowercase()
        )
    };
    
    Ok(AIDetectionResult {
        overall_confidence,
        ai_likelihood,
        patterns_detected: detected_patterns.len(),
        detected_patterns,
        analysis_summary,
    })
}

#[tauri::command]
pub async fn get_file_types_summary() -> Result<FileTypesSummary, String> {
    let asset_manager = AssetManager::new()?;
    Ok(asset_manager.file_types)
}
