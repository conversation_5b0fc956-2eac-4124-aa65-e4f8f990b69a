#!/bin/bash
# Comprehensive Auto-Test Script for Bash Interface
# Tests all functions, error handling, verbose output, and edge cases

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASH_SCRIPT="$SCRIPT_DIR/scripts/codebase_analyzer.sh"
TEST_DIR="$SCRIPT_DIR/test_bash_interface"
LOG_FILE="$TEST_DIR/test_results.log"
VERBOSE=false
SKIP_BUILD=false
TEST_FILTER=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Test statistics
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# Test results array
declare -a TEST_RESULTS=()

# Usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

OPTIONS:
    -v, --verbose       Enable verbose output
    -s, --skip-build    Skip building the analyzer
    -f, --filter TEXT   Only run tests matching TEXT
    -h, --help          Show this help

EXAMPLES:
    $0                           # Run all tests
    $0 --verbose                 # Run with verbose output
    $0 --filter "Basic"          # Run only basic tests
    $0 --skip-build --filter "Error"  # Skip build, test only error handling

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1" >&2
    fi
}

# Test output functions
write_test_header() {
    local title="$1"
    echo ""
    echo -e "${CYAN}$(printf '=%.0s' {1..80})${NC}"
    echo -e "${CYAN}TEST: $title${NC}"
    echo -e "${CYAN}$(printf '=%.0s' {1..80})${NC}"
}

write_test_result() {
    local test_name="$1"
    local passed="$2"
    local details="${3:-}"
    local expected="${4:-}"
    local actual="${5:-}"
    
    ((TOTAL_TESTS++))
    
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    local log_entry="[$timestamp] "
    
    if [[ "$passed" == "true" ]]; then
        ((PASSED_TESTS++))
        echo -e "✅ ${GREEN}PASS${NC}: $test_name"
        log_entry+="PASS: $test_name"
    else
        ((FAILED_TESTS++))
        echo -e "❌ ${RED}FAIL${NC}: $test_name"
        log_entry+="FAIL: $test_name"
        
        if [[ -n "$details" ]]; then
            echo -e "   ${YELLOW}Details: $details${NC}"
            log_entry+=" - $details"
        fi
        if [[ -n "$expected" ]]; then
            echo -e "   ${YELLOW}Expected: $expected${NC}"
        fi
        if [[ -n "$actual" ]]; then
            echo -e "   ${YELLOW}Actual: $actual${NC}"
        fi
    fi
    
    # Store result
    TEST_RESULTS+=("$log_entry")
    echo "$log_entry" >> "$LOG_FILE"
}

# Function to run bash script with timeout and capture
run_bash_script() {
    local args="$1"
    local expected_exit_code="${2:-0}"
    local timeout_seconds="${3:-30}"
    local temp_out="$TEST_DIR/temp_output_$$"
    local temp_err="$TEST_DIR/temp_error_$$"
    
    # Run with timeout
    timeout "$timeout_seconds" bash "$BASH_SCRIPT" $args > "$temp_out" 2> "$temp_err" || true
    local exit_code=$?
    
    # Read outputs
    local output=""
    local error=""
    if [[ -f "$temp_out" ]]; then
        output=$(cat "$temp_out")
        rm -f "$temp_out"
    fi
    if [[ -f "$temp_err" ]]; then
        error=$(cat "$temp_err")
        rm -f "$temp_err"
    fi
    
    # Return result via global variables (bash limitation)
    RESULT_EXIT_CODE=$exit_code
    RESULT_OUTPUT="$output"
    RESULT_ERROR="$error"
    
    log_debug "Command: bash $BASH_SCRIPT $args"
    log_debug "Exit Code: $exit_code"
    log_debug "Output Length: ${#output}"
    log_debug "Error Length: ${#error}"
}

# Test functions
test_bash_script_exists() {
    write_test_header "Bash Script Existence and Permissions"
    
    # Test 1: Script exists
    if [[ -f "$BASH_SCRIPT" ]]; then
        write_test_result "Bash script exists" "true" "Path: $BASH_SCRIPT"
    else
        write_test_result "Bash script exists" "false" "Path: $BASH_SCRIPT"
        return 1
    fi
    
    # Test 2: Script is readable
    if [[ -r "$BASH_SCRIPT" ]]; then
        write_test_result "Bash script is readable" "true"
    else
        write_test_result "Bash script is readable" "false"
    fi
    
    # Test 3: Script is executable
    if [[ -x "$BASH_SCRIPT" ]]; then
        write_test_result "Bash script is executable" "true"
    else
        write_test_result "Bash script is executable" "false"
        # Try to make it executable
        chmod +x "$BASH_SCRIPT" 2>/dev/null || true
    fi
    
    # Test 4: Script has valid shebang
    local first_line=$(head -n1 "$BASH_SCRIPT")
    if [[ "$first_line" =~ ^#!/ ]]; then
        write_test_result "Bash script has valid shebang" "true" "First line: $first_line"
    else
        write_test_result "Bash script has valid shebang" "false" "First line: $first_line"
    fi
}

test_basic_commands() {
    write_test_header "Basic Command Testing"
    
    # Test 1: Help command
    run_bash_script "--help" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && "$RESULT_OUTPUT" =~ USAGE ]]; then
        write_test_result "Help command works" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Help command works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: No arguments (should show help)
    run_bash_script "" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && ("$RESULT_OUTPUT" =~ USAGE || "$RESULT_ERROR" =~ USAGE) ]]; then
        write_test_result "No arguments shows help" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "No arguments shows help" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 3: Invalid command
    run_bash_script "invalid-command" 2
    if [[ $RESULT_EXIT_CODE -eq 2 ]]; then
        write_test_result "Invalid command returns error code 2" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Invalid command returns error code 2" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 4: Health check
    run_bash_script "health" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Health command works" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Health command works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

test_verbose_output() {
    write_test_header "Verbose Output Testing"
    
    # Test 1: Normal vs verbose output
    run_bash_script "health" 0
    local normal_output_len=$((${#RESULT_OUTPUT} + ${#RESULT_ERROR}))
    
    run_bash_script "--verbose health" 0
    local verbose_output_len=$((${#RESULT_OUTPUT} + ${#RESULT_ERROR}))
    
    if [[ $verbose_output_len -gt $normal_output_len ]]; then
        write_test_result "Verbose flag increases output" "true" "Normal: $normal_output_len chars, Verbose: $verbose_output_len chars"
    else
        write_test_result "Verbose flag increases output" "false" "Normal: $normal_output_len chars, Verbose: $verbose_output_len chars"
    fi
    
    # Test 2: Debug messages in verbose
    if [[ "$RESULT_OUTPUT" =~ DEBUG || "$RESULT_ERROR" =~ DEBUG ]]; then
        write_test_result "Verbose shows debug messages" "true"
    else
        write_test_result "Verbose shows debug messages" "false"
    fi
    
    # Test 3: Quiet mode
    run_bash_script "--quiet health" 0
    local quiet_output_len=${#RESULT_OUTPUT}
    
    if [[ $quiet_output_len -le $normal_output_len ]]; then
        write_test_result "Quiet flag reduces output" "true" "Quiet: $quiet_output_len chars"
    else
        write_test_result "Quiet flag reduces output" "false" "Quiet: $quiet_output_len chars"
    fi
}

test_dry_run_mode() {
    write_test_header "Dry Run Mode Testing"
    
    # Create test directory
    local test_path="$TEST_DIR/dry_run_test"
    mkdir -p "$test_path"
    echo "console.log('test');" > "$test_path/test.js"
    
    # Test 1: Dry run analyze
    run_bash_script "--dry-run analyze \"$test_path\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && ("$RESULT_OUTPUT" =~ "DRY RUN" || "$RESULT_ERROR" =~ "DRY RUN") ]]; then
        write_test_result "Dry run analyze works" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Dry run analyze works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: Dry run doesn't create files
    local reports_dir="$SCRIPT_DIR/reports"
    local files_before=0
    if [[ -d "$reports_dir" ]]; then
        files_before=$(find "$reports_dir" -type f | wc -l)
    fi
    
    run_bash_script "--dry-run analyze \"$test_path\"" 0
    
    local files_after=0
    if [[ -d "$reports_dir" ]]; then
        files_after=$(find "$reports_dir" -type f | wc -l)
    fi
    
    if [[ $files_after -eq $files_before ]]; then
        write_test_result "Dry run doesn't create output files" "true" "Before: $files_before, After: $files_after"
    else
        write_test_result "Dry run doesn't create output files" "false" "Before: $files_before, After: $files_after"
    fi
}

test_error_handling() {
    write_test_header "Error Handling Testing"
    
    # Test 1: Missing directory
    run_bash_script "analyze /nonexistent/directory" 3
    if [[ $RESULT_EXIT_CODE -eq 3 ]]; then
        write_test_result "Missing directory returns exit code 3" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Missing directory returns exit code 3" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: Missing file for scan
    run_bash_script "scan /nonexistent/file.js" 3
    if [[ $RESULT_EXIT_CODE -eq 3 ]]; then
        write_test_result "Missing file returns exit code 3" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Missing file returns exit code 3" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 3: Missing file for export
    run_bash_script "export /nonexistent/analysis.json" 3
    if [[ $RESULT_EXIT_CODE -eq 3 ]]; then
        write_test_result "Missing analysis file returns exit code 3" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Missing analysis file returns exit code 3" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 4: Invalid format
    local test_path="$TEST_DIR/error_test"
    mkdir -p "$test_path"
    echo "console.log('test');" > "$test_path/test.js"
    
    run_bash_script "--format invalid analyze \"$test_path\"" 2
    if [[ $RESULT_EXIT_CODE -eq 2 ]]; then
        write_test_result "Invalid format returns exit code 2" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Invalid format returns exit code 2" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 5: Command missing arguments
    run_bash_script "analyze" 2
    if [[ $RESULT_EXIT_CODE -eq 2 ]]; then
        write_test_result "Missing arguments returns exit code 2" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Missing arguments returns exit code 2" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

test_output_formats() {
    write_test_header "Output Format Testing"
    
    # Create test directory
    local test_path="$TEST_DIR/format_test"
    mkdir -p "$test_path"
    
    # Create test files
    echo "console.log('clean file');" > "$test_path/clean.js"
    printf "console.log('test');\u200B" > "$test_path/suspicious.js"  # Zero-width space
    
    local formats=("json" "markdown" "text")
    
    for format in "${formats[@]}"; do
        run_bash_script "--format $format analyze \"$test_path\"" 0
        if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
            write_test_result "$format format works" "true" "Exit code: $RESULT_EXIT_CODE"
            
            # Check if output files were created
            local reports_dir="$SCRIPT_DIR/reports"
            if [[ -d "$reports_dir" ]]; then
                local format_files=$(find "$reports_dir" -name "*.$format" | wc -l)
                if [[ $format_files -gt 0 ]]; then
                    write_test_result "$format output files created" "true" "Files found: $format_files"
                else
                    write_test_result "$format output files created" "false" "Files found: $format_files"
                fi
            else
                write_test_result "$format output files created" "false" "Reports directory not found"
            fi
        else
            write_test_result "$format format works" "false" "Exit code: $RESULT_EXIT_CODE"
        fi
    done
}

test_scan_function() {
    write_test_header "File Scan Function Testing"
    
    # Create test files
    local test_path="$TEST_DIR/scan_test"
    mkdir -p "$test_path"
    
    # Clean file
    local clean_file="$test_path/clean.js"
    echo "console.log('This is a clean file');" > "$clean_file"
    
    # Suspicious file with zero-width space
    local suspicious_file="$test_path/suspicious.js"
    printf "console.log('test');\u200B// zero-width space" > "$suspicious_file"
    
    # Test 1: Scan clean file
    run_bash_script "scan \"$clean_file\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && ("$RESULT_OUTPUT" =~ Clean || "$RESULT_ERROR" =~ Clean) ]]; then
        write_test_result "Scan clean file works" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Scan clean file works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: Scan suspicious file
    run_bash_script "scan \"$suspicious_file\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && ("$RESULT_OUTPUT" =~ Issues || "$RESULT_ERROR" =~ Issues) ]]; then
        write_test_result "Scan suspicious file detects issues" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Scan suspicious file detects issues" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

test_analyze_function() {
    write_test_header "Directory Analysis Function Testing"
    
    # Create comprehensive test directory
    local test_path="$TEST_DIR/analyze_test"
    mkdir -p "$test_path/js" "$test_path/css"
    
    # Create test files
    echo "console.log('clean JavaScript');" > "$test_path/js/clean.js"
    printf "console.log('test');\u200B" > "$test_path/js/suspicious.js"
    echo "body { color: blue; }" > "$test_path/css/styles.css"
    echo "# README" > "$test_path/README.md"
    
    # Test 1: Basic analysis
    run_bash_script "analyze \"$test_path\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Directory analysis works" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Directory analysis works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: Check if reports are created
    local reports_dir="$SCRIPT_DIR/reports"
    if [[ -d "$reports_dir" ]]; then
        local report_count=$(find "$reports_dir" -name "analysis_*.json" | wc -l)
        if [[ $report_count -gt 0 ]]; then
            write_test_result "Analysis creates report files" "true" "Reports found: $report_count"
        else
            write_test_result "Analysis creates report files" "false" "Reports found: $report_count"
        fi
    else
        write_test_result "Analysis creates report files" "false" "Reports directory not found"
    fi
    
    # Test 3: Custom output directory
    local custom_output="$TEST_DIR/custom_reports"
    run_bash_script "--output \"$custom_output\" analyze \"$test_path\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 && -d "$custom_output" ]]; then
        write_test_result "Custom output directory works" "true"
    else
        write_test_result "Custom output directory works" "false"
    fi
}

test_export_function() {
    write_test_header "Export Function Testing"
    
    # First create an analysis to export
    local test_path="$TEST_DIR/export_test"
    mkdir -p "$test_path"
    echo "console.log('test');" > "$test_path/test.js"
    
    # Create analysis
    run_bash_script "analyze \"$test_path\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        # Find the created analysis file
        local reports_dir="$SCRIPT_DIR/reports"
        local analysis_file=$(find "$reports_dir" -name "analysis_*.json" | head -n1)
        
        if [[ -n "$analysis_file" && -f "$analysis_file" ]]; then
            # Test export to different formats
            local formats=("markdown" "text" "json")
            for format in "${formats[@]}"; do
                run_bash_script "--format $format export \"$analysis_file\"" 0
                if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
                    write_test_result "Export to $format works" "true" "Exit code: $RESULT_EXIT_CODE"
                else
                    write_test_result "Export to $format works" "false" "Exit code: $RESULT_EXIT_CODE"
                fi
            done
        else
            write_test_result "Export test setup failed" "false" "No analysis file found"
        fi
    else
        write_test_result "Export test setup failed" "false" "Analysis creation failed"
    fi
}

test_test_command() {
    write_test_header "Built-in Test Command Testing"
    
    # Test the built-in test command
    run_bash_script "test" 0 60
    if [[ $RESULT_EXIT_CODE -eq 0 || $RESULT_EXIT_CODE -eq 1 ]]; then
        write_test_result "Built-in test command runs" "true" "Exit code: $RESULT_EXIT_CODE"
        
        # Check if test output contains expected sections
        if [[ "$RESULT_OUTPUT" =~ "Test Summary" || "$RESULT_ERROR" =~ "Test Summary" ]]; then
            write_test_result "Test command shows results summary" "true"
        else
            write_test_result "Test command shows results summary" "false"
        fi
        
        if [[ "$RESULT_OUTPUT" =~ "Test [0-9]" || "$RESULT_ERROR" =~ "Test [0-9]" ]]; then
            write_test_result "Test command shows individual test results" "true"
        else
            write_test_result "Test command shows individual test results" "false"
        fi
    else
        write_test_result "Built-in test command runs" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

test_demo_command() {
    write_test_header "Demo Command Testing"
    
    # Test the demo command
    run_bash_script "demo" 0 45
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Demo command works" "true" "Exit code: $RESULT_EXIT_CODE"
        
        # Check if demo creates expected output
        if [[ "$RESULT_OUTPUT" =~ Demo || "$RESULT_ERROR" =~ Demo ]]; then
            write_test_result "Demo command produces output" "true"
        else
            write_test_result "Demo command produces output" "false"
        fi
        
        # Check if demo creates sample files
        local reports_dir="$SCRIPT_DIR/reports"
        if [[ -d "$reports_dir" ]]; then
            write_test_result "Demo creates analysis reports" "true"
        else
            write_test_result "Demo creates analysis reports" "false"
        fi
    else
        write_test_result "Demo command works" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

test_edge_cases() {
    write_test_header "Edge Cases and Boundary Testing"
    
    # Test 1: Empty directory
    local empty_dir="$TEST_DIR/empty_dir"
    mkdir -p "$empty_dir"
    
    run_bash_script "analyze \"$empty_dir\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Empty directory analysis handled" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Empty directory analysis handled" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 2: Directory with special characters
    local special_dir="$TEST_DIR/special dir with spaces & symbols!"
    mkdir -p "$special_dir"
    echo "test" > "$special_dir/test.txt"
    
    run_bash_script "analyze \"$special_dir\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Directory with special characters handled" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Directory with special characters handled" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
    
    # Test 3: Binary file
    local binary_file="$TEST_DIR/binary.bin"
    printf '\x00\x01\x02\x03\xFF\xFE\xFD' > "$binary_file"
    
    run_bash_script "scan \"$binary_file\"" 0
    if [[ $RESULT_EXIT_CODE -eq 0 ]]; then
        write_test_result "Binary file scan handled" "true" "Exit code: $RESULT_EXIT_CODE"
    else
        write_test_result "Binary file scan handled" "false" "Exit code: $RESULT_EXIT_CODE"
    fi
}

# Initialize test environment
initialize_test_environment() {
    log_info "Initializing test environment..."
    
    # Create test directory
    rm -rf "$TEST_DIR"
    mkdir -p "$TEST_DIR"
    
    # Initialize log file
    echo "Bash Interface Comprehensive Test Log - $(date)" > "$LOG_FILE"
    
    # Check if bash script exists
    if [[ ! -f "$BASH_SCRIPT" ]]; then
        log_error "Bash script not found: $BASH_SCRIPT"
        exit 1
    fi
    
    # Make script executable
    chmod +x "$BASH_SCRIPT" 2>/dev/null || true
    
    log_success "Test environment initialized."
}

# Write test summary
write_test_summary() {
    echo ""
    echo -e "${CYAN}$(printf '=%.0s' {1..80})${NC}"
    echo -e "${CYAN}TEST SUMMARY${NC}"
    echo -e "${CYAN}$(printf '=%.0s' {1..80})${NC}"
    
    echo -e "${WHITE}Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    echo -e "${YELLOW}Skipped: $SKIPPED_TESTS${NC}"
    
    local success_rate=0
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    fi
    
    if [[ $success_rate -ge 90 ]]; then
        echo -e "${GREEN}Success Rate: $success_rate%${NC}"
    elif [[ $success_rate -ge 75 ]]; then
        echo -e "${YELLOW}Success Rate: $success_rate%${NC}"
    else
        echo -e "${RED}Success Rate: $success_rate%${NC}"
    fi
    
    # Write summary to log
    echo "" >> "$LOG_FILE"
    echo "=== TEST SUMMARY ===" >> "$LOG_FILE"
    echo "Total: $TOTAL_TESTS, Passed: $PASSED_TESTS, Failed: $FAILED_TESTS, Success Rate: $success_rate%" >> "$LOG_FILE"
    
    if [[ $FAILED_TESTS -gt 0 ]]; then
        echo -e "\n${RED}Failed Tests:${NC}"
        for result in "${TEST_RESULTS[@]}"; do
            if [[ "$result" =~ FAIL ]]; then
                echo -e "  ${RED}- ${result#*FAIL: }${NC}"
            fi
        done
    fi
    
    echo -e "\n${CYAN}Detailed test log: $LOG_FILE${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n🎉 ${GREEN}All tests passed!${NC}"
        exit 0
    else
        echo -e "\n❌ ${RED}Some tests failed. Check the log for details.${NC}"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -s|--skip-build)
                SKIP_BUILD=true
                shift
                ;;
            -f|--filter)
                TEST_FILTER="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Main function
main() {
    parse_arguments "$@"
    
    echo -e "${CYAN}🚀 Bash Interface Comprehensive Test Suite${NC}"
    echo -e "${WHITE}Testing script: $BASH_SCRIPT${NC}"
    
    initialize_test_environment
    
    # Define test functions
    local test_functions=(
        "test_bash_script_exists"
        "test_basic_commands"
        "test_verbose_output"
        "test_dry_run_mode"
        "test_error_handling"
        "test_output_formats"
        "test_scan_function"
        "test_analyze_function"
        "test_export_function"
        "test_test_command"
        "test_demo_command"
        "test_edge_cases"
    )
    
    # Run test functions
    for test_function in "${test_functions[@]}"; do
        if [[ -n "$TEST_FILTER" && ! "$test_function" =~ $TEST_FILTER ]]; then
            log_debug "Skipping $test_function (filter: $TEST_FILTER)"
            continue
        fi
        
        if declare -f "$test_function" >/dev/null; then
            "$test_function"
        else
            log_error "Test function $test_function not found"
            ((FAILED_TESTS++))
        fi
    done
    
    write_test_summary
}

# Global variables for run_bash_script results
RESULT_EXIT_CODE=0
RESULT_OUTPUT=""
RESULT_ERROR=""

# Run main function
main "$@"
