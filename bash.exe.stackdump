Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE4AC70000 ntdll.dll
7FFE499C0000 KERNEL32.DLL
7FFE48080000 KERNELBASE.dll
7FFE49FC0000 USER32.dll
7FFE48710000 win32u.dll
7FFE49700000 GDI32.dll
7FFE47D60000 gdi32full.dll
7FFE47FB0000 msvcp_win.dll
7FFE47E90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE48920000 advapi32.dll
7FFE4A1C0000 msvcrt.dll
7FFE4A530000 sechost.dll
7FFE48050000 bcrypt.dll
7FFE49E10000 RPCRT4.dll
7FFE475E0000 CRYPTBASE.DLL
7FFE48740000 bcryptPrimitives.dll
7FFE4A180000 IMM32.DLL
