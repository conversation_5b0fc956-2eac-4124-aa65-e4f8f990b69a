﻿// Test file to verify AssetManager integration works correctly
// This tests the Bad Character Scanner with the new JSON asset loading

#[cfg(test)]
mod asset_integration_tests {
    use std::path::Path;

    // Mock test functions to simulate what the real CharacterAnalyzer should do
    
    #[test]
    fn test_asset_files_exist() {
        // Verify that our JSON asset files exist
        let bad_chars_path = Path::new("assets/Bad_Characters.json");
        let file_types_path = Path::new("assets/FileTypesSummary.json");
        
        assert!(bad_chars_path.exists(), "Bad_Characters.json should exist");
        assert!(file_types_path.exists(), "FileTypesSummary.json should exist");
        
        println!("✅ Asset files exist: Bad_Characters.json and FileTypesSummary.json");
    }
    
    #[test]
    fn test_json_parsing_structure() {
        // Test that we can parse the JSON files correctly
        let bad_chars_content = std::fs::read_to_string("assets/Bad_Characters.json")
            .expect("Failed to read Bad_Characters.json");
        let file_types_content = std::fs::read_to_string("assets/FileTypesSummary.json")
            .expect("Failed to read FileTypesSummary.json");
            
        // Basic JSON validation
        let bad_chars_json: serde_json::Value = serde_json::from_str(&bad_chars_content)
            .expect("Bad_Characters.json should be valid JSON");
        let file_types_json: serde_json::Value = serde_json::from_str(&file_types_content)
            .expect("FileTypesSummary.json should be valid JSON");
            
        println!("✅ JSON files parse correctly");
        println!("Bad Characters structure: {}", bad_chars_json.is_object());
        println!("File Types structure: {}", file_types_json.is_object());
    }
    
    #[test]
    fn test_character_severity_lookup() {
        // Test that we can find severity levels for specific characters
        let content = std::fs::read_to_string("assets/Bad_Characters.json")
            .expect("Failed to read Bad_Characters.json");
        let json: serde_json::Value = serde_json::from_str(&content)
            .expect("Invalid JSON");
            
        // Look for some specific problematic characters that should be in the JSON
        let test_chars = vec![
            '\u{202E}', // RIGHT-TO-LEFT OVERRIDE
            '\u{200B}', // ZERO WIDTH SPACE
            '\u{FEFF}', // BYTE ORDER MARK
        ];
        
        for ch in test_chars {
            let code = format!("U+{:04X}", ch as u32);
            println!("Testing character: {} ({})", ch, code);
            
            // This simulates what AssetManager.get_character_severity() should do
            if let Some(char_data) = json.get(&code) {
                if let Some(severity) = char_data.get("severity") {
                    println!("  ✅ Found severity: {}", severity);
                } else {
                    println!("  ⚠️  No severity field for {}", code);
                }
            } else {
                println!("  ⚠️  Character {} not found in Bad_Characters.json", code);
            }
        }
    }
    
    #[test]
    fn test_file_extension_loading() {
        // Test that we can load file extensions from FileTypesSummary.json
        let content = std::fs::read_to_string("assets/FileTypesSummary.json")
            .expect("Failed to read FileTypesSummary.json");
        let json: serde_json::Value = serde_json::from_str(&content)
            .expect("Invalid JSON");
            
        // Count how many file types we have
        if let Some(obj) = json.as_object() {
            println!("✅ Found {} file type categories", obj.len());
            
            // Look for common extensions
            let mut total_extensions = 0;
            for (category, data) in obj {
                if let Some(extensions) = data.get("extensions") {
                    if let Some(ext_array) = extensions.as_array() {
                        total_extensions += ext_array.len();
                        println!("  {}: {} extensions", category, ext_array.len());
                    }
                }
            }
            println!("✅ Total extensions available: {}", total_extensions);
            assert!(total_extensions > 50, "Should have many file extensions available");
        }
    }
}

// Integration test functions that simulate real usage
fn main() {
    println!("🧪 Testing Bad Character Scanner AssetManager Integration");
    println!("======================================================");
    
    // Test 1: Verify files exist
    println!("\n📁 Test 1: Asset File Existence");
    test_asset_files_exist();
    
    // Test 2: JSON parsing
    println!("\n📋 Test 2: JSON Structure Validation");
    test_json_parsing();
    
    // Test 3: Character severity lookup
    println!("\n🔍 Test 3: Character Severity Analysis");
    test_character_severity();
    
    // Test 4: File extension loading
    println!("\n📄 Test 4: File Extension Loading");
    test_file_extensions();
    
    println!("\n✅ AssetManager Integration Test Complete!");
}

fn test_asset_files_exist() {
    use std::path::Path;
    
    let bad_chars_path = Path::new("assets/Bad_Characters.json");
    let file_types_path = Path::new("assets/FileTypesSummary.json");
    
    if bad_chars_path.exists() && file_types_path.exists() {
        println!("  ✅ Both asset files exist");
    } else {
        println!("  ❌ Missing asset files");
        if !bad_chars_path.exists() { println!("    - Bad_Characters.json missing"); }
        if !file_types_path.exists() { println!("    - FileTypesSummary.json missing"); }
    }
}

fn test_json_parsing() {
    match std::fs::read_to_string("assets/Bad_Characters.json") {
        Ok(content) => {
            match serde_json::from_str::<serde_json::Value>(&content) {
                Ok(_) => println!("  ✅ Bad_Characters.json parses correctly"),
                Err(e) => println!("  ❌ Bad_Characters.json parse error: {}", e),
            }
        }
        Err(e) => println!("  ❌ Cannot read Bad_Characters.json: {}", e),
    }
    
    match std::fs::read_to_string("assets/FileTypesSummary.json") {
        Ok(content) => {
            match serde_json::from_str::<serde_json::Value>(&content) {
                Ok(_) => println!("  ✅ FileTypesSummary.json parses correctly"),
                Err(e) => println!("  ❌ FileTypesSummary.json parse error: {}", e),
            }
        }
        Err(e) => println!("  ❌ Cannot read FileTypesSummary.json: {}", e),
    }
}

fn test_character_severity() {
    let test_characters = vec![
        ('\u{202E}', "RIGHT-TO-LEFT OVERRIDE"),
        ('\u{200B}', "ZERO WIDTH SPACE"), 
        ('\u{FEFF}', "BYTE ORDER MARK"),
        ('\u{00A0}', "NO-BREAK SPACE"),
    ];
    
    match std::fs::read_to_string("assets/Bad_Characters.json") {
        Ok(content) => {
            match serde_json::from_str::<serde_json::Value>(&content) {
                Ok(json) => {
                    for (ch, name) in test_characters {
                        let code = format!("U+{:04X}", ch as u32);
                        if let Some(char_data) = json.get(&code) {
                            if let Some(severity) = char_data.get("severity").and_then(|s| s.as_str()) {
                                println!("  ✅ {}: {} -> severity: {}", code, name, severity);
                            } else {
                                println!("  ⚠️  {}: {} -> no severity data", code, name);
                            }
                        } else {
                            println!("  ⚠️  {}: {} -> not found in JSON", code, name);
                        }
                    }
                }
                Err(e) => println!("  ❌ JSON parse error: {}", e),
            }
        }
        Err(e) => println!("  ❌ File read error: {}", e),
    }
}

fn test_file_extensions() {
    match std::fs::read_to_string("assets/FileTypesSummary.json") {
        Ok(content) => {
            match serde_json::from_str::<serde_json::Value>(&content) {
                Ok(json) => {
                    if let Some(obj) = json.as_object() {
                        let mut total_extensions = 0;
                        let mut categories_with_extensions = 0;
                        
                        for (category, data) in obj {
                            if let Some(extensions) = data.get("extensions") {
                                if let Some(ext_array) = extensions.as_array() {
                                    total_extensions += ext_array.len();
                                    categories_with_extensions += 1;
                                    
                                    // Show first few extensions for verification
                                    if ext_array.len() > 0 {
                                        let first_few: Vec<String> = ext_array.iter()
                                            .take(3)
                                            .filter_map(|v| v.as_str())
                                            .map(|s| s.to_string())
                                            .collect();
                                        println!("  ✅ {}: {} extensions (e.g., {})", 
                                               category, ext_array.len(), first_few.join(", "));
                                    }
                                }
                            }
                        }
                        
                        println!("  📊 Summary: {} categories, {} total extensions", 
                               categories_with_extensions, total_extensions);
                        
                        if total_extensions > 50 {
                            println!("  ✅ Sufficient file type coverage");
                        } else {
                            println!("  ⚠️  Limited file type coverage");
                        }
                    }
                }
                Err(e) => println!("  ❌ JSON parse error: {}", e),
            }
        }
        Err(e) => println!("  ❌ File read error: {}", e),
    }
}
