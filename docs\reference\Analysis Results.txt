Analysis Results
Export TXT
Export JSON
3
Errors

10
Warnings

20
Total Findings

Directory: C:\Users\<USER>\Documents\Software\TauriV2_Checker\Tauri_checker_app\src-tauri\..\..\test_comprehensive

Summary: Found 20 potential issue(s) requiring attention.

Findings
📁 File:
C:\Users\<USER>\Documents\Software\TauriV2_Checker\Tauri_checker_app\src-tauri\..\..\test_comprehensive\src\legacy-api.ts
10 issues
🟡 Warning
📍 Line 2:0
Message:
Detected import from legacy '@tauri-apps/api/tauri' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 3:0
Message:
Detected import from legacy '@tauri-apps/api/dialog' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 4:0
Message:
Detected import from legacy '@tauri-apps/api/fs' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 5:0
Message:
Detected import from legacy '@tauri-apps/api/window' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 6:0
Message:
Detected import from legacy '@tauri-apps/api/event' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Info
📍 Line 7:0
Message:
Detected Tauri API import: '@tauri-apps/api/shell'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🟡 Info
📍 Line 8:0
Message:
Detected Tauri API import: '@tauri-apps/api/cli'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🟡 Info
📍 Line 9:0
Message:
Detected Tauri API import: '@tauri-apps/api/app'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🟡 Info
📍 Line 10:0
Message:
Detected Tauri API import: '@tauri-apps/api/os'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🔴 Error
📍 Line 13:0
Message:
Detected import from legacy '@tauri-apps/api' main package.
💡 Suggestion:
Replace with imports from specific v2 modules like '@tauri-apps/api/core', '@tauri-apps/api/window', etc.
📁 File:
C:\Users\<USER>\Documents\Software\TauriV2_Checker\Tauri_checker_app\src-tauri\..\..\test_comprehensive\src\main.js
10 issues
🟡 Warning
📍 Line 2:0
Message:
Detected import from legacy '@tauri-apps/api/tauri' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 3:0
Message:
Detected import from legacy '@tauri-apps/api/dialog' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 4:0
Message:
Detected import from legacy '@tauri-apps/api/fs' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 5:0
Message:
Detected import from legacy '@tauri-apps/api/window' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Warning
📍 Line 6:0
Message:
Detected import from legacy '@tauri-apps/api/event' module.
💡 Suggestion:
Verify this is the correct V2 module path and update to latest API patterns.
🟡 Info
📍 Line 7:0
Message:
Detected Tauri API import: '@tauri-apps/api/shell'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🟡 Info
📍 Line 8:0
Message:
Detected Tauri API import: '@tauri-apps/api/cli'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🟡 Info
📍 Line 9:0
Message:
Detected Tauri API import: '@tauri-apps/api/app'
💡 Suggestion:
Ensure this module is compatible with your Tauri version.
🔴 Error
📍 Line 12:0
Message:
Detected import from legacy '@tauri-apps/api' main package.
💡 Suggestion:
Replace with imports from specific v2 modules like '@tauri-apps/api/core', '@tauri-apps/api/window', etc.
🔴 Error
📍 Line 13:0
Message:
Detected import from legacy '@tauri-apps/api' main package.
💡 Suggestion:
Replace with imports from specific v2 modules like '@tauri-apps/api/core', '@tauri-apps/api/window', etc.