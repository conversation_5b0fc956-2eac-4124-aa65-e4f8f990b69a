# TICKET: TEST-PS-001
**Title**: Implement PowerShell CLI Interface Tests

## Status
- **Priority**: 🟡 High
- **Status**: 📋 Planned
- **Created**: 2025-06-28
- **Parent**: TEST-SUITE-001

## Description
Implement comprehensive tests for the PowerShell command-line interface, leveraging PowerShell's object-oriented nature and advanced features.

## Test Cases

### 1. Basic Cmdlet Tests
```powershell
# Test 1: Analyze text with cmdlet
"Hello‌World" | Analyze-BadCharacters

# Test 2: Analyze file
Analyze-BadCharacters -Path "test.txt"

# Test 3: Analyze with detailed output
Analyze-BadCharacters -Path "test.txt" -Detailed
```

### 2. Codebase Analysis Tests
```powershell
# Test 1: Scan project
Scan-Codebase -Path "C:\Projects\MyApp"

# Test 2: Scan with progress
Scan-Codebase -Path "C:\Projects\MyApp" -ShowProgress

# Test 3: Scan with filters
Scan-Codebase -Path "C:\Projects\MyApp" -Include @("*.js", "*.ts") -Exclude @("node_modules", "dist")
```

### 3. Cleaning Operations Tests
```powershell
# Test 1: Clean file
Clean-BadCharacters -InputPath "dirty.txt" -OutputPath "clean.txt"

# Test 2: Clean directory
Clean-Codebase -SourcePath "C:\dirty" -DestinationPath "C:\clean"

# Test 3: Clean with validation
Clean-BadCharacters -Path "file.txt" -ValidateOnly
```

### 4. PowerShell-Specific Features
```powershell
# Test 1: Object pipeline
Get-ChildItem *.txt | Analyze-BadCharacters | Where-Object { $_.SuspiciousCount -gt 0 }

# Test 2: Export results
Scan-Codebase -Path "." | Export-Csv -Path "results.csv"

# Test 3: Format output
Analyze-BadCharacters -Path "test.txt" | Format-Table -AutoSize

# Test 4: Error handling
try {
    Analyze-BadCharacters -Path "nonexistent.txt" -ErrorAction Stop
} catch {
    # Should catch properly
}
```

## PowerShell Integration Tests
- [ ] Module import/export
- [ ] Help documentation (`Get-Help`)
- [ ] Parameter validation
- [ ] Pipeline support
- [ ] Progress reporting
- [ ] Verbose/Debug output

## Expected Behaviors
- Proper PowerShell objects returned
- Support for common parameters
- Pipeline compatibility
- Progress bar for long operations
- Proper error records

---
*Last updated: 2025-06-28*