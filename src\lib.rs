use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use console_error_panic_hook::set_once as set_panic_hook;

// Component modules - keeping GUI simple and modular
mod components;
mod context;
mod icons;
mod utils;
use components::*;
use context::provide_analysis_context;
use icons::BCSLogo;
use utils::logging;

// Tauri command bindings
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

// Helper functions for Tauri commands  
#[allow(dead_code)]
async fn tauri_invoke_with_args<T: Serialize>(cmd: &str, args: &T) -> Result<JsValue, JsValue> {
    let args_js = serde_wasm_bindgen::to_value(args)?;
    Ok(invoke(cmd, args_js).await)
}

// Data structures matching backend functionality
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub id: String,
    pub timestamp: String, // Will be serialized from DateTime<Utc>
    pub input_text: String,
    pub text_hash: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub total_graphemes: usize,
    pub visual_width: usize,
    pub encoding_info: EncodingInfo,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub character_breakdown: std::collections::HashMap<String, usize>,
    pub script_breakdown: std::collections::HashMap<String, usize>,
    pub analysis_duration_ms: u64,
    pub confidence_score: f32,
    pub security_analysis: SecurityAnalysis,
    pub patterns_found: Vec<PatternMatch>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub unicode_block: String,
    pub category: String,
    pub codepoint: u32,
    pub utf8_bytes: Vec<u8>,
    pub utf16_units: Vec<u16>,
    pub is_suspicious: bool,
    pub suspicion_reasons: Vec<String>,
    pub recommendations: Vec<String>,
    pub visual_width: usize,
    pub is_combining: bool,
    pub is_emoji: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingInfo {
    pub detected_encoding: String,
    pub confidence: f64,
    pub is_valid_utf8: bool,
    pub bom_detected: Option<String>,
    pub line_endings: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub risk_level: String,
    pub phishing_indicators: Vec<String>,
    pub homograph_attacks: Vec<String>,
    pub steganography_potential: bool,
    pub script_mixing: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternMatch {
    pub pattern_name: String,
    pub description: String,
    pub start_position: usize,
    pub end_position: usize,
    pub matched_text: String,
    pub severity: String,
}

#[component]
pub fn App() -> impl IntoView {
    provide_meta_context();

    // Provide analysis context for sharing data between components
    let _analysis_context = provide_analysis_context();

    view! {
        <Stylesheet id="leptos" href="/pkg/laptos-tauri.css"/>
        <HomePage/>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    // Two main modes: text analysis and codebase analysis
    let (active_mode, set_active_mode) = create_signal("text".to_string());
    let (show_settings, set_show_settings) = create_signal(false);

    view! {
        <div class="min-h-screen" style="background: var(--bg-secondary);">
            <div class="container mx-auto px-6 py-8 max-w-6xl">
                // Professional header matching previous version
                <header class="mb-8">
                    <div class="text-center mb-8">
                        <div class="flex items-center justify-center gap-3 mb-4">
                            <BCSLogo class="icon-xl text-purple-600" />
                            <div>
                                <h1 class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                                    "Bad Character Scanner"
                                </h1>
                            </div>
                        </div>
                        <p class="text-lg text-gray-600 mb-2">
                            "Advanced Unicode Analysis & Security Scanner"
                        </p>
                        <p class="text-sm text-blue-600">
                            "with comprehensive text analysis capabilities - By John Shoy"
                        </p>
                    </div>

                    // Professional mode selection
                    <div class="flex justify-center mb-8">
                        <div class="card p-2 inline-flex">
                            <button
                                class=move || {
                                    format!(
                                        "px-8 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center gap-3 {}",
                                        if active_mode.get() == "text" {
                                            "bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg"
                                        } else {
                                            "text-gray-600 hover:text-purple-600 hover:bg-purple-50"
                                        }
                                    )
                                }
                                on:click=move |_| set_active_mode.set("text".to_string())
                            >
                                <svg class="btn-icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                "Text Analysis & Cleaning"
                            </button>
                            <button
                                class=move || {
                                    format!(
                                        "px-8 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center gap-3 {}",
                                        if active_mode.get() == "codebase" {
                                            "bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg"
                                        } else {
                                            "text-gray-600 hover:text-purple-600 hover:bg-purple-50"
                                        }
                                    )
                                }
                                on:click=move |_| set_active_mode.set("codebase".to_string())
                            >
                                <svg class="btn-icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                                "Code Base Analysis & Cleaning"
                            </button>
                        </div>
                    </div>

                    // Settings toggle (minimalist)
                    <div class="flex justify-end">
                        <button
                            class="btn-secondary text-sm px-4 py-2 flex items-center gap-2"
                            on:click=move |_| set_show_settings.update(|s| *s = !*s)
                        >
                            <svg class="btn-icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            "Settings"
                        </button>
                    </div>
                </header>

                // Professional settings panel
                <Show when=move || show_settings.get()>
                    <div class="mb-8 card p-6">
                        <div class="flex items-center gap-3 mb-6">
                            <div class="bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center" style="width: 24px; height: 24px;">
                                <svg class="icon-sm text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-800">"Application Settings"</h3>
                                <p class="text-sm text-gray-600">"Configure appearance and export options"</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <span class="font-medium text-gray-800">"Theme Mode"</span>
                                        <p class="text-sm text-gray-600">"Switch between light and dark themes"</p>
                                    </div>
                                    <ThemeToggle />
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="p-4 bg-gray-50 rounded-lg">
                                    <ExportComponent />
                                </div>
                            </div>
                        </div>
                    </div>
                </Show>

                // Main content area with professional styling
                <main class="card overflow-hidden">
                    {move || match active_mode.get().as_str() {
                        "text" => view! { <SimpleTextAnalyzer /> }.into_view(),
                        "codebase" => view! { <CodebaseComponent /> }.into_view(),
                        _ => view! { <SimpleTextAnalyzer /> }.into_view(),
                    }}
                </main>
            </div>
        </div>
    }
}

#[component]
fn NotFound() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
            <div class="text-center">
                <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">"404"</h1>
                <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">"Page not found"</p>
                <a
                    href="/"
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    "Go Home"
                </a>
            </div>
        </div>
    }
}

// Main entry point for WASM
#[wasm_bindgen(start)]
pub fn main() {
    set_panic_hook();

    // Initialize logging system
    logging::init_logging();
    // Mount to the specific app div instead of body
    let app_element = web_sys::window()
        .unwrap()
        .document()
        .unwrap()
        .get_element_by_id("app")
        .expect("Failed to find #app element");
    
    // Cast Element to HtmlElement
    let html_element = app_element
        .dyn_into::<web_sys::HtmlElement>()
        .expect("Failed to cast to HtmlElement");
    
    mount_to(html_element, || view! { <App /> })
}
