use leptos::*;
use gloo_timers::future::sleep;
use std::time::Duration;
use crate::components::progress_bar::ProgressBar;

#[component]
pub fn CodebaseComponent() -> impl IntoView {
    let (total_files, set_total_files) = create_signal(0);
    let (files_processed, set_files_processed) = create_signal(0);
    let (files_failed, set_files_failed) = create_signal(0);
    let (error_messages, set_error_messages) = create_signal(Vec::new());

    // Function to simulate scanner progress
    let run_scanner = move || {
        spawn_local(async move {
            // Example: total files is 100
            set_total_files.set(100);
            for i in 0..100 {
                // Simulate processing each file
                sleep(Duration::from_millis(100)).await;
                if i % 10 == 0 {  // Simulate a failure every 10 files
                    set_files_failed.update(|v| *v += 1);
                    set_error_messages.update(|v| v.push(format!("Failed to scan file {}", i)));
                } else {
                    set_files_processed.update(|v| *v += 1);
                }
            }
        });
    };

    // Start the scanner when a button is clicked
    let on_scan_click = move |_| {
        run_scanner();
    };

    let progress = create_memo(move |_| {
        if total_files.get() == 0 {
            0.0
        } else {
            (files_processed.get() + files_failed.get()) as f64 / total_files.get() as f64
        }
    });
    
    let (progress_signal, set_progress_signal) = create_signal(0.0);
    
    // Update progress signal when memo changes
    create_effect(move |_| {
        set_progress_signal.set(progress.get());
    });

    let success_percentage = create_memo(move |_| {
        if total_files.get() == 0 {
            0.0
        } else {
            files_processed.get() as f64 / total_files.get() as f64 * 100.0
        }
    });

    view! {
        <div>
            <h1>Scanner</h1>
            <button on:click=on_scan_click>Start Scan</button>
            <ProgressBar progress=progress_signal/>
            <div>
                <p>Overall Progress: {move || (progress.get() * 100.0).round()}%</p>
                <p>Successfully Scanned: {move || files_processed.get()} / {move || total_files.get()}</p>
                <p>Failed: {move || files_failed.get()}</p>
                <p>Success Rate: {move || success_percentage.get().round()}%</p>
            </div>
            <div>
                <h3>Error Messages:</h3>
                <ul class="error-list">
                    {move || error_messages.get().iter().map(|msg| view! { <li>{msg}</li> }).collect::<Vec<_>>()}
                </ul>
            </div>
        </div>
    }
}
