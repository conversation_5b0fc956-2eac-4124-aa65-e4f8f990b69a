use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

pub async fn tauri_invoke<T: Serialize, R: for<'de> Deserialize<'de>>(
    cmd: &str,
    args: &T,
) -> Result<R, String> {
    let args_js = serde_wasm_bindgen::to_value(args).map_err(|e| e.to_string())?;
    let result_js = invoke(cmd, args_js).await;

    if result_js.is_null() || result_js.is_undefined() {
        serde_wasm_bindgen::from_value(JsValue::NULL).map_err(|e| e.to_string())
    } else {
        serde_wasm_bindgen::from_value(result_js).map_err(|e| e.to_string())
    }
}
