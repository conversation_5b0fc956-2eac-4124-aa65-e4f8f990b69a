# CODEBASE-1 - Clean and Analyze Code Base Feature

**Status:** 🟡 In Progress  
**Priority:** High  
**Created:** 2025-05-28  
**Updated:** 2025-05-28  
**Assigned To:** @dev  
**Related Issues:** CLEANUP-1

## Description

Implement a "Clean and Analyze Code Base" feature that allows users to:

1. Select a folder containing their code base
2. Analyze all files for suspicious characters
3. Create a complete cleaned copy of the code base
4. Provide detailed reports on what was cleaned
5. Preserve original code base (read-only analysis)

## Requirements

### Frontend Features
- [ ] New "Clean and Analyze Code Base" button next to "Analyze Text"
- [ ] Dedicated `/codebase` route for code base processing
- [ ] Folder selection interface using Tauri file system APIs
- [ ] Progress tracking for multi-file processing
- [ ] Results dashboard showing:
  - [ ] Files processed count
  - [ ] Issues found per file
  - [ ] Total characters cleaned
  - [ ] Before/after comparison stats
- [ ] Download link for cleaned code base (ZIP)

### Backend Features
- [ ] Tauri command for folder selection dialog
- [ ] Recursive file scanning with configurable filters
- [ ] File type detection and processing
- [ ] Batch cleaning with progress reporting
- [ ] ZIP archive creation for cleaned code base
- [ ] Detailed logging and reporting

### Technical Requirements
- [ ] Support for common file extensions (.rs, .js, .ts, .py, .java, .cpp, etc.)
- [ ] Configurable file size limits
- [ ] Memory-efficient processing for large code bases
- [ ] Error handling for unreadable files
- [ ] Preserve directory structure in cleaned copy

## User Flow

1. User clicks "Clean and Analyze Code Base"
2. Navigate to `/codebase` page
3. User clicks "Select Folder" → Folder selection dialog
4. User configures cleaning options and file filters
5. User clicks "Start Analysis" → Processing begins
6. Real-time progress display with file-by-file status
7. Results dashboard shows summary and detailed reports
8. User can download cleaned code base as ZIP

## Success Criteria

- [ ] Folder selection works correctly
- [ ] Progress tracking is accurate and responsive
- [ ] Cleaned code base preserves directory structure
- [ ] Original code base remains untouched
- [ ] Reports are comprehensive and useful
- [ ] Performance is acceptable for typical code bases (< 10,000 files)

## Notes

This feature extends the text cleaning capabilities to entire code bases, making it useful for:
- Security audits of inherited code
- Cleaning up code before deployment
- Preparing code for security reviews
- Educational purposes (showing hidden characters in code)
