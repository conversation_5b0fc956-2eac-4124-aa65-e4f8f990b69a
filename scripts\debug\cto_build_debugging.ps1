# CTO-Level Build Debugging Script
# By <PERSON> - 2025
# 
# This script provides comprehensive analysis of build failures across the entire codebase
# to identify root causes and dependencies that could cause UI bugs like the Advanced Security Analysis issue

Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "    CTO-LEVEL BUILD DEBUGGING ANALYSIS" -ForegroundColor Cyan  
Write-Host "    By <PERSON> Shoy - 2025" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host ""

# Function to check file dependencies
function Test-FileDependencies {
    param([string]$FilePath, [string]$Description)
    
    Write-Host "🔍 Checking: $Description" -ForegroundColor Yellow
    if (Test-Path $FilePath) {
        Write-Host "   ✅ EXISTS: $FilePath" -ForegroundColor Green
    } else {
        Write-Host "   ❌ MISSING: $FilePath - CRITICAL DEPENDENCY FAILURE" -ForegroundColor Red
    }
}

# Function to analyze specific code issues
function Test-CodeIssue {
    param([string]$FilePath, [string]$Pattern, [string]$Issue)
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        if ($content -match $Pattern) {
            Write-Host "   🚨 FOUND ISSUE: $Issue in $FilePath" -ForegroundColor Red
            return $true
        }
    }
    return $false
}

Write-Host "1. CRITICAL FILE DEPENDENCIES CHECK" -ForegroundColor Magenta
Write-Host "   Checking files that could cause Advanced Security Analysis UI bugs..." -ForegroundColor Gray

# Critical files for Advanced Security Analysis
Test-FileDependencies "src/components/codebase/ui/results.rs" "Frontend Analysis Results Parser"
Test-FileDependencies "src/components/codebase/ui/summary.rs" "UI Summary Component"  
Test-FileDependencies "src/components/codebase/types.rs" "Analysis Data Types"
Test-FileDependencies "src-tauri/src/commands/comprehensive_analysis.rs" "Backend Analysis Command"
Test-FileDependencies "assets/BadCharacterPatterns.json" "Core Pattern Database"
Test-FileDependencies "assets/AdvancedAI_Patterns.json" "AI Detection Patterns"

Write-Host ""
Write-Host "2. LEPTOS SIGNAL API ISSUES ANALYSIS" -ForegroundColor Magenta

# Check for old signal syntax issues
$signalIssues = 0
$filesToCheck = @(
    "src/components/drag_and_drop.rs",
    "src/components/settings_button.rs", 
    "src/components/theme.rs"
)

foreach ($file in $filesToCheck) {
    if (Test-CodeIssue $file "set_.*\(" "Old Leptos Signal Syntax") { $signalIssues++ }
    if (Test-CodeIssue $file "is_.*\(\)" "Old Signal Reading Syntax") { $signalIssues++ }
}

Write-Host "   📊 Total Signal API Issues Found: $signalIssues" -ForegroundColor $(if ($signalIssues -gt 0) { "Red" } else { "Green" })

Write-Host ""
Write-Host "3. WEB-SYS API COMPATIBILITY CHECK" -ForegroundColor Magenta

if (Test-CodeIssue "src/components/theme.rs" "MediaQueryListEvent" "Obsolete web-sys API") {
    Write-Host "   🔧 SOLUTION: Replace MediaQueryListEvent with MediaQueryList" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. JSON PARSING DEPENDENCY TRACE" -ForegroundColor Magenta
Write-Host "   Tracing the Advanced Security Analysis data flow..." -ForegroundColor Gray

# Check if our JSON parsing fix is in place
$correctParsing = $false
if (Test-Path "src/components/codebase/ui/results.rs") {
    $content = Get-Content "src/components/codebase/ui/results.rs" -Raw
    if ($content -match "results.*risk_analysis.*overall_score") {
        Write-Host "   ✅ CORRECT JSON PARSING: results.risk_analysis.overall_score path found" -ForegroundColor Green
        $correctParsing = $true
    } else {
        Write-Host "   ❌ INCORRECT JSON PARSING: Still using flat field names" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "5. BUILD COMPILATION ERROR ANALYSIS" -ForegroundColor Magenta

# Run a quick compilation check
Write-Host "   Running compilation check..." -ForegroundColor Gray
$buildOutput = & cargo check 2>&1
$errorCount = ($buildOutput | Where-Object { $_ -match "error\[" }).Count
$warningCount = ($buildOutput | Where-Object { $_ -match "warning:" }).Count

Write-Host "   📊 Compilation Errors: $errorCount" -ForegroundColor $(if ($errorCount -gt 0) { "Red" } else { "Green" })
Write-Host "   📊 Compilation Warnings: $warningCount" -ForegroundColor $(if ($warningCount -gt 10) { "Red" } elseif ($warningCount -gt 0) { "Yellow" } else { "Green" })

Write-Host ""
Write-Host "6. DEPENDENCY VERSION ANALYSIS" -ForegroundColor Magenta

if (Test-Path "Cargo.toml") {
    $cargoContent = Get-Content "Cargo.toml" -Raw
    if ($cargoContent -match 'leptos.*=.*"([^"]+)"') {
        Write-Host "   📦 Leptos Version: $($matches[1])" -ForegroundColor Cyan
    }
    if ($cargoContent -match 'web-sys.*=.*"([^"]+)"') {
        Write-Host "   📦 web-sys Version: $($matches[1])" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "7. ASSET INTEGRITY CHECK" -ForegroundColor Magenta

$assetFiles = @(
    "assets/BadCharacterPatterns.json",
    "assets/Advanced_AI_Patterns.json", 
    "assets/HomoglyphMap.json",
    "assets/UnicodeCategories.json"
)

$assetIssues = 0
foreach ($asset in $assetFiles) {
    if (Test-Path $asset) {
        try {
            $jsonContent = Get-Content $asset -Raw | ConvertFrom-Json
            Write-Host "   ✅ VALID JSON: $asset" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ INVALID JSON: $asset - $($_.Exception.Message)" -ForegroundColor Red
            $assetIssues++
        }
    } else {
        Write-Host "   ❌ MISSING: $asset" -ForegroundColor Red
        $assetIssues++
    }
}

Write-Host ""
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "    EXECUTIVE SUMMARY FOR CTO" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan

if ($correctParsing) {
    Write-Host "✅ SECURITY FIX: Advanced Security Analysis JSON parsing is corrected" -ForegroundColor Green
} else {
    Write-Host "❌ SECURITY RISK: Advanced Security Analysis still has incorrect JSON parsing" -ForegroundColor Red
}

if ($signalIssues -gt 0) {
    Write-Host "❌ BUILD BLOCKER: $signalIssues Leptos signal API compatibility issues" -ForegroundColor Red
    Write-Host "   📋 ACTION REQUIRED: Update signal syntax to Leptos 0.6+ format" -ForegroundColor Yellow
}

if ($assetIssues -gt 0) {
    Write-Host "❌ CRITICAL: $assetIssues asset integrity issues detected" -ForegroundColor Red
    Write-Host "   📋 ACTION REQUIRED: Fix asset files before deployment" -ForegroundColor Yellow
}

if ($errorCount -gt 0) {
    Write-Host "❌ BUILD STATUS: $errorCount compilation errors prevent testing" -ForegroundColor Red
    Write-Host "   📋 PRIORITY: Fix compilation before security testing" -ForegroundColor Yellow
} else {
    Write-Host "✅ BUILD STATUS: Clean compilation" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 RECOMMENDED ACTIONS:" -ForegroundColor Yellow
Write-Host "   1. Fix Leptos signal syntax (HIGH PRIORITY)" -ForegroundColor White
Write-Host "   2. Update web-sys MediaQueryListEvent usage (MEDIUM)" -ForegroundColor White  
Write-Host "   3. Test Advanced Security Analysis fix (CRITICAL)" -ForegroundColor White
Write-Host "   4. Verify asset file integrity (HIGH)" -ForegroundColor White
Write-Host ""
Write-Host "🚨 SECURITY IMPACT: Until compilation issues are fixed," -ForegroundColor Red
Write-Host "   the Advanced Security Analysis UI bug fix cannot be properly tested!" -ForegroundColor Red
Write-Host ""
