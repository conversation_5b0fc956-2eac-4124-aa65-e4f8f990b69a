#!/usr/bin/env powershell
<#
.SYNOPSIS
    Simplified Mega Testing Suite for Bad Character Scanner
    
.DESCRIPTION
    This script runs tests across all interfaces:
    - Bash CLI
    - PowerShell CLI
    - GUI (via automation)
    
.PARAMETER TestType
    Type of test to run: All, Bash, PowerShell, GUI, Quick
    
.EXAMPLE
    .\mega-test-suite-simple.ps1 -TestType Quick
#>

param(
    [ValidateSet("All", "Bash", "PowerShell", "GUI", "Quick")]
    [string]$TestType = "Quick",
    
    [string]$OutputPath = "./test-results",
    
    [switch]$SkipGUI
)

# Script configuration
$script:TestStartTime = Get-Date
$script:ProjectRoot = Split-Path -Parent $PSScriptRoot
$script:TestDataPath = Join-Path $OutputPath "test-data"
$script:ResultsPath = Join-Path $OutputPath "results"
$script:ReportsPath = Join-Path $OutputPath "reports"
$script:TotalTests = 0
$script:PassedTests = 0
$script:FailedTests = 0

# Helper Functions
function Write-TestLog {
    param(
        [string]$Message,
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "Success" { Write-Host "[$timestamp] [OK] $Message" -ForegroundColor Green }
        "Error"   { Write-Host "[$timestamp] [FAIL] $Message" -ForegroundColor Red }
        "Warning" { Write-Host "[$timestamp] [WARN] $Message" -ForegroundColor Yellow }
        "Info"    { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Cyan }
        "Debug"   { Write-Host "[$timestamp] [DEBUG] $Message" -ForegroundColor Gray }
    }
    
    # Log to file
    $logFile = Join-Path $script:ResultsPath "test-log.txt"
    "$timestamp [$Level] $Message" | Add-Content $logFile -ErrorAction SilentlyContinue
}

function Initialize-TestEnvironment {
    Write-TestLog "Initializing test environment..." "Info"
    
    # Create directories
    @($OutputPath, $script:TestDataPath, $script:ResultsPath, $script:ReportsPath) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
        }
    }
    
    # Generate test data
    Generate-TestData
    
    Write-TestLog "Test environment initialized" "Success"
}

function Generate-TestData {
    Write-TestLog "Generating test data..." "Info"
    
    # Test file with known bad characters - using Unicode escapes
    $badCharsContent = @"
Normal text here.
Zero-width characters: Hello$([char]0x200C)World$([char]0x200D)Test
Bidirectional override: User $([char]0x202E)Oliver$([char]0x202C) sent
Homoglyphs: Apole (Greek A) vs Apple (Latin A)
Control characters: $([char]0)$([char]1)$([char]2)
Mixed scripts: Hello мир 世界
"@
    
    $badCharsContent | Set-Content (Join-Path $script:TestDataPath "bad-chars.txt") -Encoding UTF8
    
    # Clean file
    "This is clean text without any suspicious characters." | 
        Set-Content (Join-Path $script:TestDataPath "clean.txt") -Encoding UTF8
    
    # JavaScript file with issues
    $jsContent = @"
// Normal comment
const message = "Hello$([char]0x200C)World"; // Zero-width character
const user = "$([char]0x202E)Oliver$([char]0x202C)"; // RTL override
console.log(message);
"@
    $jsContent | Set-Content (Join-Path $script:TestDataPath "bad-code.js") -Encoding UTF8
    
    # Create test project structure
    $testProject = Join-Path $script:TestDataPath "test-project"
    New-Item -ItemType Directory -Path $testProject -Force | Out-Null
    
    # Add various files to test project
    "clean code" | Set-Content (Join-Path $testProject "clean.js")
    $badCharsContent | Set-Content (Join-Path $testProject "dirty.txt")
    
    Write-TestLog "Test data generated" "Success"
}

function Test-Command {
    param(
        [string]$Name,
        [scriptblock]$Command,
        [scriptblock]$Validation,
        [string]$Interface = "Unknown"
    )
    
    $script:TotalTests++
    Write-TestLog "Running test: $Name ($Interface)" "Info"
    
    try {
        $result = & $Command 2>&1
        $success = $false
        
        if ($Validation) {
            $success = & $Validation -Result $result
        } else {
            $success = $LASTEXITCODE -eq 0
        }
        
        if ($success) {
            $script:PassedTests++
            Write-TestLog "PASS: $Name" "Success"
            return @{
                Test = $Name
                Interface = $Interface
                Status = "Passed"
                Result = $result
                Timestamp = Get-Date
            }
        } else {
            $script:FailedTests++
            Write-TestLog "FAIL: $Name" "Error"
            Write-TestLog "Result: $result" "Debug"
            return @{
                Test = $Name
                Interface = $Interface
                Status = "Failed"
                Result = $result
                Error = "Validation failed"
                Timestamp = Get-Date
            }
        }
    } catch {
        $script:FailedTests++
        Write-TestLog "ERROR: $Name - $_" "Error"
        return @{
            Test = $Name
            Interface = $Interface
            Status = "Error"
            Error = $_.ToString()
            Timestamp = Get-Date
        }
    }
}

# Bash Interface Tests
function Test-BashInterface {
    Write-TestLog "`nStarting Bash Interface Tests" "Info"
    
    $bashTests = @()
    
    # Test 1: Check if cargo CLI exists
    $bashTests += Test-Command -Name "Bash: Check analyzer_cli binary" -Interface "Bash" -Command {
        Set-Location (Join-Path $script:ProjectRoot "src-tauri")
        cargo build --bin analyzer_cli --release 2>&1
        $LASTEXITCODE
    } -Validation {
        param($Result)
        $Result -eq 0
    }
    
    # Test 2: Basic text analysis
    $bashTests += Test-Command -Name "Bash: Analyze text with bad characters" -Interface "Bash" -Command {
        $testFile = Join-Path $script:TestDataPath "bad-chars.txt"
        Set-Location (Join-Path $script:ProjectRoot "src-tauri")
        $output = & cargo run --bin analyzer_cli -- analyze $testFile 2>&1
        $output -join "`n"
    } -Validation {
        param($Result)
        $Result -match "suspicious" -or $Result -match "Zero-width" -or $Result -match "found"
    }
    
    # Test 3: Clean text file
    $bashTests += Test-Command -Name "Bash: Clean bad characters from file" -Interface "Bash" -Command {
        $inputFile = Join-Path $script:TestDataPath "bad-chars.txt"
        $outputFile = Join-Path $script:ResultsPath "bash-cleaned.txt"
        Set-Location (Join-Path $script:ProjectRoot "src-tauri")
        & cargo run --bin analyzer_cli -- clean $inputFile -o $outputFile 2>&1
        Test-Path $outputFile
    } -Validation {
        param($Result)
        $Result -eq $true
    }
    
    return $bashTests
}

# PowerShell Interface Tests  
function Test-PowerShellInterface {
    Write-TestLog "`nStarting PowerShell Interface Tests" "Info"
    
    $psTests = @()
    
    # Check if PowerShell scripts exist
    $analyzeScript = Join-Path $script:ProjectRoot "scripts\analyze-text.ps1"
    $cleanScript = Join-Path $script:ProjectRoot "scripts\clean-file.ps1"
    $scanScript = Join-Path $script:ProjectRoot "scripts\scan-codebase.ps1"
    
    # Test 1: Check scripts exist
    $psTests += Test-Command -Name "PowerShell: Check scripts exist" -Interface "PowerShell" -Command {
        $scripts = @($analyzeScript, $cleanScript, $scanScript)
        $missing = $scripts | Where-Object { -not (Test-Path $_) }
        if ($missing) {
            Write-Output "Missing scripts: $($missing -join ', ')"
            return $false
        }
        return $true
    } -Validation {
        param($Result)
        $Result -eq $true
    }
    
    # Test 2: Run basic PowerShell command
    $psTests += Test-Command -Name "PowerShell: Test basic functionality" -Interface "PowerShell" -Command {
        $testFile = Join-Path $script:TestDataPath "bad-chars.txt"
        if (Test-Path $testFile) {
            $content = Get-Content $testFile -Raw
            if ($content -match '\u200C|\u200D|\u202E|\u202C') {
                return "Found bad characters"
            }
        }
        return "No bad characters found"
    } -Validation {
        param($Result)
        $Result -eq "Found bad characters"
    }
    
    return $psTests
}

# GUI Tests (Placeholder)
function Test-GUIInterface {
    Write-TestLog "`nStarting GUI Tests" "Info"
    
    if ($SkipGUI) {
        Write-TestLog "GUI tests skipped (SkipGUI flag set)" "Warning"
        return @()
    }
    
    $guiTests = @()
    
    # Placeholder test
    $guiTests += Test-Command -Name "GUI: Placeholder test" -Interface "GUI" -Command {
        Write-Output "GUI testing requires Selenium/WebDriver setup"
        return $true
    } -Validation {
        param($Result)
        $true
    }
    
    return $guiTests
}

# Cross-Validation Tests
function Test-CrossValidation {
    Write-TestLog "`nStarting Cross-Validation Tests" "Info"
    
    $crossTests = @()
    
    # Test file consistency
    $crossTests += Test-Command -Name "Cross: Verify test data integrity" -Interface "Cross" -Command {
        $testFile = Join-Path $script:TestDataPath "bad-chars.txt"
        if (Test-Path $testFile) {
            $content = Get-Content $testFile -Raw
            $hasZeroWidth = $content -match '\u200C|\u200D'
            $hasRTL = $content -match '\u202E|\u202C'
            return ($hasZeroWidth -and $hasRTL)
        }
        return $false
    } -Validation {
        param($Result)
        $Result -eq $true
    }
    
    return $crossTests
}

# Report Generation
function Generate-TestReport {
    param(
        [array]$TestResults
    )
    
    Write-TestLog "`nGenerating test report..." "Info"
    
    # Generate JSON report
    $jsonReport = @{
        TestRun = @{
            StartTime = $script:TestStartTime
            EndTime = Get-Date
            Duration = ((Get-Date) - $script:TestStartTime).ToString()
            Machine = $env:COMPUTERNAME
            User = $env:USERNAME
        }
        Summary = @{
            TotalTests = $script:TotalTests
            Passed = $script:PassedTests
            Failed = $script:FailedTests
            PassRate = if ($script:TotalTests -gt 0) { 
                [Math]::Round(($script:PassedTests / $script:TotalTests) * 100, 2) 
            } else { 0 }
        }
        Results = $TestResults
    }
    
    $jsonPath = Join-Path $script:ReportsPath "test-results.json"
    $jsonReport | ConvertTo-Json -Depth 10 | Set-Content $jsonPath
    
    # Generate simple text report
    $textReport = @"
=====================================
Bad Character Scanner - Test Report
=====================================

Test Run Information:
- Start Time: $($script:TestStartTime)
- End Time: $(Get-Date)
- Duration: $((Get-Date) - $script:TestStartTime)
- Machine: $env:COMPUTERNAME
- User: $env:USERNAME

Summary:
- Total Tests: $script:TotalTests
- Passed: $script:PassedTests
- Failed: $script:FailedTests
- Pass Rate: $(if ($script:TotalTests -gt 0) { [Math]::Round(($script:PassedTests / $script:TotalTests) * 100, 2) } else { 0 })%

Detailed Results:
"@
    
    foreach ($test in $TestResults) {
        $textReport += "`n[$($test.Interface)] $($test.Test): $($test.Status)"
        if ($test.Error) {
            $textReport += "`n  Error: $($test.Error)"
        }
    }
    
    $textPath = Join-Path $script:ReportsPath "test-report.txt"
    $textReport | Set-Content $textPath
    
    Write-TestLog "Reports generated:" "Success"
    Write-TestLog "  JSON: $jsonPath" "Info"
    Write-TestLog "  Text: $textPath" "Info"
}

# Main Execution
try {
    Write-Host @"

====================================
Bad Character Scanner - Test Suite
====================================

"@ -ForegroundColor Cyan

    # Initialize
    Initialize-TestEnvironment
    
    $allResults = @()
    
    # Run tests based on type
    switch ($TestType) {
        "All" {
            $allResults += Test-BashInterface
            $allResults += Test-PowerShellInterface
            $allResults += Test-GUIInterface
            $allResults += Test-CrossValidation
        }
        "Bash" {
            $allResults += Test-BashInterface
        }
        "PowerShell" {
            $allResults += Test-PowerShellInterface
        }
        "GUI" {
            $allResults += Test-GUIInterface
        }
        "Quick" {
            # Quick subset of tests
            Write-TestLog "Running quick test subset..." "Info"
            $allResults += Test-BashInterface
            $allResults += Test-PowerShellInterface
            $allResults += Test-CrossValidation
        }
    }
    
    # Generate report
    Generate-TestReport -TestResults $allResults
    
    # Summary
    Write-Host "`n=====================================" -ForegroundColor Cyan
    Write-Host "           TEST SUMMARY              " -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan
    
    Write-Host "`nTotal Tests: $script:TotalTests" -ForegroundColor White
    Write-Host "Passed: $script:PassedTests" -ForegroundColor Green
    Write-Host "Failed: $script:FailedTests" -ForegroundColor Red
    
    $passRate = if ($script:TotalTests -gt 0) { 
        [Math]::Round(($script:PassedTests / $script:TotalTests) * 100, 2) 
    } else { 0 }
    
    Write-Host "`nPass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } elseif ($passRate -ge 60) { "Yellow" } else { "Red" })
    
    $duration = (Get-Date) - $script:TestStartTime
    Write-Host "Duration: $([Math]::Round($duration.TotalMinutes, 2)) minutes" -ForegroundColor Gray
    
    if ($script:FailedTests -eq 0) {
        Write-Host "`nAll tests passed!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "`nSome tests failed. Check the report for details." -ForegroundColor Yellow
        exit 1
    }
    
} catch {
    Write-Host "`nFatal error during test execution:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host $_.ScriptStackTrace -ForegroundColor DarkGray
    exit 2
}