use web_sys;
use js_sys;

/// Check if we're running in Tauri context
pub fn is_tauri_available() -> bool {
    web_sys::window()
        .and_then(|w| js_sys::Reflect::get(&w, &"__TAURI__".into()).ok())
        .map(|v| !v.is_undefined() && !v.is_null())
        .unwrap_or(false)
}

/// Demo analysis result for web context
pub fn get_demo_result() -> &'static str {
    r#"{
        "analysis_time_ms": 1250,
        "total_files": 45,
        "files_with_issues": 2,
        "total_suspicious_chars": 12,
        "health_score": 97.3,
        "file_details": [
            {
                "file_path": "demo/example.js",
                "file_size": 2048,
                "file_type": "js",
                "suspicious_characters": 5,
                "total_characters": 2048,
                "issues": [
                    "Suspicious character '‍' at position 156",
                    "Suspicious character '؍' at position 892"
                ],
                "analysis_status": "success",
                "encoding": "UTF-8"
            }
        ]
    }"#
}