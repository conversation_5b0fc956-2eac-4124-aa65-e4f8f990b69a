# 🤝 Contributing to Bad Character Scanner

**Welcome! We're excited to have you contribute to the Bad Character Scanner project.**

**This is a priveat project**
---

## 🚀 **Quick Start for Contributors**

### **✅ Prerequisites** (5 minutes)
```powershell
# Verify you have these installed
rustc --version          # Rust 1.75+
node --version           # Node.js 18+
cargo tauri --version    # Tauri CLI v2.5+
```

### **🛠️ Setup** (10 minutes)
```powershell
# 1. Fork and clone
git clone <your-fork-url>
cd Leptos_TaurieV2_BCS

# 2. Install dependencies
npm install
cargo update

# 3. Start development
cargo tauri dev

# 4. Verify everything works
cargo test
```

**✅ Ready to contribute!** Follow the guidelines below.

---

## 🎯 **Contribution Types**

### **🐛 Bug Reports**
| Information | Required |
|-------------|----------|
| **Clear title** | ✅ Required |
| **Steps to reproduce** | ✅ Required |
| **Expected vs actual behavior** | ✅ Required |
| **Environment details** | ✅ Required |
| **Error messages/screenshots** | ✅ Helpful |

### **✨ Feature Requests**
| Information | Required |
|-------------|----------|
| **Feature description** | ✅ Required |
| **Use case/value** | ✅ Required |
| **Implementation ideas** | 📝 Optional |
| **Examples/references** | 📝 Optional |

### **🔧 Code Contributions**
| Step | Action |
|------|--------|
| **1** | Fork repository |
| **2** | Create feature branch (`feat/amazing-feature`) |
| **3** | Make changes following code standards |
| **4** | Run tests (`cargo test`) |
| **5** | Commit using conventional commits |
| **6** | Push and create Pull Request |

---

## 📝 **Code Standards**

### **Commit Message Format**
```bash
# Format: type(scope): description
feat(analysis): add Unicode normalization detection
fix(ui): resolve export button alignment issue
docs(readme): update installation instructions
test(scanner): add comprehensive Unicode tests
refactor(core): improve analysis pipeline performance
```

### **Code Style Guidelines**
| Aspect | Requirement | Tool |
|--------|-------------|------|
| **Formatting** | Rust standard | `rustfmt` |
| **Linting** | All warnings fixed | `clippy` |
| **Documentation** | Public APIs documented | `cargo doc` |
| **Testing** | New features tested | `cargo test` |
| **Dependencies** | Justified and minimal | Code review |

### **File Organization**
```
src/
├── lib.rs              # Main frontend (production)
├── lib_simple.rs       # Simple demo/testing
├── lib_new.rs          # Experimental (empty)
├── components/         # Reusable UI components
├── pages/             # Page components
└── utils/             # Utility functions

src-tauri/
├── src/
│   ├── main.rs        # Tauri app entry
│   ├── commands.rs    # Tauri commands
│   ├── analysis/      # Analysis modules
│   └── file_handler.rs # File operations
└── Cargo.toml         # Backend dependencies
```

---

## 🧪 **Development Workflow**

### **Daily Development**
```powershell
# Start development server (with hot reload)
cargo tauri dev

# Run tests continuously
cargo watch -x test

# Format code
cargo fmt

# Check lints
cargo clippy

# Generate documentation
cargo doc --open
```

### **Before Submitting PR**
```powershell
# 1. Run full test suite
cargo test --all

# 2. Check formatting
cargo fmt --check

# 3. Fix all lints
cargo clippy --all-targets --all-features -- -D warnings

# 4. Update documentation if needed
cargo doc

# 5. Test production build
cargo tauri build
```

---

## 🛡️ **Code Quality Requirements**

### **✅ Quality Gates**
| Gate | Requirement | Command |
|------|-------------|---------|
| **Tests** | All tests pass | `cargo test` |
| **Formatting** | Code formatted | `cargo fmt --check` |
| **Lints** | No warnings | `cargo clippy` |
| **Documentation** | APIs documented | `cargo doc` |
| **Build** | Production build works | `cargo tauri build` |

### **📊 Performance Standards**
- **Startup time**: < 2 seconds
- **File processing**: > 1MB/second
- **Memory usage**: < 100MB typical
- **Binary size**: < 20MB release build

---

## 🏗️ **Architecture Guidelines**

### **Frontend (Leptos)**
- **Reactive components** using Leptos signals
- **Type-safe** communication with backend
- **Responsive design** with Tailwind CSS
- **Error handling** with user feedback
- **Performance** optimized for WASM

### **Backend (Tauri)**
- **Secure APIs** with input validation
- **Memory-safe** Rust implementation
- **Async operations** for file I/O
- **Cross-platform** compatibility
- **Minimal system access** (security)

---

## 🔒 **Security Guidelines**

### **Input Validation**
```rust
// Always validate inputs
fn analyze_text(input: String) -> Result<Analysis, Error> {
    if input.len() > MAX_INPUT_SIZE {
        return Err(Error::InputTooLarge);
    }
    // ... continue with analysis
}
```

### **File Operations**
```rust
// Use secure path handling
use std::path::PathBuf;

fn safe_file_read(path: PathBuf) -> Result<String, Error> {
    // Validate path is within allowed directories
    // Check file size limits
    // Use secure reading methods
}
```

### **Tauri Commands**
```rust
// Secure command implementation
#[tauri::command]
async fn secure_command(input: ValidatedInput) -> Result<Output, Error> {
    // Input validation
    // Authorization checks
    // Safe processing
    // Secure response
}
```

---

## 🧪 **Testing Standards**

### **Test Categories**
| Type | Coverage | Location |
|------|----------|----------|
| **Unit Tests** | All modules | `src/` with `#[cfg(test)]` |
| **Integration Tests** | API endpoints | `tests/` directory |
| **E2E Tests** | User workflows | `tests/e2e/` directory |
| **Performance Tests** | Critical paths | `benches/` directory |

### **Test Examples**
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_unicode_analysis() {
        let input = "test\u{200B}string"; // Zero-width space
        let result = analyze_unicode(&input).unwrap();
        assert!(result.has_invisible_chars);
        assert_eq!(result.invisible_count, 1);
    }

    #[tokio::test]
    async fn test_file_processing() {
        let file_path = "test_data/sample.txt";
        let result = process_file(file_path).await.unwrap();
        assert!(!result.is_empty());
    }
}
```

---

## 📚 **Documentation Standards**

### **Code Documentation**
```rust
/// Analyzes Unicode text for security threats
/// 
/// # Arguments
/// * `text` - The input text to analyze
/// * `options` - Analysis configuration options
/// 
/// # Returns
/// * `Ok(AnalysisResult)` - Successful analysis
/// * `Err(AnalysisError)` - Analysis failed
/// 
/// # Examples
/// ```
/// let result = analyze_unicode("Hello\u{200B}World", &options)?;
/// assert!(result.has_threats);
/// ```
pub fn analyze_unicode(text: &str, options: &AnalysisOptions) -> Result<AnalysisResult, AnalysisError> {
    // Implementation
}
```

### **README Updates**
- Update feature lists when adding functionality
- Include usage examples for new features
- Update installation instructions if dependencies change
- Add troubleshooting for common issues

---

## 🤖 **Development Tools**

### **VS Code Setup** (Recommended)
```json
// .vscode/extensions.json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "tauri-apps.tauri-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
```

### **Useful Commands**
```powershell
# Development shortcuts
cargo install cargo-watch  # Auto-rebuild on changes
cargo install cargo-edit    # Easy dependency management
cargo install trunk        # Frontend bundling

# Project commands
cargo add <dependency>      # Add new dependency
cargo rm <dependency>       # Remove dependency
cargo update               # Update all dependencies
cargo outdated             # Check for outdated dependencies
```

---

## 🎯 **Release Process** (for Maintainers)

### **Version Bumping**
1. Update version in `Cargo.toml`
2. Update version in `src-tauri/Cargo.toml`
3. Update version in `src-tauri/tauri.conf.json`
4. Update `CHANGELOG.md`
5. Create version tag: `git tag v0.3.2`

### **Release Checklist**
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] Version numbers consistent
- [ ] Production build tested
- [ ] Cross-platform compatibility verified

---

## 🆘 **Getting Help**

### **Resources**
- **[📚 Documentation](../README.md)** - Start here
- **[⚡ Quick Navigation](../QUICK_NAVIGATION.md)** - Find any document
- **[🔧 Developer Guide](../DEVELOPER_GUIDE.md)** - Detailed development info
- **[🐛 Quick Fix Guide](../guides/QUICK_FIX_GUIDE.md)** - Common problems

### **Communication**
- **Issues** - For bugs and feature requests
- **Discussions** - For questions and ideas
- **Pull Requests** - For code contributions
- **Documentation** - For detailed guidance

---

## 📄 **License**

By contributing, you agree that your contributions will be licensed under the **MIT License**.

---

*Thank you for contributing to the Bad Character Scanner! Your efforts help make Unicode text analysis safer and more accessible for everyone.*
