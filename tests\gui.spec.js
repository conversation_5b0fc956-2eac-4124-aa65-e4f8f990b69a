import { test, expect } from '@playwright/test';

test.describe('GUI Functionality', () => {
  test('should load the main page and display the header', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('h1')).toHaveText('Bad Character Scanner');
  });

  test('should switch to Code Base Fixing Tools and trigger analysis', async ({ page }) => {
    await page.goto('/');
    await page.click('button:has-text("Code Base Fixing Tools")');
    await page.fill('input[type="text"]', './src');
    await page.click('button:has-text("Analyze Codebase")');

    // Wait for the results to appear
    await page.waitForSelector('.result-container');

    // Check for a specific result, for example, the presence of a file in the analysis
    await expect(page.locator('.result-container')).toContainText('lib.rs');
  });
});
