use leptos::*;
use wasm_bindgen::prelude::*;
use js_sys;
use crate::context::use_analysis_context;

// Export request structure is now handled inline with serde_json::json!

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[component]
pub fn ExportComponent() -> impl IntoView {
    let (export_format, set_export_format) = create_signal("json".to_string());
    let (export_result, set_export_result) = create_signal(None::<String>);
    let (is_exporting, set_is_exporting) = create_signal(false);

    // Get analysis context to check for available data
    let analysis_context = use_analysis_context();

    let export_data = move |_| {
        set_is_exporting.set(true);
        let format = export_format.get();

        // Check if we have analysis data to export
        let analysis_data = analysis_context.get_analysis_data();
        let analysis_type = analysis_context.get_analysis_type();

        if analysis_data.is_none() {
            set_export_result.set(Some("No analysis data available to export. Please run an analysis first.".to_string()));
            set_is_exporting.set(false);
            return;
        }

        spawn_local(async move {
            // Generate filename based on current timestamp
            let timestamp = js_sys::Date::new_0().to_iso_string();
            let filename = format!("analysis_export_{}.{}",
                timestamp.as_string().unwrap_or_default().replace(":", "-").replace(".", "-"),
                if format == "json" { "json" } else if format == "csv" { "csv" } else { "html" }
            );

            // Choose the appropriate export command based on analysis type
            let (command, args) = if analysis_type.as_deref() == Some("codebase") {
                // Use codebase-specific export command - parse the JSON string back to object
                let analysis_json: serde_json::Value = serde_json::from_str(&analysis_data.unwrap())
                    .map_err(|e| format!("Failed to parse analysis data: {}", e))
                    .unwrap_or_default();

                let args = serde_json::json!({
                    "analysis_data": analysis_json,
                    "format_type": format
                });
                ("export_codebase_report", args)
            } else {
                // Use general export command - backend expects format, filename, analysis_data
                let args = serde_json::json!({
                    "format": format,
                    "filename": filename,
                    "analysis_data": analysis_data
                });
                ("export_analysis", args)
            };
            match serde_wasm_bindgen::to_value(&args) {
                Ok(args_js) => {
                    let result = invoke(command, args_js).await;
                    match serde_wasm_bindgen::from_value::<String>(result) {
                        Ok(path) => set_export_result.set(Some(format!("Exported to: {}", path))),
                        Err(e) => set_export_result.set(Some(format!("Error: {:?}", e))),
                    }
                }
                Err(e) => set_export_result.set(Some(format!("Serialization error: {:?}", e))),
            }
            set_is_exporting.set(false);
        });
    };

    view! {
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">"Export Format"</label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input
                            type="radio"
                            name="export-format"
                            value="json"
                            class="mr-2"
                            checked=move || export_format.get() == "json"
                            on:change=move |_| set_export_format.set("json".to_string())
                        />
                        <span>"JSON Format"</span>
                    </label>
                    <label class="flex items-center">
                        <input
                            type="radio"
                            name="export-format"
                            value="csv"
                            class="mr-2"
                            checked=move || export_format.get() == "csv"
                            on:change=move |_| set_export_format.set("csv".to_string())
                        />
                        <span>"CSV Format"</span>
                    </label>
                    <label class="flex items-center">
                        <input
                            type="radio"
                            name="export-format"
                            value="html"
                            class="mr-2"
                            checked=move || export_format.get() == "html"
                            on:change=move |_| set_export_format.set("html".to_string())
                        />
                        <span>"HTML Report"</span>
                    </label>
                </div>
            </div>

            <button
                class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                on:click=export_data
                disabled=move || is_exporting.get()
            >
                {move || if is_exporting.get() { "Exporting..." } else { "Export Analysis" }}
            </button>

            {move || {
                export_result
                    .get()
                    .map(|result| {
                        view! {
                            <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                <h3 class="text-lg font-semibold mb-2">"Export Result"</h3>
                                <p class="text-sm">{result}</p>
                            </div>
                        }
                    })
            }}
        </div>
    }
}