# Documentation Structure Guide

*Hey there! Lost in all the docs? No worries - this is your friendly map to everything in this documentation folder.*

---

## Start Here (New Contributor Path)

```
📚 docs/
├── 👋 README.md                           ← Start here (project overview)
├── 🚀 ONBOARDING_NEW.md                   ← Then this (setup guide)
├── 💻 DEVELOPER_GUIDE.md                  ← Development basics
└── 🛡️ ASSET_FOLDER_CRITICAL_GUIDE.md     ← CRITICAL: Asset system guide
```

---

## For Technical Leaders (CTO/Architect Path)

```
📊 Executive Level:
├── 🏛️ CTO_HOLISTIC_SYSTEM_OVERVIEW.md    ← System architecture & risks
├── 🛡️ ASSET_FOLDER_CRITICAL_GUIDE.md     ← Asset dependencies (CRITICAL)
└── 📈 FEATURES.md                         ← Feature overview

🔧 Technical Deep Dive:
├── 💻 DEVELOPER_GUIDE.md                  ← Technical implementation
├── 🎨 MODERN_GUI_IMPLEMENTATION_GUIDE.md  ← UI architecture
└── 📋 technical_reference/                ← Detailed specs
```

---

## 👤 **For End Users** (Just Want to Use the App)

```
📖 User Guides:
├── 📋 usermanuals/USER_MANUAL.md          ← How to use the app
├── ⚡ usermanuals/QUICK_REFERENCE_CARD.md ← Quick commands
├── 💻 usermanuals/CLI_FIXED_USAGE.md      ← Command line usage
└── 🔧 guides/                             ← Step-by-step tutorials
```

---

## 🔄 **For Maintainers** (Keeping Things Running)

```
🛠️ Maintenance:
├── 🤝 contributing/CONTRIBUTING.md        ← How to contribute
├── 🔒 contributing/SECURITY.md            ← Security practices
├── 📂 project-management/                 ← Project planning docs
└── 🗃️ archive/                           ← Historical documentation
```

---

## 📁 **Folder Breakdown** (What's Where)

### **📚 Root Level**
- **README.md** - Project introduction and quick start
- **QUICK_NAVIGATION.md** - Fast access to common tasks
- **FEATURES.md** - What this app can do
- **ONBOARDING_NEW.md** - Modern onboarding for new contributors

### **🎨 guides/**
User-friendly tutorials for specific tasks:
```
guides/
├── 📝 scanning-files.md                  ← How to scan files
├── 🧹 cleaning-text.md                   ← How to clean text
├── 💾 exporting-results.md               ← How to export results
└── 🔧 troubleshooting.md                 ← When things go wrong
```

### **📋 technical_reference/**
Detailed technical documentation:
```
technical_reference/
├── 🔌 api-reference.md                   ← API documentation
├── 🧮 algorithms.md                      ← Detection algorithms
├── 📊 data-formats.md                    ← File format specifications
└── 🏗️ architecture.md                   ← System architecture
```

### **📖 usermanuals/**
End-user documentation:
```
usermanuals/
├── 📋 USER_MANUAL.md                     ← Complete user guide
├── ⚡ QUICK_REFERENCE_CARD.md            ← Cheat sheet
├── 💻 CLI_FIXED_USAGE.md                 ← Command line guide
└── 🆘 FAQ.md                             ← Frequently asked questions
```

### **🤝 contributing/**
For people who want to help:
```
contributing/
├── 🤝 CONTRIBUTING.md                    ← How to contribute
├── 🔒 SECURITY.md                        ← Security guidelines
├── 📝 CODING_STANDARDS.md                ← Code style guide
└── 🧪 TESTING.md                         ← Testing procedures
```

### **📂 project-management/**
Planning and project tracking:
```
project-management/
├── 🎯 roadmap.md                         ← Future plans
├── 📊 milestones.md                      ← Release planning
├── 🐛 known-issues.md                    ← Current bugs
└── 💡 feature-requests.md                ← Proposed features
```

### **🗃️ archive/**
Historical and reference documentation:
```
archive/
├── 📜 legacy-documentation/              ← Old docs (for reference)
├── 🔄 migration-guides/                  ← Version upgrade guides
└── 📈 release-notes/                     ← Change history
```

---

## Common Scenarios (What You're Probably Looking For)

### **"I'm new here, where do I start?"**
1. 👋 `README.md` - Get the big picture
2. 🚀 `ONBOARDING_NEW.md` - Set up your environment
3. 💻 `DEVELOPER_GUIDE.md` - Start coding
4. 🛡️ `ASSET_FOLDER_CRITICAL_GUIDE.md` - Understand the critical parts

### **"I need to use this app"**
1. 📋 `usermanuals/USER_MANUAL.md` - Complete guide
2. ⚡ `usermanuals/QUICK_REFERENCE_CARD.md` - Quick commands
3. 🔧 `guides/` - Step-by-step tutorials

### **"Something's broken, help!"**
1. 🔧 `guides/troubleshooting.md` - Common fixes
2. 🛡️ `ASSET_FOLDER_CRITICAL_GUIDE.md` - Critical system guide
3. 🐛 `project-management/known-issues.md` - Known problems
4. 🤝 `contributing/CONTRIBUTING.md` - Report issues

### **"I want to understand the system architecture"**
1. 🏛️ `CTO_HOLISTIC_SYSTEM_OVERVIEW.md` - Executive overview
2. 🛡️ `ASSET_FOLDER_CRITICAL_GUIDE.md` - Critical dependencies
3. 🏗️ `technical_reference/architecture.md` - Detailed architecture
4. 🎨 `MODERN_GUI_IMPLEMENTATION_GUIDE.md` - UI architecture

### **"I want to contribute"**
1. 🤝 `contributing/CONTRIBUTING.md` - Contribution guide
2. 📝 `contributing/CODING_STANDARDS.md` - Code standards
3. 🧪 `contributing/TESTING.md` - Testing procedures
4. 🔒 `contributing/SECURITY.md` - Security practices

---

## Quick Navigation Tips

- **🚨 Red flags** in documentation = Critical, read immediately
- **🟡 Yellow flags** = Important but not critical
- **✅ Green flags** = Nice to know, reference material
- **📋 Checklists** = Step-by-step procedures
- **💡 Tips** = Pro tips and best practices

---

## 📝 **Documentation Standards**

We try to keep our docs:
- **🎯 Clear** - No jargon unless necessary
- **🚀 Actionable** - Tell you what to DO, not just what IS
- **🔍 Searchable** - Good headings and keywords
- **🆕 Up-to-date** - We update docs when code changes
- **🤝 Human** - Written by humans, for humans

---

*💡 **Pro Tip:** Bookmark this page! Use it whenever you're lost in the documentation maze. And if something's confusing or missing, let us know - we're always improving our docs.*