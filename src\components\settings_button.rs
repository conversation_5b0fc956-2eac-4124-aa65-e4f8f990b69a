use leptos::*;
use crate::components::{Icon, IconSize};

/// Apple-inspired settings button with floating panel
/// Provides tabbed settings interface with consistent design
/// By J.Shoy - 2025
#[component]
pub fn SettingsButton() -> impl IntoView {
    let (is_open, set_is_open) = create_signal(false);
    let (active_tab, set_active_tab) = create_signal("general");

    let toggle_settings = move |_| {
        set_is_open.update(|open| *open = !*open);
    };

    let close_settings = move |_| {
        set_is_open.set(false);
    };

    let select_tab = move |tab: &'static str| {
        move |_| set_active_tab.set(tab)
    };

    view! {
        <div class="fixed top-4 right-4 z-50">
            // Apple-inspired settings button
            <button
                on:click=toggle_settings
                class="w-12 h-12 bg-white/95 backdrop-blur-md border border-gray-200 dark:border-gray-700 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group hover:bg-gray-50 dark:bg-gray-800/95 dark:hover:bg-gray-700"
                class:rotate-12=move || is_open.get()
                aria-label="Settings"
            >
                <div class="transition-transform duration-300" class:rotate-90=move || is_open.get()>
                    <Icon
                        name="settings"
                        size=IconSize::LG
                        class="text-gray-600 group-hover:text-gray-800 dark:text-gray-300 dark:group-hover:text-gray-100 transition-colors duration-300"
                    />
                </div>
            </button>

            // Apple-inspired settings panel overlay
            <Show when=move || is_open.get()>
                <div
                    class="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 transition-opacity duration-300"
                    on:click=close_settings
                ></div>

                <div
                    class="absolute top-16 right-0 w-96 bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden z-50 transform transition-all duration-300 origin-top-right"
                    class:scale-100=move || is_open.get()
                    class:scale-95=move || !is_open.get()
                    class:opacity-100=move || is_open.get()
                    class:opacity-0=move || !is_open.get()
                >

                    // Apple-inspired header
                    <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                        <div class="flex items-center justify-between">
                            <h2 class="text-title-3 font-semibold">"Settings"</h2>
                            <button
                                on:click=close_settings
                                class="p-2 hover:bg-white/20 rounded-full transition-all duration-200 active:scale-95"
                                aria-label="Close settings"
                            >
                                <Icon name="close" size=IconSize::SM class="text-white" />
                            </button>
                        </div>
                    </div>

                    // Apple-inspired tab navigation
                    <div class="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                        <button
                            on:click=select_tab("general")
                            class="flex-1 px-4 py-3 text-subheadline font-medium transition-all duration-200 relative min-h-[44px] flex items-center justify-center"
                            class:text-blue-600=move || active_tab.get() == "general"
                            class:bg-white=move || active_tab.get() == "general"
                            class:dark:bg-gray-900=move || active_tab.get() == "general"
                            class:text-gray-500=move || active_tab.get() != "general"
                            class:dark:text-gray-400=move || active_tab.get() != "general"
                            class:hover:text-gray-700=move || active_tab.get() != "general"
                            class:dark:hover:text-gray-200=move || active_tab.get() != "general"
                        >
                            "General"
                            <Show when=move || active_tab.get() == "general">
                                <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-t-full"></div>
                            </Show>
                        </button>
                        <button
                            on:click=select_tab("analysis")
                            class="flex-1 px-4 py-3 text-subheadline font-medium transition-all duration-200 relative min-h-[44px] flex items-center justify-center"
                            class:text-blue-600=move || active_tab.get() == "analysis"
                            class:bg-white=move || active_tab.get() == "analysis"
                            class:dark:bg-gray-900=move || active_tab.get() == "analysis"
                            class:text-gray-500=move || active_tab.get() != "analysis"
                            class:dark:text-gray-400=move || active_tab.get() != "analysis"
                            class:hover:text-gray-700=move || active_tab.get() != "analysis"
                            class:dark:hover:text-gray-200=move || active_tab.get() != "analysis"
                        >
                            "Analysis"
                            <Show when=move || active_tab.get() == "analysis">
                                <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-t-full"></div>
                            </Show>
                        </button>
                        <button
                            on:click=select_tab("export")
                            class="flex-1 px-4 py-3 text-subheadline font-medium transition-all duration-200 relative min-h-[44px] flex items-center justify-center"
                            class:text-blue-600=move || active_tab.get() == "export"
                            class:bg-white=move || active_tab.get() == "export"
                            class:dark:bg-gray-900=move || active_tab.get() == "export"
                            class:text-gray-500=move || active_tab.get() != "export"
                            class:dark:text-gray-400=move || active_tab.get() != "export"
                            class:hover:text-gray-700=move || active_tab.get() != "export"
                            class:dark:hover:text-gray-200=move || active_tab.get() != "export"
                        >
                            "Export"
                            <Show when=move || active_tab.get() == "export">
                                <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-t-full"></div>
                            </Show>
                        </button>
                    </div>

                    // Tab content
                    <div class="p-6 max-h-96 overflow-y-auto">
                        // General Tab
                        <Show when=move || active_tab.get() == "general">
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        "Theme"
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>"System"</option>
                                        <option>"Light"</option>
                                        <option>"Dark"</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Auto-save analysis results"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            checked
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Show detailed character info"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Enable sound notifications"
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </Show>

                        // Analysis Tab
                        <Show when=move || active_tab.get() == "analysis">
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        "Detection Sensitivity"
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>"Low"</option>
                                        <option selected>"Medium"</option>
                                        <option>"High"</option>
                                        <option>"Paranoid"</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            checked
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Detect zero-width characters"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            checked
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Analyze Unicode normalization"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Deep script analysis"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        "Max File Size (MB)"
                                    </label>
                                    <input
                                        type="number"
                                        value="10"
                                        min="1"
                                        max="100"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            </div>
                        </Show>

                        // Export Tab
                        <Show when=move || active_tab.get() == "export">
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        "Default Export Format"
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>"JSON"</option>
                                        <option>"CSV"</option>
                                        <option>"XML"</option>
                                        <option>"HTML Report"</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            checked
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Include metadata"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span class="text-sm text-gray-700">
                                            "Compress large exports"
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        "Export Directory"
                                    </label>
                                    <div class="flex space-x-2">
                                        <input
                                            type="text"
                                            placeholder="/path/to/exports"
                                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                            "Browse"
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </Show>
                    </div>

                    // Footer with action buttons
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
                        <button class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                            "Reset to defaults"
                        </button>
                        <div class="flex space-x-3">
                            <button
                                on:click=close_settings
                                class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                "Cancel"
                            </button>
                            <button class="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                "Save Settings"
                            </button>
                        </div>
                    </div>
                </div>
            </Show>
        </div>
    }
}
