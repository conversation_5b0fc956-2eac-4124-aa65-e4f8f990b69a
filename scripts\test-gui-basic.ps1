<#
.SYNOPSIS
    Basic GUI functionality test
    
.DESCRIPTION
    Tests if the GUI can be started and is accessible
#>

Write-Host "=== Basic GUI Test ===" -ForegroundColor Cyan
Write-Host ""

# Check if the app is already running
Write-Host "[1] Checking if GUI is already running..." -ForegroundColor Yellow
$guiUrl = "http://localhost:1420"
try {
    $response = Invoke-WebRequest -Uri $guiUrl -TimeoutSec 2 -ErrorAction Stop
    Write-Host "[OK] GUI is already running at $guiUrl" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "[INFO] GUI is not currently running" -ForegroundColor Yellow
}

# Try to start the dev server
Write-Host ""
Write-Host "[2] Starting GUI development server..." -ForegroundColor Yellow
Write-Host "This will take a moment..." -ForegroundColor Gray

$projectRoot = Split-Path -Parent $PSScriptRoot
$logFile = "gui-test-log.txt"

# Start the dev server in background
$devProcess = Start-Process -FilePath "npm" -Argument<PERSON>ist "run", "dev" -WorkingDirectory $projectRoot -PassThru -WindowStyle Hidden -RedirectStandardError $logFile

# Wait for server to start
Write-Host "Waiting for server to start..." -ForegroundColor Gray
$attempts = 0
$maxAttempts = 30

while ($attempts -lt $maxAttempts) {
    Start-Sleep -Seconds 2
    $attempts++
    
    try {
        $response = Invoke-WebRequest -Uri $guiUrl -TimeoutSec 2 -ErrorAction Stop
        Write-Host "[OK] GUI started successfully!" -ForegroundColor Green
        Write-Host "GUI is accessible at: $guiUrl" -ForegroundColor Cyan
        
        # Test basic endpoints
        Write-Host ""
        Write-Host "[3] Testing GUI endpoints..." -ForegroundColor Yellow
        
        # Check if we get HTML response
        if ($response.Content -match "<html" -or $response.Content -match "<!DOCTYPE") {
            Write-Host "[OK] GUI returns valid HTML" -ForegroundColor Green
        } else {
            Write-Host "[WARN] GUI response doesn't look like HTML" -ForegroundColor Yellow
        }
        
        # Check status code
        if ($response.StatusCode -eq 200) {
            Write-Host "[OK] GUI returns HTTP 200 OK" -ForegroundColor Green
        } else {
            Write-Host "[WARN] GUI returns status code: $($response.StatusCode)" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "GUI is running! You can:" -ForegroundColor White
        Write-Host "  1. Open your browser to $guiUrl" -ForegroundColor Gray
        Write-Host "  2. Test the interface manually" -ForegroundColor Gray
        Write-Host "  3. Press Ctrl+C in the terminal to stop the server" -ForegroundColor Gray
        
        # Keep process info
        Write-Host ""
        Write-Host "Process ID: $($devProcess.Id)" -ForegroundColor Gray
        Write-Host "To stop later: Stop-Process -Id $($devProcess.Id)" -ForegroundColor Gray
        
        exit 0
    } catch {
        Write-Host "." -NoNewline
    }
}

Write-Host ""
Write-Host "[FAIL] GUI failed to start after $maxAttempts attempts" -ForegroundColor Red

# Check log file for errors
if (Test-Path $logFile) {
    Write-Host ""
    Write-Host "Error log:" -ForegroundColor Red
    Get-Content $logFile | Select-Object -Last 20
    Remove-Item $logFile -Force
}

# Clean up process
if ($devProcess -and -not $devProcess.HasExited) {
    Stop-Process -Id $devProcess.Id -Force
}

exit 1