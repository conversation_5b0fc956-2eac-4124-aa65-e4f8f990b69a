use leptos::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct CleanCopySettings {
    pub remove_zero_width: bool,
    pub remove_bidi_overrides: bool,
    pub remove_homoglyphs: bool,
    pub remove_control_chars: bool,
    pub replace_with_ascii: bool,
    pub preserve_emojis: bool,
    pub create_backup: bool,
    pub output_directory: String,
}

impl Default for CleanCopySettings {
    fn default() -> Self {
        Self {
            remove_zero_width: true,
            remove_bidi_overrides: true,
            remove_homoglyphs: true,
            remove_control_chars: true,
            replace_with_ascii: true,
            preserve_emojis: false,
            create_backup: true,
            output_directory: String::from("cleaned_codebase"),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CleanCopyProgress {
    pub current_file: String,
    pub files_processed: usize,
    pub total_files: usize,
    pub issues_fixed: usize,
    pub percentage: f32,
}

#[component]
pub fn CleanCopyComponent() -> impl IntoView {
    let (settings, set_settings) = create_signal(CleanCopySettings::default());
    let (is_processing, set_is_processing) = create_signal(false);
    let (progress, _set_progress) = create_signal(None::<CleanCopyProgress>);
    let (result_message, set_result_message) = create_signal(None::<String>);
    let (selected_path, set_selected_path) = create_signal(String::new());

    let select_codebase = move |_| {
        spawn_local(async move {
            match crate::tauri_invoke_with_args("select_folder", &serde_json::json!({})).await {
                Ok(result) => {
                    if let Ok(path) = serde_wasm_bindgen::from_value::<String>(result) {
                        set_selected_path.set(path);
                    }
                }
                Err(e) => {
                    set_result_message.set(Some(format!("Failed to select folder: {:?}", e)));
                }
            }
        });
    };

    let create_clean_copy = move |_| {
        let path = selected_path.get();
        if path.is_empty() {
            set_result_message.set(Some("Please select a codebase first".to_string()));
            return;
        }

        set_is_processing.set(true);
        set_result_message.set(None);
        
        spawn_local(async move {
            let args = serde_json::json!({
                "path": path,
                "settings": settings.get()
            });

            match crate::tauri_invoke_with_args("create_clean_codebase_copy", &args).await {
                Ok(result) => {
                    set_is_processing.set(false);
                    if let Ok(summary) = serde_wasm_bindgen::from_value::<String>(result) {
                        set_result_message.set(Some(format!("✅ Clean copy created successfully!\n{}", summary)));
                    }
                }
                Err(e) => {
                    set_is_processing.set(false);
                    set_result_message.set(Some(format!("❌ Failed to create clean copy: {:?}", e)));
                }
            }
        });
    };

    view! {
        <div class="p-6 max-w-4xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
                    "Create Clean Codebase Copy"
                </h2>
                
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    "This feature creates a cleaned copy of your codebase with all bad characters removed and homoglyphs replaced with ASCII equivalents. Your original codebase remains untouched."
                </p>

                // Codebase Selection
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        "Select Codebase to Clean"
                    </label>
                    <div class="flex gap-4">
                        <input
                            type="text"
                            value=move || selected_path.get()
                            readonly=true
                            placeholder="No codebase selected"
                            class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                        <button
                            on:click=select_codebase
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            "Browse"
                        </button>
                    </div>
                </div>

                // Settings
                <div class="mb-6 space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        "Cleaning Options"
                    </h3>
                    
                    <SettingToggle
                        label="Remove Zero-Width Characters"
                        checked=move || settings.get().remove_zero_width
                        on_change=move |checked| {
                            set_settings.update(|s| s.remove_zero_width = checked);
                        }
                    />
                    
                    <SettingToggle
                        label="Remove Bidirectional Overrides"
                        checked=move || settings.get().remove_bidi_overrides
                        on_change=move |checked| {
                            set_settings.update(|s| s.remove_bidi_overrides = checked);
                        }
                    />
                    
                    <SettingToggle
                        label="Replace Homoglyphs with ASCII"
                        checked=move || settings.get().replace_with_ascii
                        on_change=move |checked| {
                            set_settings.update(|s| s.replace_with_ascii = checked);
                        }
                    />
                    
                    <SettingToggle
                        label="Remove Control Characters"
                        checked=move || settings.get().remove_control_chars
                        on_change=move |checked| {
                            set_settings.update(|s| s.remove_control_chars = checked);
                        }
                    />
                    
                    <SettingToggle
                        label="Preserve Emojis"
                        checked=move || settings.get().preserve_emojis
                        on_change=move |checked| {
                            set_settings.update(|s| s.preserve_emojis = checked);
                        }
                    />
                    
                    <SettingToggle
                        label="Create Backup First"
                        checked=move || settings.get().create_backup
                        on_change=move |checked| {
                            set_settings.update(|s| s.create_backup = checked);
                        }
                    />
                </div>

                // Output Directory
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        "Output Directory Name"
                    </label>
                    <input
                        type="text"
                        value=move || settings.get().output_directory
                        on:input=move |ev| {
                            let value = event_target_value(&ev);
                            set_settings.update(|s| s.output_directory = value);
                        }
                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                </div>

                // Progress Display
                <Show when=move || progress.get().is_some()>
                    <div class="mb-6">
                        {move || progress.get().map(|p| view! {
                            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {format!("Processing: {}", p.current_file)}
                                    </span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {format!("{}/{} files", p.files_processed, p.total_files)}
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                    <div
                                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style=move || format!("width: {}%", p.percentage)
                                    />
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                    {format!("{} issues fixed", p.issues_fixed)}
                                </p>
                            </div>
                        })}
                    </div>
                </Show>

                // Result Message
                <Show when=move || result_message.get().is_some()>
                    <div class=move || {
                        let msg = result_message.get().unwrap_or_default();
                        if msg.starts_with("✅") {
                            "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6"
                        } else if msg.starts_with("❌") {
                            "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"
                        } else {
                            "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"
                        }
                    }>
                        <pre class="text-sm whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                            {move || result_message.get().unwrap_or_default()}
                        </pre>
                    </div>
                </Show>

                // Action Button
                <div class="flex justify-end">
                    <button
                        on:click=create_clean_copy
                        disabled=move || is_processing.get() || selected_path.get().is_empty()
                        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                    >
                        <Show
                            when=move || is_processing.get()
                            fallback=|| view! {
                                <>
                                    <span class="text-xl">"🧹"</span>
                                    "Create Clean Copy"
                                </>
                            }
                        >
                            <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"/>
                            "Processing..."
                        </Show>
                    </button>
                </div>
            </div>
        </div>
    }
}

#[component]
fn SettingToggle<F>(
    label: &'static str,
    checked: impl Fn() -> bool + 'static,
    on_change: F,
) -> impl IntoView
where
    F: Fn(bool) + 'static,
{
    view! {
        <label class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</span>
            <input
                type="checkbox"
                checked=checked
                on:change=move |ev| {
                    let checked = event_target_checked(&ev);
                    on_change(checked);
                }
                class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
        </label>
    }
}