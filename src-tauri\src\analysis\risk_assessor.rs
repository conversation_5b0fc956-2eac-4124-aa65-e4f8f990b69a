use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use super::{HomoglyphThreat, PatternThreat, SecurityThreat, ThreatSeverity};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub overall_risk_score: f32,
    pub threat_breakdown: ThreatBreakdown,
    pub file_risk_scores: HashMap<String, f32>,
    pub recommendations: Vec<SecurityRecommendation>,
    pub compliance_status: ComplianceStatus,
    pub attack_vectors: Vec<AttackVector>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ThreatBreakdown {
    pub homoglyph_threats: usize,
    pub pattern_threats: usize,
    pub security_threats: usize,
    pub critical_issues: usize,
    pub high_issues: usize,
    pub medium_issues: usize,
    pub low_issues: usize,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityRecommendation {
    pub priority: RecommendationPriority,
    pub category: RecommendationCategory,
    pub title: String,
    pub description: String,
    pub action_items: Vec<String>,
    pub impact: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Immediate,  // Fix within 24 hours
    Urgent,     // Fix within 1 week
    High,       // Fix within 1 month
    Medium,     // Fix within 3 months
    Low,        // Fix when convenient
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationCategory {
    CodeInjection,
    InputValidation,
    Authentication,
    Cryptography,
    DataProtection,
    AccessControl,
    Logging,
    Configuration,
    Dependencies,
    General,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStatus {
    pub owasp_top_10: HashMap<String, ComplianceLevel>,
    pub cwe_top_25: HashMap<String, ComplianceLevel>,
    pub gdpr_compliance: ComplianceLevel,
    pub pci_dss_compliance: ComplianceLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplianceLevel {
    Compliant,
    PartiallyCompliant,
    NonCompliant,
    NotApplicable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttackVector {
    pub vector_type: AttackVectorType,
    pub likelihood: AttackLikelihood,
    pub impact: AttackImpact,
    pub description: String,
    pub affected_files: Vec<String>,
    pub mitigation_steps: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AttackVectorType {
    RemoteCodeExecution,
    SqlInjection,
    CrossSiteScripting,
    CommandInjection,
    PathTraversal,
    PrivilegeEscalation,
    DataExfiltration,
    DenialOfService,
    SessionHijacking,
    ManInTheMiddle,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AttackLikelihood {
    VeryHigh,
    High,
    Medium,
    Low,
    VeryLow,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AttackImpact {
    Critical,
    High,
    Medium,
    Low,
    Minimal,
}

pub struct RiskAssessor;

impl RiskAssessor {
    pub fn new() -> Self {
        RiskAssessor
    }

    pub fn assess_risks(
        &self,
        homoglyph_threats: &[HomoglyphThreat],
        pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
        file_paths: &[String],
    ) -> RiskAssessment {
        let threat_breakdown = self.calculate_threat_breakdown(
            homoglyph_threats,
            pattern_threats,
            security_threats,
        );

        let overall_risk_score = self.calculate_overall_risk_score(
            homoglyph_threats,
            pattern_threats,
            security_threats,
        );

        let file_risk_scores = self.calculate_file_risk_scores(
            homoglyph_threats,
            pattern_threats,
            security_threats,
            file_paths,
        );

        let recommendations = self.generate_recommendations(
            homoglyph_threats,
            pattern_threats,
            security_threats,
        );

        let compliance_status = self.assess_compliance(
            homoglyph_threats,
            pattern_threats,
            security_threats,
        );

        let attack_vectors = self.identify_attack_vectors(
            homoglyph_threats,
            pattern_threats,
            security_threats,
        );

        RiskAssessment {
            overall_risk_score,
            threat_breakdown,
            file_risk_scores,
            recommendations,
            compliance_status,
            attack_vectors,
        }
    }

    fn calculate_threat_breakdown(
        &self,
        homoglyph_threats: &[HomoglyphThreat],
        pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> ThreatBreakdown {
        let mut critical_issues = 0;
        let mut high_issues = 0;
        let mut medium_issues = 0;
        let mut low_issues = 0;

        // Count homoglyph threats by severity
        for threat in homoglyph_threats {
            match threat.risk_level {
                super::HomoglyphRiskLevel::Critical => critical_issues += 1,
                super::HomoglyphRiskLevel::High => high_issues += 1,
                super::HomoglyphRiskLevel::Medium => medium_issues += 1,
                super::HomoglyphRiskLevel::Low => low_issues += 1,
            }
        }

        // Count pattern threats by severity
        for threat in pattern_threats {
            match threat.risk_level {
                super::RiskLevel::Critical => critical_issues += 1,
                super::RiskLevel::High => high_issues += 1,
                super::RiskLevel::Medium => medium_issues += 1,
                super::RiskLevel::Low => low_issues += 1,
            }
        }

        // Count security threats by severity
        for threat in security_threats {
            match threat.severity {
                ThreatSeverity::Critical => critical_issues += 1,
                ThreatSeverity::High => high_issues += 1,
                ThreatSeverity::Medium => medium_issues += 1,
                ThreatSeverity::Low => low_issues += 1,
            }
        }

        ThreatBreakdown {
            homoglyph_threats: homoglyph_threats.len(),
            pattern_threats: pattern_threats.len(),
            security_threats: security_threats.len(),
            critical_issues,
            high_issues,
            medium_issues,
            low_issues,
        }
    }

    fn calculate_overall_risk_score(
        &self,
        homoglyph_threats: &[HomoglyphThreat],
        pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> f32 {
        let mut score_deduction = 0.0;

        // Homoglyph threats scoring
        for threat in homoglyph_threats {
            score_deduction += match threat.risk_level {
                super::HomoglyphRiskLevel::Critical => 15.0,
                super::HomoglyphRiskLevel::High => 10.0,
                super::HomoglyphRiskLevel::Medium => 5.0,
                super::HomoglyphRiskLevel::Low => 2.0,
            };
        }

        // Pattern threats scoring
        for threat in pattern_threats {
            score_deduction += match threat.risk_level {
                super::RiskLevel::Critical => 20.0,
                super::RiskLevel::High => 12.0,
                super::RiskLevel::Medium => 6.0,
                super::RiskLevel::Low => 2.0,
            };
        }

        // Security threats scoring (highest weight)
        for threat in security_threats {
            score_deduction += match threat.severity {
                ThreatSeverity::Critical => 30.0,
                ThreatSeverity::High => 18.0,
                ThreatSeverity::Medium => 8.0,
                ThreatSeverity::Low => 3.0,
            };
        }

        (100.0f32 - score_deduction as f32).max(0.0)
    }

    fn calculate_file_risk_scores(
        &self,
        _homoglyph_threats: &[HomoglyphThreat],
        _pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
        file_paths: &[String],    ) -> HashMap<String, f32> {
        let mut file_scores: HashMap<String, f32> = HashMap::new();

        // Initialize all files with perfect score
        for path in file_paths {
            file_scores.insert(path.clone(), 100.0f32);
        }

        // Deduct points for security threats (highest priority)
        for threat in security_threats {
            if let Some(score) = file_scores.get_mut(&threat.file_path) {
                *score -= match threat.severity {
                    ThreatSeverity::Critical => 40.0,
                    ThreatSeverity::High => 25.0,
                    ThreatSeverity::Medium => 15.0,
                    ThreatSeverity::Low => 5.0,
                };
                *score = (*score as f32).max(0.0f32);
            }
        }

        // Note: Pattern and homoglyph threats would need file_path fields to be included here
        // For now, they contribute to overall score but not file-specific scores

        file_scores
    }

    fn generate_recommendations(
        &self,
        homoglyph_threats: &[HomoglyphThreat],
        pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> Vec<SecurityRecommendation> {
        let mut recommendations = Vec::new();

        // Critical security threat recommendations
        if security_threats.iter().any(|t| matches!(t.severity, ThreatSeverity::Critical)) {
            recommendations.push(SecurityRecommendation {
                priority: RecommendationPriority::Immediate,
                category: RecommendationCategory::CodeInjection,
                title: "CRITICAL: Immediate Security Vulnerabilities Detected".to_string(),
                description: "Critical security vulnerabilities have been detected that could lead to immediate system compromise".to_string(),
                action_items: vec![
                    "Review and fix all critical security threats immediately".to_string(),
                    "Implement input validation and sanitization".to_string(),
                    "Review authentication and authorization mechanisms".to_string(),
                    "Conduct emergency security review".to_string(),
                ],
                impact: "Remote code execution, data breach, system compromise".to_string(),
            });
        }

        // Homoglyph attack recommendations
        if !homoglyph_threats.is_empty() {
            recommendations.push(SecurityRecommendation {
                priority: RecommendationPriority::Urgent,
                category: RecommendationCategory::InputValidation,
                title: "Homoglyph Attack Vectors Detected".to_string(),
                description: "Characters that visually impersonate legitimate characters have been detected".to_string(),
                action_items: vec![
                    "Implement Unicode normalization".to_string(),
                    "Add homoglyph detection to CI/CD pipeline".to_string(),
                    "Review and validate all user-facing text inputs".to_string(),
                    "Consider implementing character allow-lists".to_string(),
                ],
                impact: "Social engineering, domain spoofing, code obfuscation".to_string(),
            });
        }

        // Pattern-based recommendations
        if !pattern_threats.is_empty() {
            recommendations.push(SecurityRecommendation {
                priority: RecommendationPriority::High,
                category: RecommendationCategory::General,
                title: "Suspicious Code Patterns Identified".to_string(),
                description: "Code patterns associated with security risks have been identified".to_string(),
                action_items: vec![
                    "Review flagged code patterns for security implications".to_string(),
                    "Implement secure coding practices".to_string(),
                    "Add static analysis tools to development workflow".to_string(),
                    "Provide security training for development team".to_string(),
                ],
                impact: "Potential security vulnerabilities, code quality issues".to_string(),
            });
        }

        recommendations
    }

    fn assess_compliance(
        &self,
        _homoglyph_threats: &[HomoglyphThreat],
        _pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> ComplianceStatus {
        let mut owasp_top_10 = HashMap::new();
        let mut cwe_top_25 = HashMap::new();

        // Assess OWASP Top 10 compliance
        owasp_top_10.insert("A01:2021-Broken Access Control".to_string(), 
            if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::AuthenticationBypass | super::SecurityThreatType::PrivilegeEscalation)) {
                ComplianceLevel::NonCompliant
            } else {
                ComplianceLevel::Compliant
            });

        owasp_top_10.insert("A03:2021-Injection".to_string(),
            if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::SqlInjection | super::SecurityThreatType::CodeInjection | super::SecurityThreatType::CommandInjection)) {
                ComplianceLevel::NonCompliant
            } else {
                ComplianceLevel::Compliant
            });

        // Assess CWE Top 25 compliance
        cwe_top_25.insert("CWE-79: Cross-site Scripting".to_string(),
            if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::XssVulnerability)) {
                ComplianceLevel::NonCompliant
            } else {
                ComplianceLevel::Compliant
            });

        let gdpr_compliance = if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::InformationDisclosure)) {
            ComplianceLevel::NonCompliant
        } else {
            ComplianceLevel::Compliant
        };

        let pci_dss_compliance = if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::HardcodedCredentials | super::SecurityThreatType::WeakCryptography)) {
            ComplianceLevel::NonCompliant
        } else {
            ComplianceLevel::Compliant
        };

        ComplianceStatus {
            owasp_top_10,
            cwe_top_25,
            gdpr_compliance,
            pci_dss_compliance,
        }
    }

    fn identify_attack_vectors(
        &self,
        _homoglyph_threats: &[HomoglyphThreat],
        _pattern_threats: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> Vec<AttackVector> {
        let mut vectors = Vec::new();

        // Identify RCE vectors
        if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::CodeInjection | super::SecurityThreatType::CommandInjection)) {
            vectors.push(AttackVector {
                vector_type: AttackVectorType::RemoteCodeExecution,
                likelihood: AttackLikelihood::High,
                impact: AttackImpact::Critical,
                description: "Remote code execution possible through injection vulnerabilities".to_string(),
                affected_files: security_threats.iter()
                    .filter(|t| matches!(t.threat_type, super::SecurityThreatType::CodeInjection | super::SecurityThreatType::CommandInjection))
                    .map(|t| t.file_path.clone())
                    .collect(),
                mitigation_steps: vec![
                    "Implement strict input validation".to_string(),
                    "Use parameterized queries and prepared statements".to_string(),
                    "Apply principle of least privilege".to_string(),
                ],
            });
        }

        // Identify XSS vectors
        if security_threats.iter().any(|t| matches!(t.threat_type, super::SecurityThreatType::XssVulnerability)) {
            vectors.push(AttackVector {
                vector_type: AttackVectorType::CrossSiteScripting,
                likelihood: AttackLikelihood::Medium,
                impact: AttackImpact::High,
                description: "Cross-site scripting attacks possible through unvalidated output".to_string(),
                affected_files: security_threats.iter()
                    .filter(|t| matches!(t.threat_type, super::SecurityThreatType::XssVulnerability))
                    .map(|t| t.file_path.clone())
                    .collect(),
                mitigation_steps: vec![
                    "Implement output encoding".to_string(),
                    "Use Content Security Policy (CSP)".to_string(),
                    "Validate and sanitize all user inputs".to_string(),
                ],
            });
        }

        vectors
    }
}
