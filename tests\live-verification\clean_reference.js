// Clean reference file - no suspicious characters
function cleanFunction() {
    const data = "hello world";
    return data;
}

const normalVariable = "safe";

console.log("This file contains no suspicious Unicode characters");

// Standard ASCII characters only
const config = {
    debug: false,
    admin: false,
    secure: true
};

function processCleanData() {
    return "legitimate function";
}

// No homographs, no zero-width chars, no bidirectional overrides
const result = cleanFunction();
console.log("Clean file processing complete");
