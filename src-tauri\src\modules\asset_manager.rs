// Asset Manager module - Handles loading and management of JSON configuration files
// Extracted from main_module.rs to improve code organization

use std::collections::HashSet;
use std::path::Path;
use std::fs;
use crate::modules::data_structures::*;

#[derive(Debug, <PERSON>lone)]
pub struct AssetManager {
    pub bad_characters: BadCharactersConfig,
    pub file_types: FileTypesSummary,
    pub ai_patterns: Option<AdvancedAIPatternsConfig>,
    pub supported_extensions: HashSet<String>,
}

impl AssetManager {
    pub fn new() -> Result<Self, String> {
        // Try to load assets from multiple possible locations
        let bad_characters = Self::load_bad_characters()?;
        let file_types = Self::load_file_types()?;
          // Try to load AI patterns (optional, not critical for basic functionality)
        let ai_patterns = match Self::load_ai_patterns() {
            Ok(patterns) => {
                eprintln!("✅ Successfully loaded Advanced AI Patterns");
                Some(patterns)
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load Advanced AI Patterns: {}", e);
                eprintln!("   (Continuing without AI detection capabilities)");
                None
            }
        };

        // Build supported extensions set from Programming Source Code category
        let mut supported_extensions = HashSet::new();
        
        // Add C Family extensions
        for ext in &file_types.categories.programming_source_code.c_family {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Java/JVM extensions
        for ext in &file_types.categories.programming_source_code.java_jvm {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Python extensions
        for ext in &file_types.categories.programming_source_code.python {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add JavaScript/TypeScript extensions
        for ext in &file_types.categories.programming_source_code.javascript_typescript {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Other extensions
        for ext in &file_types.categories.programming_source_code.other {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }

        // Add Configuration Files (commonly analyzed)
        for ext in &file_types.categories.configuration_files {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }

        Ok(Self {
            bad_characters,
            file_types,
            ai_patterns,
            supported_extensions,
        })
    }

    pub fn is_supported_extension(&self, extension: &str) -> bool {
        self.supported_extensions.contains(&extension.to_lowercase())
    }

    pub fn get_character_severity(&self, codepoint: u32) -> Option<&str> {
        let hex_code = format!("U+{:04X}", codepoint);
        
        // Check extremely big problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.extremely_big_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("extremely_big_problems");
            }
        }
        
        // Check high problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.high_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("high_problems");
            }
        }
        
        // Check medium problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.medium_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("medium_problems");
            }
        }
        
        // Check low problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.low_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("low_problems");
            }
        }
        
        None
    }

    pub fn get_file_type_documentation(&self, extension: &str) -> String {
        let ext_with_dot = format!(".{}", extension.trim_start_matches('.'));
        
        // Check in Programming Source Code categories
        if self.file_types.categories.programming_source_code.c_family.contains(&ext_with_dot) {
            return "C/C++ source code file - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.java_jvm.contains(&ext_with_dot) {
            return "Java/JVM language source code - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.python.contains(&ext_with_dot) {
            return "Python source code file - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.javascript_typescript.contains(&ext_with_dot) {
            return "JavaScript/TypeScript source code - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.other.contains(&ext_with_dot) {
            return "Programming source code file - read as text with UTF-8 encoding".to_string();
        }
        
        // Check in Configuration Files
        if self.file_types.categories.configuration_files.contains(&ext_with_dot) {
            return "Configuration file - read as text with UTF-8 encoding".to_string();
        }
        
        "Text-based file - read as text with UTF-8 encoding".to_string()
    }
    
    /// Check if AI patterns are available
    pub fn has_ai_patterns(&self) -> bool {
        self.ai_patterns.is_some()
    }
    
    /// Get all AI code patterns for analysis
    pub fn get_ai_code_patterns(&self) -> Vec<&AIPattern> {
        match &self.ai_patterns {
            Some(patterns) => {
                let mut all_patterns = Vec::new();
                all_patterns.extend(&patterns.categories.ai_code_patterns.high_confidence_patterns);
                all_patterns.extend(&patterns.categories.ai_code_patterns.medium_confidence_patterns);
                all_patterns.extend(&patterns.categories.ai_code_patterns.low_confidence_patterns);
                all_patterns
            }
            None => Vec::new()
        }
    }
    
    /// Get advanced homoglyph patterns
    pub fn get_advanced_homoglyph_patterns(&self) -> Vec<&AIPattern> {
        match &self.ai_patterns {
            Some(patterns) => {
                let mut all_patterns = Vec::new();
                all_patterns.extend(&patterns.categories.advanced_homoglyph_patterns.critical_patterns);
                all_patterns.extend(&patterns.categories.advanced_homoglyph_patterns.patterns);
                all_patterns
            }
            None => Vec::new()
        }
    }
    
    /// Get steganography detection patterns
    pub fn get_steganography_patterns(&self) -> Vec<&AIPattern> {
        match &self.ai_patterns {
            Some(patterns) => {
                patterns.categories.steganography_patterns.suspicious_patterns.iter().collect()
            }
            None => Vec::new()
        }
    }
    
    /// Get code injection patterns
    pub fn get_code_injection_patterns(&self) -> Vec<&AIPattern> {
        match &self.ai_patterns {
            Some(patterns) => {
                patterns.categories.code_injection_patterns.dangerous_patterns.iter().collect()
            }
            None => Vec::new()
        }
    }
    
    /// Get bidirectional attack patterns
    pub fn get_bidirectional_attack_patterns(&self) -> Vec<&AIPattern> {
        match &self.ai_patterns {
            Some(patterns) => {
                patterns.categories.advanced_bidirectional_attacks.patterns.iter().collect()
            }
            None => Vec::new()
        }
    }
}

// Asset loading methods
impl AssetManager {    fn load_bad_characters() -> Result<BadCharactersConfig, String> {
        eprintln!("🔍 Loading Bad_Characters.json...");
        
        // Try embedded assets first (always works)
        match Self::load_embedded_bad_characters() {
            Ok(config) => {
                eprintln!("✅ Successfully loaded Bad_Characters.json from embedded data");
                return Ok(config);
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load embedded Bad_Characters.json: {}", e);
                eprintln!("🔄 Trying file system paths...");
            }
        }
        
        // Fallback to file system paths
        let possible_paths = [
            "assets/Bad_Characters.json",           // Development
            "./assets/Bad_Characters.json",         // Alternative development
            "../assets/Bad_Characters.json",        // Relative to binary
            "Bad_Characters.json",                  // Direct in working dir
            // Add Windows-specific paths
            r".\assets\Bad_Characters.json",
            r"..\assets\Bad_Characters.json",
            // Add more specific development paths
            r"C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS\assets\Bad_Characters.json",
        ];
        
        let mut last_error = String::new();        
        for path_str in &possible_paths {
            let path = Path::new(path_str);
            eprintln!("🔍 Trying path: {}", path_str);
            
            match fs::read_to_string(path) {
                Ok(content) => {
                    eprintln!("📄 File read successfully, {} bytes", content.len());
                    match serde_json::from_str::<BadCharactersConfig>(&content) {
                        Ok(config) => {
                            eprintln!("✅ Successfully loaded Bad_Characters.json from: {}", path_str);
                            return Ok(config);
                        }
                        Err(e) => {
                            last_error = format!("Failed to parse Bad_Characters.json from {}: {}", path_str, e);
                            eprintln!("❌ {}", last_error);
                        }
                    }
                }
                Err(e) => {
                    last_error = format!("Failed to read Bad_Characters.json from {}: {}", path_str, e);
                    eprintln!("❌ {}", last_error);
                }
            }
        }
        
        Err(format!("Failed to load Bad_Characters.json from any location. Last error: {}", last_error))
    }
      fn load_file_types() -> Result<FileTypesSummary, String> {
        eprintln!("🔍 Loading FileTypesSummary.json...");
        
        // Try embedded assets first (always works)
        match Self::load_embedded_file_types() {
            Ok(config) => {
                eprintln!("✅ Successfully loaded FileTypesSummary.json from embedded data");
                return Ok(config);
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load embedded FileTypesSummary.json: {}", e);
                eprintln!("🔄 Trying file system paths...");
            }
        }
        
        // Fallback to file system paths
        let possible_paths = [
            "assets/FileTypesSummary.json",         // Development
            "./assets/FileTypesSummary.json",       // Alternative development
            "../assets/FileTypesSummary.json",      // Relative to binary
            "FileTypesSummary.json",                // Direct in working dir
            // Add Windows-specific paths
            r".\assets\FileTypesSummary.json",
            r"..\assets\FileTypesSummary.json",
            // Add more specific development paths
            r"C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS\assets\FileTypesSummary.json",
        ];
          let mut last_error = String::new();
        
        for path_str in &possible_paths {
            let path = Path::new(path_str);
            eprintln!("🔍 Trying path: {}", path_str);
            
            match fs::read_to_string(path) {
                Ok(content) => {
                    eprintln!("📄 File read successfully, {} bytes", content.len());
                    match serde_json::from_str::<FileTypesSummary>(&content) {
                        Ok(config) => {
                            eprintln!("✅ Successfully loaded FileTypesSummary.json from: {}", path_str);
                            return Ok(config);
                        }
                        Err(e) => {
                            last_error = format!("Failed to parse FileTypesSummary.json from {}: {}", path_str, e);
                            eprintln!("❌ {}", last_error);
                        }
                    }
                }
                Err(e) => {
                    last_error = format!("Failed to read FileTypesSummary.json from {}: {}", path_str, e);
                    eprintln!("❌ {}", last_error);
                }
            }
        }
        
        Err(format!("Failed to load FileTypesSummary.json from any location. Last error: {}", last_error))
    }
      fn load_ai_patterns() -> Result<AdvancedAIPatternsConfig, String> {
        eprintln!("🔍 Loading Advanced_AI_Patterns.json...");
        
        // Try embedded assets first (always works)
        match Self::load_embedded_ai_patterns() {
            Ok(config) => {
                eprintln!("✅ Successfully loaded Advanced_AI_Patterns.json from embedded data");
                return Ok(config);
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load embedded Advanced_AI_Patterns.json: {}", e);
                eprintln!("🔄 Trying file system paths...");
            }
        }
        
        // Fallback to file system paths
        let possible_paths = [
            "assets/Advanced_AI_Patterns.json",         // Development
            "./assets/Advanced_AI_Patterns.json",       // Alternative development
            "../assets/Advanced_AI_Patterns.json",      // Relative to binary
            "Advanced_AI_Patterns.json",                // Direct in working dir
            // Add Windows-specific paths
            r".\assets\Advanced_AI_Patterns.json",
            r"..\assets\Advanced_AI_Patterns.json",
            // Add more specific development paths
            r"C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS\assets\Advanced_AI_Patterns.json",
        ];
          let mut last_error = String::new();
        
        for path_str in &possible_paths {
            let path = Path::new(path_str);
            eprintln!("🔍 Trying path: {}", path_str);
            
            match fs::read_to_string(path) {
                Ok(content) => {
                    eprintln!("📄 File read successfully, {} bytes", content.len());
                    match serde_json::from_str::<AdvancedAIPatternsConfig>(&content) {
                        Ok(config) => {
                            eprintln!("✅ Successfully loaded Advanced_AI_Patterns.json from: {}", path_str);
                            return Ok(config);
                        }
                        Err(e) => {
                            last_error = format!("Failed to parse Advanced_AI_Patterns.json from {}: {}", path_str, e);
                            eprintln!("❌ {}", last_error);
                        }
                    }
                }
                Err(e) => {
                    last_error = format!("Failed to read Advanced_AI_Patterns.json from {}: {}", path_str, e);
                    eprintln!("❌ {}", last_error);
                }
            }
        }
        
        Err(format!("Failed to load Advanced_AI_Patterns.json from any location. Last error: {}", last_error))
    }
    
    // Enhanced fallback method: embed the JSON as a string in the binary for production builds
    fn load_embedded_bad_characters() -> Result<BadCharactersConfig, String> {
        // Include the JSON file as a string at compile time
        const BAD_CHARACTERS_JSON: &str = include_str!("../../../assets/Bad_Characters.json");
        
        serde_json::from_str(BAD_CHARACTERS_JSON)
            .map_err(|e| format!("Failed to parse embedded Bad_Characters.json: {}", e))
    }
    
    fn load_embedded_file_types() -> Result<FileTypesSummary, String> {
        // Include the JSON file as a string at compile time
        const FILE_TYPES_JSON: &str = include_str!("../../../assets/FileTypesSummary.json");
        
        serde_json::from_str(FILE_TYPES_JSON)
            .map_err(|e| format!("Failed to parse embedded FileTypesSummary.json: {}", e))
    }
    
    fn load_embedded_ai_patterns() -> Result<AdvancedAIPatternsConfig, String> {
        // Include the JSON file as a string at compile time
        const AI_PATTERNS_JSON: &str = include_str!("../../../assets/Advanced_AI_Patterns.json");
        
        serde_json::from_str(AI_PATTERNS_JSON)
            .map_err(|e| format!("Failed to parse embedded Advanced_AI_Patterns.json: {}", e))
    }
}
