﻿// Simple test to verify AssetManager integration without external dependencies
use std::fs;
use std::path::Path;

fn main() {
    println!("🧪 Testing Bad Character Scanner AssetManager Integration");
    println!("======================================================");
    
    // Test 1: Verify files exist
    println!("\n📁 Test 1: Asset File Existence");
    test_asset_files_exist();
    
    // Test 2: Basic file reading
    println!("\n📋 Test 2: Basic File Reading");
    test_basic_file_reading();
    
    // Test 3: JSON structure validation (basic)
    println!("\n🔍 Test 3: Basic JSON Structure");
    test_basic_json_structure();
    
    println!("\n✅ AssetManager Integration Test Complete!");
}

fn test_asset_files_exist() {
    let bad_chars_path = Path::new("assets/Bad_Characters.json");
    let file_types_path = Path::new("assets/FileTypesSummary.json");
    
    if bad_chars_path.exists() && file_types_path.exists() {
        println!("  ✅ Both asset files exist");
        
        // Check file sizes
        if let Ok(metadata) = fs::metadata(bad_chars_path) {
            println!("    Bad_Characters.json: {} bytes", metadata.len());
        }
        if let Ok(metadata) = fs::metadata(file_types_path) {
            println!("    FileTypesSummary.json: {} bytes", metadata.len());
        }
    } else {
        println!("  ❌ Missing asset files");
        if !bad_chars_path.exists() { 
            println!("    - Bad_Characters.json missing"); 
        }
        if !file_types_path.exists() { 
            println!("    - FileTypesSummary.json missing"); 
        }
    }
}

fn test_basic_file_reading() {
    match fs::read_to_string("assets/Bad_Characters.json") {
        Ok(content) => {
            println!("  ✅ Bad_Characters.json readable ({} chars)", content.len());
            
            // Basic content validation
            if content.contains("U+") && content.contains("severity") {
                println!("    Contains expected structure (U+ codes and severity)");
            } else {
                println!("    ⚠️  Missing expected structure elements");
            }
        }
        Err(e) => println!("  ❌ Cannot read Bad_Characters.json: {}", e),
    }
    
    match fs::read_to_string("assets/FileTypesSummary.json") {
        Ok(content) => {
            println!("  ✅ FileTypesSummary.json readable ({} chars)", content.len());
            
            // Basic content validation
            if content.contains("extensions") && content.contains("[") {
                println!("    Contains expected structure (extensions arrays)");
            } else {
                println!("    ⚠️  Missing expected structure elements");
            }
        }
        Err(e) => println!("  ❌ Cannot read FileTypesSummary.json: {}", e),
    }
}

fn test_basic_json_structure() {
    // Test that the files at least look like JSON
    match fs::read_to_string("assets/Bad_Characters.json") {
        Ok(content) => {
            let trimmed = content.trim();
            if trimmed.starts_with('{') && trimmed.ends_with('}') {
                println!("  ✅ Bad_Characters.json has JSON structure");
                
                // Count some basic elements
                let u_codes = content.matches("U+").count();
                let severity_entries = content.matches("severity").count();
                println!("    Found {} U+ character codes", u_codes);
                println!("    Found {} severity entries", severity_entries);
                
                // Look for specific problematic characters
                let test_chars = vec!["U+202E", "U+200B", "U+FEFF", "U+00A0"];
                for char_code in test_chars {
                    if content.contains(char_code) {
                        println!("    ✅ Found {}", char_code);
                    } else {
                        println!("    ⚠️  Missing {}", char_code);
                    }
                }
            } else {
                println!("  ❌ Bad_Characters.json doesn't look like JSON");
            }
        }
        Err(e) => println!("  ❌ Cannot test Bad_Characters.json structure: {}", e),
    }
    
    match fs::read_to_string("assets/FileTypesSummary.json") {
        Ok(content) => {
            let trimmed = content.trim();
            if trimmed.starts_with('{') && trimmed.ends_with('}') {
                println!("  ✅ FileTypesSummary.json has JSON structure");
                
                // Count some basic elements
                let extensions_count = content.matches("extensions").count();
                let arrays_count = content.matches("[").count();
                println!("    Found {} extension entries", extensions_count);
                println!("    Found {} array structures", arrays_count);
                
                // Look for common file types
                let test_types = vec!["\"rs\"", "\"js\"", "\"ts\"", "\"py\"", "\"cpp\""];
                for file_type in test_types {
                    if content.contains(file_type) {
                        println!("    ✅ Found file type {}", file_type);
                    } else {
                        println!("    ⚠️  Missing file type {}", file_type);
                    }
                }
            } else {
                println!("  ❌ FileTypesSummary.json doesn't look like JSON");
            }
        }
        Err(e) => println!("  ❌ Cannot test FileTypesSummary.json structure: {}", e),
    }
}
