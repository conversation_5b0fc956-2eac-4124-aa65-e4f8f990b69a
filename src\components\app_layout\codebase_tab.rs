use crate::utils::tauri::tauri_invoke;
use crate::components::progress_bar::ProgressBar;
use gloo_timers::future::sleep;
use leptos::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub enum InterfaceState {
    SelectionMode,
    ActionsMode,
    ProcessingMode,
}

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct FolderInfo {
    pub path: String,
    pub exists: bool,
    pub readable: bool,
    pub file_count: u64,
    pub total_size_bytes: u64,
}

#[derive(<PERSON><PERSON>, Debug, PartialEq, Serialize, Deserialize)]
pub struct RecentFolder {
    pub path: String,
    pub last_used: String,
}

#[component]
pub fn CodebaseComponent() -> impl IntoView {
    let (interface_state, set_interface_state) = create_signal(InterfaceState::SelectionMode);
    let (path_input, set_path_input) = create_signal(String::new());
    let (selected_folder, set_selected_folder) = create_signal::<Option<String>>(None);
    let (_path_validation, _set_path_validation) = create_signal::<Option<FolderInfo>>(None);
    let (error_message, set_error_message) = create_signal::<Option<String>>(None);
    let (_recent_folders, _set_recent_folders) = create_signal::<Vec<RecentFolder>>(vec![]);
    let (_quick_access_folders, _set_quick_access_folders) = create_signal::<Vec<FolderInfo>>(vec![]);
    let (_show_recent_dropdown, set_show_recent_dropdown) = create_signal(false);
    let (_is_drag_over, _set_is_drag_over) = create_signal(false);

    // Scanner state
    let (total_files, set_total_files) = create_signal(0);
    let (files_processed, set_files_processed) = create_signal(0);
    let (files_failed, set_files_failed) = create_signal(0);
    let (error_messages, set_error_messages) = create_signal(Vec::new());

    let select_folder_from_path = move |path: String| {
        set_selected_folder.set(Some(path.clone()));
        set_path_input.set(path.clone());
        set_show_recent_dropdown.set(false);
        set_error_message.set(None);
        set_interface_state.set(InterfaceState::ActionsMode);
        spawn_local(async move {
            let _ = tauri_invoke::<_, ()>("save_recent_folder", &serde_json::json!({ "path": path })).await;
        });
    };

    let select_folder = move |_| {
        spawn_local(async move {
            let starting_path = path_input.get_untracked();
            match tauri_invoke::<_, String>("select_folder", &serde_json::json!({ "defaultPath": starting_path })).await {
                Ok(folder_path) => {
                    if !folder_path.is_empty() && folder_path != "null" {
                        select_folder_from_path(folder_path);
                    }
                },
                Err(e) => {
                    set_error_message.set(Some(format!("❌ Failed to select folder: {}", e)));
                }
            }
        });
    };

    let run_scanner = move || {
        set_interface_state.set(InterfaceState::ProcessingMode);
        spawn_local(async move {
            set_total_files.set(100);
            for i in 0..100 {
                sleep(Duration::from_millis(50)).await;
                if i % 10 == 0 {
                    set_files_failed.update(|v| *v += 1);
                    set_error_messages.update(|v| v.push(format!("Failed to scan file {}", i)));
                } else {
                    set_files_processed.update(|v| *v += 1);
                }
            }
        });
    };

    let progress = create_memo(move |_| {
        if total_files.get() == 0 {
            0.0
        } else {
            (files_processed.get() + files_failed.get()) as f64 / total_files.get() as f64
        }
    });
    
    let (progress_signal, set_progress_signal) = create_signal(0.0);
    
    // Update progress signal when memo changes
    create_effect(move |_| {
        set_progress_signal.set(progress.get());
    });

    let success_percentage = create_memo(move |_| {
        if total_files.get() == 0 {
            0.0
        } else {
            files_processed.get() as f64 / total_files.get() as f64 * 100.0
        }
    });

    view! {
        <div class="p-4">
            <img src="assets/images/ui_images_40x40/uiimg_007.png" class="icon-40" style="width:40px;height:40px;display:inline-block;" alt="Code Base Analysis Icon" />
            {move || match interface_state.get() {
                InterfaceState::SelectionMode => view! {
                    <div>
                        <div class="flex space-x-2">
                            <input
                                type="text"
                                class="flex-grow p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded"
                                placeholder="Enter folder path or drag and drop"
                                on:input=move |ev| set_path_input.set(event_target_value(&ev))
                                prop:value=path_input
                            />
                            <button
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
                                on:click=select_folder
                            >
                                "Browse..."
                            </button>
                        </div>
                        <div class="mt-2 text-red-500">{error_message}</div>
                    </div>
                },
                InterfaceState::ActionsMode => view! {
                    <div>
                        <img src="assets/images/ui_images_40x40/uiimg_008.png" class="icon-40" style="width:40px;height:40px;display:inline-block;" alt="Selected Folder Icon" />
                        <button
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded mr-2"
                            on:click=move |_| run_scanner()
                        >
                            "Start Scan"
                        </button>
                        <button
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded"
                            on:click=move |_| {
                                set_interface_state.set(InterfaceState::SelectionMode);
                                set_files_processed.set(0);
                                set_files_failed.set(0);
                                set_total_files.set(0);
                                set_error_messages.set(vec![]);
                            }
                        >
                            "Change Folder"
                        </button>
                    </div>
                },
                InterfaceState::ProcessingMode => view! {
                    <div>
                        <h2 class="text-xl font-semibold mb-2">"Scanning..."</h2>
                        <ProgressBar progress=progress_signal />
                        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <p class="text-gray-500">"Overall Progress"</p>
                                <p class="text-2xl font-bold">{move || (progress.get() * 100.0).round()}%</p>
                            </div>
                             <div>
                                <p class="text-gray-500">"Succeeded"</p>
                                <p class="text-2xl font-bold">{move || files_processed.get()}"/"{move || total_files.get()}</p>
                            </div>
                            <div>
                                <p class="text-gray-500">"Failed"</p>
                                <p class="text-2xl font-bold">{move || files_failed.get()}</p>
                            </div>
                             <div>
                                <p class="text-gray-500">"Success Rate"</p>
                                <p class="text-2xl font-bold">{move || success_percentage.get().round()}%</p>
                            </div>
                        </div>
                        {move || if !error_messages.get().is_empty() {
                            view! {
                                <div class="mt-4">
                                    <h3 class="font-semibold text-red-600">"Error Messages:"</h3>
                                    <ul class="error-list bg-red-50 dark:bg-red-900/20 p-2 rounded max-h-40 overflow-y-auto">
                                        {move || error_messages.get().iter().map(|msg| view! { <li class="text-red-600 font-mono text-sm">{msg}</li> }).collect_view()}
                                    </ul>
                                </div>
                            }.into_view()
                        } else {
                            view! { <div/> }.into_view()
                        }}
                    </div>
                },
            }}
        </div>
    }
}