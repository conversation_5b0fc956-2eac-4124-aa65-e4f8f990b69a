# Script Archives

This folder contains scripts that are no longer relevant to the current codebase version but are preserved for historical reference.

## Archived Scripts

### Reconstruction Scripts (Built for older lib.rs structure)
- `analyze_lib_structure.py` - Analyzed old monolithic lib.rs structure
- `rebuild_lib.py` - Rebuilt lib.rs from extracted components
- `rebuild_lib_ascii.py` - ASCII version of the rebuilder
- `run_complete_reconstruction.py` - Master reconstruction orchestrator
- `generate_enhanced_components.py` - Generated component templates
- `MASTER_RECONSTRUCTION_REPORT.txt` - Final reconstruction report

### Legacy Fix Scripts (Addressed specific past issues)
- `fix-cleaning-functionality.ps1` & `fix-cleaning-functionality-clean.ps1` - Fixed text cleaning issues
- `fix-folder-selection.ps1` & `fix-folder-selection-clean.ps1` - Fixed folder selection problems

### Superseded Scripts
- `setup-simple.ps1` - Simple setup script (replaced by setup-dev-environment.ps1)

### Python Cache
- `__pycache__/` - Python bytecode cache (not needed)

## Why These Were Archived

These scripts were built for an older version of the codebase that had a monolithic `lib.rs` file structure. The current codebase has been refactored into a modular component-based architecture, making these reconstruction and fix scripts obsolete.

## Current Active Scripts

See the main `scripts/` directory for current, maintained scripts that work with the modern codebase structure.

---
*Archived on: 2025-06-19*
*Reason: Codebase architecture modernization*
