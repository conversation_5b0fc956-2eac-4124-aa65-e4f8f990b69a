// Test file for AI detection functionality
// This tests the backend AI detection without needing the full Tauri app

use std::collections::HashMap;
use serde_json;

// Import our main module to test AI detection
mod main_module;
use main_module::{Asset<PERSON>anager, AIDetectionResult};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing AI Detection Functionality");
    println!("=====================================");
    
    // Test with some AI-like content patterns
    let test_cases = vec![
        ("Normal text", "This is just normal text with nothing suspicious."),
        ("Zero-width chars", "This‌has‍hidden‌characters"),
        ("Bidirectional", "This is ‮attack‬ text with RTL override"),
        ("Mixed scripts", "This is Ρуѕѕіаn mixed with Latin"),
        ("Homoglyphs", "This contains ο (omicron) and о (cyrillic o)"),
    ];
    
    for (name, content) in test_cases {
        println!("\n🔍 Testing: {}", name);
        println!("Content: {}", content);
        
        // Call our AI detection function
        match main_module::detect_ai_content(content.to_string()).await {
            Ok(result) => {
                println!("✅ Result:");
                println!("   Overall Confidence: {:.2}%", result.overall_confidence * 100.0);
                println!("   AI Likelihood: {}", result.ai_likelihood);
                println!("   Patterns Detected: {}", result.patterns_detected);
                println!("   Summary: {}", result.analysis_summary);
                
                if !result.detected_patterns.is_empty() {
                    println!("   Detected Patterns:");
                    for pattern in &result.detected_patterns {
                        println!("     - {}: {} (confidence: {:.2}%)", 
                            pattern.pattern_name, 
                            pattern.description,
                            pattern.confidence * 100.0);
                    }
                }
            }
            Err(e) => {
                println!("❌ Error: {}", e);
            }
        }
        println!("{}", "-".repeat(50));
    }
    
    println!("\n🎉 AI Detection Testing Complete!");
    Ok(())
}
