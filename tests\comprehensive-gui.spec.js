import { test, expect } from '@playwright/test';

test.describe('Bad Character Scanner - Complete GUI Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
  });

  test('should display the main application header', async ({ page }) => {
    // Test main header elements
    await expect(page.getByText('Bad Character Scanner')).toBeVisible();
    await expect(page.getByText('Advanced Unicode Analysis & Security Scanner')).toBeVisible();
    await expect(page.getByText('with comprehensive text analysis capabilities - By <PERSON>')).toBeVisible();
  });

  test('should have functional mode switcher tabs', async ({ page }) => {
    // Test mode switcher buttons exist and are functional
    const textAnalysisTab = page.getByRole('button', { name: /Text Analysis & Cleaning/i });
    const codebaseAnalysisTab = page.getByRole('button', { name: /Code Base Analysis & Cleaning/i });
    
    await expect(textAnalysisTab).toBeVisible();
    await expect(codebaseAnalysisTab).toBeVisible();
    
    // Test switching between modes
    await codebaseAnalysisTab.click();
    await expect(page.getByText('Code Base Analysis & Cleaning')).toBeVisible();
    
    await textAnalysisTab.click();
    // Should switch back to text analysis mode
    await expect(page.locator('textarea')).toBeVisible(); // Text input area should be visible
  });

  test('should display codebase analysis interface', async ({ page }) => {
    // Switch to codebase analysis mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    // Test folder selection interface
    await expect(page.getByText('Code Base Analysis & Cleaning')).toBeVisible();
    await expect(page.getByPlaceholder('Enter folder path or drag and drop')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Browse...' })).toBeVisible();
  });

  test('should handle folder path input', async ({ page }) => {
    // Switch to codebase analysis mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    const pathInput = page.getByPlaceholder('Enter folder path or drag and drop');
    await pathInput.fill('C:\\\\test\\\\folder');
    await expect(pathInput).toHaveValue('C:\\\\test\\\\folder');
  });

  test('should show folder selection actions after path input', async ({ page }) => {
    // Switch to codebase analysis mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    // For testing purposes, we can simulate what would happen after folder selection
    // Note: In a real test, you'd need to mock the Tauri backend or use test fixtures
    const pathInput = page.getByPlaceholder('Enter folder path or drag and drop');
    await pathInput.fill('C:\\\\test\\\\folder');
    
    // The browse button should be clickable
    const browseButton = page.getByRole('button', { name: 'Browse...' });
    await expect(browseButton).toBeEnabled();
  });

  test('should have functional settings toggle', async ({ page }) => {
    const settingsButton = page.getByRole('button', { name: /Settings/i });
    await expect(settingsButton).toBeVisible();
    
    // Click to open settings
    await settingsButton.click();
    await expect(page.getByText('Application Settings')).toBeVisible();
    await expect(page.getByText('Configure appearance and export options')).toBeVisible();
    
    // Click again to close settings
    await settingsButton.click();
    await expect(page.getByText('Application Settings')).not.toBeVisible();
  });

  test('should display text analysis interface by default', async ({ page }) => {
    // Text analysis should be the default mode
    await expect(page.locator('textarea')).toBeVisible();
    
    // Test that text can be input
    const textArea = page.locator('textarea');
    await textArea.fill('Test input text with special characters: áéíóú');
    await expect(textArea).toHaveValue('Test input text with special characters: áéíóú');
  });

  test('should validate responsive design', async ({ page }) => {
    // Test that the layout works at different screen sizes
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.getByText('Bad Character Scanner')).toBeVisible();
    
    await page.setViewportSize({ width: 1024, height: 768 });
    await expect(page.getByText('Bad Character Scanner')).toBeVisible();
    
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.getByText('Bad Character Scanner')).toBeVisible();
  });

  test('should handle theme toggle in settings', async ({ page }) => {
    // Open settings
    await page.getByRole('button', { name: /Settings/i }).click();
    
    // Look for theme-related controls (these would be in ThemeToggle component)
    await expect(page.getByText('Theme Mode')).toBeVisible();
  });

  test('should display export options in settings', async ({ page }) => {
    // Open settings
    await page.getByRole('button', { name: /Settings/i }).click();
    
    // Check for export component presence
    await expect(page.locator('div').filter({ hasText: /export/i }).first()).toBeVisible();
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Test tab navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Focus should be manageable via keyboard
    const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
    expect(['BUTTON', 'INPUT', 'TEXTAREA']).toContain(focusedElement);
  });

  test('should maintain state when switching modes', async ({ page }) => {
    // Enter text in text analysis mode
    const textArea = page.locator('textarea');
    await textArea.fill('Test state persistence');
    
    // Switch to codebase mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    await expect(page.getByText('Code Base Analysis & Cleaning')).toBeVisible();
    
    // Switch back to text mode
    await page.getByRole('button', { name: /Text Analysis & Cleaning/i }).click();
    
    // Text should still be there (if component maintains state)
    // Note: This depends on how the Leptos components are implemented
    await expect(textArea).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Switch to codebase analysis
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    // Try to interact with browse button (this might show an error if no backend)
    const browseButton = page.getByRole('button', { name: 'Browse...' });
    await browseButton.click();
    
    // Should not crash the application
    await expect(page.getByText('Code Base Analysis & Cleaning')).toBeVisible();
  });

  test('should validate accessibility requirements', async ({ page }) => {
    // Check for proper heading structure
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    // Check that interactive elements are focusable
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    expect(buttonCount).toBeGreaterThan(0);
    
    // Check for proper ARIA labels or accessible text
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      const text = await button.textContent();
      expect(text?.length).toBeGreaterThan(0);
    }
  });

  test('should verify CSS classes are applied correctly', async ({ page }) => {
    // Check that Tailwind CSS classes are working
    const mainContainer = page.locator('.min-h-screen').first();
    await expect(mainContainer).toBeVisible();
    
    // Check for card styling
    const cards = page.locator('.card');
    if (await cards.count() > 0) {
      await expect(cards.first()).toBeVisible();
    }
  });

  test('should validate icon system functionality', async ({ page }) => {
    // Check for SVG icons
    const svgIcons = page.locator('svg');
    const iconCount = await svgIcons.count();
    expect(iconCount).toBeGreaterThan(0);
    
    // Icons should be visible and properly sized
    if (iconCount > 0) {
      await expect(svgIcons.first()).toBeVisible();
    }
  });
});
