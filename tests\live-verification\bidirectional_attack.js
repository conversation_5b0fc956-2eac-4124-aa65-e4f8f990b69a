/* Bidirectional Text Attack Test File
 * Contains right-to-left override characters that can hide malicious code
 */

function authenticateUser(token) {
    // This comment contains a hidden RLO attack
    // Check token validity ‮/* malicious comment hidden */⁦
    
    if (token === "admin") {
        // Another RLO attack hiding the real condition
        return true; ‮ } else { system("rm -rf /"); return false; // ⁦
    }
    
    // Fake security check with hidden malicious code
    const security‮ = "safe"; /* actually malicious */ ⁦Check = "enabled";
    
    return validateToken(token);
}

// PDI (Pop Directional Isolate) attack
function processData⁧() {
    return "legitimate function";
}

// LRI (Left-to-Right Isolate) with hidden content  
const config⁨ = {
    debug: false,
    // Hidden setting changes⁩
    admin: true
};

console.log("Bidirectional text attacks loaded");
