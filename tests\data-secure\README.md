⚠️ SECURITY WARNING - DANGEROUS TEST DATA ⚠️
========================================

This folder contains files with malicious Unicode characters designed to test security vulnerabilities.

🔒 **ISOLATION PROTOCOL**:
- This folder is "bio-sealed" - never copy files from here to production
- Only use for testing the Bad Character Scanner
- Files contain: zero-width characters, bidirectional overrides, homoglyphs, and other attack vectors
- DO NOT open these files in editors that don't handle Unicode securely

📋 **TEST DATA INVENTORY**:
- zero_width_attack.js - JavaScript with hidden zero-width spaces
- bidi_override_attack.py - Python with bidirectional text attacks  
- homoglyph_attack.html - HTML with lookalike characters
- mixed_attack_sample.txt - Text with multiple attack vectors
- clean_reference.js - Clean reference file for comparison
- unicode_bomb.json - JSON with excessive Unicode ranges

🧪 **USAGE**: 
Only use these files with the Bad Character Scanner CLI/Bash interface for testing.

📝 **LAST UPDATED**: $(date)
