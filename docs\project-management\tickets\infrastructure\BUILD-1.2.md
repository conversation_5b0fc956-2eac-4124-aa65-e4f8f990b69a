# BUILD-1.2 - Tauri v2 Build System Modernization

**Status:** 🟢 Open  
**Priority:** Medium  
**Created:** 2025-06-20  
**Updated:** 2025-06-20  
**Assigned To:** Infrastructure Team  
**Estimated Effort:** 3-4 hours  
**Story Points:** 5  
**Parent Ticket:** BUILD-1

## Description

Modernize the Tauri v2 build system to use latest best practices, resolve configuration issues, and optimize the desktop application build process. This complements BUILD-CONFIG-1 by focusing on the build system itself rather than just configuration files.

## Current Issues

### Build System Problems
- **Dual configuration files** causing confusion (tauri.conf.json vs tauri.config.json)
- **Legacy v1 patterns** still present in build scripts
- **Suboptimal build performance** for desktop application
- **Missing modern Tauri v2 features** in build process

### Integration Issues
- **Frontend-backend integration** could be smoother
- **Asset bundling** between Trunk and Tauri needs optimization
- **Development workflow** has friction points
- **Production builds** may not be fully optimized

## Acceptance Criteria

- [ ] Single, modern Tauri v2 configuration file
- [ ] Build system uses latest Tauri v2 best practices
- [ ] Optimized build performance for desktop application
- [ ] Smooth frontend-backend integration during builds
- [ ] Modern capabilities-based security model implemented
- [ ] Production builds fully optimized
- [ ] Development workflow improved

## Technical Details

### Current Build Process Analysis
1. **Frontend build**: Trunk compiles Leptos to WASM
2. **Asset bundling**: Static assets processed
3. **Tauri build**: Desktop application packaging
4. **Final output**: Platform-specific installers

### Modernization Areas
1. **Configuration consolidation**
2. **Build pipeline optimization**
3. **Security model updates**
4. **Asset handling improvements**
5. **Development experience enhancements**

## Implementation Plan

### Phase 1: Configuration Modernization (60 minutes)
1. **Consolidate configuration files**
   - Merge tauri.conf.json and tauri.config.json
   - Apply Tauri v2 configuration schema
   - Remove deprecated settings

2. **Update security configuration**
   - Implement capabilities-based permissions
   - Configure CSP settings
   - Update allowlist to capabilities

### Phase 2: Build Pipeline Optimization (90 minutes)
1. **Optimize build scripts**
   - Update Cargo.toml dependencies
   - Configure build profiles (dev/release)
   - Optimize compilation flags

2. **Improve asset integration**
   - Streamline Trunk → Tauri asset flow
   - Optimize static asset handling
   - Configure proper asset paths

### Phase 3: Modern Features Implementation (60 minutes)
1. **Implement Tauri v2 features**
   - Update plugin system usage
   - Configure modern window management
   - Implement updated IPC patterns

2. **Security enhancements**
   - Apply principle of least privilege
   - Configure secure defaults
   - Implement proper error handling

### Phase 4: Testing and Validation (30 minutes)
1. **Build testing**
   - Test development builds
   - Test production builds
   - Verify cross-platform compatibility

2. **Feature validation**
   - Ensure all functionality works
   - Test security restrictions
   - Verify performance improvements

## Expected Configuration Updates

### Modern tauri.config.json
```json
{
  "productName": "Bad Character Scanner",
  "version": "0.3.1",
  "identifier": "com.jshoy.badcharacterscanner",
  "build": {
    "beforeDevCommand": "trunk serve",
    "beforeBuildCommand": "trunk build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "app": {
    "security": {
      "csp": "default-src 'self'; style-src 'self' 'unsafe-inline'"
    }
  },
  "bundle": {
    "active": true,
    "targets": "all",
    "icon": ["icons/32x32.png", "icons/128x128.png", "icons/icon.icns", "icons/icon.ico"]
  }
}
```

### Updated Cargo.toml Dependencies
```toml
[dependencies]
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"
tauri-plugin-dialog = "2.0"
tauri-plugin-fs = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
```

## Testing Strategy

### Build Testing
- **Development builds**: `cargo tauri dev` works correctly
- **Production builds**: `cargo tauri build` creates proper installers
- **Cross-platform**: Test on Windows, macOS, Linux
- **Performance**: Measure build times and optimization

### Functionality Testing
- **Core features**: All BCS functionality works
- **File operations**: Dialog and filesystem access
- **Security**: Capabilities properly restrict access
- **Integration**: Frontend-backend communication works

### Regression Testing
- **Existing features**: No functionality lost
- **User experience**: Interface remains responsive
- **Performance**: No performance degradation
- **Compatibility**: Works on target platforms

## Dependencies

### Prerequisites
- Understanding of Tauri v2 architecture
- Knowledge of current application functionality
- Access to multiple platforms for testing

### Related Tickets
- **BUILD-CONFIG-1**: Configuration file modernization
- **BUILD-1.1**: Trunk optimization (should be completed first)
- **BUILD-1.3**: Production pipeline setup

### Blocks
- This ticket blocks BUILD-1.3 (production pipeline)
- Must be completed before major feature additions

## Risk Assessment

### Medium Risk
- Configuration changes could break existing functionality
- Build system changes affect entire development workflow
- Security model changes could restrict needed functionality

### Mitigation Strategies
- **Incremental changes**: Apply updates step by step
- **Backup configurations**: Keep working configurations as backup
- **Thorough testing**: Test each change before proceeding
- **Rollback plan**: Document how to revert changes if needed

## Success Metrics

- **Build Performance**: Faster build times for desktop application
- **Configuration Clarity**: Single source of truth for Tauri config
- **Security**: Modern capabilities-based security implemented
- **Developer Experience**: Smoother development workflow
- **Production Quality**: Optimized production builds

## Notes

- This modernization is essential for long-term maintainability
- May unlock access to newer Tauri v2 features
- Should improve both development and production workflows
- Changes should be well-documented for team knowledge

---
*Last updated: 2025-06-20*
