use leptos::*;

use wasm_bindgen_futures::spawn_local;
use serde::Serialize;
use crate::invoke;
use serde_wasm_bindgen;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
struct CodebaseAnalysisRequest {
    path: String,
}
#[component]
pub fn CodeBaseAnalysis() -> impl IntoView {
    let (path, set_path) = create_signal(String::new());
    let (result, set_result) = create_signal(None::<String>);
    let (error, set_error) = create_signal(None::<String>);

    let analyze = move || {
        let path_val = path.get();
        if path_val.trim().is_empty() {
            set_error.set(Some("Please enter a codebase path.".to_string()));
            return;
        }
        set_error.set(None);
        let request = CodebaseAnalysisRequest { path: path_val };
        let args = serde_wasm_bindgen::to_value(&serde_json::json!({ "request": request })).unwrap();
        spawn_local(async move {
            let resp = invoke("analyze_codebase", args).await;
            // Try to parse as string or error
            if let Some(s) = resp.as_string() {
                set_result.set(Some(s));
            } else {
                set_error.set(Some(format!("Unexpected response: {:?}", resp)));
            }
        });
    };

    view! {
        <div class="tab-content-inner">
            <h4>"Code Base Analysis"</h4>
            <div class="input-group">
                <input
                    type="text"
                    placeholder="Enter codebase path..."
                    prop:value=path
                    on:input=move |ev| set_path.set(event_target_value(&ev))
                />
                <button on:click=move |_| analyze() >"Analyze"</button>
            </div>
            <Show when=move || error.get().is_some()>
                <div class="error-msg">{error.get().unwrap_or_default()}</div>
            </Show>
            <Show when=move || result.get().is_some()>
                <div class="result-msg">{result.get().unwrap_or_default()}</div>
            </Show>
        </div>
    }
}
