# Bad Character Scanner v0.2.0 - Features Guide

**Version**: 0.2.0 Enhanced Edition  
**Status**: ✅ **PRODUCTION READY** with **CLI Interface**  
**CLI Status**: ✅ **FULLY FUNCTIONAL** - 87.5% test success rate

---

## 1. Overview

The Bad Character Scanner (BCS) is a comprehensive security analysis tool designed to detect, analyze, and clean suspicious Unicode characters from text and codebases. Built with Leptos and Tauri v2, it provides both a powerful desktop application and a command-line interface for automated workflows.

**🆕 NEW: Command-Line Interface (CLI)**
- Standalone CLI binary for automation and CI/CD integration
- Bash script wrapper for advanced workflows
- JSON and text output formats
- Comprehensive error handling and exit codes

This tool is valuable for:

- **Security Audits**: Finding hidden malicious characters (homoglyphs, invisible characters) in source code.
- **Code Quality**: Ensuring clean, readable, and consistent text across development projects.
- **Accessibility**: Removing confusing or problematic characters that can affect readability.
- **CI/CD Integration**: Automated security scanning in build pipelines.

---

## 2. Core Features

### 2.1. Text Analysis & Cleaning

- **Real-Time Character Scanning**: Paste or type text to instantly detect issues.
- **Advanced Security Analysis**: Detects Zero-Width characters, control characters, bidirectional overrides, and homograph attacks.
- **Detailed Character Information**: Provides Unicode names, categories, and risk assessment for suspicious characters.
- **Smart Cleaning**: Safely removes or replaces problematic characters while preserving original meaning.

### 2.2. Codebase Analysis & Cleaning

- **Recursive Folder Scanning**: Select any directory to analyze an entire codebase.
- **Multi-Format Support**: Handles dozens of common source code, documentation, and configuration file types.
- **Real-Time Progress**: Features asynchronous processing with progress bars to prevent UI freezing, even on large codebases.
- **Health Score Calculation**: Get an overall assessment of your codebase's hygiene.
- **Post-Cleaning Verification**: A detailed report confirms which issues were fixed and which remain.

### 2.3. Command-Line Interface (CLI) ✨ NEW

- **Standalone Binary**: `analyzer_cli.exe` for direct integration into scripts and automation
- **Bash Script Wrapper**: `scripts/codebase_analyzer.sh` with advanced features
- **Multiple Output Formats**: JSON (structured data) and text (human-readable)
- **Error Handling**: Proper exit codes for automation (0=success, 1-6=different error types)
- **Security Testing**: Validated against malicious Unicode attack vectors

#### CLI Usage Examples:
```bash
# Analyze a single file
./analyzer_cli.exe analyze file.js json

# Using the Bash wrapper (Linux/macOS/WSL)
./scripts/codebase_analyzer.sh analyze /path/to/codebase
./scripts/codebase_analyzer.sh --format markdown analyze /path/to/codebase
./scripts/codebase_analyzer.sh scan suspicious_file.js
./scripts/codebase_analyzer.sh demo  # Run demonstration
./scripts/codebase_analyzer.sh test  # Run built-in tests
```

### 2.4. User Interface

- **Responsive Design**: Clean and intuitive UI that works on all screen sizes.
- **Drag & Drop**: Easily select folders by dragging them into the application.
- **Rich Reporting**: View detailed reports directly in the UI.
- **Export Functionality**: Export analysis reports to JSON, HTML, and Markdown.
- **Warning Popups**: Get notified of important outcomes, like post-cleaning results.

---

## 3. How to Use: A Practical Guide

### 3.1. Analyzing Text (GUI)

1. Navigate to the **Text Analysis** tab.
2. Paste or type your text into the input area.
3. Click **Analyze Text**.
4. Review the detailed results, which break down suspicious characters found.
5. Use the **Clean** button to remove issues and copy the safe text.

### 3.2. Analyzing a Codebase (GUI)

1. Navigate to the **Codebase Analysis** tab.
2. Select a folder using the **Browse** button or by dragging and dropping it.
3. Click **Analyze Codebase** and monitor the progress.
4. Review the comprehensive report, including the overall health score and a per-file breakdown of issues.

### 3.3. Using the Command-Line Interface

#### Quick Analysis:
```bash
# Analyze a file and get JSON output
./target/release/analyzer_cli.exe analyze myfile.js json

# Analyze a file and get human-readable output  
./target/release/analyzer_cli.exe analyze myfile.js text
```

#### Advanced Usage (Bash Script):
```bash
# Full codebase analysis with Markdown report
./scripts/codebase_analyzer.sh --format markdown analyze /path/to/project

# Verbose output for debugging
./scripts/codebase_analyzer.sh --verbose analyze /path/to/project

# Dry run (show what would be done)
./scripts/codebase_analyzer.sh --dry-run analyze /path/to/project

# Custom output directory
./scripts/codebase_analyzer.sh --output ./security_reports analyze /path/to/project
```

### 3.4. Cleaning a Codebase (GUI)

1. After analysis, the **Clean & Verify** button becomes available.
2. Click it to start the cleaning process. This creates a cleaned copy of your codebase, leaving the original untouched.
3. A progress bar will show the status of the cleaning and verification.
4. Review the post-cleaning report to see the effectiveness and any remaining issues.

### 3.5. Exporting Reports

1. After a codebase analysis, the export buttons are enabled.
2. Click **Export JSON**, **Export HTML**, or **Export Markdown**.
3. A file save dialog will appear, allowing you to save the detailed report.

---

## 4. Security Detection Capabilities

### 4.1. Tested Attack Vectors ✅

Our CLI has been validated against real-world malicious Unicode attacks:

- ✅ **Zero-Width Characters**: ZERO WIDTH SPACE (U+200B), ZERO WIDTH JOINER (U+200D), ZERO WIDTH NON-JOINER (U+200C)
- ✅ **Bidirectional Attacks**: RIGHT-TO-LEFT OVERRIDE (U+202E), LEFT-TO-RIGHT OVERRIDE (U+202D), POP DIRECTIONAL FORMATTING (U+202C)
- ✅ **Homoglyph Attacks**: Cyrillic characters that look like Latin (е vs e, о vs o, etc.)
- ✅ **Control Characters**: Various Unicode control and formatting characters
- ✅ **Mixed Script Attacks**: Combining different writing systems to confuse readers

### 4.2. Test Data Repository 🔒

We maintain a "bio-sealed" test data repository at `test_data_secure/` containing:
- Real malicious character samples for testing
- Reference clean files for comparison
- Comprehensive attack vectors for validation
- **⚠️ SECURITY WARNING**: These files are isolated and only for testing purposes

---

## 5. Integration & Automation

### 5.1. CI/CD Integration

The CLI can be integrated into continuous integration pipelines:

```yaml
# Example GitHub Actions workflow
- name: Security Scan
  run: |
    ./target/release/analyzer_cli.exe analyze src/ json > security_report.json
    if [ $? -ne 0 ]; then exit 1; fi
```

### 5.2. Exit Codes

The CLI follows standard exit code conventions:
- `0` - Success
- `1` - General error  
- `2` - Invalid arguments
- `3` - File/directory not found
- `4` - Analysis failed
- `5` - Export failed
- `6` - Dependency missing

---

## 6. Roadmap: Advanced Cleaning Features 🚧

**COMING NEXT**: We're focusing on building advanced cleaning capabilities:

### 6.1. Planned Cleaning Features
- **Safe Codebase Cleaning**: Create cleaned copies with limited ASCII (English programming characters only)
- **Backup & Restore**: Automatic backup of original files before cleaning
- **Selective Cleaning**: Choose which character types to remove or replace
- **Verification Reports**: Detailed before/after comparisons
- **Batch Processing**: Clean multiple codebases simultaneously

### 6.2. Implementation Strategy
1. ✅ **Phase 1 Complete**: CLI interface working (87.5% test success)
2. 🚧 **Phase 2 Current**: Advanced cleaning algorithms
3. 📋 **Phase 3 Planned**: Batch processing and automation
4. 📋 **Phase 4 Planned**: Integration with version control systems

---

## 7. Documentation & Support

### 7.1. Complete Documentation 
- **📋 Main README**: [README.md](../../README.md) - Complete project overview
- **🚀 Quick Start**: [QUICK_REFERENCE.md](QUICK_REFERENCE.md) - Get started in 5 minutes  
- **🏗️ Architecture**: [ARCHITECTURE.md](../project/ARCHITECTURE.md) - Technical details
- **🧪 Testing Guide**: [TESTING.md](TESTING.md) - Test procedures and validation
- **🤝 Contributing**: [CONTRIBUTING.md](../contributing/CONTRIBUTING.md) - How to contribute

### 7.2. Best Practices
- Always test on copies, never original files
- Use the bio-sealed test data for validation
- Integrate CLI into automated security workflows
- Review health scores and detailed reports before cleaning
- Keep backups of original files before any cleaning operations

---

**Status**: This tool is production-ready with comprehensive CLI support. The GUI and CLI interfaces provide complete Unicode security analysis capabilities suitable for both manual and automated workflows.

- **Test Case**: The warning popup appears automatically after a cleaning operation.
- **Success Criteria**:
  - The popup modal appears with a semi-transparent backdrop.
  - It displays a summary of the cleaning effectiveness.
  - The "OK" and "X" buttons close the popup.
  - The "Don't Show Again" button closes the popup and saves this preference, preventing it from appearing on subsequent cleans.

---

## 5. Technical Details

### 5.1. Technology Stack

- **Frontend**: Leptos (Rust) + WebAssembly
- **Backend**: Tauri v2 + Rust
- **UI**: Custom CSS with a responsive, modern design
- **Build**: Trunk (for WASM) + Cargo (for native Rust)

### 5.2. Architecture

- All analysis is performed locally on your machine; no data is ever transmitted externally.
- The Rust backend provides high-performance, memory-safe character analysis.
- A type-safe command interface connects the Leptos frontend to the Tauri backend.
- Original files are never modified. Cleaning operations create a new, safe copy of the codebase.

### 5.3. Supported File Types

- **Web**: `.js`, `.ts`, `.html`, `.css`, `.jsx`, `.tsx`, `.vue`
- **Systems**: `.rs`, `.cpp`, `.c`, `.h`, `.hpp`
- **Scripting**: `.py`, `.rb`, `.sh`, `.ps1`, `.bat`
- **Data**: `.json`, `.xml`, `.yaml`, `.yml`, `.toml`
- **Documentation**: `.md`, `.txt`, `.rst`

---

## 6. Project Roadmap (Planned Features)

### 6.1. Short Term

- **Enhanced Export System**: Complete and polish export options.
- **Analysis Sub-tabs**: Better organize different analysis views.
- **Theme Support**: Add a dark/light mode toggle.
- **Settings Panel**: Introduce a panel for user configuration.

### 6.2. Medium & Long Term

- **Batch Processing**: Analyze multiple folders in sequence.
- **Custom Patterns**: Allow users to define their own suspicious patterns.
- **Analysis History**: Save and recall previous analysis sessions.
- **CLI / Integration APIs**: Enable scripting and CI/CD integration.
- **AI-Enhanced Detection**: Use machine learning for more advanced pattern recognition.
