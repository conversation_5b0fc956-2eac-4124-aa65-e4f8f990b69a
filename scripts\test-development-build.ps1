#!/usr/bin/env powershell
# Comprehensive development testing script for Bad Character Scanner
# Tests build, logging, error handling, and basic functionality

param(
    [switch]$SkipBuild,
    [switch]$SkipFrontend,
    [switch]$SkipBackend,
    [switch]$Verbose
)

Write-Host "`n=== BAD CHARACTER SCANNER DEVELOPMENT TEST ===" -ForegroundColor Cyan
Write-Host "Testing build, logging, and error handling systems" -ForegroundColor Gray

$project_root = $PSScriptRoot | Split-Path -Parent
$src_tauri = Join-Path $project_root "src-tauri"
$test_results = @()

function Test-Step {
    param(
        [string]$Name,
        [scriptblock]$Test,
        [switch]$ContinueOnError
    )
    
    Write-Host "`n[TEST] $Name" -ForegroundColor Yellow
    $start_time = Get-Date
    
    try {
        $result = & $Test
        $duration = (Get-Date) - $start_time
        Write-Host "[PASS] $Name (${duration}ms)" -ForegroundColor Green
        $script:test_results += @{
            Name = $Name
            Status = "PASS"
            Duration = $duration
            Result = $result
        }
        return $true
    } catch {
        $duration = (Get-Date) - $start_time
        Write-Host "[FAIL] $Name (${duration}ms)" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        $script:test_results += @{
            Name = $Name
            Status = "FAIL"
            Duration = $duration
            Error = $_.ToString()
        }
        if (-not $ContinueOnError) {
            throw
        }
        return $false
    }
}

# 1. Check Prerequisites
Test-Step "Check Rust Installation" {
    $rust_version = cargo --version
    if (-not $rust_version) {
        throw "Rust is not installed"
    }
    Write-Host "  Rust: $rust_version" -ForegroundColor Gray
}

Test-Step "Check Node.js Installation" {
    $node_version = node --version
    if (-not $node_version) {
        throw "Node.js is not installed"
    }
    Write-Host "  Node: $node_version" -ForegroundColor Gray
}

Test-Step "Check Tauri CLI" {
    $tauri_version = cargo tauri --version
    if (-not $tauri_version) {
        Write-Host "  Installing Tauri CLI..." -ForegroundColor Yellow
        cargo install tauri-cli
    }
    Write-Host "  Tauri CLI: $tauri_version" -ForegroundColor Gray
}

# 2. Backend Tests
if (-not $SkipBackend) {
    Write-Host "`n=== BACKEND TESTS ===" -ForegroundColor Cyan
    
    Push-Location $src_tauri
    
    Test-Step "Backend Compilation Check" {
        cargo check 2>&1 | Out-String
        if ($LASTEXITCODE -ne 0) {
            throw "Backend compilation failed"
        }
    }
    
    Test-Step "Backend Unit Tests" {
        cargo test --lib 2>&1 | Out-String
        if ($LASTEXITCODE -ne 0) {
            throw "Backend tests failed"
        }
    }
    
    Test-Step "Check Error Module" {
        if (-not (Test-Path "src/error.rs")) {
            throw "Error module not found"
        }
        Write-Host "  Error module exists" -ForegroundColor Gray
    }
    
    Test-Step "Check Logging Module" {
        if (-not (Test-Path "src/logging.rs")) {
            throw "Logging module not found"
        }
        Write-Host "  Logging module exists" -ForegroundColor Gray
    }
    
    Pop-Location
}

# 3. Frontend Tests
if (-not $SkipFrontend) {
    Write-Host "`n=== FRONTEND TESTS ===" -ForegroundColor Cyan
    
    Test-Step "Frontend Dependencies" {
        if (-not (Test-Path "node_modules")) {
            Write-Host "  Installing dependencies..." -ForegroundColor Yellow
            npm install
        }
        Write-Host "  Dependencies installed" -ForegroundColor Gray
    }
    
    Test-Step "WASM Target Check" {
        $targets = rustup target list --installed
        if ($targets -notcontains "wasm32-unknown-unknown") {
            Write-Host "  Installing WASM target..." -ForegroundColor Yellow
            rustup target add wasm32-unknown-unknown
        }
        Write-Host "  WASM target available" -ForegroundColor Gray
    }
    
    Test-Step "Frontend Build Test" {
        trunk build 2>&1 | Select-String -Pattern "error" -NotMatch | Out-String
        if ($LASTEXITCODE -ne 0) {
            throw "Frontend build failed"
        }
    }
}

# 4. Full Application Build
if (-not $SkipBuild) {
    Write-Host "`n=== FULL APPLICATION BUILD ===" -ForegroundColor Cyan
    
    Test-Step "Tauri Development Build" {
        # Just build, don't run
        cargo tauri build --debug 2>&1 | Select-String -Pattern "error|Error" -NotMatch | Out-String
        if ($LASTEXITCODE -ne 0) {
            throw "Tauri build failed"
        }
    }
}

# 5. Test Our Own Bad Character Scanner
Write-Host "`n=== SELF-SCAN TEST ===" -ForegroundColor Cyan

Test-Step "Scan Project for Bad Characters" -ContinueOnError {
    $analyzer_cli = Join-Path $src_tauri "target\debug\analyzer_cli.exe"
    if (Test-Path $analyzer_cli) {
        Write-Host "  Running self-scan..." -ForegroundColor Gray
        & $analyzer_cli $project_root --output-format json | Out-String
    } else {
        Write-Host "  CLI not built, building now..." -ForegroundColor Yellow
        Push-Location $src_tauri
        cargo build --bin analyzer_cli
        Pop-Location
        & $analyzer_cli $project_root --output-format json | Out-String
    }
}

# 6. Check Log Files
Test-Step "Verify Logging System" -ContinueOnError {
    $log_dir = Join-Path $env:LOCALAPPDATA "bad-character-scanner\logs"
    if (Test-Path $log_dir) {
        $log_files = Get-ChildItem $log_dir -Filter "*.log" | Sort-Object LastWriteTime -Descending
        if ($log_files) {
            Write-Host "  Found $($log_files.Count) log file(s)" -ForegroundColor Gray
            $latest_log = $log_files[0]
            Write-Host "  Latest: $($latest_log.Name)" -ForegroundColor Gray
            
            # Check if log contains expected entries
            $log_content = Get-Content $latest_log.FullName -Tail 10
            if ($log_content -match "Starting Bad Character Scanner") {
                Write-Host "  Log initialization confirmed" -ForegroundColor Green
            }
        } else {
            Write-Host "  No log files found yet" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  Log directory not created yet" -ForegroundColor Yellow
    }
}

# 7. Summary Report
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
$passed = ($test_results | Where-Object { $_.Status -eq "PASS" }).Count
$failed = ($test_results | Where-Object { $_.Status -eq "FAIL" }).Count
$total = $test_results.Count

Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor $(if ($failed -gt 0) { "Red" } else { "Green" })

if ($Verbose) {
    Write-Host "`nDetailed Results:" -ForegroundColor Yellow
    foreach ($result in $test_results) {
        $color = if ($result.Status -eq "PASS") { "Green" } else { "Red" }
        Write-Host "  [$($result.Status)] $($result.Name) - $($result.Duration.TotalMilliseconds)ms" -ForegroundColor $color
        if ($result.Error) {
            Write-Host "    Error: $($result.Error)" -ForegroundColor Red
        }
    }
}

# 8. Next Steps
if ($failed -eq 0) {
    Write-Host "`n[SUCCESS] All tests passed! You can now run:" -ForegroundColor Green
    Write-Host "  cargo tauri dev" -ForegroundColor White
    Write-Host "to start the development server." -ForegroundColor Gray
} else {
    Write-Host "`n[WARNING] Some tests failed. Please fix the issues before running the app." -ForegroundColor Yellow
}

Write-Host "`nLog files location: %LOCALAPPDATA%\bad-character-scanner\logs" -ForegroundColor Gray
Write-Host "Fighting for accessibility, one test at a time!" -ForegroundColor Cyan