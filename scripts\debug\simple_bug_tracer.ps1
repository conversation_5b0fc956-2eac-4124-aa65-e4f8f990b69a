# Simple Advanced Security Analysis Bug Tracer
# By <PERSON> - 2025

Write-Host "Advanced Security Analysis Bug Tracer" -ForegroundColor Cyan
Write-Host "By <PERSON> - 2025" -ForegroundColor Green
Write-Host ""

Write-Host "=== CHECKING CRITICAL FILES ===" -ForegroundColor Magenta

$criticalFiles = @(
    "src\components\codebase\ui\results.rs",
    "src-tauri\src\analysis\codebase_analyzer.rs", 
    "src-tauri\src\main_module.rs"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "✅ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== CHECKING JSON PARSING FIX ===" -ForegroundColor Magenta

$resultsFile = "src\components\codebase\ui\results.rs"
if (Test-Path $resultsFile) {
    $content = Get-Content $resultsFile -Raw
    
    if ($content -match "analysis_metadata") {
        Write-Host "✅ Found analysis_metadata field access" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing analysis_metadata field access" -ForegroundColor Red
    }
    
    if ($content -match "risk_assessment") {
        Write-Host "✅ Found risk_assessment field access" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing risk_assessment field access" -ForegroundColor Red  
    }
    
    if ($content -match "homoglyph_threats") {
        Write-Host "✅ Found homoglyph_threats field access" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing homoglyph_threats field access" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== TESTING WITH SAMPLE DATA ===" -ForegroundColor Magenta

$testFiles = @("analysis_results_utf8.json", "analysis_results.json")
$foundTestFile = $null

foreach ($testFile in $testFiles) {
    if (Test-Path $testFile) {
        $foundTestFile = $testFile
        break
    }
}

if ($foundTestFile) {
    Write-Host "Testing with: $foundTestFile" -ForegroundColor Cyan
    
    try {
        $testData = Get-Content $foundTestFile -Raw | ConvertFrom-Json
        
        if ($testData.analysis_metadata) {
            Write-Host "✅ Test data has analysis_metadata" -ForegroundColor Green
        }
        
        if ($testData.risk_assessment) {
            Write-Host "✅ Test data has risk_assessment" -ForegroundColor Green
            $riskScore = $testData.risk_assessment.overall_risk_score
            Write-Host "   Risk Score: $riskScore" -ForegroundColor White
        }
        
        if ($testData.executive_summary) {
            Write-Host "✅ Test data has executive_summary" -ForegroundColor Green
            $totalThreats = $testData.executive_summary.total_threats
            Write-Host "   Total Threats: $totalThreats" -ForegroundColor White
            
            if ($totalThreats -gt 0) {
                Write-Host "🚨 CRITICAL: $totalThreats threats found but UI may show 0!" -ForegroundColor Red
            }
        }
        
    } catch {
        Write-Host "❌ Error parsing test data: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ No test data found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== COMPILATION CHECK ===" -ForegroundColor Magenta

try {
    $result = cargo check 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Project compiles successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Compilation errors found" -ForegroundColor Red
        Write-Host "First few errors:" -ForegroundColor Yellow
        $result | Select-Object -First 5 | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    }
} catch {
    Write-Host "⚠️ Could not run cargo check" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Magenta
Write-Host "🔍 Next steps:" -ForegroundColor Cyan
Write-Host "1. Run the app and test Advanced Security Analysis" -ForegroundColor White
Write-Host "2. Check browser dev tools for JSON response structure" -ForegroundColor White
Write-Host "3. Verify UI shows non-zero threats for malicious files" -ForegroundColor White
Write-Host ""
Write-Host "📋 Critical fix applied:" -ForegroundColor Green
Write-Host "- Updated results.rs to use ComprehensiveAnalysisResult structure" -ForegroundColor White
Write-Host "- Fixed JSON field paths for analysis_metadata and risk_assessment" -ForegroundColor White
Write-Host "- Added proper threat counting from separate arrays" -ForegroundColor White
Write-Host ""
Write-Host "Bug tracer complete! By John Shoy - 2025" -ForegroundColor Green
