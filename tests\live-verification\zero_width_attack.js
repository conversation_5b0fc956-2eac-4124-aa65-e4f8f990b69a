// Test file with Zero Width characters (Invisible attack)
function test‌Function() {
    const data = "hello​world";  // Contains ZERO WIDTH SPACE
    return data;
}

// This file contains 3 types of zero-width characters:
// 1. ZERO WIDTH NON-JOINER (U+200C) after "test"
// 2. <PERSON>ERO WIDTH SPACE (U+200B) between "hello" and "world" 
// 3. ZERO WIDTH JOINER (U+200D) - will be added below

const malicious‍Variable = "stealth";  // Contains ZERO WIDTH JOINER

console.log("File size should be larger than visible content suggests");
