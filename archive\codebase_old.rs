use leptos::*;
use serde::{Serialize, Deserialize};
use serde_json;
use wasm_bindgen::prelude::*;
use wasm_bindgen::closure::Closure;
use web_sys::DragEvent;
use js_sys;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct BasicProgress {
    current: u32,
    total: u32,
    percentage: f32,
    current_file: String,
    status: String,
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
    
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "event"])]
    async fn listen(event: &str, handler: &js_sys::Function) -> JsValue;
    
    #[wasm_bindgen(js_namespace = ["window"], catch)]
    fn check_tauri() -> Result<JsValue, JsValue>;
}

// Check if we're running in Tauri context
fn is_tauri_available() -> bool {
    web_sys::window()
        .and_then(|w| js_sys::Reflect::get(&w, &"__TAURI__".into()).ok())
        .map(|v| !v.is_undefined() && !v.is_null())
        .unwrap_or(false)
}

#[component]
pub fn CodebaseComponent() -> impl IntoView {
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    let (analysis_result, set_analysis_result) = create_signal(None::<String>);
    let (drag_over, set_drag_over) = create_signal(false);
    let (selected_path, set_selected_path) = create_signal(String::new());
    let (progress, set_progress) = create_signal(None::<BasicProgress>);

    // Set up progress event listener
    create_effect(move |_| {
        spawn_local(async move {
            let progress_handler = Closure::wrap(Box::new(move |event: JsValue| {
                if let Ok(progress_data) = serde_wasm_bindgen::from_value::<BasicProgress>(event) {
                    set_progress.set(Some(progress_data));
                }
            }) as Box<dyn FnMut(JsValue)>);
            
            let _ = listen("analysis-progress", progress_handler.as_ref().unchecked_ref()).await;
            progress_handler.forget(); // Keep the closure alive
        });
    });    let analyze_codebase = move |path: String| {
        set_is_analyzing.set(true);
        set_progress.set(None); // Reset progress
        set_selected_path.set(path.clone());
        
        spawn_local(async move {
            if !is_tauri_available() {
                // Web context - show demo data
                let demo_result = r#"{
                    "analysis_time_ms": 1250,
                    "total_files": 45,
                    "files_with_issues": 2,
                    "total_suspicious_chars": 12,
                    "health_score": 97.3,
                    "file_details": [
                        {
                            "file_path": "demo/example.js",
                            "file_size": 2048,
                            "file_type": "js",
                            "suspicious_characters": 5,
                            "total_characters": 2048,
                            "issues": [
                                "Suspicious character '‍' at position 156",
                                "Suspicious character '؍' at position 892"
                            ],
                            "analysis_status": "success",
                            "encoding": "UTF-8"
                        }
                    ]
                }"#;
                
                // Simulate analysis delay
                gloo_timers::future::sleep(std::time::Duration::from_millis(1500)).await;
                set_analysis_result.set(Some(demo_result.to_string()));
                set_is_analyzing.set(false);
                return;
            }              // Tauri context - use real analysis
            // Pass the path directly as Tauri expects
            let tauri_args = serde_json::json!({
                "path": path
            });
            
            match serde_wasm_bindgen::to_value(&tauri_args) {
                Ok(args_js) => {
                    match invoke("analyze_codebase_advanced", args_js).await {
                        result if !result.is_undefined() => {
                            match js_sys::JSON::stringify(&result) {
                                Ok(json_string) => {
                                    let formatted = json_string.as_string().unwrap_or_else(|| "Failed to format result".to_string());
                                    match serde_json::from_str::<serde_json::Value>(&formatted) {
                                        Ok(json_value) => {
                                            match serde_json::to_string_pretty(&json_value) {
                                                Ok(pretty) => set_analysis_result.set(Some(pretty)),
                                                Err(_) => set_analysis_result.set(Some(formatted)),
                                            }
                                        }
                                        Err(_) => set_analysis_result.set(Some(formatted)),
                                    }
                                }
                                Err(e) => set_analysis_result.set(Some(format!("Error: {:?}", e))),
                            }
                        }
                        _ => set_analysis_result.set(Some("No result returned".to_string())),
                    }
                }
                Err(e) => set_analysis_result.set(Some(format!("Serialization error: {:?}", e))),
            }
            set_is_analyzing.set(false);
            set_progress.set(None); // Clear progress when done
        });
    };    let select_folder = move |_| {
        spawn_local(async move {
            if !is_tauri_available() {
                // Web context - use demo path
                analyze_codebase("Demo Project (Web Mode)".to_string());
                return;
            }
            
            // Tauri context - use real folder selection
            let result = invoke("select_folder", JsValue::NULL).await;
            if let Some(path) = result.as_string() {
                if !path.is_empty() {
                    analyze_codebase(path);
                }
            }
        });
    };

    let handle_drop = move |ev: DragEvent| {
        ev.prevent_default();
        ev.stop_propagation();
        set_drag_over.set(false);
        
        // Note: Web security prevents direct file system access
        // We'll need to use Tauri's file dialog instead
        web_sys::console::log_1(&"Drop event detected - use folder selection button instead".into());
    };

    let handle_drag_over = move |ev: DragEvent| {
        ev.prevent_default();
        ev.stop_propagation();
        set_drag_over.set(true);
    };

    let handle_drag_leave = move |ev: DragEvent| {
        ev.prevent_default();
        ev.stop_propagation();
        set_drag_over.set(false);
    };    view! {
        <div class="space-y-4">
            // Environment indicator
            {move || {
                if !is_tauri_available() {
                    view! {
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg
                                        class="h-5 w-5 text-blue-400"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">
                                        "Web Demo Mode"
                                    </h3>
                                    <div class="mt-1 text-sm text-blue-700">
                                        "You're using the web version with demo data. Download the desktop app for full functionality."
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                        .into_view()
                } else {
                    view! {}.into_view()
                }
            }}
            <div
                class=move || {
                    format!(
                        "border-2 border-dashed rounded-lg p-8 text-center transition-colors {}",
                        if drag_over.get() {
                            "border-blue-500 bg-blue-50"
                        } else {
                            "border-gray-300"
                        },
                    )
                }
                on:drop=handle_drop
                on:dragover=handle_drag_over
                on:dragleave=handle_drag_leave
            >
                <svg
                    class="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                </svg>
                <p class="mt-2 text-sm text-gray-600">
                    "Drag and drop a folder here, or click to select"
                </p>
                <button
                    class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    on:click=select_folder
                    disabled=move || is_analyzing.get()
                >
                    "Select Folder"
                </button>
            </div>
            {move || {
                (!selected_path.get().is_empty())
                    .then(|| {
                        view! {
                            <div class="p-3 bg-gray-100 rounded-md">
                                <span class="text-sm text-gray-600">"Selected: "</span>
                                <span class="text-sm font-mono">{selected_path.get()}</span>
                            </div>
                        }
                    })
            }}
            {move || {
                is_analyzing
                    .get()
                    .then(|| {
                        view! {
                            <div class="p-4 bg-blue-50 rounded-md border border-blue-200">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-blue-800">
                                        "Analyzing Codebase"
                                    </h3>
                                    {move || {
                                        progress
                                            .get()
                                            .as_ref()
                                            .map(|p| {
                                                view! {
                                                    <span class="text-sm text-blue-600">
                                                        {format!("{}/{} files", p.current, p.total)}
                                                    </span>
                                                }
                                            })
                                    }}
                                </div>

                                <div class="mb-3">
                                    <div class="w-full bg-blue-200 rounded-full h-2.5">
                                        <div
                                            class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                                            style=move || {
                                                let percentage = progress
                                                    .get()
                                                    .as_ref()
                                                    .map(|p| p.percentage)
                                                    .unwrap_or(0.0);
                                                format!("width: {}%", percentage)
                                            }
                                        ></div>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                        <span class="text-blue-700">
                                            {move || {
                                                progress
                                                    .get()
                                                    .as_ref()
                                                    .map(|p| p.status.clone())
                                                    .unwrap_or_else(|| "Starting...".to_string())
                                            }}
                                        </span>
                                    </div>

                                    {move || {
                                        progress
                                            .get()
                                            .as_ref()
                                            .map(|p| {
                                                view! {
                                                    <span class="text-blue-600 font-mono text-xs">
                                                        {format!("{:.1}%", p.percentage)}
                                                    </span>
                                                }
                                            })
                                    }}
                                </div>

                                {move || {
                                    progress
                                        .get()
                                        .as_ref()
                                        .and_then(|p| {
                                            if !p.current_file.is_empty()
                                                && p.current_file != "Starting analysis..."
                                            {
                                                Some(
                                                    view! {
                                                        <div class="mt-2 p-2 bg-white rounded border">
                                                            <div class="text-xs text-gray-600">
                                                                "Currently analyzing:"
                                                            </div>
                                                            <div
                                                                class="text-sm font-mono text-gray-800 truncate"
                                                                title=p.current_file.clone()
                                                            >
                                                                {p.current_file.clone()}
                                                            </div>
                                                        </div>
                                                    },
                                                )
                                            } else {
                                                Some(
                                                    view! {
                                                        <div class="mt-2 text-center text-sm text-blue-600">
                                                            "Preparing analysis..."
                                                        </div>
                                                    },
                                                )
                                            }
                                        })
                                }}
                            </div>
                        }
                    })
            }}
            {move || {
                analysis_result
                    .get()
                    .map(|result| {
                        match serde_json::from_str::<serde_json::Value>(&result) {
                            Ok(json) => {
                                let analysis_id = json
                                    .get("analysis_id")
                                    .and_then(|v| v.as_str())
                                    .map(|s| s.to_string())
                                    .unwrap_or_else(|| "Unknown".to_string());
                                let total_files = json
                                    .get("total_files")
                                    .and_then(|v| v.as_u64())
                                    .unwrap_or(0);
                                let files_analyzed = json
                                    .get("files_analyzed")
                                    .and_then(|v| v.as_u64())
                                    .unwrap_or(0);
                                let overall_risk_score = json
                                    .get("overall_risk_score")
                                    .and_then(|v| v.as_f64())
                                    .unwrap_or(0.0);
                                let homoglyph_threats = json
                                    .get("homoglyph_threats")
                                    .and_then(|v| v.as_array())
                                    .map(|arr| arr.len())
                                    .unwrap_or(0);
                                let pattern_threats = json
                                    .get("pattern_threats")
                                    .and_then(|v| v.as_array())
                                    .map(|arr| arr.len())
                                    .unwrap_or(0);
                                let security_threats = json
                                    .get("security_threats")
                                    .and_then(|v| v.as_array())
                                    .map(|arr| arr.len())
                                    .unwrap_or(0);
                                let unicode_threats = json
                                    .get("unicode_threats")
                                    .and_then(|v| v.as_array())
                                    .map(|arr| arr.len())
                                    .unwrap_or(0);
                                let total_threats = homoglyph_threats + pattern_threats
                                    + security_threats + unicode_threats;
                                let risk_level = if overall_risk_score >= 80.0 {
                                    ("Critical", "text-red-800", "bg-red-100")
                                } else if overall_risk_score >= 60.0 {
                                    ("High", "text-red-600", "bg-red-50")
                                } else if overall_risk_score >= 40.0 {
                                    ("Medium", "text-yellow-600", "bg-yellow-50")
                                } else if overall_risk_score >= 20.0 {
                                    ("Low", "text-green-600", "bg-green-50")
                                } else {
                                    ("Minimal", "text-green-800", "bg-green-100")
                                };
                                // Extract comprehensive analysis data

                                // Determine risk level and color

                                view! {
                                    <div class="mt-4 space-y-6">
                                        // Header with analysis ID and risk level
                                        <div class="flex justify-between items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">
                                                    "Advanced Security Analysis"
                                                </h3>
                                                <p class="text-sm text-gray-600">
                                                    {"Analysis ID: "}{analysis_id}
                                                </p>
                                            </div>
                                            <div class=format!(
                                                "px-4 py-2 rounded-full text-sm font-bold {} {}",
                                                risk_level.1,
                                                risk_level.2,
                                            )>{format!("{} Risk", risk_level.0)}</div>
                                        </div>

                                        // Summary statistics
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                            <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="text-3xl font-bold text-blue-600">
                                                    {total_files}
                                                </div>
                                                <div class="text-sm text-gray-600">"Total Files"</div>
                                            </div>
                                            <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="text-3xl font-bold text-green-600">
                                                    {files_analyzed}
                                                </div>
                                                <div class="text-sm text-gray-600">"Files Analyzed"</div>
                                            </div>
                                            <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="text-3xl font-bold text-red-600">
                                                    {total_threats}
                                                </div>
                                                <div class="text-sm text-gray-600">"Total Threats"</div>
                                            </div>
                                            <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                                                <div class=format!(
                                                    "text-3xl font-bold {}",
                                                    risk_level.1,
                                                )>{format!("{:.1}", overall_risk_score)}</div>
                                                <div class="text-sm text-gray-600">"Risk Score"</div>
                                            </div>
                                        </div>

                                        // Threat breakdown
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                            <div class="p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-semibold text-gray-700">
                                                        "Homoglyph Attacks"
                                                    </h4>
                                                    <span class="text-2xl">{"🔤"}</span>
                                                </div>
                                                <div class="text-2xl font-bold text-purple-600">
                                                    {homoglyph_threats}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    "Suspicious character substitutions"
                                                </div>
                                            </div>

                                            <div class="p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-semibold text-gray-700">
                                                        "Pattern Threats"
                                                    </h4>
                                                    <span class="text-2xl">{"🔍"}</span>
                                                </div>
                                                <div class="text-2xl font-bold text-orange-600">
                                                    {pattern_threats}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    "Malicious code patterns"
                                                </div>
                                            </div>

                                            <div class="p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-semibold text-gray-700">
                                                        "Security Issues"
                                                    </h4>
                                                    <span class="text-2xl">{"🛡️"}</span>
                                                </div>
                                                <div class="text-2xl font-bold text-red-600">
                                                    {security_threats}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    "Security vulnerabilities"
                                                </div>
                                            </div>

                                            <div class="p-4 bg-white rounded-lg shadow-sm border">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-semibold text-gray-700">
                                                        "Unicode Threats"
                                                    </h4>
                                                    <span class="text-2xl">{"⚠️"}</span>
                                                </div>
                                                <div class="text-2xl font-bold text-indigo-600">
                                                    {unicode_threats}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    "Unicode-based attacks"
                                                </div>
                                            </div>
                                        </div>

                                        // Detailed results (expandable)
                                        <details class="bg-white border rounded-lg">
                                            <summary class="cursor-pointer p-4 font-semibold text-gray-700 hover:bg-gray-50">
                                                "📊 Detailed Analysis Results"
                                            </summary>
                                            <div class="p-4 border-t bg-gray-50">
                                                <pre class="whitespace-pre-wrap text-xs overflow-auto max-h-96 bg-white p-3 rounded border">
                                                    {result}
                                                </pre>
                                            </div>
                                        </details>
                                    </div>
                                }
                            }
                            Err(_) => {
                                view! {
                                    <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                        <h3 class="text-lg font-semibold mb-2">
                                            "Analysis Results"
                                        </h3>
                                        <pre class="whitespace-pre-wrap text-sm">{result}</pre>
                                    </div>
                                }
                            }
                        }
                    })
            }}
        </div>
    }
}