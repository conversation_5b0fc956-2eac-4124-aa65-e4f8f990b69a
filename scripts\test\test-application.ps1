# Bad Character Scanner - Test Script
# This script helps test the Tauri application functionality

Write-Host "=== Bad Character Scanner Test Script ===" -ForegroundColor Cyan
Write-Host ""

# Function to test if cargo tauri is available
function Test-TauriAvailable {
    try {
        $result = cargo tauri --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

# Function to check if the application builds successfully
function Test-Build {
    Write-Host "Testing build..." -ForegroundColor Yellow
    try {
        $buildResult = cargo build 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Build successful!" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Build failed:" -ForegroundColor Red
            Write-Host $buildResult
            return $false
        }
    }
    catch {
        Write-Host "❌ Build error: $_" -ForegroundColor Red
        return $false
    }
}

# Function to start the development server
function Start-DevServer {
    Write-Host "Starting development server..." -ForegroundColor Yellow
    Write-Host "This will open a new window with the application." -ForegroundColor Gray
    Write-Host "The application should start without any 'Command not found' errors." -ForegroundColor Gray
    Write-Host ""
    
    try {
        # Start in background so we can continue
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cargo tauri dev"
        Write-Host "✅ Development server started in new window!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Test the following features:" -ForegroundColor Cyan
        Write-Host "1. Text cleaning - Enter text and click 'Clean Text (Detailed)'" -ForegroundColor White
        Write-Host "2. Folder selection - Click 'Select Folder' button" -ForegroundColor White
        Write-Host "3. Codebase analysis - Select folder and analyze with progress bar" -ForegroundColor White
        Write-Host "4. Recent folders - Check if folder appears in recent list" -ForegroundColor White
        return $true
    }
    catch {
        Write-Host "❌ Failed to start development server: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "Checking environment..." -ForegroundColor Yellow

# Check if we're in the right directory
if (!(Test-Path "Cargo.toml")) {
    Write-Host "❌ Not in the project root directory. Please run this from the Laptos_TaurieV2_HelloWorld folder." -ForegroundColor Red
    exit 1
}

# Check if Tauri is available
if (!(Test-TauriAvailable)) {
    Write-Host "❌ Tauri CLI not found. Please install it with: cargo install tauri-cli" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Environment check passed!" -ForegroundColor Green
Write-Host ""

# Test build
if (!(Test-Build)) {
    Write-Host "❌ Build test failed. Please fix build errors before testing." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Ask user if they want to start the dev server
$startServer = Read-Host "Start development server for testing? (y/n)"
if ($startServer.ToLower() -eq 'y' -or $startServer.ToLower() -eq 'yes') {
    Start-DevServer
}

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Commands registered: 19 total" -ForegroundColor White
Write-Host "- Character analysis: 6 commands" -ForegroundColor Gray
Write-Host "- Codebase operations: 4 commands" -ForegroundColor Gray
Write-Host "- Text processing: 3 commands" -ForegroundColor Gray
Write-Host "- Folder management: 5 commands" -ForegroundColor Gray
Write-Host "- Reporting: 1 command" -ForegroundColor Gray
Write-Host ""
Write-Host "Key features to test:" -ForegroundColor White
Write-Host "✓ Text cleaning (should not show 'command not found')" -ForegroundColor Gray
Write-Host "✓ Folder selection and recent folders" -ForegroundColor Gray
Write-Host "✓ Codebase analysis with progress bars" -ForegroundColor Gray
Write-Host "✓ Real-time progress updates" -ForegroundColor Gray
Write-Host ""
Write-Host "Done! 🎉" -ForegroundColor Green
