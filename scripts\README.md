# 🛠️ Bad Character Scanner - Developer Scripts

This directory contains powerful scripts to help develop, test, and maintain the Bad Character Scanner project.

## 🚀 Quick Start

```powershell
# First time setup
.\setup-dev-environment.ps1

# Check project health
.\doctor.ps1

# Start development
.\dev-workflow.ps1 start
```

## 📋 Script Categories

### 🏥 Health & Diagnostics

#### **doctor.ps1**
Comprehensive health check for the entire project.
```powershell
.\doctor.ps1           # Run health check
.\doctor.ps1 -Fix      # Auto-fix issues
.\doctor.ps1 -Verbose  # Detailed output
```

#### **quick-doctor.ps1**
Fast health check for essential components.
```powershell
.\quick-doctor.ps1     # Quick status check
```

#### **project-status.ps1**
Shows current project state and progress.
```powershell
.\project-status.ps1              # Basic status
.\project-status.ps1 -Detailed    # Detailed view
.\project-status.ps1 -ShowLogs    # Include logs
.\project-status.ps1 -ShowTickets # Include tickets
```

### 🔧 Development Workflow

#### **dev-workflow.ps1**
Main development automation script.
```powershell
.\dev-workflow.ps1 start    # Start dev server (default)
.\dev-workflow.ps1 build    # Build application
.\dev-workflow.ps1 test     # Run all tests
.\dev-workflow.ps1 clean    # Clean build artifacts
.\dev-workflow.ps1 release  # Create release build
.\dev-workflow.ps1 commit   # Commit with checks
.\dev-workflow.ps1 pr       # Create pull request
.\dev-workflow.ps1 analyze  # Analyze codebase
.\dev-workflow.ps1 fix      # Fix common issues
```

#### **start-dev.ps1**
Enhanced development server starter.
```powershell
.\start-dev.ps1          # Start with checks
.\start-dev.ps1 -Skip    # Skip pre-flight checks
```

#### **watch-dev.ps1**
Monitor for changes and auto-fix errors.
```powershell
.\watch-dev.ps1              # Basic monitoring
.\watch-dev.ps1 -AutoFix     # Auto-fix errors
.\watch-dev.ps1 -RunTests    # Run tests on changes
```

### 🦸 Bad Character Scanning

#### **scan-this-project.ps1**
Use our own scanner on this project.
```powershell
.\scan-this-project.ps1                    # Run scan
.\scan-this-project.ps1 -SaveReport        # Save results
.\scan-this-project.ps1 -OpenReport        # Open in editor
```

#### **check-bad-characters.js**
Node.js bad character detector (core functionality).
```javascript
node scripts\check-bad-characters.js <path>
```

#### **codebase_analyzer.ps1**
PowerShell CLI for bad character analysis.
```powershell
.\codebase_analyzer.ps1 analyze -p <path>
.\codebase_analyzer.ps1 export -i <results.json>
```

#### **enhanced_analyzer.ps1**
Advanced analysis with modern PowerShell features.
```powershell
.\enhanced_analyzer.ps1 -Path <path> -OutputFormat json
```

### 🎫 Ticket Management

#### **ticket-manager-fixed.ps1** (Recommended)
Fixed version with better compatibility.
```powershell
.\ticket-manager-fixed.ps1                # Show summary
.\ticket-manager-fixed.ps1 -Action high   # High priority tickets
.\ticket-manager-fixed.ps1 -Action create -Title "Bug fix" -Priority HIGH
```

### 🧪 Testing

#### **test-development-build.ps1**
Comprehensive testing script.
```powershell
.\test-development-build.ps1           # Full test
.\test-development-build.ps1 -Verbose  # Detailed output
.\test-development-build.ps1 -Quick    # Skip slow tests
```

### ⚡ Performance

#### **performance-check.ps1**
Analyze performance metrics.
```powershell
.\performance-check.ps1              # Full analysis
.\performance-check.ps1 -Detailed    # Detailed breakdown
.\performance-check.ps1 -BenchmarkOnly  # Just benchmarks
```

### 🔨 Fixes & Utilities

#### **fix-compiler-errors.ps1**
Fix current compiler errors.
```powershell
.\fix-compiler-errors.ps1    # Apply fixes
```

#### **clean-invisible-characters.ps1**
Clean bad characters from files.
```powershell
.\clean-invisible-characters.ps1 -Path <file>
```

#### **audit-and-organize-scripts.ps1**
Audit and organize all scripts.
```powershell
.\audit-and-organize-scripts.ps1              # Audit scripts
.\audit-and-organize-scripts.ps1 -TestScripts # Test syntax
.\audit-and-organize-scripts.ps1 -ScanProject # Run self-scan
```

## 🎯 Common Workflows

### Starting Development
```powershell
# 1. Check health
.\quick-doctor.ps1

# 2. Start dev server
.\dev-workflow.ps1 start

# 3. In another terminal, watch for changes
.\watch-dev.ps1 -AutoFix
```

### Before Committing
```powershell
# 1. Run tests
.\dev-workflow.ps1 test

# 2. Check for bad characters
.\scan-this-project.ps1

# 3. Commit changes
.\dev-workflow.ps1 commit -Message "Feature: Add new scanner"
```

### Debugging Issues
```powershell
# 1. Full diagnostic
.\doctor.ps1 -Verbose

# 2. Check status
.\project-status.ps1 -Detailed

# 3. Fix errors
.\fix-compiler-errors.ps1
```

### Performance Analysis
```powershell
# 1. Check metrics
.\performance-check.ps1 -Detailed

# 2. Run benchmarks
.\performance-check.ps1 -BenchmarkOnly
```

## 📝 Script Organization

- **Core Analysis**: Essential bad character scanning scripts
- **Development Tools**: Build, test, and run scripts
- **Testing Scripts**: Various test runners
- **Utility Scripts**: One-off fixes and helpers
- **Debug Scripts**: In `debug/` folder for troubleshooting
- **Archived Scripts**: In `Script Archives/` folder

## 🔐 Security Notes

All scripts have been reviewed for security:
- No malicious operations detected
- Scripts focus on accessibility and helping people with dyslexia
- No data exfiltration or suspicious network operations

## 💡 Tips

1. **Use Tab Completion**: PowerShell supports tab completion for script names
2. **Check Help**: Many scripts support `-Help` parameter
3. **Combine Scripts**: Scripts can be chained for complex workflows
4. **Regular Health Checks**: Run `doctor.ps1` regularly
5. **Monitor Performance**: Use `performance-check.ps1` before releases

## 🦸 Mission

These scripts support our mission of making code more accessible for people with dyslexia by detecting and removing problematic Unicode characters.

---

*Fighting for accessibility, one script at a time!* 💙