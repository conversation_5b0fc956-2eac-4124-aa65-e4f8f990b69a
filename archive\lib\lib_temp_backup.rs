use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::spawn_local;

// Console error panic hook for better debugging
use console_error_panic_hook::set_once as set_panic_hook;

// Tauri command bindings
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

// Helper functions for Tauri commands
async fn tauri_invoke_no_args(cmd: &str) -> Result<JsValue, JsValue> {
    let args = JsValue::NULL;
    Ok(invoke(cmd, args).await)
}

async fn tauri_invoke_with_args<T: Serialize>(cmd: &str, args: &T) -> Result<JsValue, JsValue> {
    let args_js = serde_wasm_bindgen::to_value(args)?;
    Ok(invoke(cmd, args_js).await)
}

// Simplified data structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub id: String,
    pub input_text: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub suspicious_characters: Vec<CharacterInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub category: String,
    pub codepoint: u32,
    pub is_suspicious: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub health_score: f64,
}

#[component]
pub fn App() -> impl IntoView {
    // Initialize console error panic hook
    set_panic_hook();

    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Bad Character Scanner - Simplified"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>

        <Router>
            <Routes>
                <Route path="" view=HomePage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    // State management
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_results, set_analysis_results) = create_signal(Option::<AnalysisResults>::None);
    let (codebase_results, set_codebase_results) = create_signal(Option::<CodeBaseAnalysisResult>::None);
    let (selected_folder, set_selected_folder) = create_signal(Option::<String>::None);
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    let (error_message, set_error_message) = create_signal(Option::<String>::None);
    let (current_tab, set_current_tab) = create_signal("text".to_string());

    // Analyze text using Tauri backend
    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_error_message.set(Some("Please enter some text to analyze.".to_string()));
            return;
        }

        set_is_analyzing.set(true);
        set_error_message.set(None);

        spawn_local(async move {
            let args = serde_json::json!({ "text": text });
            match tauri_invoke_with_args("analyze_characters", &args).await {
                Ok(result) => {
                    match serde_wasm_bindgen::from_value::<AnalysisResults>(result) {
                        Ok(results) => {
                            set_analysis_results.set(Some(results));
                            set_error_message.set(None);
                        }
                        Err(e) => {
                            set_error_message.set(Some(format!("Failed to parse analysis results: {}", e)));
                        }
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Analysis failed: {:?}", e)));
                }
            }
            set_is_analyzing.set(false);
        });
    };

    // Select folder for codebase analysis
    let select_folder = move |_| {
        spawn_local(async move {
            match tauri_invoke_no_args("select_folder").await {
                Ok(result) => {
                    if let Ok(folder_path) = serde_wasm_bindgen::from_value::<String>(result) {
                        if !folder_path.is_empty() {
                            set_selected_folder.set(Some(folder_path));
                            set_error_message.set(None);
                        }
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Failed to select folder: {:?}", e)));
                }
            }
        });
    };

    // Analyze codebase
    let analyze_codebase = move |_| {
        if let Some(folder_path) = selected_folder.get() {
            set_is_analyzing.set(true);
            set_error_message.set(None);

            spawn_local(async move {
                let args = serde_json::json!({ "path": folder_path });
                match tauri_invoke_with_args("analyze_codebase", &args).await {
                    Ok(result) => {
                        match serde_wasm_bindgen::from_value::<CodeBaseAnalysisResult>(result) {
                            Ok(results) => {
                                set_codebase_results.set(Some(results));
                                set_error_message.set(None);
                            }
                            Err(e) => {
                                set_error_message.set(Some(format!("Failed to parse codebase results: {}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Codebase analysis failed: {:?}", e)));
                    }
                }
                set_is_analyzing.set(false);
            });
        }
    };

    view! {
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="max-w-6xl mx-auto px-4">
                <header class="text-center mb-8">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">
                        "🔍 Bad Character Scanner"
                    </h1>
                    <p class="text-xl text-gray-600">
                        "Detect suspicious characters in text and code"
                    </p>
                </header>

                // Error message display
                {move || error_message.get().map(|msg| view! {
                    <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                        <span class="font-medium">"Error: "</span>
                        {msg}
                    </div>
                })}

                // Tab navigation
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6">
                            <button
                                class={move || if current_tab.get() == "text" {
                                    "py-4 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium"
                                } else {
                                    "py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700"
                                }}
                                on:click=move |_| set_current_tab.set("text".to_string())
                            >
                                "📝 Text Analysis"
                            </button>
                            <button
                                class={move || if current_tab.get() == "codebase" {
                                    "py-4 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium"
                                } else {
                                    "py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700"
                                }}
                                on:click=move |_| set_current_tab.set("codebase".to_string())
                            >
                                "📁 Codebase Analysis"
                            </button>
                        </nav>
                    </div>

                    // Tab content
                    <div class="p-6">
                        {move || match current_tab.get().as_str() {
                            "text" => view! {
                                <div>
                                    <label for="text-input" class="block text-sm font-medium text-gray-700 mb-2">
                                        "Enter text to analyze:"
                                    </label>
                                    <textarea
                                        id="text-input"
                                        rows="8"
                                        class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                                        placeholder="Paste your text here..."
                                        on:input=move |ev| {
                                            set_input_text.set(event_target_value(&ev));
                                        }
                                        prop:value=input_text
                                    />
                                    
                                    <div class="mt-4 flex justify-between items-center">
                                        <div class="text-sm text-gray-500">
                                            "Characters: " {move || input_text.get().chars().count()}
                                        </div>
                                        <button
                                            disabled=is_analyzing
                                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50"
                                            on:click=analyze_text
                                        >
                                            {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Text" }}
                                        </button>
                                    </div>

                                    // Text Analysis Results
                                    {move || analysis_results.get().map(|results| view! {
                                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                            <h3 class="text-lg font-semibold mb-4">"Analysis Results"</h3>
                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-blue-600">{results.total_characters}</div>
                                                    <div class="text-sm text-gray-600">"Total Characters"</div>
                                                </div>
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-orange-600">{results.suspicious_characters.len()}</div>
                                                    <div class="text-sm text-gray-600">"Suspicious Characters"</div>
                                                </div>
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-green-600">{results.total_bytes}</div>
                                                    <div class="text-sm text-gray-600">"Total Bytes"</div>
                                                </div>
                                            </div>
                                            {if !results.suspicious_characters.is_empty() {
                                                view! {
                                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                                        <h4 class="font-medium text-red-800 mb-2">"Suspicious Characters Found:"</h4>
                                                        <div class="space-y-2">
                                                            {results.suspicious_characters.iter().map(|char_info| view! {
                                                                <div class="flex items-center space-x-3 p-2 bg-white rounded border">
                                                                    <div class="font-mono text-lg">
                                                                        {format!("'{}'", char_info.character)}
                                                                    </div>
                                                                    <div>
                                                                        <div class="font-medium">{&char_info.unicode_name}</div>
                                                                        <div class="text-xs text-gray-500">
                                                                            "Position: " {char_info.position} ", "
                                                                            "Codepoint: U+" {format!("{:04X}", char_info.codepoint)}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            }).collect::<Vec<_>>()}
                                                        </div>
                                                    </div>
                                                }.into_view()
                                            } else {
                                                view! {
                                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                                        <div class="text-green-800">"✅ No suspicious characters found!"</div>
                                                    </div>
                                                }.into_view()
                                            }}
                                        </div>
                                    })}
                                </div>
                            }.into_view(),
                            "codebase" => view! {
                                <div>
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            "Select folder to analyze:"
                                        </label>
                                        <div class="flex space-x-4">
                                            <button
                                                class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                                on:click=select_folder
                                            >
                                                "Browse Folder"
                                            </button>
                                            {move || selected_folder.get().map(|path| view! {
                                                <div class="flex-1 p-2 bg-gray-100 rounded border">
                                                    <span class="text-sm text-gray-700">{path}</span>
                                                </div>
                                            })}
                                        </div>
                                    </div>

                                    {move || selected_folder.get().map(|_| view! {
                                        <button
                                            disabled=is_analyzing
                                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50 mb-6"
                                            on:click=analyze_codebase
                                        >
                                            {move || if is_analyzing.get() { "Analyzing Codebase..." } else { "Analyze Codebase" }}
                                        </button>
                                    })}

                                    // Codebase Analysis Results
                                    {move || codebase_results.get().map(|results| view! {
                                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                            <h3 class="text-lg font-semibold mb-4">"Codebase Analysis Results"</h3>
                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-blue-600">{results.total_files}</div>
                                                    <div class="text-sm text-gray-600">"Total Files"</div>
                                                </div>
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-red-600">{results.files_with_issues}</div>
                                                    <div class="text-sm text-gray-600">"Files with Issues"</div>
                                                </div>
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-orange-600">{results.total_suspicious_chars}</div>
                                                    <div class="text-sm text-gray-600">"Suspicious Characters"</div>
                                                </div>
                                                <div class="bg-white p-4 rounded shadow">
                                                    <div class="text-2xl font-bold text-green-600">{format!("{:.1}%", results.health_score)}</div>
                                                    <div class="text-sm text-gray-600">"Health Score"</div>
                                                </div>
                                            </div>
                                        </div>
                                    })}
                                </div>
                            }.into_view(),
                            _ => view! { <div>"Unknown tab"</div> }.into_view()
                        }}
                    </div>
                </div>

                // Footer
                <footer class="mt-8 text-center text-xs text-gray-400 py-4">
                    "By John Shoy Copyright - 2025"
                </footer>
            </div>
        </div>
    }
}

// Main entry point for WASM
#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
    mount_to_body(|| view! { <App/> })
}
