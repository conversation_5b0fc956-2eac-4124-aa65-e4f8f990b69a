# LEPTOS-TAURI-3 - Fix Frontend Command Invocation Pattern

**Status:** 🔴 Critical  
**Priority:** P0 (Blocking)  
**Type:** 🐛 Bug Fix  
**Created:** 2025-06-12  
**Estimated Effort:** 2-3 hours  

## 🎯 Problem Statement

The Leptos frontend is successfully preparing and sending command arguments, but the invocation pattern or command structure may not match what Tauri v2 expects, leading to communication failures.

## 🔍 Evidence from Logs

```javascript
// Frontend is doing this correctly:
Leptos Frontend: Invoking 'analyze_characters' with args: 
Object {"text": String("This file simulates...")}

// But then fails at the invoke level:
$leptos_frontend::invoke::{{closure}}::h90be1086a4afd0b3
$leptos_frontend::tauri_invoke_with_args::{{closure}}::hd955fba56ff0f523
```

## 🎯 Root Cause Analysis

### Potential Issues:
1. **Command Name Mismatch**: Frontend calling wrong command name
2. **Argument Structure**: Args format doesn't match backend expectations
3. **Invoke Function Issues**: Frontend invoke wrapper has bugs
4. **WASM Bridge Problems**: Issues in the Rust-WASM-JS bridge

## ✅ Acceptance Criteria

- [ ] Frontend invoke function works correctly
- [ ] Command arguments reach backend in expected format
- [ ] Backend receives commands with proper parameter names
- [ ] Successful round-trip: frontend → backend → frontend response

## 🔧 Investigation Steps

### Phase 1: Verify Command Registration
- [ ] Check exact command name in backend (`analyze_characters`)
- [ ] Verify command signature matches frontend call
- [ ] Confirm command is in invoke_handler

### Phase 2: Test Frontend Invoke Function
- [ ] Review `leptos_frontend::invoke` implementation
- [ ] Test with simplified command first
- [ ] Check WASM-JS bindings

### Phase 3: Argument Format Verification
- [ ] Compare frontend args with backend expectations
- [ ] Test with minimal arguments first
- [ ] Verify serialization/deserialization

## 🧪 Testing Plan

1. **Simple Command Test**:
   ```rust
   // Backend
   #[tauri::command]
   fn hello_world() -> String {
       "Hello from Tauri!".to_string()
   }
   ```

2. **Frontend Test**:
   ```rust
   // Frontend (Leptos)
   let result = invoke("hello_world", ()).await;
   ```

3. **Gradual Complexity**:
   - Test with no args → simple string arg → complex object

## 🔍 Key Files to Check

### Frontend (Leptos):
- `src/lib.rs` - Main frontend logic
- Invoke wrapper functions
- Command argument preparation

### Backend (Tauri):
- `src-tauri/src/main.rs` - Command registration
- `src-tauri/src/lib.rs` - Invoke handler setup
- Command function signatures

## 🚨 Quick Diagnostic Steps

1. **Check Command List**:
   ```rust
   // In main.rs, verify this exists:
   .invoke_handler(tauri::generate_handler![analyze_characters])
   ```

2. **Test Simple Invoke**:
   ```javascript
   // In browser console:
   window.__TAURI__.invoke('analyze_characters', {text: 'test'})
   ```

3. **Backend Logging**:
   ```rust
   #[tauri::command]
   fn analyze_characters(text: String) -> String {
       println!("Backend received: {}", text);  // Add this
       // ... rest of function
   }
   ```

## 📋 Technical Details

- **Frontend**: Leptos 0.6 with WASM
- **Backend**: Tauri v2.5.1
- **Invoke Method**: Tauri command invocation
- **Error Pattern**: Frontend prep succeeds, invoke fails

## 🔗 Related Issues

- May resolve once LEPTOS-TAURI-1 (IPC) is fixed
- Could be caused by LEPTOS-TAURI-2 (protocol issues)
- Part of overall frontend-backend integration problem

## 💡 Debugging Strategy

1. **Start Simple**: Get "hello world" command working first
2. **Add Logging**: Log every step of invoke process
3. **Test Isolation**: Test backend commands independently
4. **Incremental Complexity**: Build up to full `analyze_characters`

---

**Next Steps**: Start with simple command test, then build up complexity.

**Success Metric**: At least one command successfully executes end-to-end.
