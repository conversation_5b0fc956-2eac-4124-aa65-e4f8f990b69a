#!/usr/bin/env powershell
# Script to audit, test, and organize all scripts in the project
# Identifies which scripts are still relevant and moves outdated ones to archives

param(
    [switch]$DryRun,
    [switch]$TestScripts,
    [switch]$ScanProject
)

Write-Host "`n=== SCRIPT AUDIT AND ORGANIZATION TOOL ===" -ForegroundColor Cyan
Write-Host "Auditing all scripts for relevance and functionality" -ForegroundColor Gray

$script_dir = $PSScriptRoot
$archive_dir = Join-Path $script_dir "Script Archives"
$project_root = $script_dir | Split-Path -Parent

# Ensure archive directory exists
if (-not (Test-Path $archive_dir)) {
    New-Item -ItemType Directory -Path $archive_dir -Force | Out-Null
}

# Categories of scripts
$script_categories = @{
    "Core Analysis" = @(
        "codebase_analyzer.ps1",
        "enhanced_analyzer.ps1",
        "check-bad-characters.js",
        "codebase_analyzer.sh",
        "enhanced_analyzer.sh"
    )
    "Development Tools" = @(
        "setup-dev-environment.ps1",
        "start-dev.ps1",
        "test-development-build.ps1",
        "dev_startup.ps1"
    )
    "Ticket Management" = @(
        "ticket-manager.ps1",
        "ticket-manager-simple.ps1",
        "ticket-manager-fixed.ps1"
    )
    "Testing Scripts" = @(
        "test-application.ps1",
        "test-cli-simple.ps1",
        "test-comprehensive-cli.ps1",
        "test-phase1-implementation.ps1"
    )
    "Utility Scripts" = @(
        "clean-invisible-characters.ps1",
        "check-quality.ps1",
        "check-tauri-version.js"
    )
    "Fix Scripts" = @(
        "fix-bom.ps1",
        "fix-powershell-workspace.ps1",
        "remove_bom.ps1",
        "fix_json.ps1"
    )
    "Possibly Outdated" = @(
        "audit_and_cleanup_framework.ps1",
        "validate_scripts.py",
        "test_powershell_wrapper.ps1"
    )
}

# Function to test if a script is functional
function Test-ScriptFunctionality {
    param([string]$ScriptPath)
    
    $script_name = Split-Path $ScriptPath -Leaf
    Write-Host "  Testing: $script_name" -ForegroundColor Yellow
    
    # Check if file exists
    if (-not (Test-Path $ScriptPath)) {
        Write-Host "    [MISSING] File not found" -ForegroundColor Red
        return @{ Status = "Missing"; Reason = "File not found" }
    }
    
    # Check file extension and syntax
    $extension = [System.IO.Path]::GetExtension($ScriptPath).ToLower()
    
    switch ($extension) {
        ".ps1" {
            # Test PowerShell syntax
            $errors = @()
            $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $ScriptPath -Raw), [ref]$errors)
            if ($errors.Count -gt 0) {
                Write-Host "    [SYNTAX ERROR] PowerShell syntax errors found" -ForegroundColor Red
                return @{ Status = "SyntaxError"; Reason = "PowerShell syntax errors"; Errors = $errors }
            }
            Write-Host "    [OK] PowerShell syntax valid" -ForegroundColor Green
        }
        ".js" {
            # Basic JavaScript check
            if (Get-Command node -ErrorAction SilentlyContinue) {
                $result = & node --check $ScriptPath 2>&1
                if ($LASTEXITCODE -ne 0) {
                    Write-Host "    [SYNTAX ERROR] JavaScript syntax errors found" -ForegroundColor Red
                    return @{ Status = "SyntaxError"; Reason = "JavaScript syntax errors"; Errors = $result }
                }
                Write-Host "    [OK] JavaScript syntax valid" -ForegroundColor Green
            } else {
                Write-Host "    [SKIP] Node.js not available for syntax check" -ForegroundColor Yellow
            }
        }
        ".sh" {
            Write-Host "    [INFO] Shell script - manual review needed" -ForegroundColor Yellow
        }
        ".py" {
            Write-Host "    [INFO] Python script - manual review needed" -ForegroundColor Yellow
        }
    }
    
    return @{ Status = "OK"; Reason = "Syntax valid" }
}

# Audit scripts by category
$audit_results = @{}

foreach ($category in $script_categories.Keys) {
    Write-Host "`n=== $category ===" -ForegroundColor Cyan
    
    foreach ($script in $script_categories[$category]) {
        $script_path = Join-Path $script_dir $script
        
        if ($TestScripts) {
            $result = Test-ScriptFunctionality $script_path
            $audit_results[$script] = $result
        } else {
            if (Test-Path $script_path) {
                Write-Host "  [EXISTS] $script" -ForegroundColor Green
                $audit_results[$script] = @{ Status = "Exists" }
            } else {
                Write-Host "  [MISSING] $script" -ForegroundColor Red
                $audit_results[$script] = @{ Status = "Missing" }
            }
        }
    }
}

# Find uncategorized scripts
Write-Host "`n=== Uncategorized Scripts ===" -ForegroundColor Yellow
$all_categorized = $script_categories.Values | ForEach-Object { $_ } | Select-Object -Unique
$all_scripts = Get-ChildItem $script_dir -Filter "*.ps1", "*.js", "*.sh", "*.py" -File | Where-Object { $_.Name -ne "audit-and-organize-scripts.ps1" }

foreach ($script in $all_scripts) {
    if ($all_categorized -notcontains $script.Name) {
        Write-Host "  [UNCATEGORIZED] $($script.Name)" -ForegroundColor Yellow
        
        # Check if it's a test script
        if ($script.Name -match "test-") {
            Write-Host "    -> Appears to be a test script" -ForegroundColor Gray
        }
        # Check if it's in subdirectories
        elseif ($script.DirectoryName -ne $script_dir) {
            $subdir = $script.DirectoryName.Replace($script_dir, "").TrimStart("\", "/")
            Write-Host "    -> In subdirectory: $subdir" -ForegroundColor Gray
        }
    }
}

# Run self-scan if requested
if ($ScanProject) {
    Write-Host "`n=== SELF-SCAN: Running Bad Character Analysis ===" -ForegroundColor Cyan
    
    # Try different analysis scripts
    $analyzer_scripts = @(
        @{ Path = "check-bad-characters.js"; Type = "node" },
        @{ Path = "codebase_analyzer.ps1"; Type = "powershell" },
        @{ Path = "enhanced_analyzer.ps1"; Type = "powershell" }
    )
    
    foreach ($analyzer in $analyzer_scripts) {
        $analyzer_path = Join-Path $script_dir $analyzer.Path
        if (Test-Path $analyzer_path) {
            Write-Host "`nRunning $($analyzer.Path)..." -ForegroundColor Yellow
            
            try {
                switch ($analyzer.Type) {
                    "node" {
                        if (Get-Command node -ErrorAction SilentlyContinue) {
                            & node $analyzer_path $project_root
                        } else {
                            Write-Host "  Node.js not available" -ForegroundColor Red
                        }
                    }
                    "powershell" {
                        & $analyzer_path -Path $project_root -OutputFormat summary
                    }
                }
                break  # If successful, don't try other analyzers
            } catch {
                Write-Host "  Error running analyzer: $_" -ForegroundColor Red
            }
        }
    }
}

# Generate report
Write-Host "`n=== AUDIT SUMMARY ===" -ForegroundColor Cyan

$total_scripts = $audit_results.Count
$existing_scripts = ($audit_results.Values | Where-Object { $_.Status -eq "Exists" -or $_.Status -eq "OK" }).Count
$missing_scripts = ($audit_results.Values | Where-Object { $_.Status -eq "Missing" }).Count
$error_scripts = ($audit_results.Values | Where-Object { $_.Status -eq "SyntaxError" }).Count

Write-Host "Total Scripts Audited: $total_scripts" -ForegroundColor White
Write-Host "Existing/Valid: $existing_scripts" -ForegroundColor Green
Write-Host "Missing: $missing_scripts" -ForegroundColor Red
Write-Host "Syntax Errors: $error_scripts" -ForegroundColor Red

# Recommendations
Write-Host "`n=== RECOMMENDATIONS ===" -ForegroundColor Cyan

Write-Host "`n1. High Priority Scripts to Keep:" -ForegroundColor Yellow
foreach ($script in $script_categories["Core Analysis"] + $script_categories["Development Tools"]) {
    if ($audit_results[$script].Status -ne "Missing") {
        Write-Host "   - $script" -ForegroundColor Green
    }
}

Write-Host "`n2. Scripts to Review for Archival:" -ForegroundColor Yellow
foreach ($script in $script_categories["Possibly Outdated"]) {
    if ($audit_results[$script].Status -ne "Missing") {
        Write-Host "   - $script" -ForegroundColor Yellow
    }
}

Write-Host "`n3. Missing Critical Scripts:" -ForegroundColor Red
foreach ($script in $script_categories["Core Analysis"]) {
    if ($audit_results[$script].Status -eq "Missing") {
        Write-Host "   - $script" -ForegroundColor Red
    }
}

if (-not $DryRun) {
    Write-Host "`n[TIP] Run with -DryRun to see what would be moved to archives" -ForegroundColor Gray
    Write-Host "[TIP] Run with -TestScripts to validate script syntax" -ForegroundColor Gray
    Write-Host "[TIP] Run with -ScanProject to scan for bad characters" -ForegroundColor Gray
}

Write-Host "`nFighting for accessibility through better code organization!" -ForegroundColor Cyan