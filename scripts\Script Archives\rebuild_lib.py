#!/usr/bin/env python3
"""
Phase 3: Integration Script for lib.rs Reconstruction
Rebuilds complete lib.rs from generated components with safety features
"""

import os
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
import re

class LibRebuilder:
    def __init__(self):
        self.lib_path = Path("src/lib.rs")
        self.backup_dir = Path("scripts/backups")
        self.generated_dir = Path("scripts/generated_components")
        self.templates_dir = Path("scripts/component_templates")
        self.extracted_dir = Path("scripts/extracted_components")
        
    def create_safety_backup(self):
        """Create safety backup before rebuilding"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"lib_rs_pre_rebuild_{timestamp}.rs"
        
        if self.lib_path.exists():
            shutil.copy2(self.lib_path, backup_path)
            print(f"✅ Safety backup created: {backup_path}")
            return backup_path
        else:
            print("❌ Warning: Original lib.rs not found")
            return None
    
    def extract_text_analysis_component(self):
        """Extract the working text analysis component from current lib.rs"""
        if not self.lib_path.exists():
            print("❌ lib.rs not found")
            return None
            
        with open(self.lib_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the text analysis component (known working)
        # Look for the text analysis view! block
        patterns = [
            r'("Text Analysis".*?(?=div\s*\{[^}]*class\s*=\s*"tab-pane)|(?=\}\s*,?\s*div\s*\{)|$)',
            r'(div\s*\{[^}]*"Text Analysis".*?textarea.*?(?=div\s*\{[^}]*class)|(?=\}\s*,?\s*div)|$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                text_analysis = match.group(1)
                print("✅ Extracted text analysis component")
                return text_analysis
        
        print("⚠️ Could not extract text analysis component, using template")
        return self._get_text_analysis_template()
    
    def _get_text_analysis_template(self):
        """Fallback text analysis template"""
        return '''div(class="text-analysis-container") {
            div(class="text-analysis-header") {
                h2(class="analysis-title") { "📝 Text Analysis" }
                p(class="analysis-subtitle") { 
                    "Paste text below to analyze for bad characters and encoding issues" 
                }
            }
            
            div(class="text-input-section enhanced-card") {
                label(class="input-label") { "Input Text" }
                textarea(
                    class="text-input-area",
                    placeholder="Paste your text here to analyze for bad characters...",
                    rows="10",
                    prop:value=move || text_input.get(),
                    on:input=move |ev| {
                        text_input.set(event_target_value(&ev));
                    }
                ) {}
                
                div(class="input-actions") {
                    button(
                        class="btn btn-primary",
                        disabled=move || text_input.get().is_empty() || is_analyzing.get(),
                        on:click=move |_| {
                            if !text_input.get().is_empty() {
                                analyze_text();
                            }
                        }
                    ) {
                        {move || if is_analyzing.get() {
                            view! { span { "🔄 Analyzing..." } }
                        } else {
                            view! { 
                                span(class="btn-icon") { "🔍" }
                                span { "Analyze Text" }
                            }
                        }}
                    }
                    
                    button(
                        class="btn btn-secondary",
                        on:click=move |_| {
                            text_input.set(String::new());
                            analysis_result.set(None);
                        }
                    ) { "Clear" }
                }
            }
            
            // Results Display
            {move || {
                if let Some(result) = analysis_result.get() {
                    view! {
                        div(class="text-results-section enhanced-card") {
                            div(class="results-header") {
                                h3(class="results-title") { "📊 Analysis Results" }
                            }
                            
                            div(class="results-summary") {
                                div(class="summary-stats") {
                                    div(class="stat-item") {
                                        span(class="stat-value") { &result.total_characters.to_string() }
                                        span(class="stat-label") { "Total Characters" }
                                    }
                                    div(class="stat-item") {
                                        span(class="stat-value") { &result.problematic_characters.to_string() }
                                        span(class="stat-label") { "Problematic" }
                                    }
                                }
                            }
                            
                            {if !result.bad_characters.is_empty() {
                                view! {
                                    div(class="bad-characters-list") {
                                        h4 { "Bad Characters Found:" }
                                        {result.bad_characters.iter().map(|bad_char| {
                                            view! {
                                                div(class="bad-char-item") {
                                                    span(class="char-display") { &bad_char.character }
                                                    span(class="char-unicode") { 
                                                        format!("U+{:04X}", bad_char.unicode_value) 
                                                    }
                                                    span(class="char-description") { &bad_char.description }
                                                    span(class="char-count") { 
                                                        format!("{} occurrences", bad_char.positions.len()) 
                                                    }
                                                }
                                            }
                                        }).collect_view()}
                                    }
                                }
                            } else {
                                view! {
                                    div(class="no-issues") {
                                        span(class="success-icon") { "✅" }
                                        span { "No bad characters found!" }
                                    }
                                }
                            }}
                        }
                    }
                } else {
                    view! { div {} }
                }
            }}
        }'''
    
    def load_generated_components(self):
        """Load all generated components"""
        components = {}
        
        # Load component files
        component_files = [
            "imports.rs",
            "data_structures.rs", 
            "signals.rs",
            "helpers.rs"
        ]
        
        for filename in component_files:
            file_path = self.generated_dir / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    components[filename.replace('.rs', '')] = f.read()
                print(f"✅ Loaded {filename}")
            else:
                print(f"⚠️ {filename} not found, using defaults")
                components[filename.replace('.rs', '')] = ""
        
        return components
    
    def load_component_templates(self):
        """Load component templates"""
        templates = {}
        
        template_files = [
            "selection_mode.rs.template",
            "actions_mode.rs.template", 
            "processing_mode.rs.template",
            "results_display.rs.template",
            "homepage.rs.template"
        ]
        
        for filename in template_files:
            file_path = self.templates_dir / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    templates[filename.replace('.rs.template', '')] = f.read()
                print(f"✅ Loaded template {filename}")
            else:
                print(f"⚠️ Template {filename} not found")
                templates[filename.replace('.rs.template', '')] = ""
        
        return templates
    
    def build_complete_lib_rs(self, components, templates, text_analysis):
        """Build the complete lib.rs file"""
        print("🔧 Building complete lib.rs...")
        
        # Build the complete file structure
        lib_content = []
        
        # Add imports
        lib_content.append("// Enhanced Bad Character Scanner - Complete Implementation")
        lib_content.append("// Auto-generated by rebuild script")
        lib_content.append(f"// Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lib_content.append("")
        lib_content.append(components.get('imports', ''))
        lib_content.append("")
        
        # Add data structures  
        lib_content.append("// Data Structures")
        lib_content.append(components.get('data_structures', ''))
        lib_content.append("")
        
        # Add main app function
        lib_content.append('''#[component]
pub fn App() -> impl IntoView {''')
        
        # Add signals
        lib_content.append("    // Signal Definitions")
        lib_content.append("    " + components.get('signals', '').replace('\n', '\n    '))
        lib_content.append("")
        
        # Add helper functions (inside App component)
        lib_content.append("    // Helper Functions")
        helper_functions = components.get('helpers', '').replace('\n', '\n    ')
        lib_content.append("    " + helper_functions)
        lib_content.append("")
        
        # Add effect for loading recent folders
        lib_content.append('''    // Load recent folders on mount
    create_effect(move |_| {
        load_recent_folders();
    });
    
    // Listen for progress updates
    create_effect(move |_| {
        // Progress event listener would go here
        // This would be connected to Tauri events
    });
''')
        
        # Build the main view
        lib_content.append("    // Main View")
        lib_content.append("    view! {")
        
        # Insert homepage template with component placeholders replaced
        homepage = templates.get('homepage', '')
        
        # Replace placeholders with actual components
        homepage = homepage.replace(
            "// TEXT_ANALYSIS_COMPONENT_PLACEHOLDER", 
            text_analysis
        )
        homepage = homepage.replace(
            "// SELECTION_MODE_COMPONENT_PLACEHOLDER",
            templates.get('selection_mode', '')
        )
        homepage = homepage.replace(
            "// ACTIONS_MODE_COMPONENT_PLACEHOLDER", 
            templates.get('actions_mode', '')
        )
        homepage = homepage.replace(
            "// PROCESSING_MODE_COMPONENT_PLACEHOLDER",
            templates.get('processing_mode', '')
        )
        homepage = homepage.replace(
            "// RESULTS_DISPLAY_COMPONENT_PLACEHOLDER",
            templates.get('results_display', '')
        )
        
        # Indent the homepage content properly
        indented_homepage = "        " + homepage.replace('\n', '\n        ')
        lib_content.append(indented_homepage)
        
        # Close the view and function
        lib_content.append("    }")
        lib_content.append("}")
        
        # Add additional helper functions outside the component
        lib_content.append("")
        lib_content.append("// Additional Helper Functions")
        lib_content.append('''
fn analyze_text() {
    is_analyzing.set(true);
    
    spawn_local(async move {
        let text = text_input.get();
        
        match invoke("analyze_text", &to_value(&text).unwrap()).await {
            Ok(result) => {
                let analysis: TextAnalysisResult = serde_wasm_bindgen::from_value(result).unwrap();
                analysis_result.set(Some(analysis));
            }
            Err(e) => {
                log::error!("Text analysis failed: {}", e);
            }
        }
        
        is_analyzing.set(false);
    });
}''')
        
        return '\n'.join(lib_content)
    
    def validate_rust_syntax(self, content):
        """Validate Rust syntax using rustc"""
        print("🔍 Validating Rust syntax...")
        
        # Write to temporary file
        temp_file = Path("scripts/temp_lib_validation.rs")
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        try:
            # Use rustc to check syntax
            result = subprocess.run(
                ["rustc", "--crate-type", "lib", "--emit=metadata", str(temp_file)],
                capture_output=True,
                text=True,
                cwd="."
            )
            
            # Clean up temp files
            temp_file.unlink(missing_ok=True)
            Path("scripts/temp_lib_validation.rmeta").unlink(missing_ok=True)
            
            if result.returncode == 0:
                print("✅ Rust syntax validation passed")
                return True, ""
            else:
                print("❌ Rust syntax validation failed")
                return False, result.stderr
                
        except FileNotFoundError:
            print("⚠️ rustc not found, skipping syntax validation")
            return True, "rustc not available"
        except Exception as e:
            print(f"⚠️ Syntax validation error: {e}")
            return True, str(e)
    
    def test_compilation(self):
        """Test compilation of the entire project"""
        print("🔨 Testing project compilation...")
        
        try:
            result = subprocess.run(
                ["cargo", "check"],
                capture_output=True,
                text=True,
                cwd="."
            )
            
            if result.returncode == 0:
                print("✅ Project compilation successful")
                return True, ""
            else:
                print("❌ Project compilation failed")
                return False, result.stderr
                
        except Exception as e:
            print(f"⚠️ Compilation test error: {e}")
            return False, str(e)
    
    def deploy_new_lib_rs(self, content, backup_path):
        """Deploy the new lib.rs with rollback capability"""
        print("🚀 Deploying new lib.rs...")
        
        try:
            # Write new content
            with open(self.lib_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ New lib.rs deployed")
            
            # Test compilation
            compile_success, compile_error = self.test_compilation()
            
            if not compile_success:
                print("❌ Compilation failed, rolling back...")
                self.rollback(backup_path)
                return False, compile_error
            
            print("✅ Deployment successful!")
            return True, ""
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            self.rollback(backup_path)
            return False, str(e)
    
    def rollback(self, backup_path):
        """Rollback to backup version"""
        if backup_path and backup_path.exists():
            shutil.copy2(backup_path, self.lib_path)
            print(f"✅ Rolled back to backup: {backup_path}")
        else:
            print("❌ No backup available for rollback")
    
    def generate_deployment_report(self, success, error_msg=""):
        """Generate deployment report"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        report_path = Path("scripts/deployment_report.txt")
        
        report = []
        report.append("# Lib.rs Reconstruction Deployment Report")
        report.append(f"Generated: {timestamp}")
        report.append("")
        
        if success:
            report.append("## ✅ Deployment Status: SUCCESS")
            report.append("")
            report.append("### Changes Applied:")
            report.append("- Enhanced folder selection interface (CODEBASE-5)")
            report.append("- Professional action cards with animations")
            report.append("- Real-time path validation")
            report.append("- Recent folders with persistence")
            report.append("- Quick access buttons")
            report.append("- Advanced file type display (UI-3)")
            report.append("- NASA-inspired design system (UX-1)")
            report.append("- Responsive design implementation")
            report.append("")
            report.append("### Features Maintained:")
            report.append("- Text analysis functionality")
            report.append("- Tauri backend integration")
            report.append("- Export functionality")
            report.append("- Progress tracking system")
            report.append("")
            report.append("### Next Steps:")
            report.append("1. Test all functionality thoroughly")
            report.append("2. Verify responsive design on different screen sizes")
            report.append("3. Test drag & drop functionality")
            report.append("4. Verify recent folders persistence")
            report.append("5. Test all export formats")
        else:
            report.append("## ❌ Deployment Status: FAILED")
            report.append("")
            report.append("### Error Details:")
            report.append(error_msg)
            report.append("")
            report.append("### Recovery Actions:")
            report.append("1. Check backup files in scripts/backups/")
            report.append("2. Review syntax validation errors")
            report.append("3. Manually fix compilation issues")
            report.append("4. Re-run deployment script")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"📊 Deployment report: {report_path}")
        return report_path

def main():
    """Main execution function"""
    print("Starting Phase 3: Integration & Deployment")
    print("=" * 50)
    
    rebuilder = LibRebuilder()
    
    # Step 1: Create safety backup
    print("\n💾 Step 1: Creating safety backup...")
    backup_path = rebuilder.create_safety_backup()
    
    # Step 2: Extract working components
    print("\n📦 Step 2: Extracting working components...")
    text_analysis = rebuilder.extract_text_analysis_component()
    
    # Step 3: Load generated components
    print("\n🔧 Step 3: Loading generated components...")
    components = rebuilder.load_generated_components()
    
    # Step 4: Load templates
    print("\n📝 Step 4: Loading component templates...")
    templates = rebuilder.load_component_templates()
    
    # Step 5: Build complete lib.rs
    print("\n🏗️ Step 5: Building complete lib.rs...")
    new_lib_content = rebuilder.build_complete_lib_rs(components, templates, text_analysis)
    
    # Step 6: Validate syntax
    print("\n🔍 Step 6: Validating syntax...")
    syntax_valid, syntax_error = rebuilder.validate_rust_syntax(new_lib_content)
    
    if not syntax_valid:
        print(f"❌ Syntax validation failed: {syntax_error}")
        rebuilder.generate_deployment_report(False, f"Syntax validation failed: {syntax_error}")
        return
    
    # Step 7: Deploy new lib.rs
    print("\n🚀 Step 7: Deploying new lib.rs...")
    deploy_success, deploy_error = rebuilder.deploy_new_lib_rs(new_lib_content, backup_path)
      # Step 8: Generate report
    print("\n📊 Step 8: Generating deployment report...")
    report_path = rebuilder.generate_deployment_report(deploy_success, deploy_error)
    
    print("\n" + "=" * 50)
    
    if deploy_success:
        print("Phase 3 Complete - Deployment Successful!")
        print("Enhanced Bad Character Scanner is ready!")
        print("")
        print("New Features Available:")
        print("  • Enhanced folder selection interface")
        print("  • Professional action cards with animations")
        print("  • Real-time path validation")
        print("  • Recent folders with persistence")
        print("  • Quick access buttons")
        print("  • Advanced file type display")
        print("  • NASA-inspired design system")
        print("  • Responsive design")
        print("")
        print("Next: Test the application with 'cargo run'")
    else:
        print("Phase 3 Failed - Deployment Unsuccessful")
        print(f"📊 Check deployment report: {report_path}")
        print(f"💾 Backup available: {backup_path}")
    
    print(f"📊 Full report: {report_path}")

if __name__ == "__main__":
    main()
