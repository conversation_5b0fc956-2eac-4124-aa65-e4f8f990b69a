# Enhanced PowerShell Startup Script for Leptos + Tauri v2 Dev Environment
# Robust startup process for new developers
# Comprehensive error handling and verification
# Health checks and process monitoring
# Clear logging and user feedback

$port = 1420
$maxAttempts = 3
$attempt = 0
$scriptVersion = "2.0"

Write-Host " Leptos + Tauri v2 Development Environment Startup v$scriptVersion" -ForegroundColor Green
Write-Host " Initializing Bad Character Scanner development servers..." -ForegroundColor Cyan
Write-Host ""

# Enhanced functions with better error handling
function Get-PortProcessId($port) {
    try {
        $netstat = netstat -ano | Select-String ":$port "
        if ($netstat) {
            $procId = $netstat -replace ".*LISTENING +", "" | ForEach-Object { $_.Trim() }
            return $procId
        }
        return $null
    }
    catch {
        Write-Warning " Error checking port $port`: $_"
        return $null
    }
}

function Stop-PortProcess($procId) {
    if ($procId) {
        try {
            $process = Get-Process -Id $procId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host " Stopping process: $($process.ProcessName) (PID: $procId) using port $port..." -ForegroundColor Yellow
                Stop-Process -Id $procId -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 1
                
                # Verify process was stopped
                $stillRunning = Get-Process -Id $procId -ErrorAction SilentlyContinue
                if (-not $stillRunning) {
                    Write-Host " Process $procId successfully stopped" -ForegroundColor Green
                } else {
                    Write-Warning "  Process $procId may still be running"
                }
            }
        }
        catch {
            Write-Warning " Error stopping process $procId`: $_"
        }
    }
}

function Test-Prerequisites {
    Write-Host " Checking prerequisites..." -ForegroundColor Cyan
    
    # Check if trunk is installed
    try {
        $trunkVersion = trunk --version 2>$null
        if ($trunkVersion) {
            Write-Host " Trunk found: $($trunkVersion.Split()[1])" -ForegroundColor Green
        } else {
            Write-Warning " Trunk not found. Install with: cargo install trunk --locked"
            return $false
        }
    }
    catch {
        Write-Warning " Trunk not found. Install with: cargo install trunk --locked"
        return $false
    }
    
    # Check if Tauri CLI is installed
    try {
        $tauriVersion = cargo tauri --version 2>$null
        if ($tauriVersion) {
            Write-Host " Tauri CLI found: $($tauriVersion.Split()[-1])" -ForegroundColor Green
        } else {
            Write-Warning " Tauri CLI not found. Install with: cargo install tauri-cli --version '^2.5'"
            return $false
        }
    }
    catch {
        Write-Warning " Tauri CLI not found. Install with: cargo install tauri-cli --version '^2.5'"
        return $false
    }
    
    # Check if we're in the right directory
    if (-not (Test-Path "Cargo.toml") -or -not (Test-Path "src-tauri")) {
        Write-Warning " Not in project root directory. Please run from Leptos_TaurieV2_BCS directory."
        return $false
    }
    
    Write-Host " All prerequisites satisfied" -ForegroundColor Green
    return $true
}

function Wait-ForProcessStart($processName, $maxWaitSeconds = 10) {
    Write-Host " Waiting for $processName to start..." -ForegroundColor Yellow
    $waited = 0
    while ($waited -lt $maxWaitSeconds) {
        $process = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($process) {
            Write-Host " $processName started successfully (PID: $($process.Id))" -ForegroundColor Green
            return $true
        }
        Start-Sleep -Seconds 1
        $waited++
    }
    Write-Warning "  $processName did not start within $maxWaitSeconds seconds"
    return $false
}

function Test-ServerHealth {
    Write-Host " Performing health checks..." -ForegroundColor Cyan
    
    # Check if port 1420 is responding
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:1420" -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host " Frontend server responding on http://localhost:1420" -ForegroundColor Green
        } else {
            Write-Warning "  Frontend server not responding properly"
        }
    }
    catch {
        Write-Warning "  Frontend server health check failed (may still be starting up)"
    }
    
    # Check if Tauri processes are running
    $tauriProcesses = Get-Process | Where-Object {$_.ProcessName -like "*tauri*" -or $_.ProcessName -like "*laptos*"}
    if ($tauriProcesses) {
        Write-Host " Tauri backend processes detected:" -ForegroundColor Green
        foreach ($proc in $tauriProcesses) {
            Write-Host "   - $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor Gray
        }
    } else {
        Write-Warning "  No Tauri backend processes detected yet"
    }
}

# Main execution
Write-Host " Step 1: Checking prerequisites..." -ForegroundColor Magenta
if (-not (Test-Prerequisites)) {
    Write-Host " Prerequisites check failed. Please resolve the issues above." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host " Step 2: Clearing port $port..." -ForegroundColor Magenta

# Port cleanup logic with enhanced feedback
while ($attempt -lt $maxAttempts) {
    $procId = Get-PortProcessId $port
    if ($procId) {
        Write-Host " Found process using port $port (PID: $procId)" -ForegroundColor Yellow
        Stop-PortProcess $procId
        Start-Sleep -Seconds 2
    } else {
        Write-Host " Port $port is available" -ForegroundColor Green
        break
    }
    $attempt++
}

# Final port check
$procId = Get-PortProcessId $port
if ($procId) {
    Write-Host " Port $port is still in use by PID $procId after $maxAttempts attempts." -ForegroundColor Red
    Write-Host " Try rebooting your system or manually kill the process." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host " Step 3: Launching development servers..." -ForegroundColor Magenta

# Launch trunk serve in a new terminal with enhanced error handling
Write-Host " Starting frontend server (trunk)..." -ForegroundColor Cyan
try {
    Start-Process powershell -ArgumentList '-NoExit', '-Command', "cd `"$PWD`"; Write-Host ' Frontend Server (Trunk)' -ForegroundColor Green; Write-Host 'Port: 1420' -ForegroundColor Gray; Write-Host 'URL: http://localhost:1420' -ForegroundColor Blue; Write-Host ''; trunk serve --port 1420"
    Start-Sleep -Seconds 3
    
    # Verify trunk started
    $trunkStarted = Wait-ForProcessStart "trunk" 5
    if (-not $trunkStarted) {
        Write-Warning "  Trunk may not have started properly. Check the trunk terminal for errors."
    }
} catch {
    Write-Warning " Failed to start trunk server: $_"
}

# Launch cargo tauri dev in a new terminal
Write-Host " Starting backend server (Tauri)..." -ForegroundColor Cyan
try {
    Start-Process powershell -ArgumentList '-NoExit', '-Command', "cd `"$PWD`"; Write-Host '  Backend Server (Tauri v2)' -ForegroundColor Green; Write-Host 'Framework: Leptos + Tauri v2' -ForegroundColor Gray; Write-Host 'Desktop App: Will open automatically' -ForegroundColor Blue; Write-Host ''; cargo tauri dev"
    Start-Sleep -Seconds 5
    
    # Note: Tauri creates multiple processes, so we check for any tauri-related process
    $tauriStarted = Wait-ForProcessStart "cargo*" 10
    if (-not $tauriStarted) {
        Write-Warning "  Tauri backend may not have started properly. Check the tauri terminal for compilation errors."
    }
} catch {
    Write-Warning " Failed to start Tauri server: $_"
}

Write-Host ""
Write-Host " Step 4: Performing health checks..." -ForegroundColor Magenta
Start-Sleep -Seconds 5  # Give servers time to fully start
Test-ServerHealth

Write-Host ""
Write-Host " Development Environment Startup Complete!" -ForegroundColor Green
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
Write-Host " Frontend URL:    http://localhost:1420" -ForegroundColor Blue
Write-Host " Desktop App:    Should open automatically" -ForegroundColor Blue  
Write-Host " Check Terminals: Look for trunk and cargo-tauri processes" -ForegroundColor Yellow
Write-Host " Documentation:   See docs/ONBOARDING.md for troubleshooting" -ForegroundColor Cyan
Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
Write-Host ""
Write-Host " Happy coding with Leptos + Tauri v2! " -ForegroundColor Green
