# 🎯 Bad Character Scanner - Quick Reference

**Version 0.3.1** | **Production Ready** | **All Platforms**

---

## ⚡ **Instant Commands**

### **🖥️ Desktop GUI** (Recommended)
```powershell
# Launch application
.\Bad-Character-Scanner.exe

# Development mode
cargo tauri dev
```

### **⌨️ CLI Analysis** (Automation)
```powershell
# Quick file analysis
.\bcs-cli.exe analyze file.txt --format json

# Directory scanning
.\bcs-cli.exe scan ./codebase --recursive

# Export results
.\bcs-cli.exe export results.json --format markdown
```

### **📜 Bash Scripting** (Advanced)
```bash
# Make executable (first time)
chmod +x scripts/analyze.sh

# Full codebase analysis
./scripts/analyze.sh --directory ./src --output ./reports

# Single file scan
./scripts/analyze.sh --file suspicious.js --format table
```

---

## 📋 **Command Reference Matrix**

### **GUI Interface**
| Action | Method | Time |
|--------|--------|------|
| **Open File** | Drag & drop or File menu | 5 seconds |
| **Analyze Text** | Paste text in input area | Instant |
| **View Results** | Results tab opens automatically | Real-time |
| **Export Data** | Export menu → Choose format | 10 seconds |
| **Clean Text** | Clean tab → Apply filters | 5 seconds |

### **CLI Commands**
| Command | Purpose | Syntax |
|---------|---------|--------|
| `analyze` | File/directory analysis | `bcs-cli analyze <path> [options]` |
| `scan` | Quick threat detection | `bcs-cli scan <path> --recursive` |
| `clean` | Text sanitization | `bcs-cli clean <input> <output>` |
| `export` | Format conversion | `bcs-cli export <file> --format <type>` |
| `batch` | Multiple file processing | `bcs-cli batch <directory>` |

### **Bash Script Options**
| Option | Description | Example |
|--------|-------------|---------|
| `--file` | Single file analysis | `--file script.js` |
| `--directory` | Directory scanning | `--directory ./src` |
| `--recursive` | Include subdirectories | `--recursive` |
| `--format` | Output format | `--format json` |
| `--output` | Output directory | `--output ./reports` |
| `--verbose` | Detailed logging | `--verbose` |
| `--quiet` | Minimal output | `--quiet` |

---

## � **Output Format Guide**

### **📊 JSON** (Default - Machine Readable)
```json
{
  "threats_found": 3,
  "invisible_chars": 2,
  "homographs": 1,
  "file_status": "UNSAFE"
}
```
**Best for:** APIs, automation, data processing

### **📝 Markdown** (Human Readable)
```markdown
## Analysis Results
- ⚠️ **Threats Found:** 3
- 👻 **Invisible Characters:** 2  
- 🔤 **Homographs:** 1
- 🔴 **Status:** UNSAFE
```
**Best for:** Reports, documentation, team sharing

### **📋 Table** (Visual)
```
┌─────────────────┬─────────┐
│ Threat Type     │ Count   │
├─────────────────┼─────────┤
│ Invisible Chars │ 2       │
│ Homographs      │ 1       │
│ Status          │ UNSAFE  │
└─────────────────┴─────────┘
```
**Best for:** Terminal output, console logs
- **Example**: `--format text`

---

## 🔍 What Gets Detected

### High-Risk Characters
- **Zero-width spaces** (U+200B, U+200C, U+200D)
- **Bidirectional overrides** (U+202D, U+202E)
- **Homograph attacks** (mixed scripts: а vs a)
- **Control characters** (non-printable Unicode)

### Security Threats
- **Domain spoofing**: google.com vs gοοgle.com
- **Code injection**: Hidden characters in code
- **Data hiding**: Steganography via Unicode
- **Social engineering**: Misleading text direction

---

## 📊 Exit Codes (CLI)

| Code | Meaning | Description |
|------|---------|-------------|
| 0 | Success | Operation completed successfully |
| 1 | General error | Unspecified error occurred |
| 2 | Invalid arguments | Wrong command syntax or parameters |
| 3 | File not found | Specified file or directory doesn't exist |
| 4 | Analysis failed | Error during character analysis |
| 5 | Export failed | Error during result export |
| 6 | Missing dependencies | Required tools not installed |

---

## 🛠️ Troubleshooting Quick Fixes

### Common Issues
| Problem | Solution |
|---------|----------|
| "Command not found" | Use full path: `./target/release/analyzer_cli.exe` |
| "Permission denied" | Make executable: `chmod +x scripts/codebase_analyzer.sh` |
| "Missing dependencies" | Install: Rust toolchain, jq |
| "File not found" | Check path and file existence |
| GUI won't start | Run as administrator, check antivirus |

### Performance Issues
| Problem | Solution |
|---------|----------|
| Slow analysis | Use CLI for large files, reduce scope |
| Memory errors | Process in smaller batches |
| Network files | Copy locally before analysis |
| Large output | Use text format instead of JSON |

---

## 📁 File Locations

### Project Structure
```
Leptos_TaurieV2_BCS/
├── analyzer_cli.exe           # CLI binary (Windows)
├── scripts/
│   └── codebase_analyzer.sh   # Bash script interface
├── docs/
│   └── usermanuals/
│       └── USER_MANUAL.md     # Complete manual
├── reports/                   # Default output directory
└── test_data_secure/         # Test files for validation
```

### Output Files
- **Analysis results**: `./reports/analysis_[name]_[timestamp].[format]`
- **Cleaned files**: `[original_name]_cleaned.[ext]`
- **Log files**: `/tmp/laptos_analyzer_[pid]/analyzer.log`

---

## 🔗 Integration Examples

### Git Pre-commit Hook
```bash
#!/bin/bash
# .git/hooks/pre-commit
git diff --cached --name-only | while read file; do
    if ! ./analyzer_cli analyze "$file" >/dev/null; then
        echo "❌ $file contains suspicious characters"
        exit 1
    fi
done
```

### CI/CD Pipeline (GitHub Actions)
```yaml
- name: Security scan
  run: |
    ./analyzer_cli analyze . json > scan_results.json
    if [ $? -ne 0 ]; then exit 1; fi
```

### Batch Processing
```bash
# Process all JavaScript files
find . -name "*.js" -exec ./analyzer_cli analyze {} json \;

# Using Bash script for directories
for dir in src/ lib/ test/; do
    ./scripts/codebase_analyzer.sh analyze "$dir"
done
```

---

## 📞 Getting Help

### Built-in Help
```bash
# CLI help
./analyzer_cli.exe --help

# Bash script help  
./scripts/codebase_analyzer.sh --help

# GUI help
F1 key or Help menu in application
```

### Documentation
- **Complete Manual**: `docs/usermanuals/USER_MANUAL.md`
- **Features Guide**: `docs/guides/FEATURES.md`
- **Quick Reference**: `docs/guides/QUICK_REFERENCE.md`

### System Diagnostics
```bash
# Check system health
./scripts/codebase_analyzer.sh health

# Run built-in tests
./scripts/codebase_analyzer.sh test

# Demo with sample data
./scripts/codebase_analyzer.sh demo
```

---

**© 2025 Bad Character Scanner v0.2.0 - Production Ready**  
**Quick Reference Card - Keep handy for daily use!**
