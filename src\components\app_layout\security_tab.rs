use leptos::*;
use crate::AnalysisResults;

#[component]
pub fn SecurityTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
    view! {
        <div class="tab-content-inner">
            <h4>"🔒 Security Analysis"</h4>
            {move || results.get().map(|res| {
                view! {
                    <div class="security-summary">
                        <div class="risk-level">
                            <img src="assets/images/ui_images_40x40/uiimg_005.png" class="risk-label icon-40" style="width:40px;height:40px;display:inline-block;" alt="Risk Level Icon" />
                            <img src="assets/images/ui_images_40x40/uiimg_006.png" class="risk-value icon-40" style="width:40px;height:40px;display:inline-block;" alt="Risk Value Icon" />
                        </div>
                    </div>
                    
                    <div class="security-threats mt-lg">
                        <h5>"Phishing Indicators:"</h5>
                        {if res.security_analysis.phishing_indicators.is_empty() {
                            view! { <p class="text-success">"✅ No phishing indicators detected"</p> }.into_view()
                        } else {
                            view! {
                                <ul class="threat-list">
                                    {res.security_analysis.phishing_indicators.iter().map(|indicator| {
                                        view! {
                                            <li class="threat-item">{indicator.clone()}</li>
                                        }
                                    }).collect_view()}
                                </ul>
                            }.into_view()
                        }}
                        <h5 class="mt-lg">"Homograph Attacks:"</h5>
                        {if res.security_analysis.homograph_attacks.is_empty() {
                            view! { <p class="text-success">"✅ No homograph attacks detected"</p> }.into_view()
                        } else {
                            view! {
                                <ul class="threat-list">
                                    {res.security_analysis.homograph_attacks.iter().map(|attack| {
                                        view! {
                                            <li class="threat-item">{attack.clone()}</li>
                                        }
                                    }).collect_view()}
                                </ul>
                            }.into_view()
                        }}
                    </div>
                    
                    <div class="recommendations mt-lg">
                        <h5>"🛡️ Security Recommendations:"</h5>
                        <ul>
                            <li>"Enable input validation for all text inputs"</li>
                            <li>"Use Unicode normalization before processing"</li>
                            <li>"Implement character allowlists for critical fields"</li>
                            <li>"Regular security audits for text processing"</li>
                        </ul>
                    </div>
                }
            }).unwrap_or_else(|| view! { <> <div>"No analysis results available"</div> </> })}
        </div>
    }
}
