# TICKET: TEST-BASH-001
**Title**: Implement Bash CLI Interface Tests

## Status
- **Priority**: 🟡 High
- **Status**: 📋 Planned
- **Created**: 2025-06-28
- **Parent**: TEST-SUITE-001

## Description
Implement comprehensive tests for the Bash command-line interface of the Bad Character Scanner, ensuring all functionality works correctly through shell commands.

## Test Cases

### 1. Basic Analysis Tests
```bash
# Test 1: Analyze simple text
echo "Hello‌World" | bcs analyze

# Test 2: Analyze file
bcs analyze --file test.txt

# Test 3: Analyze with JSON output
bcs analyze --json --file test.txt
```

### 2. Codebase Scanning Tests
```bash
# Test 1: Scan directory
bcs scan /path/to/project

# Test 2: Scan with filters
bcs scan /path/to/project --include "*.js,*.ts" --exclude "node_modules"

# Test 3: Recursive scan
bcs scan /path/to/project --recursive
```

### 3. Cleaning Operations Tests
```bash
# Test 1: Clean single file
bcs clean input.txt -o output.txt

# Test 2: Clean directory
bcs clean /input/dir -o /output/dir

# Test 3: Clean with backup
bcs clean input.txt --backup
```

### 4. Pipeline Tests
```bash
# Test 1: Pipe from curl
curl https://example.com | bcs analyze

# Test 2: Chain with other tools
cat *.js | bcs analyze | jq '.suspicious_characters'

# Test 3: Batch processing
find . -name "*.txt" -exec bcs analyze {} \;
```

## Expected Behaviors
- Proper exit codes (0 for success, non-zero for errors)
- Consistent output formatting
- Proper handling of stdin/stdout/stderr
- Signal handling (SIGINT, SIGTERM)

## Error Scenarios
- [ ] Invalid file paths
- [ ] Permission denied
- [ ] Corrupted input
- [ ] Out of memory
- [ ] Interrupted operations

---
*Last updated: 2025-06-28*