// Type definitions for Advanced Security Analysis components
// Shared data structures for security analysis UI - By <PERSON> - 2025
// Data structures for Advanced Security Analysis - By <PERSON> - 2025
// Type definitions that ensure consistent data between frontend and backend

use serde::{Serialize, Deserialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BasicProgress {
    pub current: u32,
    pub total: u32,
    pub percentage: f32,
    pub current_file: String,
    pub status: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct AnalysisStats {
    pub analysis_id: String,
    pub total_files: u64,
    pub files_analyzed: u64,
    pub overall_risk_score: f64,
    pub homoglyph_threats: usize,
    pub pattern_threats: usize,
    pub security_threats: usize,
    pub unicode_threats: usize,
}

impl AnalysisStats {
    pub fn total_threats(&self) -> usize {
        self.homoglyph_threats + self.pattern_threats + self.security_threats + self.unicode_threats
    }
    
    pub fn risk_level(&self) -> (&'static str, &'static str, &'static str) {
        if self.overall_risk_score >= 80.0 { 
            ("Critical", "text-red-800", "bg-red-100") 
        } else if self.overall_risk_score >= 60.0 { 
            ("High", "text-red-600", "bg-red-50") 
        } else if self.overall_risk_score >= 40.0 { 
            ("Medium", "text-yellow-600", "bg-yellow-50") 
        } else if self.overall_risk_score >= 20.0 { 
            ("Low", "text-green-600", "bg-green-50") 
        } else { 
            ("Minimal", "text-green-800", "bg-green-100") 
        }
    }
}