use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UnicodeAnalysis {
    pub invisible_chars: Vec<InvisibleCharacter>,
    pub bidirectional_overrides: Vec<BidirectionalThreat>,
    pub normalization_issues: Vec<NormalizationIssue>,
    pub encoding_anomalies: Vec<EncodingAnomaly>,
    pub script_mixing: Vec<ScriptMixing>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct InvisibleCharacter {
    pub position: usize,
    pub character: char,
    pub unicode_name: String,
    pub unicode_point: String,
    pub threat_level: ThreatLevel,
    pub description: String,
    pub context: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BidirectionalThreat {
    pub position: usize,
    pub override_type: BidiOverrideType,
    pub affected_text: String,
    pub visual_spoofing_potential: ThreatLevel,
    pub description: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum BidiOverrideType {
    LeftToRightOverride,
    RightToLeftOverride,
    LeftToRightEmbedding,
    RightToLeftEmbedding,
    PopDirectionalFormatting,
    LeftToRightIsolate,
    RightToLeftIsolate,
    FirstStrongIsolate,
    PopDirectionalIsolate,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalizationIssue {
    pub position: usize,
    pub original_form: String,
    pub normalized_form: String,
    pub normalization_type: NormalizationType,
    pub security_implication: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NormalizationType {
    CompositionNFC,
    DecompositionNFD,
    CompatibilityNFKC,
    CompatibilityNFKD,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingAnomaly {
    pub position: usize,
    pub detected_encoding: String,
    pub expected_encoding: String,
    pub anomaly_type: EncodingAnomalyType,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EncodingAnomalyType {
    ByteOrderMark,
    MixedEncoding,
    InvalidSequence,
    OverlongEncoding,
    SurrogateCharacter,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScriptMixing {
    pub position: usize,
    pub scripts: Vec<String>,
    pub mixed_text: String,
    pub spoofing_risk: ThreatLevel,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatLevel {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

pub struct UnicodeAnalyzer {
    invisible_chars: HashMap<char, InvisibleCharInfo>,
    bidi_chars: HashMap<char, BidiCharInfo>,
    script_detector: ScriptDetector,
}

#[derive(Debug, Clone)]
struct InvisibleCharInfo {
    name: String,
    threat_level: ThreatLevel,
    description: String,
}

#[derive(Debug, Clone)]
struct BidiCharInfo {
    override_type: BidiOverrideType,
    threat_level: ThreatLevel,
    description: String,
}

#[derive(Debug)]
struct ScriptDetector {
    script_ranges: HashMap<String, Vec<(u32, u32)>>,
}

impl UnicodeAnalyzer {
    pub fn new() -> Self {
        let mut analyzer = UnicodeAnalyzer {
            invisible_chars: HashMap::new(),
            bidi_chars: HashMap::new(),
            script_detector: ScriptDetector::new(),
        };
        
        analyzer.initialize_invisible_chars();
        analyzer.initialize_bidi_chars();
        analyzer
    }

    fn initialize_invisible_chars(&mut self) {
        // Zero-width characters
        self.invisible_chars.insert('\u{200B}', InvisibleCharInfo {
            name: "ZERO WIDTH SPACE".to_string(),
            threat_level: ThreatLevel::Critical,
            description: "Invisible character used in steganographic attacks and code injection".to_string(),
        });

        self.invisible_chars.insert('\u{200C}', InvisibleCharInfo {
            name: "ZERO WIDTH NON-JOINER".to_string(),
            threat_level: ThreatLevel::High,
            description: "May be used to break word boundaries in security filters".to_string(),
        });

        self.invisible_chars.insert('\u{200D}', InvisibleCharInfo {
            name: "ZERO WIDTH JOINER".to_string(),
            threat_level: ThreatLevel::High,
            description: "Can modify character appearance and bypass text filters".to_string(),
        });

        self.invisible_chars.insert('\u{FEFF}', InvisibleCharInfo {
            name: "ZERO WIDTH NO-BREAK SPACE (BOM)".to_string(),
            threat_level: ThreatLevel::Medium,
            description: "BOM character that may cause parsing issues if misplaced".to_string(),
        });

        self.invisible_chars.insert('\u{2060}', InvisibleCharInfo {
            name: "WORD JOINER".to_string(),
            threat_level: ThreatLevel::Medium,
            description: "Invisible character that prevents line breaks".to_string(),
        });

        // Line and paragraph separators
        self.invisible_chars.insert('\u{2028}', InvisibleCharInfo {
            name: "LINE SEPARATOR".to_string(),
            threat_level: ThreatLevel::Medium,
            description: "Unicode line separator that may bypass newline filtering".to_string(),
        });

        self.invisible_chars.insert('\u{2029}', InvisibleCharInfo {
            name: "PARAGRAPH SEPARATOR".to_string(),
            threat_level: ThreatLevel::Medium,
            description: "Unicode paragraph separator that may cause parsing issues".to_string(),
        });

        // Various space characters
        self.invisible_chars.insert('\u{00A0}', InvisibleCharInfo {
            name: "NO-BREAK SPACE".to_string(),
            threat_level: ThreatLevel::Low,
            description: "Non-breaking space that looks identical to regular space".to_string(),
        });

        self.invisible_chars.insert('\u{1680}', InvisibleCharInfo {
            name: "OGHAM SPACE MARK".to_string(),
            threat_level: ThreatLevel::Medium,
            description: "Unusual space character that may bypass space filtering".to_string(),
        });
    }

    fn initialize_bidi_chars(&mut self) {
        self.bidi_chars.insert('\u{202A}', BidiCharInfo {
            override_type: BidiOverrideType::LeftToRightEmbedding,
            threat_level: ThreatLevel::High,
            description: "Can cause visual spoofing by forcing left-to-right text direction".to_string(),
        });

        self.bidi_chars.insert('\u{202B}', BidiCharInfo {
            override_type: BidiOverrideType::RightToLeftEmbedding,
            threat_level: ThreatLevel::High,
            description: "Can cause visual spoofing by forcing right-to-left text direction".to_string(),
        });

        self.bidi_chars.insert('\u{202C}', BidiCharInfo {
            override_type: BidiOverrideType::PopDirectionalFormatting,
            threat_level: ThreatLevel::Medium,
            description: "Terminates directional embedding or override".to_string(),
        });

        self.bidi_chars.insert('\u{202D}', BidiCharInfo {
            override_type: BidiOverrideType::LeftToRightOverride,
            threat_level: ThreatLevel::Critical,
            description: "CRITICAL: Forces left-to-right override, can completely change text appearance".to_string(),
        });

        self.bidi_chars.insert('\u{202E}', BidiCharInfo {
            override_type: BidiOverrideType::RightToLeftOverride,
            threat_level: ThreatLevel::Critical,
            description: "CRITICAL: Forces right-to-left override, can completely reverse text appearance".to_string(),
        });

        self.bidi_chars.insert('\u{2066}', BidiCharInfo {
            override_type: BidiOverrideType::LeftToRightIsolate,
            threat_level: ThreatLevel::High,
            description: "Isolates text with left-to-right directionality".to_string(),
        });

        self.bidi_chars.insert('\u{2067}', BidiCharInfo {
            override_type: BidiOverrideType::RightToLeftIsolate,
            threat_level: ThreatLevel::High,
            description: "Isolates text with right-to-left directionality".to_string(),
        });

        self.bidi_chars.insert('\u{2068}', BidiCharInfo {
            override_type: BidiOverrideType::FirstStrongIsolate,
            threat_level: ThreatLevel::Medium,
            description: "Isolates text with first strong character directionality".to_string(),
        });

        self.bidi_chars.insert('\u{2069}', BidiCharInfo {
            override_type: BidiOverrideType::PopDirectionalIsolate,
            threat_level: ThreatLevel::Medium,
            description: "Terminates directional isolate".to_string(),
        });
    }

    pub fn analyze_text(&self, content: &str) -> UnicodeAnalysis {
        let invisible_chars = self.detect_invisible_characters(content);
        let bidirectional_overrides = self.detect_bidirectional_overrides(content);
        let normalization_issues = self.detect_normalization_issues(content);
        let encoding_anomalies = self.detect_encoding_anomalies(content);
        let script_mixing = self.detect_script_mixing(content);

        UnicodeAnalysis {
            invisible_chars,
            bidirectional_overrides,
            normalization_issues,
            encoding_anomalies,
            script_mixing,
        }
    }

    fn detect_invisible_characters(&self, content: &str) -> Vec<InvisibleCharacter> {
        let mut invisible_chars = Vec::new();

        for (pos, ch) in content.char_indices() {
            if let Some(char_info) = self.invisible_chars.get(&ch) {
                let context = self.extract_context(content, pos, 30);
                
                invisible_chars.push(InvisibleCharacter {
                    position: pos,
                    character: ch,
                    unicode_name: char_info.name.clone(),
                    unicode_point: format!("U+{:04X}", ch as u32),
                    threat_level: char_info.threat_level.clone(),
                    description: char_info.description.clone(),
                    context,
                });
            }
        }

        invisible_chars
    }

    fn detect_bidirectional_overrides(&self, content: &str) -> Vec<BidirectionalThreat> {
        let mut bidi_threats = Vec::new();
        let mut override_stack = Vec::new();

        for (pos, ch) in content.char_indices() {
            if let Some(bidi_info) = self.bidi_chars.get(&ch) {
                match bidi_info.override_type {
                    BidiOverrideType::PopDirectionalFormatting |
                    BidiOverrideType::PopDirectionalIsolate => {
                        if let Some((start_pos, start_char)) = override_stack.pop() {
                            let affected_text = &content[start_pos..pos];

                            // Get the description from the starting character's BidiCharInfo
                            let description = if let Some(start_bidi_info) = self.bidi_chars.get(&start_char) {
                                format!("{} (affecting {} characters)",
                                    start_bidi_info.description,
                                    pos - start_pos)
                            } else {
                                format!("Bidirectional override affecting {} characters", pos - start_pos)
                            };

                            bidi_threats.push(BidirectionalThreat {
                                position: start_pos,
                                override_type: bidi_info.override_type.clone(),
                                affected_text: affected_text.to_string(),
                                visual_spoofing_potential: bidi_info.threat_level.clone(),
                                description,
                            });
                        }
                    }
                    _ => {
                        // Store both position and character for later description lookup
                        override_stack.push((pos, ch));
                    }
                }
            }
        }

        // Handle unclosed overrides
        for (start_pos, start_char) in override_stack {
            let affected_text = &content[start_pos..];

            // Use the description from the BidiCharInfo for unclosed overrides
            let description = if let Some(bidi_info) = self.bidi_chars.get(&start_char) {
                format!("{} (unclosed override extends to end of text)", bidi_info.description)
            } else {
                "Unclosed bidirectional override extends to end of text".to_string()
            };

            bidi_threats.push(BidirectionalThreat {
                position: start_pos,
                override_type: BidiOverrideType::LeftToRightOverride, // Default
                affected_text: affected_text.chars().take(50).collect(),
                visual_spoofing_potential: ThreatLevel::High,
                description,
            });
        }

        // Also detect standalone bidirectional characters that might not be paired
        for (pos, ch) in content.char_indices() {
            if let Some(bidi_info) = self.bidi_chars.get(&ch) {
                // Check if this character is already reported as part of a paired override
                let already_reported = bidi_threats.iter().any(|threat| {
                    pos >= threat.position && pos < threat.position + threat.affected_text.len()
                });

                if !already_reported {
                    // Report standalone bidirectional character
                    let context = self.extract_context(content, pos, 30);
                    bidi_threats.push(BidirectionalThreat {
                        position: pos,
                        override_type: bidi_info.override_type.clone(),
                        affected_text: context.clone(),
                        visual_spoofing_potential: bidi_info.threat_level.clone(),
                        description: format!("{} (standalone character)", bidi_info.description),
                    });
                }
            }
        }

        bidi_threats
    }

    fn detect_normalization_issues(&self, content: &str) -> Vec<NormalizationIssue> {
        let mut issues = Vec::new();

        // This is a simplified implementation
        // In practice, you'd use a Unicode normalization library
        for (pos, ch) in content.char_indices() {
            // Check for characters that have multiple representations
            if self.has_normalization_variants(ch) {
                let _context = self.extract_context(content, pos, 20);
                
                issues.push(NormalizationIssue {
                    position: pos,
                    original_form: ch.to_string(),
                    normalized_form: self.get_normalized_form(ch),
                    normalization_type: NormalizationType::CompositionNFC,
                    security_implication: "Character may have multiple representations causing comparison bypass".to_string(),
                });
            }
        }

        issues
    }

    fn detect_encoding_anomalies(&self, content: &str) -> Vec<EncodingAnomaly> {
        let mut anomalies = Vec::new();

        // Check for BOM in middle of text
        for (pos, ch) in content.char_indices() {
            if ch == '\u{FEFF}' && pos > 0 {
                anomalies.push(EncodingAnomaly {
                    position: pos,
                    detected_encoding: "UTF-8 with embedded BOM".to_string(),
                    expected_encoding: "UTF-8".to_string(),
                    anomaly_type: EncodingAnomalyType::ByteOrderMark,
                    description: "BOM character found in middle of text, may cause parsing issues".to_string(),
                });
            }

            // Check for surrogate characters (shouldn't appear in valid UTF-8)
            if (ch as u32) >= 0xD800 && (ch as u32) <= 0xDFFF {
                anomalies.push(EncodingAnomaly {
                    position: pos,
                    detected_encoding: "Invalid UTF-8".to_string(),
                    expected_encoding: "UTF-8".to_string(),
                    anomaly_type: EncodingAnomalyType::SurrogateCharacter,
                    description: "Surrogate character detected, indicates encoding corruption".to_string(),
                });
            }
        }

        anomalies
    }

    fn detect_script_mixing(&self, content: &str) -> Vec<ScriptMixing> {
        let mut script_mixing = Vec::new();
        let mut current_scripts = Vec::new();
        let mut word_start = 0;

        for (pos, ch) in content.char_indices() {
            if ch.is_whitespace() || ch.is_ascii_punctuation() {
                if current_scripts.len() > 1 {
                    let word = &content[word_start..pos];
                    script_mixing.push(ScriptMixing {
                        position: word_start,
                        scripts: current_scripts.clone(),
                        mixed_text: word.to_string(),
                        spoofing_risk: self.assess_script_mixing_risk(&current_scripts),
                        description: format!("Mixed scripts detected: {}", current_scripts.join(", ")),
                    });
                }
                current_scripts.clear();
                word_start = pos + ch.len_utf8();
            } else {
                let script = self.script_detector.detect_script(ch);
                if !current_scripts.contains(&script) {
                    current_scripts.push(script);
                }
            }
        }

        script_mixing
    }

    fn extract_context(&self, content: &str, position: usize, window: usize) -> String {
        // Convert content to char indices for safe slicing
        let chars: Vec<char> = content.chars().collect();
        let char_count = chars.len();
        
        // Use char indices instead of byte positions to avoid char-boundary panics
        let start_char = position.saturating_sub(window);
        let end_char = (position + window).min(char_count);
        
        // Safely slice using char indices
        let context_chars: String = if start_char < char_count && end_char <= char_count {
            chars[start_char..end_char].iter().collect()
        } else {
            // Fallback to safe slicing if indices are out of bounds
            let safe_start = start_char.min(char_count);
            let safe_end = end_char.min(char_count);
            chars[safe_start..safe_end].iter().collect()
        };
        
        // Replace newlines and carriage returns for display
        context_chars.replace('\n', "\\n").replace('\r', "\\r")
    }

    fn has_normalization_variants(&self, ch: char) -> bool {
        // Simplified check for characters with normalization variants
        matches!(ch, 
            '\u{00C0}'..='\u{017F}' |  // Latin Extended
            '\u{1E00}'..='\u{1EFF}' |  // Latin Extended Additional
            '\u{0300}'..='\u{036F}'    // Combining Diacritical Marks
        )
    }

    fn get_normalized_form(&self, ch: char) -> String {
        // Simplified normalization - in practice use unicode-normalization crate
        ch.to_string()
    }

    fn assess_script_mixing_risk(&self, scripts: &[String]) -> ThreatLevel {
        if scripts.contains(&"Latin".to_string()) && 
           (scripts.contains(&"Cyrillic".to_string()) || scripts.contains(&"Greek".to_string())) {
            ThreatLevel::High
        } else if scripts.len() > 2 {
            ThreatLevel::Medium
        } else {
            ThreatLevel::Low
        }
    }
}

impl ScriptDetector {
    fn new() -> Self {
        let mut detector = ScriptDetector {
            script_ranges: HashMap::new(),
        };
        
        // Initialize basic script ranges
        detector.script_ranges.insert("Latin".to_string(), vec![
            (0x0041, 0x005A), // Basic Latin uppercase
            (0x0061, 0x007A), // Basic Latin lowercase
            (0x00C0, 0x017F), // Latin-1 Supplement and Extended-A
        ]);
        
        detector.script_ranges.insert("Cyrillic".to_string(), vec![
            (0x0400, 0x04FF), // Cyrillic
            (0x0500, 0x052F), // Cyrillic Supplement
        ]);
        
        detector.script_ranges.insert("Greek".to_string(), vec![
            (0x0370, 0x03FF), // Greek and Coptic
        ]);
        
        detector.script_ranges.insert("Arabic".to_string(), vec![
            (0x0600, 0x06FF), // Arabic
        ]);
        
        detector
    }

    fn detect_script(&self, ch: char) -> String {
        let codepoint = ch as u32;
        
        for (script, ranges) in &self.script_ranges {
            for (start, end) in ranges {
                if codepoint >= *start && codepoint <= *end {
                    return script.clone();
                }
            }
        }
        
        "Unknown".to_string()
    }
}
