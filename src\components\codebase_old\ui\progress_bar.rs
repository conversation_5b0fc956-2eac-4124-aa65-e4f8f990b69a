use leptos::*;
use crate::components::codebase::types::BasicProgress;
use std::time::{Duration, Instant};

#[component]
pub fn ProgressBar(
    progress: ReadSignal<Option<BasicProgress>>,
) -> impl IntoView {
    // Track when analysis started to determine if we should show progress bar
    let (analysis_start_time, set_analysis_start_time) = create_signal::<Option<Instant>>(None);
    let (show_progress, set_show_progress) = create_signal(false);

    // Monitor progress changes to manage visibility
    create_effect(move |_| {
        if let Some(progress_data) = progress.get() {
            // If this is the start of analysis, record the time
            if progress_data.current == 0 && analysis_start_time.get().is_none() {
                set_analysis_start_time.set(Some(Instant::now()));
                set_show_progress.set(false); // Don't show immediately

                // Set a timer to show progress bar after 400ms if still running
                spawn_local(async move {
                    gloo_timers::future::sleep(Duration::from_millis(400)).await;

                    // Check if analysis is still running
                    if let Some(current_progress) = progress.get() {
                        if current_progress.percentage < 100.0 {
                            set_show_progress.set(true);
                        }
                    }
                });
            }

            // If analysis is complete, hide progress bar
            if progress_data.percentage >= 100.0 {
                // Small delay before hiding to show completion
                spawn_local(async move {
                    gloo_timers::future::sleep(Duration::from_millis(500)).await;
                    set_show_progress.set(false);
                    set_analysis_start_time.set(None);
                });
            }
        } else {
            // No progress data means analysis hasn't started or is reset
            set_show_progress.set(false);
            set_analysis_start_time.set(None);
        }
    });
    view! {
        <div
            class="progress-container"
            style=move || {
                if show_progress.get() {
                    "display: block;"
                } else {
                    "display: none;"
                }
            }
        >
            <div class="card p-6 border-l-4 border-l-purple-500 animate-fade-in">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <div class="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-800">"Analyzing Codebase"</h3>
                    <p class="text-sm text-gray-600">"Scanning files for security threats and suspicious characters"</p>
                </div>
                {move || {
                    progress
                        .get()
                        .as_ref()
                        .map(|p| {
                            view! {
                                <div class="text-right">
                                    <div class="text-lg font-bold text-purple-600">
                                        {format!("{:.1}%", p.percentage)}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {format!("{}/{} files", p.current, p.total)}
                                    </div>
                                </div>
                            }
                        })
                }}
            </div>

            // Professional progress bar
            <div class="mb-4">
                <div class="progress-bar">
                    <div
                        class="progress-fill"
                        style=move || {
                            let percentage = progress
                                .get()
                                .as_ref()
                                .map(|p| p.percentage)
                                .unwrap_or(0.0);
                            format!("width: {}%", percentage)
                        }
                    ></div>
                </div>
            </div>

            // Status and current file
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <div class="status-badge success">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            "Active"
                        </div>
                        <span class="text-sm text-gray-600">
                            {move || {
                                progress
                                    .get()
                                    .as_ref()
                                    .map(|p| p.status.clone())
                                    .unwrap_or_else(|| "Starting analysis...".to_string())
                            }}
                        </span>
                    </div>
                </div>

                {move || {
                    progress
                        .get()
                        .as_ref()
                        .and_then(|p| {
                            if !p.current_file.is_empty() && p.current_file != "Starting analysis..." {
                                Some(
                                    view! {
                                        <div class="bg-gray-50 rounded-lg p-3 border">
                                            <div class="flex items-center gap-2 mb-1">
                                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span class="text-xs font-medium text-gray-600">"Currently analyzing:"</span>
                                            </div>
                                            <div
                                                class="text-sm font-mono text-gray-800 break-all"
                                                title=p.current_file.clone()
                                            >
                                                {p.current_file.clone()}
                                            </div>
                                        </div>
                                    },
                                )
                            } else {
                                Some(
                                    view! {
                                        <div class="text-center py-2">
                                            <div class="inline-flex items-center gap-2 text-sm text-purple-600">
                                                <div class="animate-pulse w-2 h-2 bg-purple-500 rounded-full"></div>
                                                "Preparing comprehensive analysis..."
                                            </div>
                                        </div>
                                    },
                                )
                            }
                        })
                }}
            </div>
            </div>
        </div>
    }
}