use leptos::*;

#[component]
pub fn LegalDisclaimerModal(
    show: leptos::ReadSignal<bool>,
    on_acknowledge: leptos::WriteSignal<bool>,
) -> impl IntoView {
    view! {
        <div class=move || if show.get() { "modal is-active" } else { "modal" }>
            <div class="modal-background"></div>
            <div class="modal-card">
                <header class="modal-card-head">
                    <p class="modal-card-title">"⚠️ IMPORTANT SECURITY NOTICE ⚠️"</p>
                </header>
                <section class="modal-card-body">
                    <div class="content">
                        <p>"This scan is NOT a guarantee that your code is 100% free of malicious characters."</p>
                        <p><strong>"LIMITATIONS:"</strong></p>
                        <ul>
                            <li>"New visual spoofing attacks emerge constantly"</li>
                            <li>"Homoglyph attacks evolve with new Unicode standards"</li>
                            <li>"Zero-width and invisible character techniques advance regularly"</li>
                            <li>"This tool detects well-known patterns only"</li>
                        </ul>
                        <p><strong>"RECOMMENDATION:"</strong></p>
                        <p>"For mission-critical applications requiring comprehensive security assurance, consider our Enterprise Security Solutions with:"</p>
                        <ul>
                            <li>"Real-time threat intelligence updates"</li>
                            <li>"Advanced AI-powered detection"</li>
                            <li>"Professional security audit services"</li>
                            <li>"Legal compliance guarantees"</li>
                        </ul>
                        <p><strong>"DISCLAIMER OF WARRANTIES:"</strong></p>
                        <p>"This software is provided AS IS without warranty of any kind. The developers make no representations about the completeness, accuracy, or effectiveness of this security scan."</p>
                        <p><strong>"LIMITATION OF LIABILITY:"</strong></p>
                        <p>"In no event shall the developers be liable for any damages arising from the use of this software or reliance on scan results."</p>
                        <p><strong>"USER RESPONSIBILITY:"</strong></p>
                        <p>"Users acknowledge that cybersecurity requires multiple layers of protection and professional security practices beyond automated scanning."</p>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-success" on:click=move |_| on_acknowledge.set(true)>"I Understand and Acknowledge"</button>
                    <a class="button" href="https://www.badcharacterscanner.com/enterprise" target="_blank">"Learn About Enterprise Solutions"</a>
                </footer>
            </div>
        </div>
    }
}
