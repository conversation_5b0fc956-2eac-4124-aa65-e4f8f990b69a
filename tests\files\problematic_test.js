// This file contains various problematic characters for testing
function testFunction() {
    // Zero-width characters (invisible)
    let variable1 = "Hello​World"; // Contains ZWSP (U+200B)
    let variable2 = "Test‌Example"; // Contains ZWNJ (U+200C)
    let variable3 = "Code‍Base"; // Contains ZWJ (U+200D)
    
    // Homograph attack (Cyrillic characters that look like Latin)
    let аpple = "fake variable"; // 'а' is Cyrillic U+0410, not Latin 'a'
    let dоmain = "suspicious.com"; // 'о' is Cyrillic U+043E, not Latin 'o'
    
    // Bidirectional text override (can hide malicious code)
    let normal = "Hello‮}; alert('XSS'); //‬World";
    
    // More problematic characters
    let bom = "﻿BOM at start"; // BOM character
    let strange = "Text with strange　space"; // Ideographic space
    
    console.log("Testing problematic characters");
    return true;
}

// This function has multiple issues
function рrocessData() { // Function name uses Cyrillic 'р' instead of Latin 'p'
    let data = "Normal text with​hidden​characters";
    return data;
}

export default testFunction;
