use tauri::Manager;

// Import the new modular structure
mod modules;
mod main_module;  // Keep for backward compatibility during transition
pub mod report_generator;
pub mod enhanced_analysis;

// Re-export key types and functions for CLI access from both old and new modules
pub use modules::{
    CodeBaseAnalysisResult, FileAnalysisDetail, CharacterInfo, EncodingInfo,
    SecurityAnalysis, AnalysisResults, BadCharactersConfig,
    AIDetectionMatch, AIDetectionResult, AssetManager, CharacterAnalyzer
};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    println!("Laptos_Tauri: Entering run() function");
    let app_builder = tauri::Builder::default()
        .setup(|app| {
            println!("Laptos_Tauri: Inside setup hook.");
            #[cfg(debug_assertions)]
            {
                println!("Laptos_Tauri: Debug assertions enabled, trying to get 'main' window.");
                if let Some(window) = app.get_webview_window("main") {
                    println!("Lapt<PERSON>_Tauri: 'main' window found. Attempting to open devtools.");
                    window.open_devtools();
                    println!("Laptos_Tauri: open_devtools() called for 'main' window.");
                } else {
                    println!("Laptos_Tauri: 'main' window NOT found, cannot open devtools.");
                }
            }
            #[cfg(not(debug_assertions))]
            {
                println!("Laptos_Tauri: Debug assertions NOT enabled.");
            }
            println!("Laptos_Tauri: Exiting setup hook.");
            Ok(())
        })
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())        .invoke_handler(tauri::generate_handler![
            main_module::analyze_characters,
            main_module::analyze_codebase,
            main_module::export_analysis,
            main_module::export_codebase_report,
            main_module::clean_codebase,
            main_module::clean_codebase_with_verification,
            main_module::batch_analyze,
            main_module::get_character_details,
            main_module::detect_encoding,
            main_module::check_homographs,
            main_module::normalize_text,
            main_module::get_script_info,
            main_module::clean_text,
            main_module::clean_text_detailed,
            main_module::generate_report,
            modules::commands::select_file,
            modules::commands::select_folder,
            main_module::get_file_types_summary,
            main_module::detect_ai_content,            modules::commands::health_check,
            // Enhanced Analysis Commands
            modules::enhanced_commands::enhanced_analyze_directory,
            modules::enhanced_commands::enhanced_analyze_file,
            modules::enhanced_commands::export_enhanced_report
        ]);

    println!("Laptos_Tauri: Builder configured, about to call .run()");    app_builder
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
pub async fn enhanced_analyze_directory(path: String) -> Result<enhanced_analysis::EnhancedAnalysisResult, String> {
    let config = enhanced_analysis::AnalyzerConfig {
        enable_pattern_detection: true,
        enable_machine_learning: false,
        enable_trend_analysis: false,
        enable_performance_profiling: true,
        batch_size: 100,
        max_file_size_mb: 10.0,
        confidence_threshold: 0.7,
        pattern_learning_enabled: false,
        auto_fix_enabled: false,    };
    
    let engine = enhanced_analysis::EnhancedAnalysisEngine::new(config)?;
    engine.analyze_comprehensive(path).await
        .map_err(|e| format!("Enhanced analysis failed: {}", e))
}

#[tauri::command]
pub async fn enhanced_analyze_file(path: String) -> Result<enhanced_analysis::EnhancedAnalysisResult, String> {
    let config = enhanced_analysis::AnalyzerConfig {
        enable_pattern_detection: true,
        enable_machine_learning: false,
        enable_trend_analysis: false,
        enable_performance_profiling: true,
        batch_size: 1,
        max_file_size_mb: 10.0,
        confidence_threshold: 0.7,
        pattern_learning_enabled: false,
        auto_fix_enabled: false,    };
    
    let engine = enhanced_analysis::EnhancedAnalysisEngine::new(config)?;
    engine.analyze_comprehensive(path).await
        .map_err(|e| format!("Enhanced file analysis failed: {}", e))
}

#[tauri::command]
pub async fn export_enhanced_report(
    analysis_result: enhanced_analysis::EnhancedAnalysisResult,
    format: String,
    output_path: String
) -> Result<String, String> {
    match format.as_str() {
        "json" => {
            let json = serde_json::to_string_pretty(&analysis_result)
                .map_err(|e| format!("JSON serialization failed: {}", e))?;
            std::fs::write(&output_path, json)
                .map_err(|e| format!("Failed to write JSON report: {}", e))?;
        }
        "markdown" => {
            let markdown = generate_markdown_report(&analysis_result);
            std::fs::write(&output_path, markdown)
                .map_err(|e| format!("Failed to write Markdown report: {}", e))?;
        }
        "csv" => {
            let csv = generate_csv_report(&analysis_result);
            std::fs::write(&output_path, csv)
                .map_err(|e| format!("Failed to write CSV report: {}", e))?;
        }
        _ => return Err(format!("Unsupported export format: {}", format)),
    }
    
    Ok(format!("Report exported successfully to: {}", output_path))
}

fn generate_markdown_report(result: &enhanced_analysis::EnhancedAnalysisResult) -> String {
    format!(r#"# Enhanced Analysis Report
    
## Executive Summary
- **Overall Risk Score**: {:.1}%
- **Files Analyzed**: {}
- **Files with Issues**: {}
- **Total Suspicious Characters**: {}
- **Health Score**: {:.1}%

## Security Assessment
- **Critical Issues**: {}
- **High Issues**: {}
- **Medium Issues**: {}
- **Low Issues**: {}

## Performance Metrics
- **Analysis Duration**: {}ms
- **Memory Usage**: {:.1}MB

## Recommendations
{}

Generated on: {}
"#,
        result.executive_summary.overall_risk_score,
        result.executive_summary.total_files_analyzed,
        result.executive_summary.files_with_issues,
        result.executive_summary.total_suspicious_chars,
        result.executive_summary.health_score,
        result.executive_summary.critical_issues,
        result.executive_summary.high_issues,
        result.executive_summary.medium_issues,
        result.executive_summary.low_issues,
        result.performance_metrics.analysis_duration_ms,
        result.performance_metrics.memory_usage_mb,
        result.recommendations.iter()
            .map(|r| format!("- **{}**: {}", r.title, r.description))
            .collect::<Vec<_>>()
            .join("\n"),
        result.metadata.timestamp.format("%Y-%m-%d %H:%M:%S UTC")
    )
}

fn generate_csv_report(result: &enhanced_analysis::EnhancedAnalysisResult) -> String {
    let mut csv = String::from("Category,Metric,Value\n");
    csv.push_str(&format!("Summary,Overall Risk Score,{:.1}\n", result.executive_summary.overall_risk_score));
    csv.push_str(&format!("Summary,Files Analyzed,{}\n", result.executive_summary.total_files_analyzed));
    csv.push_str(&format!("Summary,Files with Issues,{}\n", result.executive_summary.files_with_issues));
    csv.push_str(&format!("Summary,Suspicious Characters,{}\n", result.executive_summary.total_suspicious_chars));
    csv.push_str(&format!("Issues,Critical,{}\n", result.executive_summary.critical_issues));
    csv.push_str(&format!("Issues,High,{}\n", result.executive_summary.high_issues));
    csv.push_str(&format!("Issues,Medium,{}\n", result.executive_summary.medium_issues));
    csv.push_str(&format!("Issues,Low,{}\n", result.executive_summary.low_issues));
    csv.push_str(&format!("Performance,Duration (ms),{}\n", result.performance_metrics.analysis_duration_ms));
    csv.push_str(&format!("Performance,Memory (MB),{:.1}\n", result.performance_metrics.memory_usage_mb));
    csv
}
