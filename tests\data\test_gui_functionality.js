// Test file for GUI functionality with malicious Unicode characters
// This contains various types of suspicious characters for testing the Bad Character Scanner

// Zero-width characters (invisible)
const ZERO_WIDTH_TEST = "Hello​World"; // Contains U+200B (ZERO WIDTH SPACE)

// Homograph attack characters (look-alike)
const HOMOGRAPH_TEST = "раураӏ.соm"; // Uses Cyrillic characters that look like Latin

// RTL override characters
const RTL_TEST = "user@evil‮txe.com"; // Contains U+202E (RIGHT-TO-LEFT OVERRIDE)

// Mixed scripts
const MIXED_SCRIPT = "аpple.com"; // Mixes Cyrillic 'а' with Latin characters

// Emoji with skin tone modifiers  
const EMOJI_TEST = "👨🏿‍💻"; // Man technologist with dark skin tone

// Combining characters
const COMBINING_TEST = "e̵̴̵̶̷̸̡̢̨̧̡̢̧̨̛̛̖̗̘̙̜̝̞̟̠̣̤̥̦̩̪̫̬̭̮̯̰̱̲̳̖̗̘̙̜̝̞̟̠̣̤̥̦̩̪̫̬̭̮̯̰̱̲̳̹̺̻̼̽̾̿̀́̂̃̄̅̆̇̈̉̊̋̌̍̎̏̐̑̒̓̔̕̚";

// Invisible separators
const SEPARATOR_TEST = "word1 word2"; // Contains various invisible separators

// Control characters
const CONTROL_TEST = "file.txt"; // Contains control characters

// Punycode-related
const PUNYCODE_TEST = "xn--рӏе-7cd.com"; // Punycode representation

// Steganography potential
const STEGO_TEST = "​‌‍⁠"; // Various zero-width characters that could hide information

// Script mixing for phishing
const PHISHING_TEST = "ɢoogle.com"; // Uses small caps G that looks like regular G

export {
    ZERO_WIDTH_TEST,
    HOMOGRAPH_TEST,
    RTL_TEST,
    MIXED_SCRIPT,
    EMOJI_TEST,
    COMBINING_TEST,
    SEPARATOR_TEST,
    CONTROL_TEST,
    PUNYCODE_TEST,
    STEGO_TEST,
    PHISHING_TEST
};
