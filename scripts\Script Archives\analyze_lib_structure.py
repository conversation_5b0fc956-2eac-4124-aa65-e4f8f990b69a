#!/usr/bin/env python3
"""
Phase 1: Analysis & Extraction Script for lib.rs Reconstruction
Analyzes the current lib.rs structure and extracts working components
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path

class LibAnalyzer:
    def __init__(self, lib_path):
        self.lib_path = Path(lib_path)
        self.backup_dir = Path("scripts/backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self):
        """Create timestamped backup of current lib.rs"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"lib_rs_backup_{timestamp}.rs"
        
        with open(self.lib_path, 'r', encoding='utf-8') as src:
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
                
        print(f"✅ Backup created: {backup_path}")
        return backup_path
    
    def analyze_structure(self):
        """Analyze lib.rs structure and identify components"""
        with open(self.lib_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = {
            "total_lines": len(content.splitlines()),
            "components": {},
            "working_sections": [],
            "broken_sections": [],
            "imports": [],
            "signals": [],
            "functions": []
        }
        
        # Extract imports
        import_pattern = r'^use\s+[^;]+;'
        analysis["imports"] = re.findall(import_pattern, content, re.MULTILINE)
        
        # Extract signal definitions
        signal_pattern = r'create_rw_signal\([^)]+\)'
        analysis["signals"] = re.findall(signal_pattern, content)
        
        # Extract function definitions
        function_pattern = r'fn\s+(\w+)\s*\([^{]*\)\s*(?:->\s*[^{]+)?\s*\{'
        functions = re.findall(function_pattern, content)
        analysis["functions"] = functions
        
        # Analyze view! macro sections
        view_pattern = r'view!\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}'
        view_sections = re.findall(view_pattern, content, re.DOTALL)
        
        # Identify specific components
        components = {
            "text_analysis": self._find_component(content, "Text Analysis"),
            "codebase_analysis": self._find_component(content, "Codebase Analysis"),
            "selection_mode": self._find_component(content, "SelectionMode"),
            "actions_mode": self._find_component(content, "ActionsMode"),
            "processing_mode": self._find_component(content, "ProcessingMode"),
            "results_display": self._find_component(content, "results")
        }
        
        analysis["components"] = components
        
        # Check for incomplete sections
        self._identify_broken_sections(content, analysis)
        
        return analysis
    
    def _find_component(self, content, component_name):
        """Find a specific component in the content"""
        # Look for component patterns
        patterns = [
            rf'{component_name}.*?view!\s*\{{.*?\}}',
            rf'"{component_name}".*?view!\s*\{{.*?\}}',
            rf'class=.*?{component_name.lower()}.*?view!\s*\{{.*?\}}'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            if match:
                return {
                    "found": True,
                    "content": match.group(0),
                    "start_pos": match.start(),
                    "end_pos": match.end(),
                    "complete": self._is_complete_section(match.group(0))
                }
        
        return {"found": False, "complete": False}
    
    def _is_complete_section(self, section):
        """Check if a section appears complete"""
        # Count braces
        open_braces = section.count('{')
        close_braces = section.count('}')
        
        # Check for common incomplete patterns
        incomplete_patterns = [
            r'\{\s*$',  # Empty braces
            r'//\s*TODO',  # TODO comments
            r'//\s*INCOMPLETE',  # Incomplete comments
            r'""',  # Empty strings where content expected
        ]
        
        has_incomplete_patterns = any(re.search(pattern, section) for pattern in incomplete_patterns)
        
        return open_braces == close_braces and not has_incomplete_patterns
    
    def _identify_broken_sections(self, content, analysis):
        """Identify broken or incomplete sections"""
        lines = content.splitlines()
        
        broken_sections = []
        working_sections = []
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for incomplete patterns
            if any(pattern in line for pattern in ["// TODO", "// INCOMPLETE", '"",']):
                broken_sections.append({
                    "line": line_num,
                    "content": line.strip(),
                    "issue": "Incomplete implementation"
                })
            
            # Check for syntax issues
            if line.strip().endswith('{') and i + 1 < len(lines) and lines[i + 1].strip() == '}':
                broken_sections.append({
                    "line": line_num,
                    "content": line.strip(),
                    "issue": "Empty block"
                })
            
            # Identify working sections (text analysis is known to work)
            if "text_analysis" in line.lower() and "view!" in lines[min(i + 5, len(lines) - 1)]:
                working_sections.append({
                    "line": line_num,
                    "content": line.strip(),
                    "type": "Working component"
                })
        
        analysis["broken_sections"] = broken_sections
        analysis["working_sections"] = working_sections
    
    def extract_working_components(self, analysis):
        """Extract working components to separate files"""
        components_dir = Path("scripts/extracted_components")
        components_dir.mkdir(exist_ok=True)
        
        with open(self.lib_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract text analysis component (known working)
        text_analysis = self._extract_text_analysis_component(content)
        if text_analysis:
            with open(components_dir / "text_analysis.rs", 'w', encoding='utf-8') as f:
                f.write(text_analysis)
            print("✅ Extracted text analysis component")
        
        # Extract imports and use statements
        imports = '\n'.join(analysis["imports"])
        with open(components_dir / "imports.rs", 'w', encoding='utf-8') as f:
            f.write(imports)
        print("✅ Extracted imports")
        
        # Extract signal definitions
        signals_content = self._extract_signal_definitions(content)
        with open(components_dir / "signals.rs", 'w', encoding='utf-8') as f:
            f.write(signals_content)
        print("✅ Extracted signal definitions")
        
        return components_dir
    
    def _extract_text_analysis_component(self, content):
        """Extract the working text analysis component"""
        # Look for the text analysis tab content
        pattern = r'(.*"Text Analysis".*?(?=(\s*\}\s*,?\s*div\s*\{)|$))'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            return match.group(1)
        return None
    
    def _extract_signal_definitions(self, content):
        """Extract all signal definitions"""
        # Find all create_rw_signal and create_signal calls
        signal_patterns = [
            r'let\s+\w+\s*=\s*create_rw_signal\([^;]+;',
            r'let\s+\w+\s*=\s*create_signal\([^;]+;',
            r'let\s+\(\w+,\s*\w+\)\s*=\s*create_signal\([^;]+;'
        ]
        
        signals = []
        for pattern in signal_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            signals.extend(matches)
        
        return '\n'.join(signals)
    
    def generate_report(self, analysis):
        """Generate detailed analysis report"""
        report_path = Path("scripts/analysis_report.json")
        
        # Convert Path objects to strings for JSON serialization
        json_analysis = self._convert_paths_to_strings(analysis)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(json_analysis, f, indent=2)
        
        # Generate human-readable report
        text_report = self._generate_text_report(analysis)
        with open("scripts/analysis_report.txt", 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ Analysis report generated: {report_path}")
        return report_path
    
    def _convert_paths_to_strings(self, obj):
        """Convert Path objects to strings for JSON serialization"""
        if isinstance(obj, Path):
            return str(obj)
        elif isinstance(obj, dict):
            return {k: self._convert_paths_to_strings(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_paths_to_strings(item) for item in obj]
        return obj
    
    def _generate_text_report(self, analysis):
        """Generate human-readable text report"""
        report = []
        report.append("# Lib.rs Analysis Report")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Total Lines: {analysis['total_lines']}")
        report.append("")
        
        report.append("## Components Analysis")
        for name, comp in analysis["components"].items():
            status = "✅ Found & Complete" if comp.get("found") and comp.get("complete") else \
                     "⚠️ Found but Incomplete" if comp.get("found") else \
                     "❌ Not Found"
            report.append(f"- {name}: {status}")
        report.append("")
        
        report.append("## Broken Sections")
        for section in analysis["broken_sections"][:10]:  # Limit to first 10
            report.append(f"- Line {section['line']}: {section['issue']}")
            report.append(f"  Content: {section['content'][:80]}...")
        report.append("")
        
        report.append("## Working Sections")
        for section in analysis["working_sections"][:5]:  # Limit to first 5
            report.append(f"- Line {section['line']}: {section['type']}")
            report.append(f"  Content: {section['content'][:80]}...")
        report.append("")
        
        report.append(f"## Summary")
        report.append(f"- Total Functions: {len(analysis['functions'])}")
        report.append(f"- Total Imports: {len(analysis['imports'])}")
        report.append(f"- Total Signals: {len(analysis['signals'])}")
        report.append(f"- Broken Sections: {len(analysis['broken_sections'])}")
        report.append(f"- Working Sections: {len(analysis['working_sections'])}")
        
        return '\n'.join(report)

def main():
    """Main execution function"""
    lib_path = "src/lib.rs"
    
    if not os.path.exists(lib_path):
        print(f"❌ Error: {lib_path} not found")
        print("Make sure you're running this from the project root directory")
        return
    
    print("🔍 Starting Phase 1: Analysis & Extraction")
    print("=" * 50)
    
    analyzer = LibAnalyzer(lib_path)
    
    # Step 1: Create backup
    print("\n📋 Step 1: Creating backup...")
    backup_path = analyzer.create_backup()
    
    # Step 2: Analyze structure
    print("\n🔍 Step 2: Analyzing structure...")
    analysis = analyzer.analyze_structure()
    
    # Step 3: Extract working components
    print("\n📦 Step 3: Extracting working components...")
    components_dir = analyzer.extract_working_components(analysis)
    
    # Step 4: Generate report
    print("\n📊 Step 4: Generating report...")
    report_path = analyzer.generate_report(analysis)
    
    print("\n" + "=" * 50)
    print("✅ Phase 1 Complete!")
    print(f"📁 Backup: {backup_path}")
    print(f"📁 Components: {components_dir}")
    print(f"📊 Report: {report_path}")
    print("\nNext: Run scripts/generate_enhanced_components.py")

if __name__ == "__main__":
    main()
