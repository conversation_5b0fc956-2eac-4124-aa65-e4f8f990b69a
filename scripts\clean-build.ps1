#!/usr/bin/env powershell
# Clean and rebuild the project from scratch

param(
    [switch]$SkipDependencies,
    [switch]$QuickClean
)

Write-Host "`n🧹 CLEAN BUILD PROCESS" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent

# Stop any running processes
Write-Host "`n🛑 Stopping any running processes..." -ForegroundColor Yellow
$processes = @("trunk", "cargo", "laptos-tauri")
foreach ($proc in $processes) {
    $running = Get-Process -Name $proc -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "  Stopping $proc..." -ForegroundColor Gray
        Stop-Process -Name $proc -Force -ErrorAction SilentlyContinue
    }
}

# Clean build artifacts
Write-Host "`n🗑️  Cleaning build artifacts..." -ForegroundColor Yellow

$dirs_to_clean = @(
    "$project_root\dist",
    "$project_root\target",
    "$project_root\src-tauri\target"
)

if (-not $QuickClean) {
    $dirs_to_clean += @(
        "$project_root\.parcel-cache",
        "$project_root\pkg"
    )
}

foreach ($dir in $dirs_to_clean) {
    if (Test-Path $dir) {
        Write-Host "  Removing $(Split-Path $dir -Leaf)..." -ForegroundColor Gray
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clean Cargo cache for this project
Write-Host "`n🔧 Cleaning Cargo cache..." -ForegroundColor Yellow
Push-Location $project_root
cargo clean 2>&1 | Out-Null
Pop-Location

Push-Location "$project_root\src-tauri"
cargo clean 2>&1 | Out-Null
Pop-Location

# Reinstall dependencies if needed
if (-not $SkipDependencies) {
    Write-Host "`n📦 Reinstalling dependencies..." -ForegroundColor Yellow
    
    # Frontend dependencies
    if (Test-Path "$project_root\package-lock.json") {
        Remove-Item "$project_root\package-lock.json" -Force
    }
    
    Write-Host "  Installing NPM packages..." -ForegroundColor Gray
    npm install
    
    # Update Rust dependencies
    Write-Host "  Updating Cargo dependencies..." -ForegroundColor Gray
    Push-Location "$project_root\src-tauri"
    cargo update
    Pop-Location
}

# Verify tools
Write-Host "`n✅ Verifying build tools..." -ForegroundColor Yellow

$tools = @{
    "Rust" = { cargo --version }
    "Node.js" = { node --version }
    "Trunk" = { trunk --version }
    "Tauri CLI" = { cargo tauri --version }
    "WASM target" = { rustup target list --installed | Select-String "wasm32-unknown-unknown" }
}

foreach ($tool in $tools.Keys) {
    Write-Host -NoNewline "  $tool : "
    try {
        $result = & $tools[$tool] 2>$null
        if ($result) {
            Write-Host "OK" -ForegroundColor Green
        } else {
            Write-Host "Missing" -ForegroundColor Red
        }
    } catch {
        Write-Host "Error" -ForegroundColor Red
    }
}

# Test compilation
Write-Host "`n🔨 Testing compilation..." -ForegroundColor Yellow

# Backend
Write-Host "  Backend: " -NoNewline
Push-Location "$project_root\src-tauri"
$backend_result = cargo check 2>&1
$backend_success = $LASTEXITCODE -eq 0
Pop-Location

if ($backend_success) {
    Write-Host "OK" -ForegroundColor Green
} else {
    Write-Host "Failed" -ForegroundColor Red
}

# Frontend
Write-Host "  Frontend: " -NoNewline
Push-Location $project_root
$frontend_result = cargo check --target wasm32-unknown-unknown 2>&1
$frontend_success = $LASTEXITCODE -eq 0
Pop-Location

if ($frontend_success) {
    Write-Host "OK" -ForegroundColor Green
} else {
    Write-Host "Failed" -ForegroundColor Red
    Write-Host "`n  Run '.\debug-build-failure.ps1 -Frontend' for details" -ForegroundColor Yellow
}

# Summary
Write-Host "`n📊 Clean Build Summary:" -ForegroundColor Cyan
if ($backend_success -and $frontend_success) {
    Write-Host "  ✅ All systems ready!" -ForegroundColor Green
    Write-Host "`n  You can now run:" -ForegroundColor Gray
    Write-Host "  cargo tauri dev" -ForegroundColor White
} else {
    Write-Host "  ⚠️  Build issues detected" -ForegroundColor Yellow
    Write-Host "`n  Recommended actions:" -ForegroundColor Gray
    Write-Host "  1. Run '.\fix-compiler-errors.ps1'" -ForegroundColor White
    Write-Host "  2. Run '.\debug-build-failure.ps1'" -ForegroundColor White
    Write-Host "  3. Check error_handling.rs for closure issues" -ForegroundColor White
}

Write-Host "`n🦸 Clean slate for accessibility!" -ForegroundColor Cyan