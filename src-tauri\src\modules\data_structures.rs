// Data structures module - Shared types and structures used across the application
// Extracted from main_module.rs to improve code organization

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
// Removed unused chrono imports

// ==================== PROGRESS AND COMMUNICATION STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressPayload {
    pub current: u32,
    pub total: u32,
    pub message: Option<String>,
    pub operation_id: String,
    pub percentage: f32,
    pub stage: Option<String>,
}

// ==================== CHARACTER ANALYSIS STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub unicode_block: String,
    pub category: String,
    pub codepoint: u32,
    pub utf8_bytes: Vec<u8>,
    pub utf16_units: Vec<u16>,
    pub is_suspicious: bool,
    pub suspicion_reasons: Vec<String>,
    pub recommendations: Vec<String>,
    pub visual_width: usize,
    pub is_combining: bool,
    pub is_emoji: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingInfo {
    pub detected_encoding: String,
    pub confidence: f32,
    pub is_valid_utf8: bool,
    pub bom_detected: Option<String>,
    pub line_endings: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub risk_level: String, // Low, Medium, High, Critical
    pub phishing_indicators: Vec<String>,
    pub homograph_attacks: Vec<String>,
    pub steganography_potential: bool,
    pub script_mixing: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternMatch {
    pub pattern_name: String,
    pub description: String,
    pub start_position: usize,
    pub end_position: usize,
    pub matched_text: String,
    pub severity: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub id: String,
    pub timestamp: String,
    pub input_text: String,
    pub text_hash: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub total_graphemes: usize,
    pub visual_width: usize,
    pub encoding_info: EncodingInfo,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub character_breakdown: HashMap<String, usize>,
    pub script_breakdown: HashMap<String, usize>,
    pub analysis_duration_ms: u64,
    pub confidence_score: f32,
    pub security_analysis: SecurityAnalysis,
    pub patterns_found: Vec<PatternMatch>,
    pub recommendations: Vec<String>,
}

// ==================== FILE ANALYSIS STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisRequest {
    pub content: String,
    pub filename: Option<String>,
    pub encoding: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisDetail {
    pub file_path: String,
    pub relative_path: String,
    pub file_size: u64,
    pub total_characters: usize,
    pub suspicious_characters: usize,
    pub issues: Vec<String>,
    pub file_type: String,
    pub encoding: String,
    pub analysis_status: String, // "success", "error", "skipped"
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub health_score: f64,
    pub file_details: Vec<FileAnalysisDetail>,
    pub analysis_time_ms: u64,
    // New fields for advanced codebase analysis
    pub backdoor_count: usize,
    pub frontdoor_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseStats {
    pub total_files_processed: usize,
    pub files_with_issues: usize,
    pub total_suspicious_characters: usize,
    pub most_common_issues: Vec<String>,
    pub file_type_breakdown: HashMap<String, usize>,
    pub overall_health_score: f64,
}

// ==================== ASSET CONFIGURATION STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterEntry {
    pub name: Option<String>,
    pub hex: Option<String>,
    pub description: String,
    pub char: Option<String>,
    pub example: Option<String>,
    pub range: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterCategory {
    #[serde(rename = "displayName")]
    pub display_name: String,
    pub description: String,
    pub characters: Vec<BadCharacterEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterSubCategories {
    #[serde(rename = "extremelyBigProblems")]
    pub extremely_big_problems: BadCharacterCategory,
    #[serde(rename = "highProblems")]
    pub high_problems: BadCharacterCategory,
    #[serde(rename = "mediumProblems")]
    pub medium_problems: BadCharacterCategory,
    #[serde(rename = "lowProblems")]
    pub low_problems: BadCharacterCategory,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InvisibleAndFormattingCharacters {
    pub description: String,
    #[serde(rename = "subCategories")]
    pub sub_categories: BadCharacterSubCategories,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentSpecificIssues {
    #[serde(rename = "invisibleAndFormattingCharacters")]
    pub invisible_and_formatting_characters: InvisibleAndFormattingCharacters,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilenameIssues {
    #[serde(rename = "forbiddenWindowsCharacters")]
    pub forbidden_windows_characters: Vec<BadCharacterEntry>,
    #[serde(rename = "generalControlCharactersForFilenames")]
    pub general_control_characters_for_filenames: Vec<BadCharacterEntry>,
    #[serde(rename = "c1ControlCharactersForFilenames")]
    pub c1_control_characters_for_filenames: Vec<BadCharacterEntry>,
    #[serde(rename = "problematicUnicodeForFilenames")]
    pub problematic_unicode_for_filenames: Vec<BadCharacterEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharactersConfig {
    #[serde(rename = "filenameSpecificIssues")]
    pub filename_specific_issues: FilenameIssues,
    #[serde(rename = "contentSpecificIssues")]
    pub content_specific_issues: ContentSpecificIssues,
}

// ==================== FILE TYPE STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgrammingSourceCodeCategories {
    #[serde(rename = "C Family")]
    pub c_family: Vec<String>,
    #[serde(rename = "Java/JVM")]
    pub java_jvm: Vec<String>,
    #[serde(rename = "Python")]
    pub python: Vec<String>,
    #[serde(rename = "JavaScript/TypeScript")]
    pub javascript_typescript: Vec<String>,
    #[serde(rename = "Other")]
    pub other: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTypeCategories {
    #[serde(rename = "System Files")]
    pub system_files: Vec<String>,
    #[serde(rename = "Virtualization Formats")]
    pub virtualization_formats: Vec<String>,
    #[serde(rename = "Image Formats")]
    pub image_formats: Vec<String>,
    #[serde(rename = "Configuration Files")]
    pub configuration_files: Vec<String>,
    #[serde(rename = "Log Files")]
    pub log_files: Vec<String>,
    #[serde(rename = "Document Formats")]
    pub document_formats: Vec<String>,
    #[serde(rename = "Archive Formats")]
    pub archive_formats: Vec<String>,
    #[serde(rename = "Executable Formats")]
    pub executable_formats: Vec<String>,
    #[serde(rename = "Database Formats")]
    pub database_formats: Vec<String>,
    #[serde(rename = "Network Formats")]
    pub network_formats: Vec<String>,
    #[serde(rename = "Audio Formats")]
    pub audio_formats: Vec<String>,
    #[serde(rename = "Video Formats")]
    pub video_formats: Vec<String>,
    #[serde(rename = "CAD Formats")]
    pub cad_formats: Vec<String>,
    #[serde(rename = "Virtual Machines")]
    pub virtual_machines: Vec<String>,
    #[serde(rename = "Game Formats")]
    pub game_formats: Vec<String>,
    #[serde(rename = "E-Book Formats")]
    pub e_book_formats: Vec<String>,
    #[serde(rename = "BIOS/UEFI Formats")]
    pub bios_uefi_formats: Vec<String>,
    #[serde(rename = "Forensic Formats")]
    pub forensic_formats: Vec<String>,
    #[serde(rename = "Container Formats")]
    pub container_formats: Vec<String>,
    #[serde(rename = "Project Files")]
    pub project_files: Vec<String>,
    #[serde(rename = "Programming Source Code")]
    pub programming_source_code: ProgrammingSourceCodeCategories,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTypesSummary {
    #[serde(rename = "$schema")]
    pub schema: String,
    pub version: String,
    #[serde(rename = "lastUpdated")]
    pub last_updated: String,
    pub categories: FileTypeCategories,
}

// ==================== AI DETECTION STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIPattern {
    pub name: String,
    pub pattern: String,
    pub description: Option<String>,
    pub severity: Option<String>,
    #[serde(rename = "confidenceScore")]
    pub confidence_score: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIPatternCategory {
    pub description: String,
    #[serde(rename = "highConfidencePatterns", default)]
    pub high_confidence_patterns: Vec<AIPattern>,
    #[serde(rename = "mediumConfidencePatterns", default)]
    pub medium_confidence_patterns: Vec<AIPattern>,
    #[serde(rename = "lowConfidencePatterns", default)]
    pub low_confidence_patterns: Vec<AIPattern>,
    #[serde(rename = "criticalPatterns", default)]
    pub critical_patterns: Vec<AIPattern>,
    #[serde(rename = "suspiciousPatterns", default)]
    pub suspicious_patterns: Vec<AIPattern>,
    #[serde(rename = "dangerousPatterns", default)]
    pub dangerous_patterns: Vec<AIPattern>,
    #[serde(rename = "patterns", default)]
    pub patterns: Vec<AIPattern>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDetectionRules {
    pub description: String,
    pub rules: Vec<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextualAnalysis {
    pub description: String,
    pub factors: Vec<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIPatternCategories {
    #[serde(rename = "aiCodePatterns")]
    pub ai_code_patterns: AIPatternCategory,
    #[serde(rename = "advancedHomoglyphPatterns")]
    pub advanced_homoglyph_patterns: AIPatternCategory,
    #[serde(rename = "steganographyPatterns")]
    pub steganography_patterns: AIPatternCategory,
    #[serde(rename = "codeInjectionPatterns")]
    pub code_injection_patterns: AIPatternCategory,
    #[serde(rename = "advancedBidirectionalAttacks")]
    pub advanced_bidirectional_attacks: AIPatternCategory,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedAIPatternsConfig {
    #[serde(rename = "$schema")]
    pub schema: String,
    pub title: String,
    pub version: String,
    pub description: String,
    #[serde(rename = "lastUpdated")]
    pub last_updated: String,
    pub categories: AIPatternCategories,
    #[serde(rename = "detectionRules")]
    pub detection_rules: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDetectionMatch {
    pub pattern_name: String,
    pub description: String,
    pub severity: String,
    pub confidence: f64,
    pub start_position: usize,
    pub end_position: usize,
    pub matched_text: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIDetectionResult {
    pub overall_confidence: f64,
    pub ai_likelihood: String,
    pub patterns_detected: usize,
    pub detected_patterns: Vec<AIDetectionMatch>,
    pub analysis_summary: String,
}

// ==================== TEXT CLEANING STRUCTURES ====================

#[derive(Serialize, Deserialize)]
pub struct CleaningChange {
    pub start: usize,
    pub end: usize,
    pub original: String,
    pub cleaned: String,
    pub change_type: String,
}

#[derive(Serialize, Deserialize)]
pub struct CleaningResult {
    pub original: String,
    pub cleaned: String,
    pub changes: Vec<CleaningChange>,
    pub stats: HashMap<String, u32>,
}

// ==================== SECURITY VULNERABILITY STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityLocation {
    pub line: usize,
    pub column: usize,
    pub span: Option<String>, // e.g., "lines 5-7" or specific byte range
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityVulnerability {
    pub vulnerability_id: String,
    pub vulnerability_type: String, // e.g., "SQL Injection", "XSS", "Path Traversal"
    pub severity: SeverityLevel,
    pub description: String,
    pub locations: Vec<VulnerabilityLocation>, // Specific locations of the vulnerability
    pub remediation_advice: String,
    pub confidence_score: f32, // How confident the scanner is about this vulnerability
    pub cvss_score: Option<String>, // Common Vulnerability Scoring System score
    pub cwe_id: Option<String>, // Common Weakness Enumeration ID
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)] // Added Default, PartialEq, Eq
pub enum SeverityLevel {
    Low,
    Medium,
    High,
    Critical, // Added Critical, common in security contexts
    Paranoid, // Keep if used, or remove if Critical covers it
}

impl Default for SeverityLevel {
    fn default() -> Self {
        SeverityLevel::Low
    }
}

// Make sure IssueLocation also derives Serialize and Deserialize
#[derive(Debug, Clone, Serialize, Deserialize, Default)] // Added Serialize, Deserialize, Default
pub struct IssueLocation {
    pub line: usize,
    pub column: usize,
    pub span: Option<String>, 
}

// Ensure RecommendationCategory is defined or use String and update later
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationCategory {
    CharacterAnomaly,
    SecurityVulnerability,
    Performance,
    Readability,
    Other,
}

impl Default for RecommendationCategory {
    fn default() -> Self {
        RecommendationCategory::Other
    }
}


#[derive(Debug, Clone, Serialize, Deserialize, Default)] // Added Default
pub struct Recommendation {
    pub recommendation_id: String,
    pub category: RecommendationCategory, // Changed to enum
    pub title: String, // Added title
    pub description: String,
    pub priority: u8, // Added priority (e.g., 1-5)
    pub action_steps: Vec<String>,
    pub related_findings: Vec<String>, // IDs of related CharacterFinding or SecurityVulnerability
    pub implementation_guide: Option<String>, // Added
    pub estimated_effort: Option<String>, // Added (e.g., "Low", "Medium", "High")
    pub business_value: Option<String>, // Added (e.g., "Low", "Medium", "High")
    pub status: String, // Added (e.g., "Open", "In Progress", "Resolved")
}

// ==================== CHARACTER FINDING STRUCTURES ====================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterFinding {
    pub finding_id: String,
    pub character: char,
    pub line_number: usize,
    pub column_number: usize,
    pub character_info: CharacterInfo, // Contains codepoint, is_suspicious, etc.
    pub context: String,
    pub severity: SeverityLevel, // Or some other severity type
    // Add other fields as needed
}
