use leptos::*;
use crate::AnalysisResults;

#[component]
pub fn EncodingTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
    view! {
        <div class="tab-content-inner">
            <h4>"🔧 Encoding Analysis"</h4>
            {move || results.get().map(|res| {
                view! {
                    <div class="encoding-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">"Detected Encoding:"</span>
                                <span class="info-value">{res.encoding_info.detected_encoding.clone()}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">"Unicode Version:"</span>
                                <span class="info-value">"15.0"</span>
                            </div>
                            // Normalization form not available in AnalysisResults
                        </div>
                        
                        <div class="encoding-issues mt-lg">
                            <h5>"Encoding Issues:"</h5>
                            {if res.encoding_info.is_valid_utf8 {
                                view! { <p class="text-success">"✅ No encoding issues detected"</p> }.into_view()
                            } else {
                                view! {
                                    <ul>
                                        <li>"Invalid UTF-8 sequence detected."</li>
                                    </ul>
                                }.into_view()
                            }}
                        </div>
                        
                        <div class="script-usage mt-lg">
                            <h5>"Script Usage:"</h5>
                            <div class="script-list">
                                {res.script_breakdown.iter().map(|(script, count)| {
                                    view! {
                                        <div class="script-item">
                                            <span class="script-name">{script.clone()}</span>
                                            <span class="script-count">{format!("{} chars", count)}</span>
                                        </div>
                                    }
                                }).collect_view()}
                            </div>
                        </div>
                    </div>
                }
            }).unwrap_or_else(|| view! { <div>"No analysis results available"</div> })}
        </div>
    }
}
