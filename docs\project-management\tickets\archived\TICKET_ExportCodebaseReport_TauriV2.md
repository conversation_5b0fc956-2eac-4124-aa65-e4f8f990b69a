**Ticket: Implement Report Export Functionality for Codebase Analysis (Tauri v2)**

**1. Issue Description:**

The "Code Base Analysis & Cleaning" page in the Tauri v2 application currently allows users to analyze codebases and view the results within the application. However, there is no functionality to export or save these generated analysis reports. Users need a way to persist these reports for documentation, sharing, or offline review.

**2. Desired Behavior:**

Users should be able to export the complete codebase analysis report generated on the "Code Base Analysis & Cleaning" page. This includes:

*   A clear UI element (e.g., an "Export Report" or "Save Report" button) on the analysis results view.
*   When clicked, the application should prompt the user to choose a file location and name for the report.
*   The report should be exportable in one or more common, human-readable, and machine-parseable formats. Suggested initial formats:
    *   **JSON:** For easy parsing and integration with other tools.
    *   **Markdown (.md):** For good readability and easy conversion to other document formats.
    *   **test (.txt):** For simple readability and easy conversion.
    *   (Optional Future Enhancement) **CSV:** For specific tabular data if applicable.
    *   (Optional Future Enhancement) **PDF:** For a more formal, printable document.

**3. Affected Components (Tauri v2 Application):**

*   **Rust Backend (Tauri Commands):**
    *   The existing `analyze_codebase` command in `src-tauri/src/main.rs` produces `CodeBaseAnalysisResult`.
    *   A new or modified Tauri command will be needed to handle the formatting of `CodeBaseAnalysisResult` into the desired export format and manage the file saving process. The existing `export_analysis` command might offer some reusable logic but is geared towards `AnalysisResults`, not `CodeBaseAnalysisResult`.
*   **Leptos Frontend:**
    *   The UI of the "Code Base Analysis & Cleaning" page will require a new button or control to trigger the export.
    *   Frontend logic to invoke the Tauri command and potentially pass the analysis data or parameters to regenerate it.
*   **Tauri API:**
    *   `tauri_plugin_dialog::blocking::FileDialogBuilder` (or its async equivalent) to show a "save file" dialog to the user.
    *   `tauri_plugin_fs` (or `std::fs`) to write the generated report content to the selected file path.

**4. Investigation Steps (Tauri v2 Context):**

*   **Data Structure Review:**
    *   Thoroughly examine the `CodeBaseAnalysisResult` struct (and its nested structs like `FileAnalysisDetail`, `CodeBaseStats`) to understand all the data points that need to be included in the exported report.
*   **Export Formatting Logic:**
    *   Design and implement Rust functions to serialize the `CodeBaseAnalysisResult` into the chosen export formats (JSON, Markdown).
        *   For JSON: `serde_json::to_string_pretty` should be straightforward.
        *   For Markdown: This will require custom logic to structure the data into a readable Markdown document (headings, lists, tables, code blocks).
*   **Tauri Command Design:**
    *   Define a new Tauri command, e.g., `export_codebase_report(app_handle: tauri::AppHandle, analysis_data: CodeBaseAnalysisResult, format: String)`.
    *   This command will call the appropriate formatting function based on the `format` parameter.
*   **File Saving Mechanism:**
    *   Integrate `tauri_plugin_dialog` to prompt the user for a save location and filename. The dialog should suggest a default filename (e.g., `codebase_analysis_report.json`).
    *   Use `tauri_plugin_fs::write_text_file` (or `std::fs::write`) to save the formatted report string to the path selected by the user.
*   **Frontend Integration (Leptos):**
    *   Add an "Export Report" button to the relevant Leptos component.
    *   When clicked, the frontend should gather the current `CodeBaseAnalysisResult` data (if stored in frontend state) or the necessary parameters to re-fetch/re-generate it, and then `invoke` the new Tauri command.
    *   Provide user feedback (e.g., success/error notifications).

**5. Potential Solutions & Implementation Strategy (Tauri v2):**

*   **Backend (Rust):**
    *   Create a new module or functions for report generation (e.g., `report_generator.rs`).
    *   Implement `fn format_as_json(data: &CodeBaseAnalysisResult) -> Result<String, anyhow::Error>`
    *   Implement `fn format_as_markdown(data: &CodeBaseAnalysisResult) -> Result<String, anyhow::Error>`
    *   The new Tauri command:
        ```rust
        #[tauri::command]
        async fn export_codebase_report(
            app_handle: tauri::AppHandle,
            analysis_data: CodeBaseAnalysisResult, // Or parameters to get this data
            format_type: String // "json" or "markdown"
        ) -> Result<(), String> {
            let report_content = match format_type.as_str() {
                "json" => report_generator::format_as_json(&analysis_data)
                                .map_err(|e| e.to_string())?,
                "markdown" => report_generator::format_as_markdown(&analysis_data)
                                .map_err(|e| e.to_string())?,
                _ => return Err("Unsupported format".to_string()),
            };

            let default_filename = format!("codebase_analysis_report.{}", if format_type == "json" { "json" } else { "md" });
            
            let file_path = tauri_plugin_dialog::blocking::FileDialogBuilder::new(&app_handle)
                .set_title("Save Report As")
                .set_file_name(&default_filename)
                .add_filter(if format_type == "json" { "JSON files" } else { "Markdown files" }, 
                            if format_type == "json" { &["json"] } else { &["md"] })
                .save_file();

            if let Some(path) = file_path {
                tauri_plugin_fs::write_text_file(path, report_content) // Using tauri_plugin_fs
                    .map_err(|e| format!("Failed to save report: {}", e))?;
                Ok(())
            } else {
                Ok(()) // User cancelled, not an error
            }
        }
        ```
*   **Frontend (Leptos):**
    *   Add a button that, on click, calls `invoke('export_codebase_report', { analysisData: currentAnalysisData, formatType: 'json' /* or 'markdown' */ })`.
    *   Consider adding a small dropdown or radio buttons for the user to select the export format.

**6. Acceptance Criteria:**

*   An "Export Report" button (or similar) is present on the "Code Base Analysis & Cleaning" page when analysis results are available.
*   Clicking the button prompts the user with a native "save file" dialog.
*   The user can choose a filename, location, and desired format (initially JSON and Markdown).
*   The codebase analysis report is successfully saved to the specified location in the chosen format.
*   The saved report accurately reflects the analysis data shown in the UI.
*   The application handles cases where the user cancels the save dialog gracefully.

**7. Priority:** Medium (Important for data persistence and sharing)

**8. IMPLEMENTATION CHECKLIST:**

### Phase 1: Backend Implementation
#### Report Generator Module (`src-tauri/src/report_generator.rs`)
- [ ] **Create comprehensive format_as_json function**
  - [ ] Serialize `CodeBaseAnalysisResult` using `serde_json::to_string_pretty`
  - [ ] Include all fields: `total_files`, `files_with_issues`, `total_suspicious_chars`, `health_score`, `file_details`, `analysis_time_ms`
  - [ ] Format timestamp readably
  - [ ] Handle nested `FileAnalysisDetail` properly
  
- [ ] **Create comprehensive format_as_markdown function**
  - [ ] Generate structured Markdown with proper headers
  - [ ] Summary section with overview stats
  - [ ] File-by-file breakdown table or list
  - [ ] Health score visualization
  - [ ] Analysis timestamp
  - [ ] Suspicious character summary by type

#### Tauri Command (`src-tauri/src/main.rs`)
- [x] **Create export_codebase_report command** ✅ COMPLETED
  - [x] Accept `CodeBaseAnalysisResult` and `format_type` parameters
  - [x] Support "json", "markdown", and "txt" formats
  - [x] Use PowerShell-based save dialog for Windows (cross-platform compatible)
  - [x] Default filename: `codebase_analysis_report.{ext}`
  - [x] Add appropriate file filters (JSON, Markdown, Text)
  - [x] Handle user cancellation gracefully
  - [x] Write file using `std::fs::write`
  - [x] Return meaningful error messages

- [x] **Register new command in main.rs** ✅ COMPLETED
  - [x] Add to `invoke_handler` list
  - [x] Import report_generator module

#### Error Handling & Dependencies
- [x] **Update Cargo.toml if needed** ✅ COMPLETED
  - [x] Added `anyhow = "1.0"` for enhanced error handling
  - [ ] Verify `tauri_plugin_dialog` and `tauri_plugin_fs` dependencies
  
- [ ] **Implement comprehensive error handling**
  - [ ] File write permissions
  - [ ] Invalid file paths
  - [ ] Serialization errors
  - [ ] Dialog errors

### Phase 2: Frontend Implementation ✅ **COMPLETED**
> **Status**: All Phase 2 tasks completed successfully. Frontend compiles without errors and includes complete export functionality with format selection (JSON/Markdown/Text), loading states, user feedback, and error handling.

#### UI Components (`src/lib.rs`)
- [✅] **Add Export Report button to AnalysisResultsView**
  - [✅] Position near other action buttons
  - [✅] Only show when `CodeBaseAnalysisResult` is available
  - [✅] Consider format selection (dropdown/radio buttons)
  - [✅] Style consistently with existing UI

- [✅] **Implement export functionality**
  - [✅] Create `handle_export_report` function
  - [✅] Use `invoke` to call `export_codebase_report`
  - [✅] Pass current analysis data and selected format
  - [✅] Handle loading state during export
  - [✅] Show success/error notifications

#### State Management
- [✅] **Ensure analysis data is accessible**
  - [✅] Verify `CodeBaseAnalysisResult` is stored in component state
  - [✅] Pass complete data structure to export command
  - [✅] Handle cases where data might be stale

### Phase 3: Testing & Validation ✅ **RUNTIME ISSUES RESOLVED**
> **Status**: All critical runtime errors have been resolved. Export functionality is now operational and ready for manual testing verification.

#### ✅ **Critical Runtime Fixes (June 2025)**
- **Missing `timestamp` field error**: Fixed in frontend `AnalysisResults` struct
- **Missing `text_hash` field error**: Fixed in frontend `AnalysisResults` struct  
- **Signal access warnings**: Already properly handled with `get_untracked()`
- **Application crashes**: Eliminated with struct field additions
- **Deserialization failures**: Resolved with backend/frontend struct alignment

#### 🔄 **Ready for Manual Testing**
- [ ] **Test JSON export**
  - [ ] Verify all data fields are included
  - [ ] Check JSON validity and formatting
  - [ ] Test with various analysis results
  
- [ ] **Test Markdown export**
  - [ ] Verify readable formatting
  - [ ] Check all sections are included
  - [ ] Test markdown syntax validity
  
- [ ] **Test file operations**
  - [ ] Save to different locations
  - [ ] Test filename handling
  - [ ] Verify file permissions work
  - [ ] Test user cancellation

#### Edge Cases
- [ ] **Error handling**
  - [ ] Read-only directories
  - [ ] Invalid characters in filename
  - [ ] Large analysis results
  - [ ] Missing analysis data
  
- [ ] **UI/UX validation**
  - [ ] Button disabled states
  - [ ] Loading indicators
  - [ ] Success/error feedback
  - [ ] Mobile responsiveness

### Phase 4: Enhancement (Optional)
- [ ] **Format selection UI**
  - [ ] Dropdown or radio buttons for format choice
  - [ ] Format descriptions/help text
  
- [ ] **Additional formats**
  - [ ] Plain text (.txt) export
  - [ ] CSV export for tabular data
  - [ ] Future: PDF generation

---
