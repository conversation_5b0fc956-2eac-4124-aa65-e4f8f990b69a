#!/usr/bin/env powershell
# Project status - Shows current state of the Bad Character Scanner project

param(
    [switch]$Detailed,
    [switch]$ShowLogs,
    [switch]$ShowTickets
)

Write-Host "`n📊 BAD CHARACTER SCANNER - PROJECT STATUS" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

$project_root = $PSScriptRoot | Split-Path -Parent

# Version Information
Write-Host "`n📌 VERSION INFORMATION" -ForegroundColor Yellow
try {
    $cargo_toml = Get-Content "$project_root\src-tauri\Cargo.toml" -Raw
    if ($cargo_toml -match 'version\s*=\s*"([^"]+)"') {
        Write-Host "  Backend Version: $($matches[1])" -ForegroundColor White
    }
    
    $package_json = Get-Content "$project_root\package.json" -Raw | ConvertFrom-Json
    Write-Host "  Frontend Version: $($package_json.version)" -ForegroundColor White
    Write-Host "  Tauri Version: ~2.5.1" -ForegroundColor White
} catch {
    Write-Host "  Unable to read version info" -ForegroundColor Red
}

# Recent Changes
Write-Host "`n🔄 RECENT CHANGES" -ForegroundColor Yellow
Write-Host "  ✅ Error handling system implemented (thiserror)" -ForegroundColor Green
Write-Host "  ✅ Logging infrastructure added (tracing)" -ForegroundColor Green
Write-Host "  ✅ PowerShell ticket manager fixed" -ForegroundColor Green
Write-Host "  ✅ Compiler errors resolved" -ForegroundColor Green

# Current Issues
Write-Host "`n⚠️  KNOWN ISSUES" -ForegroundColor Yellow
Write-Host "  - 854 bad characters found (mostly in test files)" -ForegroundColor Yellow
Write-Host "  - Tauri commands need error type updates" -ForegroundColor Yellow
Write-Host "  - Main module needs refactoring (2600+ lines)" -ForegroundColor Yellow

# Build Status
Write-Host "`n🔨 BUILD STATUS" -ForegroundColor Yellow
Write-Host -NoNewline "  Frontend: "
if (Test-Path "$project_root\dist") {
    $dist_age = (Get-Date) - (Get-Item "$project_root\dist").LastWriteTime
    if ($dist_age.TotalHours -lt 24) {
        Write-Host "Built $([int]$dist_age.TotalHours) hours ago" -ForegroundColor Green
    } else {
        Write-Host "Built $([int]$dist_age.TotalDays) days ago" -ForegroundColor Yellow
    }
} else {
    Write-Host "Not built" -ForegroundColor Gray
}

Write-Host -NoNewline "  Backend: "
if (Test-Path "$project_root\src-tauri\target\debug") {
    $target_age = (Get-Date) - (Get-Item "$project_root\src-tauri\target\debug").LastWriteTime
    if ($target_age.TotalHours -lt 24) {
        Write-Host "Built $([int]$target_age.TotalHours) hours ago" -ForegroundColor Green
    } else {
        Write-Host "Built $([int]$target_age.TotalDays) days ago" -ForegroundColor Yellow
    }
} else {
    Write-Host "Not built" -ForegroundColor Gray
}

# Tickets Summary
if ($ShowTickets) {
    Write-Host "`n🎫 TICKET SUMMARY" -ForegroundColor Yellow
    & "$PSScriptRoot\ticket-manager-fixed.ps1" -Action summary 2>$null | Select-Object -Skip 3
}

# Git Status
Write-Host "`n📦 GIT STATUS" -ForegroundColor Yellow
Push-Location $project_root
$git_status = git status --porcelain 2>$null
if ($LASTEXITCODE -eq 0) {
    $modified = ($git_status | Where-Object { $_ -match '^ M' }).Count
    $untracked = ($git_status | Where-Object { $_ -match '^\?\?' }).Count
    $staged = ($git_status | Where-Object { $_ -match '^[AM]' }).Count
    
    Write-Host "  Modified files: $modified" -ForegroundColor $(if ($modified -gt 0) { "Yellow" } else { "Gray" })
    Write-Host "  Untracked files: $untracked" -ForegroundColor $(if ($untracked -gt 0) { "Yellow" } else { "Gray" })
    Write-Host "  Staged files: $staged" -ForegroundColor $(if ($staged -gt 0) { "Green" } else { "Gray" })
    
    $branch = git branch --show-current 2>$null
    Write-Host "  Current branch: $branch" -ForegroundColor White
} else {
    Write-Host "  Not a git repository" -ForegroundColor Gray
}
Pop-Location

# Logs
if ($ShowLogs) {
    Write-Host "`n📄 RECENT LOGS" -ForegroundColor Yellow
    $log_dir = Join-Path $env:LOCALAPPDATA "bad-character-scanner\logs"
    if (Test-Path $log_dir) {
        $latest_log = Get-ChildItem $log_dir -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        if ($latest_log) {
            Write-Host "  Latest log: $($latest_log.Name)" -ForegroundColor Gray
            Get-Content $latest_log.FullName -Tail 10 | ForEach-Object {
                Write-Host "    $_" -ForegroundColor DarkGray
            }
        }
    } else {
        Write-Host "  No logs found" -ForegroundColor Gray
    }
}

# Next Steps
Write-Host "`n🚀 NEXT STEPS" -ForegroundColor Yellow
Write-Host "  1. Update Tauri commands with new error types" -ForegroundColor White
Write-Host "  2. Implement Leptos error boundaries" -ForegroundColor White
Write-Host "  3. Complete framework testing (BCS-101)" -ForegroundColor White
Write-Host "  4. Refactor main_module.rs" -ForegroundColor White

# Quick Commands
Write-Host "`n⚡ QUICK COMMANDS" -ForegroundColor Yellow
Write-Host "  Start development: " -NoNewline -ForegroundColor Gray
Write-Host "cargo tauri dev" -ForegroundColor White
Write-Host "  Run tests: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\test-development-build.ps1" -ForegroundColor White
Write-Host "  Check health: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\doctor.ps1" -ForegroundColor White
Write-Host "  Scan project: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\scan-this-project.ps1" -ForegroundColor White

if ($Detailed) {
    Write-Host "`n📋 DETAILED MODULE STATUS" -ForegroundColor Yellow
    
    # Check each major module
    $modules = @(
        @{ Name = "Error Handling"; File = "src-tauri\src\error.rs" },
        @{ Name = "Logging System"; File = "src-tauri\src\logging.rs" },
        @{ Name = "Main Module"; File = "src-tauri\src\main_module.rs" },
        @{ Name = "Frontend Error"; File = "src\components\error_handling.rs" }
    )
    
    foreach ($module in $modules) {
        $path = Join-Path $project_root $module.File
        Write-Host -NoNewline "  $($module.Name): "
        if (Test-Path $path) {
            $lines = (Get-Content $path | Measure-Object -Line).Lines
            Write-Host "$lines lines" -ForegroundColor Green
        } else {
            Write-Host "Missing" -ForegroundColor Red
        }
    }
}

Write-Host "`n✨ Fighting for accessibility!" -ForegroundColor Cyan