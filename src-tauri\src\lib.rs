use tauri::Manager;

// Import the new modular structure
pub mod modules;
pub mod main_module; // Ensure this is public
pub mod enhanced_analysis;
pub mod report_generator;
pub mod analysis; // New advanced analysis system
pub mod error; // Error handling system
pub mod logging; // Logging infrastructure

// Re-export key types and functions for CLI access from both old and new modules
pub use modules::{
    CodeBaseAnalysisResult, FileAnalysisDetail, CharacterInfo, EncodingInfo,
    SecurityAnalysis, AnalysisResults, BadCharactersConfig,
    AIDetectionMatch, AIDetectionResult, AssetManager, CharacterAnalyzer
};

// Re-export new advanced analysis types
pub use analysis::{
    CodebaseAnalyzer, ComprehensiveAnalysisResult, ScanDepth,
    HomoglyphDetector, PatternAnalyzer, SecurityScanner, RiskAssessor, UnicodeAnalyzer
};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging before anything else
    if let Err(e) = logging::init_logging("bad-character-scanner", None) {
        eprintln!("Failed to initialize logging: {}", e);
    }
    
    tracing::info!("Starting Bad Character Scanner application");
    
    let app_builder = tauri::Builder::default()
        .setup(|app| {
            tracing::info!("Setting up Tauri application");

            // File drop handling is done through the frontend JavaScript API
            // using webview.onDragDropEvent() - no backend setup needed

            #[cfg(debug_assertions)]
            {
                tracing::debug!("Debug mode enabled, opening devtools");
                if let Some(window) = app.get_webview_window("main") {
                    window.open_devtools();
                    tracing::debug!("Devtools opened for main window");
                } else {
                    tracing::warn!("Main window not found, cannot open devtools");
                }
            }
            
            tracing::info!("Application setup completed");
            Ok(())
        })
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())        .invoke_handler(tauri::generate_handler![
            main_module::analyze_characters,
            main_module::analyze_text,
            main_module::analyze_codebase,
            main_module::analyze_codebase_advanced, // New advanced analysis
            main_module::export_analysis,
            main_module::export_codebase_report,
            main_module::clean_codebase,
            main_module::clean_codebase_with_verification,
            main_module::batch_analyze,
            main_module::get_character_details,
            main_module::detect_encoding,
            main_module::check_homographs,
            main_module::normalize_text,
            main_module::get_script_info,
            main_module::clean_text,
            main_module::clean_text_detailed,
            main_module::generate_report,
            modules::commands::select_file,
            modules::commands::select_folder,
            main_module::get_file_types_summary,
            main_module::detect_ai_content,
            modules::commands::health_check,
            // File Menu Commands
            main_module::new_file,
            main_module::open_file,
            main_module::save_file,
            main_module::open_terminal,
            main_module::save_settings,
            main_module::exit_app,
            main_module::open_reports_folder,
            // Enhanced Analysis Commands
            modules::enhanced_commands::enhanced_analyze_directory,
            modules::enhanced_commands::enhanced_analyze_file,
            modules::enhanced_commands::export_enhanced_report,
            // Drag & Drop Commands
            main_module::validate_dropped_files,
            main_module::process_dropped_files
        ]);

    tracing::info!("Tauri builder configured, starting application");
    app_builder
        .run(tauri::generate_context!())
        .map_err(|e| {
            tracing::error!(error = ?e, "Failed to run Tauri application");
            e
        })
        .expect("error while running tauri application");
}
