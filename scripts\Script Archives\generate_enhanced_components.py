#!/usr/bin/env python3
"""
Phase 2: Component Generation Script for lib.rs Reconstruction
Generates clean, enhanced components based on specifications
"""

import os
import json
from pathlib import Path
from datetime import datetime

class ComponentGenerator:
    def __init__(self):
        self.templates_dir = Path("scripts/component_templates")
        self.templates_dir.mkdir(exist_ok=True)
        self.generated_dir = Path("scripts/generated_components")
        self.generated_dir.mkdir(exist_ok=True)
        
    def create_templates(self):
        """Create component templates based on specifications"""
        print("📝 Creating component templates...")
        
        # Selection Mode Template (CODEBASE-5)
        selection_template = self._create_selection_mode_template()
        self._write_template("selection_mode.rs.template", selection_template)
        
        # Actions Mode Template (CODEBASE-5)
        actions_template = self._create_actions_mode_template()
        self._write_template("actions_mode.rs.template", actions_template)
        
        # Processing Mode Template
        processing_template = self._create_processing_mode_template()
        self._write_template("processing_mode.rs.template", processing_template)
        
        # Results Display Template (UI-3)
        results_template = self._create_results_display_template()
        self._write_template("results_display.rs.template", results_template)
        
        # Home Page Template
        homepage_template = self._create_homepage_template()
        self._write_template("homepage.rs.template", homepage_template)
        
        print("✅ Component templates created")
    
    def _create_selection_mode_template(self):
        """Create enhanced selection mode template based on CODEBASE-5"""
        return '''// Enhanced Folder Selection Interface (CODEBASE-5)
view! {
    div(class="selection-mode-container") {
        div(class="selection-header") {
            h2(class="selection-title") { "📁 Select Codebase Folder" }
            p(class="selection-subtitle") { 
                "Choose a folder to analyze for bad characters and code quality issues" 
            }
        }
        
        div(class="selection-content") {
            // Direct Path Input Section
            div(class="path-input-section enhanced-card") {
                label(class="input-label") { "📍 Direct Path Input" }
                div(class="input-group") {
                    input(
                        type="text",
                        class="path-input",
                        placeholder="Enter folder path (e.g., C:\\\\Projects\\\\MyApp)",
                        prop:value=move || current_path.get(),
                        on:input=move |ev| {
                            let value = event_target_value(&ev);
                            current_path.set(value.clone());
                            validate_path_async(value);
                        }
                    )
                    button(
                        class="btn btn-secondary browse-btn",
                        on:click=move |_| {
                            spawn_local(async move {
                                if let Ok(path) = invoke("select_folder", &()).await {
                                    let path_str: String = path;
                                    current_path.set(path_str.clone());
                                    validate_folder_info(path_str).await;
                                }
                            });
                        }
                    ) { "📂 Browse" }
                }
                
                // Path Validation Display
                {move || {
                    let validation = folder_validation.get();
                    if !current_path.get().is_empty() {
                        view! {
                            div(class="path-validation") {
                                {if validation.is_valid {
                                    view! {
                                        div(class="validation-success") {
                                            span(class="validation-icon") { "✅" }
                                            span { "Valid folder path" }
                                        }
                                    }
                                } else {
                                    view! {
                                        div(class="validation-error") {
                                            span(class="validation-icon") { "❌" }
                                            span { &validation.error_message }
                                        }
                                    }
                                }}
                            }
                        }
                    } else {
                        view! { div {} }
                    }
                }}
            }
            
            // Recent Folders Section
            div(class="recent-folders-section enhanced-card") {
                div(class="section-header") {
                    h3(class="section-title") { "🕒 Recent Folders" }
                    button(
                        class="btn btn-link clear-recent",
                        on:click=move |_| {
                            recent_folders.update(|folders| folders.clear());
                            save_recent_folders();
                        }
                    ) { "Clear All" }
                }
                
                div(class="recent-folders-list") {
                    {move || {
                        let folders = recent_folders.get();
                        if folders.is_empty() {
                            view! {
                                div(class="empty-state") {
                                    span(class="empty-icon") { "📂" }
                                    span(class="empty-text") { "No recent folders" }
                                }
                            }
                        } else {
                            folders.into_iter().map(|folder| {
                                view! {
                                    div(
                                        class="recent-folder-item",
                                        on:click=move |_| {
                                            current_path.set(folder.path.clone());
                                            validate_folder_info(folder.path.clone());
                                        }
                                    ) {
                                        div(class="folder-info") {
                                            div(class="folder-path") { &folder.path }
                                            div(class="folder-meta") {
                                                span(class="folder-date") { &folder.last_accessed }
                                                span(class="folder-size") { &folder.size_display }
                                            }
                                        }
                                        div(class="folder-actions") {
                                            button(class="btn-icon") { "📁" }
                                        }
                                    }
                                }
                            }).collect_view()
                        }
                    }}
                }
            }
            
            // Quick Access Buttons
            div(class="quick-access-section enhanced-card") {
                h3(class="section-title") { "⚡ Quick Access" }
                div(class="quick-access-grid") {
                    button(
                        class="quick-access-btn",
                        on:click=move |_| quick_access_desktop()
                    ) {
                        div(class="quick-access-icon") { "🖥️" }
                        div(class="quick-access-label") { "Desktop" }
                    }
                    button(
                        class="quick-access-btn",
                        on:click=move |_| quick_access_documents()
                    ) {
                        div(class="quick-access-icon") { "📄" }
                        div(class="quick-access-label") { "Documents" }
                    }
                    button(
                        class="quick-access-btn",
                        on:click=move |_| quick_access_projects()
                    ) {
                        div(class="quick-access-icon") { "💻" }
                        div(class="quick-access-label") { "Projects" }
                    }
                    button(
                        class="quick-access-btn",
                        on:click=move |_| quick_access_git_repos()
                    ) {
                        div(class="quick-access-icon") { "🔧" }
                        div(class="quick-access-label") { "Git Repos" }
                    }
                }
            }
            
            // Drag & Drop Zone
            div(
                class="drag-drop-zone enhanced-card",
                class:drag-over=move || drag_over.get(),
                on:dragover=move |ev| {
                    ev.prevent_default();
                    drag_over.set(true);
                },
                on:dragleave=move |_| {
                    drag_over.set(false);
                },
                on:drop=move |ev| {
                    ev.prevent_default();
                    drag_over.set(false);
                    handle_folder_drop(ev);
                }
            ) {
                div(class="drag-drop-content") {
                    div(class="drag-drop-icon") { "📁" }
                    div(class="drag-drop-text") {
                        div(class="drag-drop-primary") { 
                            "Drag & Drop Folder Here" 
                        }
                        div(class="drag-drop-secondary") { 
                            "Or click Browse to select a folder" 
                        }
                    }
                }
            }
            
            // Folder Information Display
            {move || {
                let info = folder_info.get();
                if let Some(info) = info {
                    view! {
                        div(class="folder-info-display enhanced-card") {
                            div(class="info-header") {
                                h3(class="info-title") { "📊 Folder Information" }
                            }
                            div(class="info-grid") {
                                div(class="info-item") {
                                    span(class="info-label") { "Total Files:" }
                                    span(class="info-value") { &info.file_count.to_string() }
                                }
                                div(class="info-item") {
                                    span(class="info-label") { "Total Size:" }
                                    span(class="info-value") { &info.size_display }
                                }
                                div(class="info-item") {
                                    span(class="info-label") { "Permissions:" }
                                    span(class="info-value permission-badge") { 
                                        {if info.writable { "✅ Read/Write" } else { "⚠️ Read Only" }}
                                    }
                                }
                                div(class="info-item") {
                                    span(class="info-label") { "Code Files:" }
                                    span(class="info-value") { &info.code_files.to_string() }
                                }
                            }
                        }
                    }
                } else {
                    view! { div {} }
                }
            }}
        }
        
        // Action Buttons
        div(class="selection-actions") {
            {move || {
                let path = current_path.get();
                let validation = folder_validation.get();
                
                if !path.is_empty() && validation.is_valid {
                    view! {
                        div(class="action-buttons") {
                            button(
                                class="btn btn-primary action-btn",
                                on:click=move |_| {
                                    add_to_recent_folders(path.clone());
                                    interface_state.set(InterfaceState::ActionsMode);
                                }
                            ) {
                                span(class="btn-icon") { "▶️" }
                                span { "Continue to Actions" }
                            }
                        }
                    }
                } else {
                    view! {
                        div(class="action-buttons disabled") {
                            button(class="btn btn-primary action-btn", disabled=true) {
                                span(class="btn-icon") { "▶️" }
                                span { "Select Valid Folder First" }
                            }
                        }
                    }
                }
            }}
        }
    }
}'''
    
    def _create_actions_mode_template(self):
        """Create enhanced actions mode template"""
        return '''// Enhanced Actions Mode Interface (CODEBASE-5)
view! {
    div(class="actions-mode-container") {
        div(class="actions-header") {
            div(class="current-folder-display") {
                div(class="folder-breadcrumb") {
                    span(class="breadcrumb-icon") { "📁" }
                    span(class="breadcrumb-path") { &current_path.get() }
                    button(
                        class="btn btn-link change-folder-btn",
                        on:click=move |_| {
                            interface_state.set(InterfaceState::SelectionMode);
                        }
                    ) { "Change Folder" }
                }
            }
            h2(class="actions-title") { "🎯 Choose Analysis Action" }
            p(class="actions-subtitle") { 
                "Select the type of analysis you want to perform on your codebase" 
            }
        }
        
        div(class="actions-grid") {
            // Full Analysis Action
            div(class="action-card enhanced-action-card full-analysis") {
                div(class="action-gradient") {}
                div(class="action-content") {
                    div(class="action-icon") { "🔍" }
                    div(class="action-details") {
                        h3(class="action-title") { "Full Analysis" }
                        p(class="action-description") { 
                            "Complete scan of all files with detailed bad character detection, code quality analysis, and comprehensive reporting." 
                        }
                        ul(class="action-features") {
                            li { "✅ Bad character detection" }
                            li { "✅ File type analysis" }
                            li { "✅ Code quality metrics" }
                            li { "✅ Detailed reports" }
                        }
                    }
                    div(class="action-footer") {
                        div(class="action-stats") {
                            span(class="stat-item") { "⏱️ 2-5 min" }
                            span(class="stat-item") { "📊 Full Report" }
                        }
                        button(
                            class="btn btn-primary action-btn",
                            on:click=move |_| {
                                analysis_type.set("full".to_string());
                                start_analysis();
                            }
                        ) {
                            span(class="btn-icon") { "🚀" }
                            span { "Start Full Analysis" }
                        }
                    }
                }
            }
            
            // Quick Clean Action
            div(class="action-card enhanced-action-card quick-clean") {
                div(class="action-gradient") {}
                div(class="action-content") {
                    div(class="action-icon") { "🧹" }
                    div(class="action-details") {
                        h3(class="action-title") { "Quick Clean" }
                        p(class="action-description") { 
                            "Fast scan focusing on common bad characters with basic cleanup and quick fixes." 
                        }
                        ul(class="action-features") {
                            li { "✅ Common bad chars" }
                            li { "✅ Quick fixes" }
                            li { "✅ Basic cleanup" }
                            li { "✅ Fast processing" }
                        }
                    }
                    div(class="action-footer") {
                        div(class="action-stats") {
                            span(class="stat-item") { "⏱️ 30 sec" }
                            span(class="stat-item") { "🔧 Quick Fix" }
                        }
                        button(
                            class="btn btn-secondary action-btn",
                            on:click=move |_| {
                                analysis_type.set("quick".to_string());
                                start_analysis();
                            }
                        ) {
                            span(class="btn-icon") { "⚡" }
                            span { "Start Quick Clean" }
                        }
                    }
                }
            }
            
            // Generate Report Action
            div(class="action-card enhanced-action-card generate-report") {
                div(class="action-gradient") {}
                div(class="action-content") {
                    div(class="action-icon") { "📊" }
                    div(class="action-details") {
                        h3(class="action-title") { "Generate Report" }
                        p(class="action-description") { 
                            "Create detailed analysis reports without modifying files. Perfect for audits and documentation." 
                        }
                        ul(class="action-features") {
                            li { "✅ Detailed analysis" }
                            li { "✅ Multiple formats" }
                            li { "✅ No file changes" }
                            li { "✅ Export options" }
                        }
                    }
                    div(class="action-footer") {
                        div(class="action-stats") {
                            span(class="stat-item") { "⏱️ 1-3 min" }
                            span(class="stat-item") { "📋 Report Only" }
                        }
                        button(
                            class="btn btn-accent action-btn",
                            on:click=move |_| {
                                analysis_type.set("report".to_string());
                                start_analysis();
                            }
                        ) {
                            span(class="btn-icon") { "📝" }
                            span { "Generate Report" }
                        }
                    }
                }
            }
            
            // Custom Analysis Action
            div(class="action-card enhanced-action-card custom-analysis") {
                div(class="action-gradient") {}
                div(class="action-content") {
                    div(class="action-icon") { "⚙️" }
                    div(class="action-details") {
                        h3(class="action-title") { "Custom Analysis" }
                        p(class="action-description") { 
                            "Configure specific analysis parameters, file filters, and custom character sets for specialized scanning." 
                        }
                        ul(class="action-features") {
                            li { "✅ Custom filters" }
                            li { "✅ Specific file types" }
                            li { "✅ Custom char sets" }
                            li { "✅ Advanced options" }
                        }
                    }
                    div(class="action-footer") {
                        div(class="action-stats") {
                            span(class="stat-item") { "⏱️ Variable" }
                            span(class="stat-item") { "🔧 Customizable" }
                        }
                        button(
                            class="btn btn-outline action-btn",
                            on:click=move |_| {
                                show_custom_options.set(true);
                            }
                        ) {
                            span(class="btn-icon") { "🛠️" }
                            span { "Configure Analysis" }
                        }
                    }
                }
            }
        }
        
        // Advanced Options Panel
        {move || {
            if show_custom_options.get() {
                view! {
                    div(class="custom-options-panel enhanced-card") {
                        div(class="panel-header") {
                            h3(class="panel-title") { "🛠️ Custom Analysis Options" }
                            button(
                                class="btn btn-link close-panel",
                                on:click=move |_| show_custom_options.set(false)
                            ) { "✕" }
                        }
                        
                        div(class="options-grid") {
                            div(class="option-group") {
                                label(class="option-label") { "File Type Filters" }
                                div(class="checkbox-group") {
                                    label(class="checkbox-item") {
                                        input(type="checkbox", checked=true) {}
                                        span { "Source Code (.rs, .js, .py, etc.)" }
                                    }
                                    label(class="checkbox-item") {
                                        input(type="checkbox", checked=true) {}
                                        span { "Configuration Files (.json, .yaml, etc.)" }
                                    }
                                    label(class="checkbox-item") {
                                        input(type="checkbox", checked=false) {}
                                        span { "Documentation (.md, .txt, etc.)" }
                                    }
                                }
                            }
                            
                            div(class="option-group") {
                                label(class="option-label") { "Analysis Depth" }
                                div(class="radio-group") {
                                    label(class="radio-item") {
                                        input(type="radio", name="depth", value="surface") {}
                                        span { "Surface scan (faster)" }
                                    }
                                    label(class="radio-item") {
                                        input(type="radio", name="depth", value="deep", checked=true) {}
                                        span { "Deep analysis (thorough)" }
                                    }
                                }
                            }
                        }
                        
                        div(class="panel-actions") {
                            button(
                                class="btn btn-primary",
                                on:click=move |_| {
                                    analysis_type.set("custom".to_string());
                                    start_analysis();
                                }
                            ) { "Start Custom Analysis" }
                        }
                    }
                }
            } else {
                view! { div {} }
            }
        }}
        
        // Navigation
        div(class="actions-navigation") {
            button(
                class="btn btn-secondary nav-btn",
                on:click=move |_| {
                    interface_state.set(InterfaceState::SelectionMode);
                }
            ) {
                span(class="btn-icon") { "⬅️" }
                span { "Back to Selection" }
            }
        }
    }
}'''
    
    def _create_processing_mode_template(self):
        """Create processing mode template"""
        return '''// Enhanced Processing Mode Interface
view! {
    div(class="processing-mode-container") {
        div(class="processing-header") {
            h2(class="processing-title") { "🔄 Analysis in Progress" }
            div(class="processing-subtitle") { 
                "Analyzing your codebase for bad characters and code quality issues..." 
            }
        }
        
        div(class="processing-content") {
            // Progress Bar Section
            div(class="progress-section enhanced-card") {
                div(class="progress-header") {
                    div(class="progress-stats") {
                        span(class="stat-item") { 
                            format!("Files: {}/{}", 
                                progress.get().files_processed, 
                                progress.get().total_files
                            )
                        }
                        span(class="stat-item") { 
                            format!("{}%", progress.get().percentage)
                        }
                    }
                    div(class="time-estimate") {
                        span { format!("ETA: {}", progress.get().time_remaining) }
                    }
                }
                
                div(class="progress-bar-container") {
                    div(
                        class="progress-bar",
                        style=move || format!("width: {}%", progress.get().percentage)
                    ) {}
                }
                
                div(class="current-operation") {
                    span(class="operation-icon") { "📄" }
                    span(class="operation-text") { 
                        &progress.get().current_file 
                    }
                }
            }
            
            // Live Stats Section
            div(class="stats-section enhanced-card") {
                h3(class="stats-title") { "📊 Live Statistics" }
                div(class="stats-grid") {
                    div(class="stat-card") {
                        div(class="stat-value") { &progress.get().bad_chars_found.to_string() }
                        div(class="stat-label") { "Bad Characters" }
                        div(class="stat-icon") { "⚠️" }
                    }
                    div(class="stat-card") {
                        div(class="stat-value") { &progress.get().files_with_issues.to_string() }
                        div(class="stat-label") { "Files with Issues" }
                        div(class="stat-icon") { "📁" }
                    }
                    div(class="stat-card") {
                        div(class="stat-value") { &progress.get().clean_files.to_string() }
                        div(class="stat-label") { "Clean Files" }
                        div(class="stat-icon") { "✅" }
                    }
                    div(class="stat-card") {
                        div(class="stat-value") { &format!("{:.1} MB", progress.get().data_processed) }
                        div(class="stat-label") { "Data Processed" }
                        div(class="stat-icon") { "💾" }
                    }
                }
            }
            
            // Recent Activity Feed
            div(class="activity-section enhanced-card") {
                h3(class="activity-title") { "📋 Recent Activity" }
                div(class="activity-feed") {
                    {move || {
                        recent_activity.get().into_iter().map(|activity| {
                            view! {
                                div(class="activity-item") {
                                    div(class="activity-icon") { 
                                        {match activity.activity_type.as_str() {
                                            "found_issue" => "⚠️",
                                            "cleaned_file" => "✅",
                                            "processed" => "📄",
                                            _ => "ℹ️"
                                        }}
                                    }
                                    div(class="activity-content") {
                                        div(class="activity-message") { &activity.message }
                                        div(class="activity-time") { &activity.timestamp }
                                    }
                                }
                            }
                        }).collect_view()
                    }}
                </div>
            }
        }
        
        // Control Buttons
        div(class="processing-controls") {
            button(
                class="btn btn-danger control-btn",
                on:click=move |_| {
                    spawn_local(async move {
                        let _ = invoke("cancel_analysis", &()).await;
                        interface_state.set(InterfaceState::ActionsMode);
                    });
                }
            ) {
                span(class="btn-icon") { "⏹️" }
                span { "Cancel Analysis" }
            }
            
            button(
                class="btn btn-secondary control-btn",
                on:click=move |_| {
                    // Minimize to background
                    minimize_processing.set(true);
                }
            ) {
                span(class="btn-icon") { "⬇️" }
                span { "Minimize" }
            }
        }
    }
}'''
    
    def _create_results_display_template(self):
        """Create enhanced results display template (UI-3)"""
        return '''// Enhanced Results Display (UI-3)
view! {
    div(class="results-container") {
        div(class="results-header") {
            h2(class="results-title") { "📊 Analysis Results" }
            div(class="results-summary") {
                div(class="summary-stats") {
                    div(class="summary-item") {
                        span(class="summary-value") { &results.get().total_files.to_string() }
                        span(class="summary-label") { "Files Analyzed" }
                    }
                    div(class="summary-item") {
                        span(class="summary-value") { &results.get().issues_found.to_string() }
                        span(class="summary-label") { "Issues Found" }
                    }
                    div(class="summary-item") {
                        span(class="summary-value") { &results.get().clean_files.to_string() }
                        span(class="summary-label") { "Clean Files" }
                    }
                }
            }
        }
        
        div(class="results-content") {
            // File Type Categories (UI-3)
            div(class="file-types-section enhanced-card") {
                h3(class="section-title") { "📁 File Type Analysis" }
                
                div(class="file-type-filters") {
                    button(
                        class="filter-btn",
                        class:active=move || file_type_filter.get() == "all",
                        on:click=move |_| file_type_filter.set("all".to_string())
                    ) { "All Types" }
                    button(
                        class="filter-btn",
                        class:active=move || file_type_filter.get() == "source",
                        on:click=move |_| file_type_filter.set("source".to_string())
                    ) { "Source Code" }
                    button(
                        class="filter-btn",
                        class:active=move || file_type_filter.get() == "config",
                        on:click=move |_| file_type_filter.set("config".to_string())
                    ) { "Configuration" }
                    button(
                        class="filter-btn",
                        class:active=move || file_type_filter.get() == "docs",
                        on:click=move |_| file_type_filter.set("docs".to_string())
                    ) { "Documentation" }
                }
                
                div(class="file-type-grid") {
                    {move || {
                        let filtered_types = filter_file_types(
                            results.get().file_types.clone(),
                            file_type_filter.get()
                        );
                        
                        filtered_types.into_iter().map(|file_type| {
                            view! {
                                div(class="file-type-card") {
                                    div(class="file-type-header") {
                                        span(class="file-type-icon") { &file_type.icon }
                                        span(class="file-type-name") { &file_type.name }
                                        span(class="file-type-count") { &file_type.count.to_string() }
                                    }
                                    
                                    div(class="file-type-details") {
                                        div(class="detail-item") {
                                            span(class="detail-label") { "Issues:" }
                                            span(
                                                class="detail-value issue-count",
                                                class:has-issues=file_type.issues > 0
                                            ) { &file_type.issues.to_string() }
                                        }
                                        div(class="detail-item") {
                                            span(class="detail-label") { "Size:" }
                                            span(class="detail-value") { &file_type.total_size }
                                        }
                                    }
                                    
                                    div(class="file-type-progress") {
                                        div(
                                            class="progress-bar",
                                            style=format!(
                                                "width: {}%", 
                                                (file_type.clean_files as f32 / file_type.count as f32 * 100.0)
                                            )
                                        ) {}
                                    }
                                }
                            }
                        }).collect_view()
                    }}
                }
            }
            
            // Issues List
            div(class="issues-section enhanced-card") {
                h3(class="section-title") { "⚠️ Issues Found" }
                
                div(class="issues-filters") {
                    select(
                        class="filter-select",
                        on:change=move |ev| {
                            severity_filter.set(event_target_value(&ev));
                        }
                    ) {
                        option(value="all") { "All Severities" }
                        option(value="high") { "High Priority" }
                        option(value="medium") { "Medium Priority" }
                        option(value="low") { "Low Priority" }
                    }
                }
                
                div(class="issues-list") {
                    {move || {
                        let filtered_issues = filter_issues_by_severity(
                            results.get().issues.clone(),
                            severity_filter.get()
                        );
                        
                        if filtered_issues.is_empty() {
                            view! {
                                div(class="no-issues") {
                                    span(class="no-issues-icon") { "✅" }
                                    span(class="no-issues-text") { "No issues found!" }
                                }
                            }
                        } else {
                            filtered_issues.into_iter().map(|issue| {
                                view! {
                                    div(class="issue-item") {
                                        div(class="issue-header") {
                                            span(class="issue-icon") { 
                                                {match issue.severity.as_str() {
                                                    "high" => "🔴",
                                                    "medium" => "🟡",
                                                    "low" => "🟢",
                                                    _ => "ℹ️"
                                                }}
                                            }
                                            span(class="issue-file") { &issue.file_path }
                                            span(class="issue-line") { format!("Line {}", issue.line_number) }
                                        }
                                        
                                        div(class="issue-content") {
                                            div(class="issue-description") { &issue.description }
                                            div(class="issue-character") { 
                                                format!("Character: '{}' (U+{:04X})", issue.character, issue.unicode_value)
                                            }
                                        }
                                        
                                        div(class="issue-actions") {
                                            button(class="btn btn-sm btn-primary") { "Fix" }
                                            button(class="btn btn-sm btn-secondary") { "Ignore" }
                                            button(class="btn btn-sm btn-link") { "View Context" }
                                        }
                                    }
                                }
                            }).collect_view()
                        }
                    }}
                }
            }
        }
        
        // Export Actions
        div(class="results-actions") {
            div(class="export-section") {
                h3(class="export-title") { "📤 Export Results" }
                div(class="export-buttons") {
                    button(
                        class="btn btn-primary export-btn",
                        on:click=move |_| export_results("json")
                    ) {
                        span(class="btn-icon") { "📋" }
                        span { "Export JSON" }
                    }
                    button(
                        class="btn btn-secondary export-btn",
                        on:click=move |_| export_results("markdown")
                    ) {
                        span(class="btn-icon") { "📝" }
                        span { "Export Markdown" }
                    }
                    button(
                        class="btn btn-accent export-btn",
                        on:click=move |_| export_results("csv")
                    ) {
                        span(class="btn-icon") { "📊" }
                        span { "Export CSV" }
                    }
                }
            }
            
            div(class="navigation-section") {
                button(
                    class="btn btn-outline nav-btn",
                    on:click=move |_| {
                        interface_state.set(InterfaceState::ActionsMode);
                    }
                ) {
                    span(class="btn-icon") { "🔄" }
                    span { "New Analysis" }
                }
                
                button(
                    class="btn btn-link nav-btn",
                    on:click=move |_| {
                        interface_state.set(InterfaceState::SelectionMode);
                    }
                ) {
                    span(class="btn-icon") { "🏠" }
                    span { "Start Over" }
                }
            }
        }
    }
}'''
    
    def _create_homepage_template(self):
        """Create enhanced homepage template"""
        return '''// Enhanced Home Page with Tabs
view! {
    div(class="app-container") {
        header(class="app-header") {
            div(class="header-content") {
                h1(class="app-title") { 
                    span(class="title-icon") { "🛡️" }
                    span { "Bad Character Scanner" }
                }
                p(class="app-subtitle") { 
                    "Detect and eliminate problematic characters in your codebase" 
                }
            }
        }
        
        main(class="app-main") {
            div(class="tab-container") {
                nav(class="tab-nav") {
                    button(
                        class="tab-btn",
                        class:active=move || current_tab.get() == "text",
                        on:click=move |_| current_tab.set("text".to_string())
                    ) {
                        span(class="tab-icon") { "📝" }
                        span { "Text Analysis" }
                    }
                    button(
                        class="tab-btn",
                        class:active=move || current_tab.get() == "codebase",
                        on:click=move |_| current_tab.set("codebase".to_string())
                    ) {
                        span(class="tab-icon") { "📁" }
                        span { "Codebase Analysis" }
                    }
                }
                
                div(class="tab-content") {
                    {move || {
                        match current_tab.get().as_str() {
                            "text" => view! {
                                div(class="tab-pane active") {
                                    // TEXT_ANALYSIS_COMPONENT_PLACEHOLDER
                                }
                            },
                            "codebase" => view! {
                                div(class="tab-pane active") {
                                    {match interface_state.get() {
                                        InterfaceState::SelectionMode => view! {
                                            // SELECTION_MODE_COMPONENT_PLACEHOLDER
                                        },
                                        InterfaceState::ActionsMode => view! {
                                            // ACTIONS_MODE_COMPONENT_PLACEHOLDER
                                        },
                                        InterfaceState::ProcessingMode => view! {
                                            // PROCESSING_MODE_COMPONENT_PLACEHOLDER
                                        },
                                        InterfaceState::ResultsMode => view! {
                                            // RESULTS_DISPLAY_COMPONENT_PLACEHOLDER
                                        }
                                    }}
                                }
                            },
                            _ => view! { div {} }
                        }
                    }}
                }
            }
        }
        
        footer(class="app-footer") {
            div(class="footer-content") {
                p { "Built with Leptos & Tauri • Enhanced UI v2.0" }
            }
        }
    }
}'''
    
    def _write_template(self, filename, content):
        """Write template to file"""
        template_path = self.templates_dir / filename
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def generate_components(self):
        """Generate complete components from templates"""
        print("🔧 Generating enhanced components...")
        
        # Load existing extracted components if available
        extracted_dir = Path("scripts/extracted_components")
        
        # Generate signals and imports
        signals_content = self._generate_signals()
        self._write_generated("signals.rs", signals_content)
        
        imports_content = self._generate_imports()
        self._write_generated("imports.rs", imports_content)
        
        # Generate helper functions
        helpers_content = self._generate_helper_functions()
        self._write_generated("helpers.rs", helpers_content)
        
        # Generate data structures
        structs_content = self._generate_data_structures()
        self._write_generated("data_structures.rs", structs_content)
        
        print("✅ Component generation complete")
    
    def _generate_signals(self):
        """Generate all required signals"""
        return '''// Enhanced Signal Definitions
use leptos::*;

// Tab navigation
let current_tab = create_rw_signal("codebase".to_string());

// Interface state management
let interface_state = create_rw_signal(InterfaceState::SelectionMode);

// Path and folder management
let current_path = create_rw_signal(String::new());
let folder_validation = create_rw_signal(FolderValidation::default());
let folder_info = create_rw_signal(Option::<FolderInfo>::None);
let recent_folders = create_rw_signal(Vec::<RecentFolder>::new());

// UI state
let drag_over = create_rw_signal(false);
let show_custom_options = create_rw_signal(false);
let minimize_processing = create_rw_signal(false);

// Analysis state
let analysis_type = create_rw_signal(String::new());
let progress = create_rw_signal(AnalysisProgress::default());
let recent_activity = create_rw_signal(Vec::<ActivityItem>::new());

// Results and filtering
let results = create_rw_signal(AnalysisResults::default());
let file_type_filter = create_rw_signal("all".to_string());
let severity_filter = create_rw_signal("all".to_string());

// Text analysis (existing)
let text_input = create_rw_signal(String::new());
let analysis_result = create_rw_signal(Option::<TextAnalysisResult>::None);
let is_analyzing = create_rw_signal(false);'''
    
    def _generate_imports(self):
        """Generate all required imports"""
        return '''// Enhanced Import Statements
use leptos::*;
use leptos::html::Input;
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::spawn_local;
use serde::{Deserialize, Serialize};
use serde_wasm_bindgen::to_value;
use web_sys::{Event, HtmlInputElement, DragEvent, FileList};

// Tauri imports
use tauri_leptos::invoke;

// Local modules and types
use crate::types::*;
use crate::utils::*;'''
    
    def _generate_helper_functions(self):
        """Generate helper functions"""
        return '''// Enhanced Helper Functions

async fn validate_folder_info(path: String) {
    if path.is_empty() {
        folder_validation.set(FolderValidation {
            is_valid: false,
            error_message: "Path cannot be empty".to_string(),
        });
        return;
    }
    
    match invoke("validate_folder", &to_value(&path).unwrap()).await {
        Ok(info) => {
            let folder_info: FolderInfo = serde_wasm_bindgen::from_value(info).unwrap();
            folder_validation.set(FolderValidation {
                is_valid: true,
                error_message: String::new(),
            });
            folder_info.set(Some(folder_info));
        }
        Err(e) => {
            folder_validation.set(FolderValidation {
                is_valid: false,
                error_message: format!("Invalid folder: {}", e),
            });
            folder_info.set(None);
        }
    }
}

fn validate_path_async(path: String) {
    spawn_local(async move {
        validate_folder_info(path).await;
    });
}

fn add_to_recent_folders(path: String) {
    let new_folder = RecentFolder {
        path: path.clone(),
        last_accessed: chrono::Utc::now().format("%Y-%m-%d %H:%M").to_string(),
        size_display: "Calculating...".to_string(),
    };
    
    recent_folders.update(|folders| {
        // Remove if already exists
        folders.retain(|f| f.path != path);
        // Add to front
        folders.insert(0, new_folder);
        // Keep only last 10
        folders.truncate(10);
    });
    
    save_recent_folders();
}

fn save_recent_folders() {
    spawn_local(async move {
        let folders = recent_folders.get();
        let _ = invoke("save_recent_folders", &to_value(&folders).unwrap()).await;
    });
}

fn load_recent_folders() {
    spawn_local(async move {
        match invoke("load_recent_folders", &()).await {
            Ok(folders) => {
                let folders: Vec<RecentFolder> = serde_wasm_bindgen::from_value(folders).unwrap();
                recent_folders.set(folders);
            }
            Err(_) => {
                recent_folders.set(Vec::new());
            }
        }
    });
}

fn quick_access_desktop() {
    spawn_local(async move {
        match invoke("get_desktop_path", &()).await {
            Ok(path) => {
                let path_str: String = serde_wasm_bindgen::from_value(path).unwrap();
                current_path.set(path_str.clone());
                validate_folder_info(path_str).await;
            }
            Err(_) => {}
        }
    });
}

fn quick_access_documents() {
    spawn_local(async move {
        match invoke("get_documents_path", &()).await {
            Ok(path) => {
                let path_str: String = serde_wasm_bindgen::from_value(path).unwrap();
                current_path.set(path_str.clone());
                validate_folder_info(path_str).await;
            }
            Err(_) => {}
        }
    });
}

fn quick_access_projects() {
    spawn_local(async move {
        match invoke("get_projects_path", &()).await {
            Ok(path) => {
                let path_str: String = serde_wasm_bindgen::from_value(path).unwrap();
                current_path.set(path_str.clone());
                validate_folder_info(path_str).await;
            }
            Err(_) => {}
        }
    });
}

fn quick_access_git_repos() {
    spawn_local(async move {
        match invoke("find_git_repositories", &()).await {
            Ok(paths) => {
                let paths: Vec<String> = serde_wasm_bindgen::from_value(paths).unwrap();
                if let Some(first_repo) = paths.first() {
                    current_path.set(first_repo.clone());
                    validate_folder_info(first_repo.clone()).await;
                }
            }
            Err(_) => {}
        }
    });
}

fn handle_folder_drop(ev: DragEvent) {
    ev.prevent_default();
    // Handle drag and drop folder logic
    spawn_local(async move {
        // Extract folder path from drop event and validate
        // This would need proper implementation based on Tauri capabilities
    });
}

fn start_analysis() {
    interface_state.set(InterfaceState::ProcessingMode);
    
    spawn_local(async move {
        let analysis_config = AnalysisConfig {
            path: current_path.get(),
            analysis_type: analysis_type.get(),
        };
        
        match invoke("start_analysis", &to_value(&analysis_config).unwrap()).await {
            Ok(_) => {
                // Analysis started, progress will be tracked via events
            }
            Err(e) => {
                log::error!("Failed to start analysis: {}", e);
                interface_state.set(InterfaceState::ActionsMode);
            }
        }
    });
}

fn filter_file_types(file_types: Vec<FileTypeInfo>, filter: String) -> Vec<FileTypeInfo> {
    match filter.as_str() {
        "source" => file_types.into_iter().filter(|ft| ft.category == "source").collect(),
        "config" => file_types.into_iter().filter(|ft| ft.category == "config").collect(),
        "docs" => file_types.into_iter().filter(|ft| ft.category == "docs").collect(),
        _ => file_types,
    }
}

fn filter_issues_by_severity(issues: Vec<IssueInfo>, severity: String) -> Vec<IssueInfo> {
    match severity.as_str() {
        "high" => issues.into_iter().filter(|issue| issue.severity == "high").collect(),
        "medium" => issues.into_iter().filter(|issue| issue.severity == "medium").collect(),
        "low" => issues.into_iter().filter(|issue| issue.severity == "low").collect(),
        _ => issues,
    }
}

fn export_results(format: &str) {
    spawn_local(async move {
        let export_config = ExportConfig {
            format: format.to_string(),
            results: results.get(),
        };
        
        match invoke("export_results", &to_value(&export_config).unwrap()).await {
            Ok(path) => {
                let path_str: String = serde_wasm_bindgen::from_value(path).unwrap();
                log::info!("Results exported to: {}", path_str);
            }
            Err(e) => {
                log::error!("Export failed: {}", e);
            }
        }
    });
}'''
    
    def _generate_data_structures(self):
        """Generate data structures"""
        return '''// Enhanced Data Structures

#[derive(Debug, Clone, PartialEq)]
pub enum InterfaceState {
    SelectionMode,
    ActionsMode,
    ProcessingMode,
    ResultsMode,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FolderInfo {
    pub file_count: usize,
    pub size_display: String,
    pub size_bytes: u64,
    pub writable: bool,
    pub code_files: usize,
    pub permissions: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFolder {
    pub path: String,
    pub last_accessed: String,
    pub size_display: String,
}

#[derive(Debug, Clone)]
pub struct FolderValidation {
    pub is_valid: bool,
    pub error_message: String,
}

impl Default for FolderValidation {
    fn default() -> Self {
        Self {
            is_valid: false,
            error_message: String::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisProgress {
    pub files_processed: usize,
    pub total_files: usize,
    pub percentage: u32,
    pub current_file: String,
    pub time_remaining: String,
    pub bad_chars_found: usize,
    pub files_with_issues: usize,
    pub clean_files: usize,
    pub data_processed: f64,
}

impl Default for AnalysisProgress {
    fn default() -> Self {
        Self {
            files_processed: 0,
            total_files: 0,
            percentage: 0,
            current_file: String::new(),
            time_remaining: "Calculating...".to_string(),
            bad_chars_found: 0,
            files_with_issues: 0,
            clean_files: 0,
            data_processed: 0.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityItem {
    pub activity_type: String,
    pub message: String,
    pub timestamp: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub total_files: usize,
    pub issues_found: usize,
    pub clean_files: usize,
    pub file_types: Vec<FileTypeInfo>,
    pub issues: Vec<IssueInfo>,
}

impl Default for AnalysisResults {
    fn default() -> Self {
        Self {
            total_files: 0,
            issues_found: 0,
            clean_files: 0,
            file_types: Vec::new(),
            issues: Vec::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTypeInfo {
    pub name: String,
    pub icon: String,
    pub count: usize,
    pub issues: usize,
    pub total_size: String,
    pub clean_files: usize,
    pub category: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IssueInfo {
    pub file_path: String,
    pub line_number: usize,
    pub character: String,
    pub unicode_value: u32,
    pub description: String,
    pub severity: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisConfig {
    pub path: String,
    pub analysis_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportConfig {
    pub format: String,
    pub results: AnalysisResults,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextAnalysisResult {
    pub bad_characters: Vec<BadCharacterInfo>,
    pub total_characters: usize,
    pub problematic_characters: usize,
    pub severity_breakdown: std::collections::HashMap<String, usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterInfo {
    pub character: String,
    pub unicode_value: u32,
    pub positions: Vec<usize>,
    pub severity: String,
    pub description: String,
}'''
    
    def _write_generated(self, filename, content):
        """Write generated component to file"""
        generated_path = self.generated_dir / filename
        with open(generated_path, 'w', encoding='utf-8') as f:
            f.write(content)

def main():
    """Main execution function"""
    print("🔧 Starting Phase 2: Component Generation")
    print("=" * 50)
    
    generator = ComponentGenerator()
    
    # Step 1: Create templates
    print("\n📝 Step 1: Creating component templates...")
    generator.create_templates()
    
    # Step 2: Generate components
    print("\n🔧 Step 2: Generating enhanced components...")
    generator.generate_components()
    
    print("\n" + "=" * 50)
    print("✅ Phase 2 Complete!")
    print("📁 Templates: scripts/component_templates/")
    print("📁 Generated: scripts/generated_components/")
    print("\nNext: Run scripts/rebuild_lib.py")

if __name__ == "__main__":
    main()
