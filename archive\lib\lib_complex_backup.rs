use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use wasm_bindgen::prelude::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen_futures::spawn_local;
use std::collections::HashMap;
use web_sys::window;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
    
    #[wasm_bindgen(js_name = "eval")]
    fn js_eval(s: &str) -> JsValue;
}

// Data structures matching the Rust backend
#[derive(Debug, Clone, Serialize, Deserialize)]
struct CharacterInfo {
    character: char,
    position: usize,
    unicode_name: String,
    unicode_block: String,
    category: String,
    codepoint: u32,
    utf8_bytes: Vec<u8>,
    utf16_units: Vec<u16>,
    is_suspicious: bool,
    suspicion_reasons: Vec<String>,
    recommendations: Vec<String>,
    visual_width: usize,
    is_combining: bool,
    is_emoji: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct EncodingInfo {
    detected_encoding: String,
    confidence: f32,
    is_valid_utf8: bool,
    bom_detected: Option<String>,
    line_endings: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct SecurityAnalysis {
    risk_level: String,
    phishing_indicators: Vec<String>,
    homograph_attacks: Vec<String>,
    steganography_potential: bool,
    script_mixing: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct PatternMatch {
    pattern_name: String,
    description: String,
    start_position: usize,
    end_position: usize,
    matched_text: String,
    severity: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AnalysisResults {
    id: String,
    timestamp: String,
    input_text: String,
    text_hash: String,
    total_characters: usize,
    total_bytes: usize,
    total_graphemes: usize,
    visual_width: usize,
    encoding_info: EncodingInfo,
    suspicious_characters: Vec<CharacterInfo>,
    character_breakdown: HashMap<String, usize>,
    script_breakdown: HashMap<String, usize>,
    analysis_duration_ms: u64,
    confidence_score: f32,
    security_analysis: SecurityAnalysis,
    patterns_found: Vec<PatternMatch>,
    recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CleaningChange {
    start: usize,
    end: usize,
    original: String,
    cleaned: String,
    change_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CleaningResult {
    original: String,
    cleaned: String,
    changes: Vec<CleaningChange>,    stats: HashMap<String, u32>,
}

// Data structures for codebase analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
struct CodeBaseAnalysisResult {
    total_files: usize,
    files_with_issues: usize,
    total_suspicious_chars: usize,
    health_score: f64,
    file_details: Vec<FileAnalysisDetail>,
    analysis_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FileAnalysisDetail {
    file_path: String,
    relative_path: String,
    file_size: u64,
    total_characters: usize,
    suspicious_characters: usize,
    issues: Vec<String>,
    file_type: String,
    encoding: String,
    analysis_status: String,
    error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CodeBaseStats {
    total_files: usize,
    processed_files: usize,
    current_file: String,
    progress_percent: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct DropResult {
    success: bool,
    message: String,
    file_count: Option<usize>,
    folder_path: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FolderInfo {
    path: String,
    exists: bool,
    readable: bool,
    file_count: Option<usize>,
    total_size: Option<u64>,
    error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct RecentFolder {
    path: String,
    last_used: String,
    file_count: Option<usize>,
    total_size: Option<u64>,
}

// Function to call Tauri commands using direct JavaScript evaluation
async fn call_tauri_command(command: &str, args: &str) -> Result<String, String> {
    let js_code = format!(
        r#"
        (async () => {{
            try {{
                if (typeof window === 'undefined' || !window.__TAURI__) {{
                    throw new Error('Tauri API not available');
                }}
                const result = await window.__TAURI__.core.invoke('{}', {});
                // Convert result to string if it's not already
                if (typeof result === 'string') {{
                    return result;
                }} else {{
                    return JSON.stringify(result);
                }}
            }} catch (e) {{
                throw e.toString();
            }}
        }})()
        "#,
        command, args
    );
    
    let promise = js_eval(&js_code);
    let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::from(promise))
        .await
        .map_err(|e| format!("JavaScript error: {:?}", e))?;
    
    result.as_string().ok_or_else(|| "Failed to convert result to string".to_string())
}

// Improved function to call Tauri commands with proper JSON parameter handling
async fn invoke(command: &str, args: &serde_json::Value) -> Result<serde_json::Value, String> {
    let args_str = args.to_string();
    let js_code = format!(
        r#"
        (async () => {{
            try {{
                if (typeof window === 'undefined' || !window.__TAURI__) {{
                    throw new Error('Tauri API not available');
                }}
                const params = {};
                const result = await window.__TAURI__.core.invoke('{}', params);
                return JSON.stringify(result);
            }} catch (e) {{
                throw e.toString();
            }}
        }})()
        "#,
        args_str, command
    );
    
    let promise = js_eval(&js_code);
    let result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::from(promise))
        .await
        .map_err(|e| format!("JavaScript error: {:?}", e))?;
    
    let result_str = result.as_string().ok_or_else(|| "Failed to convert result to string".to_string())?;    serde_json::from_str(&result_str).map_err(|e| format!("Failed to parse JSON result: {}", e))
}

// Helper function to listen to Tauri events
async fn listen_to_event<F>(event_name: &str, callback: F) -> Result<(), String> 
where 
    F: Fn(serde_json::Value) + 'static
{    let callback_js = wasm_bindgen::closure::Closure::wrap(Box::new(move |event_data: JsValue| {
        if let Some(json_str) = event_data.as_string() {
            if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&json_str) {
                callback(json_value);
            }
        }
    }) as Box<dyn Fn(JsValue)>);
    
    let js_code = format!(
        r#"
        (async () => {{
            try {{
                if (typeof window === 'undefined' || !window.__TAURI__) {{
                    throw new Error('Tauri API not available');
                }}
                
                const unlisten = await window.__TAURI__.event.listen('{}', (event) => {{
                    const callback = arguments[0];
                    callback(JSON.stringify(event.payload));
                }});
                
                return 'success';
            }} catch (e) {{
                throw e.toString();
            }}
        }})()
        "#,
        event_name
    );
    
    let promise = js_eval(&js_code);
    let _result = wasm_bindgen_futures::JsFuture::from(js_sys::Promise::from(promise))
        .await
        .map_err(|e| format!("JavaScript error: {:?}", e))?;
    
    // Keep the callback alive
    callback_js.forget();
    
    Ok(())
}

// Entry point for WASM
#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
    console_log::init_with_level(log::Level::Debug).expect("error initializing logger");
    
    log::info!("Starting Leptos Tauri v2 application");
    
    mount_to_body(|| {
        view! { <App/> }
    })
}

#[component]
pub fn App() -> impl IntoView {
    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Enhanced Bad Character Scanner - Leptos + Tauri v2"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>
        <Link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"/>        <Router>
            <Routes>
                <Route path="" view=HomePage/>
                <Route path="/clean" view=CleanTextPage/>
                <Route path="/codebase" view=CodeBaseAnalysisPage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_result, set_analysis_result) = create_signal(None::<AnalysisResults>);
    let (loading, set_loading) = create_signal(false);
    let (active_tab, set_active_tab) = create_signal("overview".to_string());
    let (error_message, set_error_message) = create_signal(None::<String>);
    let (export_format, set_export_format) = create_signal("json".to_string());

    // Sample texts for quick testing
    let sample_texts = vec![
        ("Regular Text", "Hello, World! This is normal text."),
        ("Zero-Width Characters", "Hello\u{200B}\u{200C}\u{200D}World"),
        ("Mixed Scripts", "Hеllo Wοrld"), // Contains Cyrillic е and Greek ο
        ("Control Characters", "Hello\x00\x01\x02World"),
        ("Bidirectional Override", "Hello\u{202E}dlroW"),
        ("Emoji Test", "Hello 👋 World 🌍 Test 🚀"),
        ("Combining Characters", "e\u{0301}\u{0302}\u{0303}\u{0304}\u{0305}"),
    ];

    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_error_message.set(Some("Please enter some text to analyze.".to_string()));
            return;
        }

        set_loading.set(true);
        set_error_message.set(None);
        set_analysis_result.set(None);

        spawn_local(async move {
            let args = format!(r#"{{"text":"{}"}}"#, serde_json::to_string(&text).unwrap_or_default().trim_matches('"'));
            
            match call_tauri_command("analyze_characters", &args).await {
                Ok(result_str) => {
                    match serde_json::from_str::<AnalysisResults>(&result_str) {
                        Ok(results) => {
                            set_analysis_result.set(Some(results));
                            set_active_tab.set("overview".to_string());
                        },
                        Err(e) => {
                            set_error_message.set(Some(format!("Failed to parse results: {}", e)));
                        }
                    }
                },
                Err(e) => {
                    set_error_message.set(Some(format!("Analysis failed: {}", e)));
                }
            }
            set_loading.set(false);
        });
    };

    let load_sample = move |text: &'static str| {
        move |_| {
            set_input_text.set(text.to_string());
        }
    };

    let export_analysis = move |_| {
        if let Some(results) = analysis_result.get() {
            let format = export_format.get();
            spawn_local(async move {
                let args = format!(r#"{{"results":{},"format":"{}"}}"#, 
                    serde_json::to_string(&results).unwrap_or_default(), format);
                
                match call_tauri_command("export_analysis", &args).await {
                    Ok(export_data) => {
                        // Create download link
                        let js_code = format!(
                            r#"
                            const blob = new Blob([{}], {{ type: 'text/plain' }});
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = 'analysis_report.{}';
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                            "#,
                            serde_json::to_string(&export_data).unwrap_or_default(),
                            format
                        );
                        js_eval(&js_code);
                    },
                    Err(e) => {
                        log(&format!("Export failed: {}", e));
                    }
                }
            });
        }
    };    let clean_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_error_message.set(Some("Please enter some text to clean.".to_string()));
            return;
        }

        // Store the text in localStorage and navigate to clean page
        let js_code = format!(
            r#"localStorage.setItem('textToClean', {});"#,
            serde_json::to_string(&text).unwrap_or_default()
        );
        js_eval(&js_code);
        // Navigate to clean page
        let _ = window().unwrap().location().set_href("/clean");
    };

    let clean_codebase = move |_| {
        // Navigate to codebase analysis page
        if let Some(window) = window() {
            let _ = window.location().set_href("/codebase");
        }
    };
    
    let clean_with_verification = move |_| {
        // Navigate to codebase analysis page
        if let Some(window) = window() {
            let _ = window.location().set_href("/codebase");
        }
    };

    view! {
        <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
            <div class="container mx-auto px-4 py-6">
                <div class="max-w-7xl mx-auto">
                    // Enhanced Header
                    <div class="text-center mb-8">
                        <h1 class="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                            "🔍 Enhanced Character Scanner"
                        </h1>
                        <p class="text-xl text-gray-600 mb-2">
                            "Advanced Unicode Analysis & Security Scanner"
                        </p>
                        <p class="text-sm text-gray-500">
                            " " <span class="font-semibold text-blue-600">""</span>
                            " with comprehensive text analysis capabilities. By John Shoy"
                        </p>                    </div>

                    // Code Base Fixing Tools Section
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-200">
                        <div class="flex flex-wrap items-center justify-between mb-4">
                            <label class="block text-lg font-semibold text-gray-800">
                                <i class="fas fa-wrench mr-2 text-purple-500"></i>
                                "Code Base Fixing Tools"
                            </label>
                        </div>                        <div class="flex flex-col gap-3 justify-center">
                            <button
                                on:click=clean_codebase
                                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                            >
                                <i class="fas fa-broom mr-2"></i>
                                "Quick Clean & Analyze"
                            </button>
                            <button
                                on:click=clean_with_verification
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                            >
                                <i class="fas fa-shield-check mr-2"></i>
                                "Enhanced Clean + Verify"
                            </button>
                        </div>
                    </div>

                    // Text Input Section
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-200">
                        <div class="flex flex-wrap items-center justify-between mb-4">
                            <label class="block text-lg font-semibold text-gray-800">
                                <i class="fas fa-edit mr-2 text-blue-500"></i>
                                "Text Input"
                            </label>
                            <div class="flex gap-2">                                <button
                                    on:click=clean_text
                                    class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm"
                                    disabled=move || input_text.get().is_empty()
                                >
                                    <i class="fas fa-broom mr-2"></i>
                                    "Clean Text"
                                </button>
                                <button
                                    on:click=analyze_text
                                    class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                                    disabled=loading
                                >
                                    {move || if loading.get() {
                                        view! { <><i class="fas fa-spinner fa-spin mr-2"></i>"Analyzing..."</> }
                                    } else {
                                        view! { <><i class="fas fa-search mr-2"></i>"Analyze Text"</> }
                                    }}
                                </button>
                            </div>
                        </div>

                        <textarea
                            rows="8"
                            class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm resize-none"
                            placeholder="Enter or paste text to analyze... Supports Unicode, control characters, invisible characters, and more."
                            on:input=move |ev| {
                                set_input_text.set(event_target_value(&ev));
                                set_error_message.set(None);
                            }
                            prop:value=input_text
                        />
                        
                        <div class="mt-4 flex flex-wrap justify-between items-center">
                            <div class="text-sm text-gray-600 space-x-4">
                                <span>
                                    <i class="fas fa-font mr-1"></i>
                                    "Characters: " {move || input_text.get().chars().count()}
                                </span>
                                <span>
                                    <i class="fas fa-database mr-1"></i>
                                    "Bytes: " {move || input_text.get().len()}
                                </span>
                            </div>
                        </div>

                        // Sample texts
                        <div class="mt-4 border-t pt-4">
                            <p class="text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-vial mr-2"></i>
                                "Quick Test Samples:"
                            </p>
                            <div class="flex flex-wrap gap-2">
                                {sample_texts.into_iter().map(|(name, text)| {
                                    let text = text;
                                    view! {
                                        <button
                                            on:click=load_sample(text)
                                            class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-xs border"
                                        >
                                            {name}
                                        </button>
                                    }
                                }).collect_view()}
                            </div>
                        </div>
                    </div>

                    // Error Display
                    {move || error_message.get().map(|msg| view! {
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                                <p class="text-red-800">{msg}</p>
                            </div>
                        </div>
                    })}

                    // Results Section
                    {move || analysis_result.get().map(|results| {
                        let risk_color = match results.security_analysis.risk_level.as_str() {
                            "Critical" => "text-red-600 bg-red-50 border-red-200",
                            "High" => "text-orange-600 bg-orange-50 border-orange-200", 
                            "Medium" => "text-yellow-600 bg-yellow-50 border-yellow-200",
                            _ => "text-green-600 bg-green-50 border-green-200"
                        };

                        view! {
                            <div class="bg-white rounded-xl shadow-lg border border-gray-200">
                                // Results Header with tabs
                                <div class="border-b border-gray-200 p-6">
                                    <div class="flex flex-wrap justify-between items-center mb-4">
                                        <h2 class="text-2xl font-bold text-gray-900">
                                            <i class="fas fa-chart-line mr-3 text-blue-500"></i>
                                            "Analysis Results"
                                        </h2>
                                        <div class="flex items-center gap-4">
                                            <div class={format!("px-3 py-1 rounded-full border font-semibold text-sm {}", risk_color)}>
                                                <i class="fas fa-shield-alt mr-2"></i>
                                                "Risk: " {results.security_analysis.risk_level.clone()}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                "ID: " {results.id.clone()}
                                            </div>
                                        </div>
                                    </div>

                                    // Tab Navigation
                                    <div class="flex flex-wrap gap-2">
                                        {["overview", "characters", "security", "patterns", "encoding", "export"].iter().map(|tab| {
                                            let tab = *tab;
                                            let is_active = move || active_tab.get() == tab;
                                            let tab_name = match tab {
                                                "overview" => "📊 Overview",
                                                "characters" => "🔍 Characters",
                                                "security" => "🛡️ Security", 
                                                "patterns" => "🎯 Patterns",
                                                "encoding" => "📝 Encoding",
                                                "export" => "💾 Export",
                                                _ => tab
                                            };
                                            
                                            view! {
                                                <button
                                                    on:click=move |_| set_active_tab.set(tab.to_string())
                                                    class=move || if is_active() {
                                                        "px-4 py-2 bg-blue-600 text-white rounded-lg font-medium"
                                                    } else {
                                                        "px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                                    }
                                                >
                                                    {tab_name}
                                                </button>
                                            }
                                        }).collect_view()}
                                    </div>
                                </div>

                                // Tab Content
                                <div class="p-6">                                    {move || match active_tab.get().as_str() {
                                        "overview" => render_overview_tab(results.clone()).into_view(),
                                        "characters" => render_characters_tab(results.clone()).into_view(),
                                        "security" => render_security_tab(results.clone()).into_view(),
                                        "patterns" => render_patterns_tab(results.clone()).into_view(),
                                        "encoding" => render_encoding_tab(results.clone()).into_view(),
                                        "export" => render_export_tab(results.clone(), export_format, set_export_format, export_analysis.clone()).into_view(),
                                        _ => view! { <div>"Unknown tab"</div> }.into_view()
                                    }}
                                </div>
                            </div>
                        }
                    })}
                </div>
            </div>
        </div>
    }
}

// Tab rendering functions
fn render_overview_tab(results: AnalysisResults) -> impl IntoView {
    view! {
        <div class="space-y-6">
            // Statistics Grid
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div class="text-blue-600 text-sm font-medium">"Characters"</div>
                    <div class="text-2xl font-bold text-blue-900">{results.total_characters}</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div class="text-purple-600 text-sm font-medium">"Bytes"</div>
                    <div class="text-2xl font-bold text-purple-900">{results.total_bytes}</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div class="text-green-600 text-sm font-medium">"Visual Width"</div>
                    <div class="text-2xl font-bold text-green-900">{results.visual_width}</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                    <div class="text-orange-600 text-sm font-medium">"Suspicious"</div>
                    <div class="text-2xl font-bold text-orange-900">{results.suspicious_characters.len()}</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <div class="text-yellow-600 text-sm font-medium">"Confidence"</div>
                    <div class="text-2xl font-bold text-yellow-900">{format!("{:.1}%", results.confidence_score * 100.0)}</div>
                </div>
            </div>

            // Character Breakdown
            {if !results.character_breakdown.is_empty() {
                view! {
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-chart-pie mr-2 text-indigo-500"></i>
                            "Character Breakdown"
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {results.character_breakdown.iter().map(|(category, count)| {
                                view! {
                                    <div class="text-center">
                                        <div class="text-sm text-gray-600">{category}</div>
                                        <div class="text-lg font-bold text-gray-900">{count.to_string()}</div>
                                    </div>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Script Breakdown
            {if !results.script_breakdown.is_empty() && results.script_breakdown.len() > 1 {
                view! {
                    <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-globe mr-2 text-blue-500"></i>
                            "Script Analysis"
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            {results.script_breakdown.iter().map(|(script, count)| {
                                view! {
                                    <div class="bg-white p-3 rounded border">
                                        <div class="text-sm text-gray-600">{script}</div>
                                        <div class="text-lg font-bold text-blue-900">{count.to_string()}</div>
                                    </div>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Recommendations
            {if !results.recommendations.is_empty() {
                view! {
                    <div class="bg-green-50 p-6 rounded-lg border border-green-200">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-lightbulb mr-2 text-green-500"></i>
                            "Recommendations"
                        </h3>
                        <ul class="space-y-2">
                            {results.recommendations.iter().map(|rec| {
                                view! {
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1 flex-shrink-0"></i>
                                        <span class="text-green-800">{rec}</span>
                                    </li>
                                }
                            }).collect_view()}
                        </ul>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

fn render_characters_tab(results: AnalysisResults) -> impl IntoView {
    view! {
        <div class="space-y-6">
            {if results.suspicious_characters.is_empty() {
                view! {
                    <div class="text-center py-12">
                        <i class="fas fa-check-circle text-green-500 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">"No Suspicious Characters Found"</h3>
                        <p class="text-gray-500">"The text appears to contain only standard characters."</p>
                    </div>
                }.into_view()
            } else {
                view! {
                    <div>
                        <div class="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                            <h3 class="font-semibold text-orange-800 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {results.suspicious_characters.len()} " Suspicious Characters Detected"
                            </h3>
                            <p class="text-sm text-orange-700">
                                "Review these characters carefully as they may be invisible, misleading, or potentially malicious."
                            </p>
                        </div>

                        <div class="space-y-4">
                            {results.suspicious_characters.iter().enumerate().map(|(i, char_info)| {
                                let display_char = if char_info.character.is_control() || char_info.character as u32 <= 32 {
                                    format!("[U+{:04X}]", char_info.codepoint)
                                } else {
                                    char_info.character.to_string()
                                };

                                view! {
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex items-center">
                                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full mr-3">
                                                    {i + 1}
                                                </span>
                                                <div>
                                                    <span class="text-lg font-mono bg-white px-2 py-1 rounded border">
                                                        {display_char}
                                                    </span>
                                                    <span class="ml-2 text-sm text-gray-600">
                                                        "at position " {char_info.position}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="text-right text-sm text-gray-500">
                                                <div>"U+"{format!("{:04X}", char_info.codepoint)}</div>
                                                {if char_info.is_emoji {
                                                    view! { <div class="text-xs text-purple-600">"emoji"</div> }.into_view()
                                                } else if char_info.is_combining {
                                                    view! { <div class="text-xs text-blue-600">"combining"</div> }.into_view()
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </div>
                                        </div>

                                        <div class="grid md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <div class="font-medium text-gray-700 mb-2">"Character Details:"</div>
                                                <ul class="space-y-1 text-gray-600">
                                                    <li><strong>"Name:"</strong> {char_info.unicode_name.clone()}</li>
                                                    <li><strong>"Block:"</strong> {char_info.unicode_block.clone()}</li>
                                                    <li><strong>"Category:"</strong> {char_info.category.clone()}</li>
                                                    <li><strong>"Visual Width:"</strong> {char_info.visual_width}</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-700 mb-2">"Issues Found:"</div>
                                                <ul class="space-y-1">
                                                    {char_info.suspicion_reasons.iter().map(|reason| {
                                                        view! {
                                                            <li class="flex items-center text-red-700">
                                                                <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
                                                                {reason}
                                                            </li>
                                                        }
                                                    }).collect_view()}
                                                </ul>
                                                {if !char_info.recommendations.is_empty() {
                                                    view! {
                                                        <div class="mt-3">
                                                            <div class="font-medium text-gray-700 mb-1">"Recommendations:"</div>
                                                            <ul class="space-y-1">
                                                                {char_info.recommendations.iter().map(|rec| {
                                                                    view! {
                                                                        <li class="flex items-center text-blue-700 text-sm">
                                                                            <i class="fas fa-lightbulb mr-2 text-blue-500"></i>
                                                                            {rec}
                                                                        </li>
                                                                    }
                                                                }).collect_view()}
                                                            </ul>
                                                        </div>
                                                    }.into_view()
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </div>
                                        </div>
                                    </div>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                }.into_view()
            }}
        </div>
    }
}

fn render_security_tab(results: AnalysisResults) -> impl IntoView {
    let security = &results.security_analysis;
    let risk_color = match security.risk_level.as_str() {
        "Critical" => ("text-red-700", "bg-red-50", "border-red-200", "fas fa-skull"),
        "High" => ("text-orange-700", "bg-orange-50", "border-orange-200", "fas fa-exclamation-triangle"),
        "Medium" => ("text-yellow-700", "bg-yellow-50", "border-yellow-200", "fas fa-exclamation"),
        _ => ("text-green-700", "bg-green-50", "border-green-200", "fas fa-shield-alt")
    };

    view! {
        <div class="space-y-6">
            // Risk Level Header
            <div class={format!("p-6 rounded-lg border {}", format!("{} {}", risk_color.1, risk_color.2))}>
                <div class="flex items-center mb-4">
                    <i class={format!("{} mr-3 text-2xl {}", risk_color.3, risk_color.0)}></i>
                    <h3 class={format!("text-xl font-bold {}", risk_color.0)}>
                        "Security Risk Level: " {security.risk_level.clone()}
                    </h3>
                </div>
                <p class={format!("text-sm {}", risk_color.0)}>
                    {match security.risk_level.as_str() {
                        "Critical" => "Immediate attention required. Text contains dangerous patterns.",
                        "High" => "High risk detected. Review and remediate before use.",
                        "Medium" => "Moderate risk. Consider review and cleanup.",
                        _ => "Low risk. Text appears safe for normal use."
                    }}
                </p>
            </div>

            // Homograph Attacks
            {if !security.homograph_attacks.is_empty() {
                view! {
                    <div class="bg-red-50 p-6 rounded-lg border border-red-200">
                        <h4 class="text-lg font-semibold text-red-800 mb-4">
                            <i class="fas fa-eye mr-2"></i>
                            "Homograph Attack Detected"
                        </h4>
                        <p class="text-sm text-red-700 mb-4">
                            "Characters that look similar to common Latin letters but are from different Unicode blocks:"
                        </p>
                        <ul class="space-y-2">
                            {security.homograph_attacks.iter().map(|attack| {
                                view! {
                                    <li class="flex items-center bg-red-100 p-2 rounded">
                                        <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                                        <code class="text-sm font-mono">{attack}</code>
                                    </li>
                                }
                            }).collect_view()}
                        </ul>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Phishing Indicators
            {if !security.phishing_indicators.is_empty() {
                view! {
                    <div class="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <h4 class="text-lg font-semibold text-orange-800 mb-4">
                            <i class="fas fa-fishing mr-2"></i>
                            "Phishing Indicators"
                        </h4>
                        <ul class="space-y-2">
                            {security.phishing_indicators.iter().map(|indicator| {
                                view! {
                                    <li class="flex items-center">
                                        <i class="fas fa-warning text-orange-500 mr-2"></i>
                                        <span class="text-orange-800">{indicator}</span>
                                    </li>
                                }
                            }).collect_view()}
                        </ul>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Script Mixing
            {if !security.script_mixing.is_empty() && security.script_mixing.len() > 1 {
                view! {
                    <div class="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
                        <h4 class="text-lg font-semibold mb-4">
                            <i class="fas fa-globe mr-2"></i>
                            "Mixed Script Usage"
                        </h4>
                        <p class="text-sm text-yellow-700 mb-4">
                            "Text contains characters from multiple writing systems:"
                        </p>
                        <div class="flex flex-wrap gap-2">
                            {security.script_mixing.iter().map(|script| {
                                view! {
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-medium">
                                        {script}
                                    </span>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Steganography Potential
            {if security.steganography_potential {
                view! {
                    <div class="bg-purple-50 p-6 rounded-lg border border-purple-200">
                        <h4 class="text-lg font-semibold text-purple-800 mb-4">
                            <i class="fas fa-mask mr-2"></i>
                            "Steganography Potential"
                        </h4>
                        <p class="text-purple-700">
                            "High concentration of zero-width characters detected. This could indicate hidden content or data steganography."
                        </p>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // All Clear
            {if security.risk_level == "Low" && security.homograph_attacks.is_empty() && 
               security.phishing_indicators.is_empty() && !security.steganography_potential {
                view! {
                    <div class="bg-green-50 p-6 rounded-lg border border-green-200 text-center">
                        <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
                        <h4 class="text-lg font-semibold text-green-800 mb-2">"Security Check Passed"</h4>
                        <p class="text-green-700">
                            "No security risks detected. The text appears safe for normal use."
                        </p>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

fn render_patterns_tab(results: AnalysisResults) -> impl IntoView {
    view! {
        <div class="space-y-6">
            {if results.patterns_found.is_empty() {
                view! {
                    <div class="text-center py-12">
                        <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">"No Suspicious Patterns Found"</h3>
                        <p class="text-gray-500">"No predefined suspicious patterns were detected in the text."</p>
                    </div>
                }.into_view()
            } else {
                view! {
                    <div>
                        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h3 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-search mr-2"></i>
                                {results.patterns_found.len()} " Patterns Detected"
                            </h3>
                            <p class="text-sm text-blue-700">
                                "The following suspicious patterns were found using regex analysis."
                            </p>
                        </div>

                        <div class="space-y-4">
                            {results.patterns_found.iter().enumerate().map(|(i, pattern)| {
                                let severity_color = match pattern.severity.as_str() {
                                    "Critical" => ("bg-red-50", "border-red-200", "text-red-800", "fas fa-skull"),
                                    "High" => ("bg-orange-50", "border-orange-200", "text-orange-800", "fas fa-exclamation-triangle"),
                                    "Medium" => ("bg-yellow-50", "border-yellow-200", "text-yellow-800", "fas fa-exclamation"),
                                    _ => ("bg-blue-50", "border-blue-200", "text-blue-800", "fas fa-info-circle")
                                };

                                view! {
                                    <div class={format!("p-4 rounded-lg border {} {}", severity_color.0, severity_color.1)}>
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex items-center">
                                                <span class="bg-white px-2 py-1 rounded text-xs font-medium mr-3">
                                                    {i + 1}
                                                </span>
                                                <div>
                                                    <h4 class={format!("font-semibold {}", severity_color.2)}>
                                                        <i class={format!("{} mr-2", severity_color.3)}></i>
                                                        {pattern.pattern_name.clone()}
                                                    </h4>
                                                    <p class={format!("text-sm {}", severity_color.2)}>
                                                        {pattern.description.clone()}
                                                    </p>
                                                </div>
                                            </div>
                                            <span class={format!("px-2 py-1 rounded text-xs font-medium {} border", severity_color.2)}>
                                                {pattern.severity.clone()}
                                            </span>
                                        </div>

                                        <div class="grid md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <div class="font-medium text-gray-700 mb-1">"Location:"</div>
                                                <div class="text-gray-600">
                                                    "Position " {pattern.start_position} " to " {pattern.end_position}
                                                </div>
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-700 mb-1">"Matched Text:"</div>
                                                <code class="bg-white px-2 py-1 rounded border text-xs break-all">
                                                    {if pattern.matched_text.chars().any(|c| c.is_control()) {
                                                        pattern.matched_text.chars().map(|c| {
                                                            if c.is_control() {
                                                                format!("[U+{:04X}]", c as u32)
                                                            } else {
                                                                c.to_string()
                                                            }
                                                        }).collect::<String>()
                                                    } else {
                                                        pattern.matched_text.clone()
                                                    }}
                                                </code>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                }.into_view()
            }}
        </div>
    }
}

fn render_encoding_tab(results: AnalysisResults) -> impl IntoView {
    let encoding = &results.encoding_info;
    
    view! {
        <div class="space-y-6">
            <div class="bg-gray-50 p-6 rounded-lg border">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-code mr-2 text-blue-500"></i>
                    "Encoding Analysis"
                </h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-600 mb-1">"Detected Encoding:"</div>
                            <div class="text-lg font-semibold text-gray-900">{encoding.detected_encoding.clone()}</div>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-600 mb-1">"Confidence:"</div>
                            <div class="flex items-center">
                                <div class="text-lg font-semibold text-gray-900 mr-2">
                                    {format!("{:.1}%", encoding.confidence * 100.0)}
                                </div>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div 
                                        class="bg-blue-500 h-2 rounded-full"
                                        style:width=format!("{}%", encoding.confidence * 100.0)
                                    ></div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-600 mb-1">"UTF-8 Valid:"</div>
                            <div class="flex items-center">
                                {if encoding.is_valid_utf8 {
                                    view! {
                                        <>
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-green-700 font-medium">"Yes"</span>
                                        </>
                                    }
                                } else {
                                    view! {
                                        <>
                                            <i class="fas fa-times-circle text-red-500 mr-2"></i>
                                            <span class="text-red-700 font-medium">"No"</span>
                                        </>
                                    }
                                }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-600 mb-1">"Byte Order Mark (BOM):"</div>
                            <div>
                                {if let Some(bom) = &encoding.bom_detected {
                                    view! {
                                        <span class="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm">
                                            <i class="fas fa-tag mr-1"></i>
                                            {bom}
                                        </span>
                                    }.into_view()
                                } else {
                                    view! {
                                        <span class="text-gray-500">"None detected"</span>
                                    }.into_view()
                                }}
                            </div>
                        </div>
                        
                        <div>
                            <div class="text-sm font-medium text-gray-600 mb-1">"Line Endings:"</div>
                            <div class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                                <i class="fas fa-level-down-alt mr-1"></i>
                                {encoding.line_endings.clone()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            // Analysis Metadata
            <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <h3 class="text-lg font-semibold mb-4">
                    <i class="fas fa-info-circle mr-2"></i>
                    "Analysis Metadata"
                </h3>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                        <div class="text-blue-600 font-medium">"Analysis ID:"</div>
                        <div class="text-blue-900 font-mono text-xs">{results.id.clone()}</div>
                    </div>
                    <div>
                        <div class="text-blue-600 font-medium">"Duration:"</div>
                        <div class="text-blue-900">{results.analysis_duration_ms}" ms"</div>
                    </div>
                    <div>
                        <div class="text-blue-600 font-medium">"Text Hash:"</div>
                        <div class="text-blue-900 font-mono text-xs">{results.text_hash[..8].to_string()}"..."</div>
                    </div>
                    <div>
                        <div class="text-blue-600 font-medium">"Timestamp:"</div>
                        <div class="text-blue-900 text-xs">{results.timestamp.clone()}</div>
                    </div>
                </div>
            </div>
        </div>
    }
}

fn render_export_tab(
    results: AnalysisResults,
    export_format: ReadSignal<String>,
    set_export_format: WriteSignal<String>,
    export_analysis: impl Fn(leptos::ev::MouseEvent) + 'static + Clone
) -> impl IntoView {
    view! {
        <div class="space-y-6">
            <div class="bg-gray-50 p-6 rounded-lg border">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-download mr-2 text-green-500"></i>
                    "Export Analysis Results"
                </h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            "Export Format:"
                        </label>
                        <div class="flex gap-4">
                            {["json", "csv", "txt", "html"].iter().map(|format| {
                                let format = *format;
                                let format_name = match format {
                                    "json" => "JSON (Structured data)",
                                    "csv" => "CSV (Spreadsheet)",
                                    "txt" => "Text Report",
                                    "html" => "HTML Report",
                                    _ => format
                                };
                                
                                view! {
                                    <label class="flex items-center">
                                        <input
                                            type="radio"
                                            name="export_format"
                                            value=format
                                            checked=move || export_format.get() == format
                                            on:change=move |_| set_export_format.set(format.to_string())
                                            class="mr-2"
                                        />
                                        <span class="text-sm text-gray-700">{format_name}</span>
                                    </label>
                                }
                            }).collect_view()}
                        </div>
                    </div>
                    
                    <button
                        on:click=export_analysis
                        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                    >
                        <i class="fas fa-download mr-2"></i>
                        "Download Report"
                    </button>
                </div>
            </div>

            // Preview
            <div class="bg-white border rounded-lg p-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">
                    <i class="fas fa-eye mr-2 text-blue-500"></i>
                    "Export Preview"
                </h4>
                
                <div class="bg-gray-50 p-4 rounded border max-h-96 overflow-auto">
                    <pre class="text-xs text-gray-700 whitespace-pre-wrap font-mono">                        {move || match export_format.get().as_str() {
                            "json" => serde_json::to_string_pretty(&results).unwrap_or_default(),
                            "csv" => format!("Position,Character,Unicode,Category,Suspicious,Reasons\n{}", 
                                results.suspicious_characters.iter().map(|c| 
                                    format!("{},{},U+{:04X},{},{},{}", 
                                        c.position, c.character, c.codepoint, c.category, 
                                        c.is_suspicious, c.suspicion_reasons.join("; ")
                                    )
                                ).collect::<Vec<_>>().join("\n")
                            ),
                            "txt" => format!("Character Analysis Report\nGenerated: {}\nID: {}\n\nSummary:\n- Total Characters: {}\n- Suspicious Characters: {}\n- Risk Level: {}\n\n{}", 
                                results.timestamp, results.id, results.total_characters, 
                                results.suspicious_characters.len(), results.security_analysis.risk_level,
                                if results.suspicious_characters.is_empty() { 
                                    "No suspicious characters found.".to_string() 
                                } else {
                                    format!("Suspicious Characters:\n{}", 
                                        results.suspicious_characters.iter().map(|c| 
                                            format!("- Position {}: '{}' (U+{:04X}) - {}", 
                                                c.position, c.character, c.codepoint, 
                                                c.suspicion_reasons.join(", ")
                                            )
                                        ).collect::<Vec<_>>().join("\n")
                                    )
                                }
                            ),
                            "html" => format!("<!DOCTYPE html>\n<html>\n<head>\n<title>Character Analysis Report</title>\n</head>\n<body>\n<h1>Analysis Report</h1>\n<p>Generated: {}</p>\n<p>Risk Level: {}</p>\n<p>Suspicious Characters: {}</p>\n</body>\n</html>", 
                                results.timestamp, results.security_analysis.risk_level, results.suspicious_characters.len()
                            ),
                            _ => "Unknown format".to_string()
                        }}
                    </pre>
                </div>
            </div>
        </div>
    }
}

#[component]
fn CleanTextPage() -> impl IntoView {
    let (original_text, set_original_text) = create_signal(String::new());
    let (cleaning_result, set_cleaning_result) = create_signal(None::<CleaningResult>);
    let (loading, set_loading) = create_signal(false);
    let (error_message, set_error_message) = create_signal(None::<String>);    let (cleaning_options, set_cleaning_options) = create_signal(CleaningOptions::default());// Load text from localStorage on mount and automatically start cleaning
    create_effect(move |_| {
        let js_code = r#"localStorage.getItem('textToClean') || ''"#;
        if let Some(text_value) = js_eval(js_code).as_string() {
            if !text_value.is_empty() {
                // Parse the JSON string
                let text = if let Ok(parsed_text) = serde_json::from_str::<String>(&text_value) {
                    parsed_text
                } else {
                    text_value
                };
                
                set_original_text.set(text.clone());
                
                // Automatically start cleaning process
                set_loading.set(true);
                set_error_message.set(None);

                let options = cleaning_options.get();
                spawn_local(async move {
                    let options_json = format!(
                        r#"{{"remove_zero_width":{},"remove_control":{},"remove_bidi":{},"normalize_whitespace":{}}}"#,
                        options.remove_zero_width,
                        options.remove_control, 
                        options.remove_bidi,
                        options.normalize_whitespace
                    );
                    let args = format!(r#"{{"text":"{}","options":{}}}"#, 
                        serde_json::to_string(&text).unwrap_or_default().trim_matches('"'), options_json);
                    
                    match call_tauri_command("clean_text_detailed", &args).await {
                        Ok(result_str) => {
                            match serde_json::from_str::<CleaningResult>(&result_str) {
                                Ok(result) => {
                                    set_cleaning_result.set(Some(result));
                                    set_loading.set(false);
                                },
                                Err(e) => {
                                    set_error_message.set(Some(format!("Failed to parse cleaning result: {}", e)));
                                    set_loading.set(false);
                                }
                            }
                        },
                        Err(e) => {
                            set_error_message.set(Some(format!("Cleaning failed: {}", e)));
                            set_loading.set(false);
                        }
                    }
                });
            }
        }
    });

    let clean_text = move |_| {
        let text = original_text.get();
        if text.is_empty() {
            set_error_message.set(Some("No text to clean.".to_string()));
            return;
        }

        set_loading.set(true);
        set_error_message.set(None);

        let options = cleaning_options.get();
        spawn_local(async move {
            let options_json = format!(
                r#"{{"remove_zero_width":{},"remove_control":{},"remove_bidi":{},"normalize_whitespace":{}}}"#,
                options.remove_zero_width,
                options.remove_control, 
                options.remove_bidi,
                options.normalize_whitespace
            );
            let args = format!(r#"{{"text":"{}","options":{}}}"#, 
                serde_json::to_string(&text).unwrap_or_default().trim_matches('"'), options_json);
            
            match call_tauri_command("clean_text_detailed", &args).await {
                Ok(result_str) => {
                    match serde_json::from_str::<CleaningResult>(&result_str) {
                        Ok(result) => {
                            set_cleaning_result.set(Some(result));
                            set_loading.set(false);
                        },
                        Err(e) => {
                            set_error_message.set(Some(format!("Failed to parse cleaning result: {}", e)));
                            set_loading.set(false);
                        }
                    }
                },
                Err(e) => {
                    set_error_message.set(Some(format!("Cleaning failed: {}", e)));
                    set_loading.set(false);
                }
            }
        });
    };

    let copy_cleaned_text = move |_| {
        if let Some(result) = cleaning_result.get() {
            let js_code = format!(
                r#"navigator.clipboard.writeText({}).then(() => {{
                    console.log('Cleaned text copied to clipboard');
                }}).catch(err => {{
                    console.error('Failed to copy text:', err);
                }});"#,
                serde_json::to_string(&result.cleaned).unwrap_or_default()
            );
            js_eval(&js_code);
        }
    };    let go_back = move |_| {
        if let Some(window) = window() {
            let _ = window.location().set_href("/");
        }
    };

    view! {
        <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
            <div class="max-w-7xl mx-auto">
                // Header
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <button
                                on:click=go_back
                                class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                            >
                                <i class="fas fa-arrow-left mr-2"></i>
                                "Back to Analysis"
                            </button>
                            <h1 class="text-3xl font-bold text-gray-800">
                                <i class="fas fa-broom mr-3 text-orange-500"></i>
                                "Text Cleaning Interface"
                            </h1>
                        </div>
                    </div>
                    <p class="text-gray-600 mt-2">
                        "Clean suspicious characters from your text with side-by-side comparison"
                    </p>
                </div>                // Error Message
                {move || error_message.get().map(|msg| view! {
                    <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {msg}
                    </div>
                })}

                // Loading indicator for initial processing
                {move || if loading.get() && cleaning_result.get().is_none() {
                    view! {
                        <div class="mb-6 p-6 bg-blue-50 border border-blue-200 rounded-xl">
                            <div class="flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
                                    <h3 class="text-lg font-semibold text-blue-800 mb-2">"Processing Your Text..."</h3>
                                    <p class="text-blue-600">"Analyzing and cleaning suspicious characters"</p>
                                </div>
                            </div>
                        </div>
                    }
                } else {
                    view! { <div></div> }
                }}

                // Cleaning Options
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-cog mr-2 text-blue-500"></i>
                        "Cleaning Options"
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox"
                                checked=move || cleaning_options.get().remove_zero_width
                                on:change=move |ev| {
                                    let checked = event_target_checked(&ev);
                                    set_cleaning_options.update(|opts| opts.remove_zero_width = checked);
                                }
                                class="form-checkbox text-blue-600"
                            />
                            <span class="text-sm text-gray-700">"Remove Zero-Width Characters"</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox"
                                checked=move || cleaning_options.get().remove_control
                                on:change=move |ev| {
                                    let checked = event_target_checked(&ev);
                                    set_cleaning_options.update(|opts| opts.remove_control = checked);
                                }
                                class="form-checkbox text-blue-600"
                            />
                            <span class="text-sm text-gray-700">"Remove Control Characters"</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox"
                                checked=move || cleaning_options.get().remove_bidi
                                on:change=move |ev| {
                                    let checked = event_target_checked(&ev);
                                    set_cleaning_options.update(|opts| opts.remove_bidi = checked);
                                }
                                class="form-checkbox text-blue-600"
                            />
                            <span class="text-sm text-gray-700">"Remove BiDi Overrides"</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input 
                                type="checkbox"
                                checked=move || cleaning_options.get().normalize_whitespace
                                on:change=move |ev| {
                                    let checked = event_target_checked(&ev);
                                    set_cleaning_options.update(|opts| opts.normalize_whitespace = checked);
                                }
                                class="form-checkbox text-blue-600"
                            />
                            <span class="text-sm text-gray-700">"Normalize Whitespace"</span>
                        </label>
                    </div>
                    <div class="mt-4">                        <button
                            on:click=clean_text
                            class="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold"
                            disabled=move || loading.get() || original_text.get().is_empty()
                        >
                            {move || if loading.get() {
                                view! { <><i class="fas fa-spinner fa-spin mr-2"></i>"Processing..."</> }
                            } else if cleaning_result.get().is_some() {
                                view! { <><i class="fas fa-redo mr-2"></i>"Re-clean with Current Options"</> }
                            } else {
                                view! { <><i class="fas fa-broom mr-2"></i>"Clean Text"</> }
                            }}
                        </button>
                    </div>
                </div>

                // Side-by-side comparison
                {move || cleaning_result.get().map(|result| view! {
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        // Original Text (with highlights and copy protection)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold mb-4 text-gray-800">
                                <i class="fas fa-file-alt mr-2 text-red-500"></i>
                                "Original Text (Read-Only)"
                            </h3>
                            <div 
                                class="border rounded-lg p-4 bg-gray-50 h-96 overflow-auto font-mono text-sm select-none"
                                style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"
                            >
                                {render_highlighted_text(&result.original, &result.changes)}
                            </div>
                            <p class="text-xs text-gray-500 mt-2">
                                <i class="fas fa-lock mr-1"></i>
                                "Original text cannot be copied for safety"
                            </p>
                        </div>

                        // Cleaned Text (copyable)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">
                                    <i class="fas fa-check-circle mr-2 text-green-500"></i>
                                    "Cleaned Text"
                                </h3>
                                <button
                                    on:click=copy_cleaned_text
                                    class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-sm"
                                >
                                    <i class="fas fa-copy mr-1"></i>
                                    "Copy"
                                </button>
                            </div>                            <textarea 
                                class="w-full h-96 border rounded-lg p-4 font-mono text-sm resize-none"
                                readonly
                            >{result.cleaned.clone()}</textarea>
                            <p class="text-xs text-green-600 mt-2">
                                <i class="fas fa-shield-alt mr-1"></i>
                                "Safe to copy and use"
                            </p>
                        </div>
                    </div>

                    // Cleaning Statistics
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4 text-gray-800">
                            <i class="fas fa-chart-bar mr-2 text-blue-500"></i>
                            "Cleaning Statistics"
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {result.stats.iter().map(|(key, value)| view! {
                                <div class="text-center p-3 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{*value}</div>
                                    <div class="text-xs text-gray-600 capitalize">{key.replace('_', " ")}</div>
                                </div>
                            }).collect::<Vec<_>>()}
                        </div>
                        
                        {if !result.changes.is_empty() {
                            view! {
                                <div class="mt-6">
                                    <h4 class="font-semibold mb-2 text-gray-700">"Detailed Changes:"</h4>
                                    <div class="space-y-2 max-h-40 overflow-auto">
                                        {result.changes.iter().map(|change| view! {
                                            <div class="text-xs p-2 bg-yellow-50 border-l-4 border-yellow-400">
                                                <span class="font-medium capitalize">{change.change_type.replace('_', " ")}</span>
                                                " at position " {change.start}
                                                {if !change.original.is_empty() {
                                                    format!(": '{}'", change.original)
                                                } else {
                                                    String::new()
                                                }}
                                            </div>
                                        }).collect::<Vec<_>>()}
                                    </div>
                                </div>
                            }
                        } else {
                            view! {
                                <div class="mt-6 text-center text-gray-500">
                                    <i class="fas fa-check-circle mr-2 text-green-500"></i>
                                    "No changes needed - text is already clean!"
                                </div>
                            }
                        }}
                    </div>
                })}
            </div>
        </div>
    }
}

#[derive(Debug, Clone)]
struct CleaningOptions {
    remove_zero_width: bool,
    remove_control: bool,
    remove_bidi: bool,
    normalize_whitespace: bool,
}

impl Default for CleaningOptions {
    fn default() -> Self {
        Self {
            remove_zero_width: true,
            remove_control: true,
            remove_bidi: true,
            normalize_whitespace: true,
        }
    }
}

fn render_highlighted_text(original: &str, changes: &[CleaningChange]) -> impl IntoView {
    let mut result = Vec::new();
    let mut last_pos = 0;
    
    // Sort changes by start position
    let mut sorted_changes = changes.to_vec();
    sorted_changes.sort_by_key(|c| c.start);
    
    for change in sorted_changes {
        // Add text before the change
        if change.start > last_pos {
            let before_text = original[last_pos..change.start].to_string();
            if !before_text.is_empty() {
                result.push(view! { <span>{before_text}</span> });
            }
        }
        
        // Add highlighted change
        if !change.original.is_empty() {
            let original_text = change.original.clone();
            let change_type = change.change_type.clone();
            result.push(view! {
                <span class="bg-red-200 text-red-800 px-1 rounded" title={change_type}>
                    {original_text}
                </span>
            });
        }
        
        last_pos = change.end;
    }
    
    // Add remaining text
    if last_pos < original.len() {
        let remaining_text = original[last_pos..].to_string();
        result.push(view! { <span>{remaining_text}</span> });
    }
    
    result
}

// Interface state machine for dynamic UX
#[derive(Clone, Debug, PartialEq)]
enum InterfaceState {
    SelectionMode,  // Default state - all selection methods visible
    ActionsMode,    // Folder selected - show actions, collapse selection methods
    ProcessingMode, // Operation in progress - show progress, disable actions
}

#[component]
fn CodeBaseAnalysisPage() -> impl IntoView {
    let (selected_folder, set_selected_folder) = create_signal(None::<String>);
    let (analysis_results, set_analysis_results) = create_signal(None::<CodeBaseAnalysisResult>);
    let (loading, set_loading) = create_signal(false);
    let (error_message, set_error_message) = create_signal(None::<String>);
    let (progress, set_progress) = create_signal(0);
    let (current_file, set_current_file) = create_signal(String::new());
    let (_analysis_stats, _set_analysis_stats) = create_signal(None::<CodeBaseStats>);
    
    // Interface state management
    let (interface_state, set_interface_state) = create_signal(InterfaceState::SelectionMode);
      // Enhanced folder selection state
    let (path_input, set_path_input) = create_signal(String::new());
    let (path_validation, set_path_validation) = create_signal(None::<FolderInfo>);
    let (recent_folders, set_recent_folders) = create_signal(Vec::<RecentFolder>::new());
    let (quick_access_folders, set_quick_access_folders) = create_signal(Vec::<FolderInfo>::new());
    let (show_recent_dropdown, set_show_recent_dropdown) = create_signal(false);
    let (validating_path, set_validating_path) = create_signal(false);
      // Drag & Drop state
    let (is_drag_over, set_is_drag_over) = create_signal(false);
    let (drag_counter, set_drag_counter) = create_signal(0);
    
    // Export functionality state
    let (is_exporting, set_is_exporting) = create_signal(false);
    let (export_status, set_export_status) = create_signal(None::<String>);
    let (export_message, set_export_message) = create_signal(String::new());

    // Load recent folders and quick access on component mount
    create_effect(move |_| {
        spawn_local(async move {
            // Load recent folders
            if let Ok(result) = invoke("get_recent_folders", &serde_json::json!({})).await {
                if let Ok(folders) = serde_json::from_value::<Vec<RecentFolder>>(result) {
                    set_recent_folders.set(folders);
                }
            }
            
            // Load quick access folders
            if let Ok(result) = invoke("get_quick_access_folders", &serde_json::json!({})).await {
                if let Ok(folders) = serde_json::from_value::<Vec<FolderInfo>>(result) {
                    set_quick_access_folders.set(folders);
                }
            }
        });
    });    // Path validation function with state transition
    let validate_path = move |path: String| {
        if !path.trim().is_empty() {
            set_validating_path.set(true);
            let path_clone = path.clone();
            spawn_local(async move {
                match invoke("validate_folder_path", &serde_json::json!({
                    "path": path_clone
                })).await {
                    Ok(result) => {
                        if let Ok(folder_info) = serde_json::from_value::<FolderInfo>(result) {
                            set_path_validation.set(Some(folder_info.clone()));
                            
                            // Auto-transition to ActionsMode if folder is valid
                            if folder_info.exists && folder_info.readable {
                                set_selected_folder.set(Some(folder_info.path.clone()));
                                set_interface_state.set(InterfaceState::ActionsMode);
                                set_error_message.set(None);
                                
                                // Save to recent folders
                                let path_for_recent = folder_info.path.clone();
                                spawn_local(async move {
                                    let _ = invoke("save_recent_folder", &serde_json::json!({
                                        "path": path_for_recent
                                    })).await;
                                });
                            }
                        } else {
                            set_path_validation.set(None);
                        }
                    },
                    Err(_) => {
                        set_path_validation.set(None);
                    }
                }
                set_validating_path.set(false);
            });
        } else {
            set_path_validation.set(None);
            set_validating_path.set(false);
        }
    };

    // Handle path input change
    let on_path_input = move |ev| {
        let value = event_target_value(&ev);
        set_path_input.set(value.clone());
        validate_path(value);
    };    // Handle folder selection from various sources with state transition
    let select_folder_from_path = move |path: String| {
        set_selected_folder.set(Some(path.clone()));
        set_path_input.set(path.clone());
        set_show_recent_dropdown.set(false);
        set_error_message.set(None);
        set_interface_state.set(InterfaceState::ActionsMode);
        
        // Save to recent folders
        let path_clone = path.clone();
        spawn_local(async move {
            let _ = invoke("save_recent_folder", &serde_json::json!({
                "path": path_clone
            })).await;
        });
    };

    // Handle recent folder selection
    let select_recent_folder = move |folder: RecentFolder| {
        let path = folder.path.clone();
        select_folder_from_path(path);
    };

    // Handle quick access folder selection
    let select_quick_access_folder = move |folder: FolderInfo| {
        let path = folder.path.clone();
        select_folder_from_path(path);
    };
    
    // Handle folder selection from validation result
    let select_validated_folder = move |_| {
        if let Some(folder_info) = path_validation.get() {
            if folder_info.exists && folder_info.readable {
                let path = folder_info.path.clone();
                select_folder_from_path(path);
            }
        }
    };
      // Return to selection mode function
    let return_to_selection = move |_: leptos::ev::MouseEvent| {
        set_interface_state.set(InterfaceState::SelectionMode);
        set_selected_folder.set(None);
        set_path_input.set(String::new());
        set_path_validation.set(None);
        set_error_message.set(None);
    };    // Setup Tauri v2 native file drop event listener using correct API
    // This replaces HTML5 drag & drop with Tauri's native implementation
    create_effect(move |_| {
        spawn_local(async move {            // Use the global Tauri API for webview drag & drop (Tauri v2 compatible)
            let _eval_result = js_eval(r#"
                (async () => {
                    try {
                        // Check if Tauri API is available globally
                        if (typeof window === 'undefined' || !window.__TAURI__) {
                            throw new Error('Tauri API not available');
                        }
                        
                        // Get the current webview using the global Tauri API
                        const webview = window.__TAURI__.webview.getCurrentWebview();
                        
                        // Listen for native file drop events using Tauri v2 API
                        const unlisten = await webview.onDragDropEvent((event) => {
                            const payload = event.payload;
                            console.log('📁 Drag event received:', payload.type, payload.paths);
                            
                            // Handle different drag event types
                            if (payload.type === 'enter') {
                                // File drag entered the drop zone
                                window.dispatchEvent(new CustomEvent('tauri-drag-enter', {
                                    detail: { paths: payload.paths, position: payload.position }
                                }));
                            } else if (payload.type === 'over') {
                                // File drag is hovering over the drop zone
                                window.dispatchEvent(new CustomEvent('tauri-drag-over', {
                                    detail: { position: payload.position }
                                }));
                            } else if (payload.type === 'drop') {
                                // Files were dropped
                                window.dispatchEvent(new CustomEvent('tauri-drag-drop', {
                                    detail: { paths: payload.paths, position: payload.position }
                                }));
                            } else if (payload.type === 'leave') {
                                // File drag left the drop zone
                                window.dispatchEvent(new CustomEvent('tauri-drag-leave'));
                            }
                        });
                        
                        
                        // Store unlisten function for cleanup
                        window.__tauriDragUnlisten = unlisten;
                        console.log('✅ Tauri v2 drag & drop setup successful using global API');
                        return true;
                    } catch (error) {
                        console.error('❌ Failed to setup Tauri v2 drag & drop:', error);
                        console.log('🔄 Falling back to HTML5 drag & drop...');
                        return false;
                    }
                })()
            "#);
            
            // Setup event listeners for the custom events
            let window = web_sys::window().unwrap();
              // Drag enter handler
            {
                let set_drag_counter = set_drag_counter.clone();
                let set_is_drag_over = set_is_drag_over.clone();
                let drag_counter = drag_counter.clone();                let closure = wasm_bindgen::closure::Closure::wrap(Box::new(move |_event: web_sys::Event| {
                    set_drag_counter.update(|c| *c += 1);
                    if drag_counter.get_untracked() == 1 {
                        set_is_drag_over.set(true);
                    }
                }) as Box<dyn FnMut(_)>);
                
                let _ = window.add_event_listener_with_callback(
                    "tauri-drag-enter",
                    closure.as_ref().unchecked_ref(),
                );
                closure.forget();
            }
            
            // Drag leave handler  
            {
                let set_drag_counter = set_drag_counter.clone();
                let set_is_drag_over = set_is_drag_over.clone();
                let drag_counter = drag_counter.clone();                let closure = wasm_bindgen::closure::Closure::wrap(Box::new(move |_event: web_sys::Event| {
                    set_drag_counter.update(|c| *c -= 1);
                    if drag_counter.get_untracked() == 0 {
                        set_is_drag_over.set(false);
                    }
                }) as Box<dyn FnMut(_)>);
                
                let _ = window.add_event_listener_with_callback(
                    "tauri-drag-leave",
                    closure.as_ref().unchecked_ref(),
                );
                closure.forget();
            }
            
            // Drag drop handler - the main event that processes actual file paths
            {
                let set_drag_counter = set_drag_counter.clone();
                let set_is_drag_over = set_is_drag_over.clone();
                let set_error_message = set_error_message.clone();
                let select_folder_from_path = select_folder_from_path.clone();
                
                let closure = wasm_bindgen::closure::Closure::wrap(Box::new(move |event: web_sys::Event| {
                    set_drag_counter.set(0);
                    set_is_drag_over.set(false);
                    
                    // Extract file paths from the event detail
                    if let Ok(custom_event) = event.dyn_into::<web_sys::CustomEvent>() {
                        if let Ok(detail) = custom_event.detail().dyn_into::<js_sys::Object>() {
                            if let Ok(paths_array) = js_sys::Reflect::get(&detail, &"paths".into()) {
                                if let Ok(paths_array) = paths_array.dyn_into::<js_sys::Array>() {
                                    if paths_array.length() > 0 {
                                        if let Some(first_path) = paths_array.get(0).as_string() {
                                            // Use the existing select_folder_from_path function to handle the dropped path
                                            select_folder_from_path(first_path);
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // Fallback error message if path extraction fails
                    set_error_message.set(Some(
                        "❌ Could not process dropped files. Please use the 'Browse' button to select your folder.".to_string()
                    ));
                }) as Box<dyn FnMut(_)>);
                
                let _ = window.add_event_listener_with_callback(
                    "tauri-drag-drop",
                    closure.as_ref().unchecked_ref(),
                );
                closure.forget();
            }
        });
    });
    
    // Placeholder event handlers for HTML elements (no longer used for actual logic)
    let on_drag_over = move |ev: leptos::ev::DragEvent| {
        ev.prevent_default();
    };

    let on_drag_enter = move |ev: leptos::ev::DragEvent| {
        ev.prevent_default();
    };

    let on_drag_leave = move |_ev: leptos::ev::DragEvent| {
        // Handled by Tauri native events
    };

    let on_drop = move |ev: leptos::ev::DragEvent| {
        ev.prevent_default();
        // Handled by Tauri native events
    };// Keyboard navigation support for enhanced accessibility
    let on_key_down = move |ev: leptos::ev::KeyboardEvent| {
        let key = ev.key();
        let ctrl_key = ev.ctrl_key();
        
        match key.as_str() {
            "Enter" => {
                // If path is valid, select it
                if let Some(validation) = path_validation.get() {
                    if validation.exists && validation.readable {
                        let path = validation.path.clone();
                        select_folder_from_path(path);
                    }
                }
            },
            "F3" | "F5" => {
                // F3 or F5 to open folder browser
                ev.prevent_default();
                spawn_local(async move {
                    let starting_path = path_input.get_untracked();
                    match invoke("select_folder", &serde_json::json!({
                        "default_path": starting_path
                    })).await {
                        Ok(result) => {
                            if let Ok(folder_path) = serde_json::from_value::<String>(result) {
                                if !folder_path.is_empty() && folder_path != "null" {
                                    set_selected_folder.set(Some(folder_path.clone()));
                                    set_path_input.set(folder_path);
                                    set_error_message.set(None);
                                }
                            }
                        },
                        Err(e) => {
                            set_error_message.set(Some(format!("Error opening folder dialog: {}", e)));
                        }
                    }
                });
            },
            "Escape" => {
                // Close recent dropdown
                set_show_recent_dropdown.set(false);
            },
            "r" if ctrl_key => {
                // Ctrl+R to toggle recent dropdown
                ev.prevent_default();
                if !recent_folders.get().is_empty() {
                    set_show_recent_dropdown.set(!show_recent_dropdown.get());
                }
            },
            _ => {}
        }
    };

    let go_back = move |_| {
        if let Some(window) = window() {
            let _ = window.location().set_href("/");
        }
    };    let select_folder = move |_| {
        spawn_local(async move {
            // Get the current path input to use as starting directory
            let starting_path = path_input.get_untracked();
            
            // Call Tauri command to open folder dialog
            match invoke("select_folder", &serde_json::json!({
                "default_path": starting_path
            })).await {
                Ok(result) => {
                    if let Ok(folder_path) = serde_json::from_value::<String>(result) {
                        if !folder_path.is_empty() && folder_path != "null" {
                            // Use existing selection handler to ensure state transition
                            select_folder_from_path(folder_path);
                        }
                    }
                },                Err(e) => {
                    set_error_message.set(Some(format!("❌ Failed to select folder: {}", e)));
                }
            }
        });
    };
    
    let analyze_codebase = move |_| {
        if let Some(folder_path) = selected_folder.get() {
            set_loading.set(true);
            set_error_message.set(None);
            set_progress.set(0);
            set_current_file.set("Starting analysis...".to_string());
            set_analysis_results.set(None);
            
            // Transition to ProcessingMode
            set_interface_state.set(InterfaceState::ProcessingMode);
            
            spawn_local(async move {
                // Set up progress listener using JavaScript evaluation
                let set_progress_clone = set_progress.clone();
                let set_current_file_clone = set_current_file.clone();
                
                // Create a JavaScript function to handle progress events
                let js_listener_code = r#"
                (async () => {
                    if (typeof window !== 'undefined' && window.__TAURI__) {
                        const unlisten = await window.__TAURI__.event.listen('analysis-progress', (event) => {
                            window.updateAnalysisProgress = window.updateAnalysisProgress || function() {};
                            window.updateAnalysisProgress(event.payload);
                        });
                        window.analysisUnlisten = unlisten;
                        return 'listener_set';
                    }
                    return 'tauri_not_available';
                })()
                "#;
                
                // Set up the listener
                let _result = js_eval(js_listener_code);
                {                    // Set up a global JavaScript function to handle updates
                    let update_fn = wasm_bindgen::closure::Closure::wrap(Box::new(move |payload: JsValue| {
                        if let Ok(progress_data) = serde_wasm_bindgen::from_value::<serde_json::Value>(payload) {
                            if let (Some(message), Some(progress_percent)) = (
                                progress_data.get("message").and_then(|v| v.as_str()),
                                progress_data.get("percentage").and_then(|v| v.as_f64())
                            ) {
                                set_current_file_clone.set(message.to_string());
                                set_progress_clone.set(progress_percent as u8);
                            }
                        }
                    }) as Box<dyn Fn(JsValue)>);
                    
                    // Expose the function to JavaScript
                    let window = web_sys::window().unwrap();
                    let _ = js_sys::Reflect::set(
                        &window,
                        &JsValue::from_str("updateAnalysisProgress"),
                        update_fn.as_ref().unchecked_ref(),
                    );
                    
                    // Keep the closure alive
                    update_fn.forget();
                }

                match invoke("analyze_codebase", &serde_json::json!({
                    "folder_path": folder_path
                })).await {
                    Ok(result) => {
                        // Clean up progress listener
                        let _ = js_eval("if (window.analysisUnlisten) { window.analysisUnlisten(); }");
                        
                        match serde_json::from_value::<CodeBaseAnalysisResult>(result) {
                            Ok(analysis_result) => {
                                set_analysis_results.set(Some(analysis_result));
                                set_loading.set(false);
                                set_progress.set(100);
                                set_current_file.set("✅ Analysis complete!".to_string());
                                
                                // Transition back to ActionsMode to show results and allow actions
                                set_interface_state.set(InterfaceState::ActionsMode);
                            },
                            Err(e) => {
                                set_error_message.set(Some(format!("❌ Failed to parse analysis results: {}", e)));
                                set_loading.set(false);
                                
                                // Return to ActionsMode on error
                                set_interface_state.set(InterfaceState::ActionsMode);
                            }
                        }
                    },
                    Err(e) => {
                        // Clean up progress listener
                        let _ = js_eval("if (window.analysisUnlisten) { window.analysisUnlisten(); }");
                        
                        set_error_message.set(Some(format!("❌ Codebase analysis failed: {}", e)));
                        set_loading.set(false);
                        
                        // Return to ActionsMode on error
                        set_interface_state.set(InterfaceState::ActionsMode);
                    }                }
            });
        } else {
            set_error_message.set(Some("⚠️ Please select a folder first".to_string()));
        }
    };
    
    let clean_codebase = move |_| {
        // Navigate to codebase analysis page
        if let Some(window) = window() {
            let _ = window.location().set_href("/codebase");
        }
    };
    
    let clean_with_verification = move |_| {
        // Navigate to codebase analysis page
        if let Some(window) = window() {
            let _ = window.location().set_href("/codebase");
        }
    };

    view! {
        <div 
            class="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-6"
            on:keydown=on_key_down
            tabindex="0"
        >
            <div class="max-w-7xl mx-auto">
                // Header
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <button
                                on:click=go_back
                                class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                            >
                                <i class="fas fa-arrow-left mr-2"></i>
                                "Back to Analysis"
                            </button>
                            <h1 class="text-3xl font-bold text-gray-800">
                                <i class="fas fa-folder-open mr-3 text-purple-500"></i>
                                "Code Base Analysis & Cleaning"
                            </h1>
                        </div>
                    </div>
                    <p class="text-gray-600 mt-2">
                        "Analyze and clean suspicious characters from your entire codebase"
                    </p>
                </div>

                // Error Message
                {move || error_message.get().map(|msg| {
                    let is_success = msg.contains("successfully");
                    let (bg_class, border_class, text_class, icon) = if is_success {
                        ("bg-green-100", "border-green-400", "text-green-700", "fas fa-check-circle")
                    } else {
                        ("bg-red-100", "border-red-400", "text-red-700", "fas fa-exclamation-triangle")
                    };
                    view! {
                        <div class={format!("mb-4 p-4 {} border {} {} rounded-lg", bg_class, border_class, text_class)}>
                            <i class={format!("{} mr-2", icon)}></i>
                            {msg}
                        </div>
                    }
                })}                // Dynamic Interface Based on State
                {move || match interface_state.get() {
                    InterfaceState::SelectionMode => {
                        view! {
                            // Enhanced Folder Selection Section
                            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                                    <i class="fas fa-folder mr-2 text-purple-500"></i>
                                    "1. Select Source Code Folder"
                                </h2>
                    
                    // Path Input Field
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                "Direct Path Input"
                            </label>
                            <div class="relative">
                                <input
                                    type="text"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-mono text-sm"
                                    placeholder="C:\\Users\\<USER>\\Projects\\MyApp or /home/<USER>/projects/myapp"
                                    on:input=on_path_input
                                    prop:value=path_input
                                />                                <div class="absolute right-3 top-3">
                                    {move || {
                                        if validating_path.get() {
                                            view! { <i class="fas fa-spinner fa-spin text-gray-400"></i> }.into_any()
                                        } else if let Some(validation) = path_validation.get() {
                                            if validation.exists && validation.readable {
                                                view! { <i class="fas fa-check-circle text-green-500"></i> }.into_any()
                                            } else {
                                                view! { <i class="fas fa-exclamation-triangle text-red-500"></i> }.into_any()
                                            }
                                        } else {
                                            view! { <div></div> }.into_any()
                                        }
                                    }}
                                </div>
                            </div>
                            
                            // Path validation feedback
                            {move || path_validation.get().map(|validation| {
                                if validation.exists && validation.readable {
                                    view! {
                                        <div class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                                            <div class="flex items-center text-green-700">
                                                <i class="fas fa-check-circle mr-2"></i>
                                                "Valid folder path"
                                            </div>                                            <div class="mt-1 text-sm text-green-600">
                                                {format!("{} files", validation.file_count.unwrap_or(0))}
                                            </div>
                                        </div>
                                    }
                                } else {
                                    view! {
                                        <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                            <div class="flex items-center text-red-700">
                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                {if !validation.exists {
                                                    "Path does not exist"
                                                } else if !validation.readable {
                                                    "Permission denied"
                                                } else {
                                                    "Invalid path"
                                                }}
                                            </div>
                                        </div>
                                    }
                                }
                            })}
                        </div>
                        
                        // Quick Actions Row
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                "Quick Actions"
                            </label>
                            <div class="flex flex-wrap gap-2">
                                <button
                                    on:click=select_folder
                                    class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                                >
                                    <i class="fas fa-folder-open mr-2"></i>
                                    "Browse..."
                                </button>
                                
                                // Recent Folders Dropdown
                                <div class="relative">
                                    <button
                                        on:click=move |_| set_show_recent_dropdown.set(!show_recent_dropdown.get())
                                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                                        disabled=move || recent_folders.get().is_empty()
                                    >
                                        <i class="fas fa-history mr-2"></i>
                                        "Recent"
                                        <i class="fas fa-chevron-down ml-1"></i>
                                    </button>
                                    
                                    {move || if show_recent_dropdown.get() && !recent_folders.get().is_empty() {
                                        view! {
                                            <div class="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                                                <div class="p-2 max-h-60 overflow-y-auto">
                                                    {recent_folders.get().into_iter().take(10).map(|folder| {
                                                        let folder_clone = folder.clone();
                                                        view! {
                                                            <button
                                                                on:click=move |_| select_recent_folder(folder_clone.clone())
                                                                class="w-full text-left px-3 py-2 hover:bg-gray-50 rounded text-sm font-mono"
                                                            >
                                                                <div class="truncate">{folder.path.clone()}</div>
                                                                <div class="text-xs text-gray-500">{folder.last_used.clone()}</div>
                                                            </button>
                                                        }
                                                    }).collect_view()}
                                                </div>
                                            </div>
                                        }
                                    } else {
                                        view! { <div></div> }
                                    }}
                                </div>                                // Quick Access Buttons
                                {move || quick_access_folders.get().into_iter().map(|folder| {
                                    let folder_clone = folder.clone();
                                    let (icon, name) = if folder.path.contains("Desktop") {
                                        ("fa-desktop", "Desktop")
                                    } else if folder.path.contains("Documents") {
                                        ("fa-file-alt", "Documents")
                                    } else if folder.path.contains(".git") || folder.path.to_lowercase().contains("git") {
                                        ("fa-code-branch", "Git Root")
                                    } else {
                                        ("fa-folder", "Folder")
                                    };
                                    view! {
                                        <button
                                            on:click=move |_| select_quick_access_folder(folder_clone.clone())
                                            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                                        >
                                            <i class={format!("fas {} mr-2", icon)}></i>
                                            {name}
                                        </button>
                                    }
                                }).collect_view()}
                            </div>
                        </div>                          // Enhanced Drag & Drop Zone with Visual Feedback
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                "Drag & Drop"
                            </label>
                            <div 
                                class=move || {
                                    let base_classes = "border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer";
                                    if is_drag_over.get() {
                                        format!("{} border-purple-500 bg-purple-50 border-solid scale-105", base_classes)
                                    } else {
                                        format!("{} border-gray-300 hover:border-purple-400 hover:bg-gray-50", base_classes)
                                    }
                                }
                                on:dragover=on_drag_over
                                on:dragenter=on_drag_enter
                                on:dragleave=on_drag_leave
                                on:drop=on_drop
                            >
                                <i class=move || {
                                    if is_drag_over.get() {
                                        "fas fa-folder-plus text-purple-500 text-3xl mb-2 animate-bounce"
                                    } else {
                                        "fas fa-upload text-gray-400 text-2xl mb-2"
                                    }
                                }></i>                                <p class=move || {
                                    if is_drag_over.get() {
                                        "text-purple-700 text-sm font-medium"
                                    } else {
                                        "text-gray-600 text-sm"
                                    }
                                }>
                                    {move || if is_drag_over.get() {
                                        "Drop folder here to analyze!"
                                    } else {
                                        "Drag a folder here or use the browse button above"
                                    }}
                                </p>
                                <p class=move || {
                                    if is_drag_over.get() {
                                        "text-purple-500 text-xs mt-2"
                                    } else {
                                        "text-gray-500 text-xs mt-2"
                                    }
                                }>
                                    {move || if is_drag_over.get() {
                                        "Release to select this folder"
                                    } else {
                                        "✨ Native drag & drop support - works perfectly!"
                                    }}
                                </p>
                            </div>
                        </div>
                        
                        // Selected Folder Display
                        {move || selected_folder.get().map(|path| view! {
                            <div class="flex items-center justify-between p-4 bg-purple-50 border border-purple-200 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-check-circle text-purple-600 text-lg"></i>
                                    <div>
                                        <div class="font-medium text-purple-800">"Selected Folder"</div>
                                        <code class="text-purple-700 text-sm">{path}</code>
                                    </div>
                                </div>                                {move || if let Some(validation) = path_validation.get() {
                                    if validation.exists && validation.readable {
                                        view! {
                                            <button
                                                on:click=select_validated_folder
                                                class="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors"
                                            >
                                                "Use This Folder"
                                            </button>
                                        }.into_any()
                                    } else {
                                        view! { <div></div> }.into_any()
                                    }
                                } else {
                                    view! { <div></div> }.into_any()
                                }}
                            </div>
                        })}
                    </div>
                    
                    <div class="mt-4 text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-2"></i>                        "Supported file types: .js, .ts, .rs, .py, .java, .cpp, .c, .h, .css, .html, .xml, .json, .md"
                    </div>
                            </div>
                    },
                    InterfaceState::ActionsMode => {
                        view! {
                            // Compact Selected Folder Display
                            <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-folder-open text-purple-600 text-lg"></i>
                                        <div>
                                            <div class="font-medium text-gray-800">"Selected Folder"</div>
                                            <code class="text-gray-600 text-sm">{selected_folder.get().unwrap_or_default()}</code>
                                        </div>
                                    </div>
                                    <button
                                        on:click=return_to_selection
                                        class="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
                                    >
                                        <i class="fas fa-edit mr-2"></i>
                                        "Change Folder"
                                    </button>
                                </div>
                            </div>

                            // Prominent Actions Section
                            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                                    <i class="fas fa-search mr-2 text-blue-500"></i>
                                    "Analyze & Clean Code Base"
                                </h2>
                        
                        <div class="flex items-center gap-4">                            <button
                                on:click=analyze_codebase
                                class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                                disabled=loading
                            >
                                {move || if loading.get() {
                                    view! { <><i class="fas fa-spinner fa-spin mr-2"></i>"Analyzing..."</> }
                                } else {
                                    view! { <><i class="fas fa-search mr-2"></i>"Analyze Files"</> }
                                }}
                            </button>
                        </div>
                          {move || analysis_results.get().map(|_| {
                            view! {
                                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                    <button
                                        on:click=clean_codebase
                                        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-semibold"
                                        disabled=loading
                                    >
                                        <i class="fas fa-broom mr-2"></i>
                                        "Quick Clean"
                                    </button>
                                    <button
                                        on:click=clean_with_verification
                                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                                        disabled=loading                                    >
                                        <i class="fas fa-shield-check mr-2"></i>
                                        "Clean + Verify"
                                    </button>
                                </div>
                            }                        })}
                        
                        // Progress indicator
                        {move || if loading.get() {
                            view! {
                                <div class="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-blue-800 font-medium">"Processing..."</span>
                                        <span class="text-blue-600">{move || format!("{}%", progress.get())}</span>
                                    </div>
                                    <div class="w-full bg-blue-200 rounded-full h-2">
                                        <div 
                                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                            style=move || format!("width: {}%", progress.get())
                                        ></div>
                                    </div>
                                    {move || if !current_file.get().is_empty() {
                                        view! {
                                            <div>
                                                <p class="text-blue-700 text-sm mt-2">
                                                    "Processing: " <code>{current_file.get()}</code>
                                                </p>
                                            </div>
                                        }
                                    } else {
                                        view! { <div></div> }
                                    }}
                                </div>
                            }
                        } else {
                            view! { <div></div> }
                        }}
                    </div>
                                            style=move || format!("width: {}%", progress.get())
                                        ></div>
                                    </div>                                    {move || if !current_file.get().is_empty() {
                                        view! {
                                            <div>
                                                <p class="text-blue-700 text-sm mt-2">
                                                    "Processing: " <code>{current_file.get()}</code>
                                                </p>
                                            </div>
                                        }                                    } else {
                                        view! { <div></div> }
                                    }}
                                </div>
                            }
                        } else {
                            view! { <div></div> }
                        }}
                    </div>
                    },
                    InterfaceState::ProcessingMode => {
                        view! {
                            // Compact Selected Folder Display
                            <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-folder-open text-purple-600 text-lg"></i>
                                    <div>
                                        <div class="font-medium text-gray-800">"Processing Folder"</div>
                                        <code class="text-gray-600 text-sm">{selected_folder.get().unwrap_or_default()}</code>
                                    </div>
                                </div>
                            </div>

                            // Processing Status Section
                            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                                    <i class="fas fa-spinner fa-spin mr-2 text-blue-500"></i>
                                    "Processing in Progress"
                                </h2>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-blue-800 font-medium">"Processing..."</span>
                                        <span class="text-blue-600">{move || format!("{}%", progress.get())}</span>
                                    </div>                                    <div class="w-full bg-blue-200 rounded-full h-2">
                                        <div 
                                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                            style=move || format!("width: {}%", progress.get())
                                        ></div>
                                    </div>
                                    {move || if !current_file.get().is_empty() {
                                        view! {
                                            <div>
                                                <p class="text-blue-700 text-sm mt-2">
                                                    "Processing: " <code>{current_file.get()}</code>
                                                </p>
                                            </div>
                                        }
                                    } else {
                                        view! { <div></div> }                                    }}
                                </div>
                            </div>
                        }
                    }
                }}

                // Results Section (shown in all modes when results exist)
                {move || analysis_results.get().map(|results| view! {
                    <div class="space-y-6">
                        // Statistics Overview
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-xl font-semibold mb-4 text-gray-800">
                                <i class="fas fa-chart-bar mr-2 text-green-500"></i>
                                "Analysis Results"
                            </h2>
                            
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-blue-600">{results.total_files}</div>
                                    <div class="text-blue-800 text-sm">"Files Scanned"</div>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-orange-600">{results.files_with_issues}</div>
                                    <div class="text-orange-800 text-sm">"Files with Issues"</div>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-red-600">{results.total_suspicious_chars}</div>
                                    <div class="text-red-800 text-sm">"Suspicious Characters"</div>
                                </div>                                <div class="bg-green-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-green-600">{format!("{:.1}%", results.health_score)}</div>
                                    <div class="text-green-800 text-sm">"Health Score"</div>
                                </div>
                            </div>
                              // Export Report Section
                            <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                                <div class="flex items-center gap-4">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">"Export Format:"</h4>
                                        <div class="flex gap-3">
                                            <label class="flex items-center">
                                                <input type="radio" name="export_format" value="json" checked=true class="mr-2"/>
                                                <span class="text-sm text-gray-600">"JSON"</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="export_format" value="markdown" class="mr-2"/>
                                                <span class="text-sm text-gray-600">"Markdown"</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="export_format" value="txt" class="mr-2"/>
                                                <span class="text-sm text-gray-600">"Text"</span>
                                            </label>
                                        </div>
                                        {move || export_status.get().map(|status| {
                                            match status.as_str() {
                                                "success" => view! {
                                                    <div class="text-green-600 text-sm mt-2 flex items-center">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        "Report exported successfully!"
                                                    </div>
                                                },
                                                "error" => view! {
                                                    <div class="text-red-600 text-sm mt-2 flex items-center">
                                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                                        {export_message.get()}
                                                    </div>
                                                },
                                                _ => view! { <div></div> }
                                            }
                                        })}
                                    </div>
                                </div>
                                <button
                                    on:click=move |_| {
                                        let results = analysis_results.get().unwrap();
                                        let format = web_sys::window()
                                            .unwrap()
                                            .document()
                                            .unwrap()
                                            .query_selector("input[name='export_format']:checked")
                                            .unwrap()
                                            .unwrap()
                                            .dyn_into::<web_sys::HtmlInputElement>()
                                            .unwrap()
                                            .value();                                        // Clear previous status
                                        set_export_status.set(None);
                                        set_export_message.set(String::new());
                                        set_is_exporting.set(true);
                                        
                                        spawn_local(async move {
                                            match invoke("export_codebase_report", &serde_json::json!({
                                                "analysisData": results,
                                                "formatType": format
                                            })).await {                                                Ok(_) => {
                                                    set_export_status.set(Some("success".to_string()));
                                                    // Clear success message after 3 seconds
                                                    let status_clone = set_export_status.clone();
                                                    spawn_local(async move {
                                                        gloo_timers::future::TimeoutFuture::new(3000).await;
                                                        status_clone.set(None);
                                                    });
                                                },                                                Err(e) => {
                                                    set_export_status.set(Some("error".to_string()));
                                                    set_export_message.set(format!("Export failed: {}", e));
                                                }
                                            }
                                            set_is_exporting.set(false);
                                        });
                                    }
                                    disabled=move || is_exporting.get()
                                    class=move || format!("px-6 py-3 text-white rounded-lg font-semibold transition-colors flex items-center {}",
                                        if is_exporting.get() {
                                            "bg-gray-400 cursor-not-allowed"
                                        } else {
                                            "bg-purple-600 hover:bg-purple-700"
                                        }
                                    )
                                >
                                    {move || if is_exporting.get() {
                                        view! {
                                            <>
                                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                "Exporting..."
                                            </>
                                        }
                                    } else {
                                        view! {
                                            <>
                                                <i class="fas fa-download mr-2"></i>
                                                "Export Report"
                                            </>
                                    }
                                    }}
                                </button>
                            </div>
                        </div>

                        // File Details
                        {if !results.file_details.is_empty() {
                            view! {
                                                               <div class="bg-white rounded-xl shadow-lg p-6">
                                    <h3 class="text-lg font-semibold mb-4 text-gray-800">
                                        <i class="fas fa-file-alt mr-2 text-purple-500"></i>
                                        "Files with Issues"
                                    </h3>
                                    
                                    <div class="space-y-3 max-h-96 overflow-auto">
                                        {results.file_details.iter().filter(|file| file.suspicious_characters > 0).map(|file| view! {
                                            <div class="border border-gray-200 rounded-lg p-4">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="font-medium text-gray-800">
                                                        <i class="fas fa-file mr-2 text-blue-500"></i>
                                                        {file.file_path.clone()}
                                                    </h4>
                                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                                        {file.suspicious_characters} " issues"
                                                    </span>
                                                </div>
                                                
                                                <div class="grid grid-cols-3 gap-4 text-sm text-gray-600">
                                                    <div>"Size: " {format!("{} bytes", file.file_size)}</div>
                                                    <div>"Characters: " {file.total_characters}</div>
                                                    <div>"Encoding: " {file.encoding.clone()}</div>
                                                </div>
                                                  {if !file.issues.is_empty() {
                                                    view! {
                                                        <div class="mt-3">
                                                            <div class="text-sm font-medium text-gray-700 mb-1">"Issues Found:"</div>
                                                            <div class="flex flex-wrap gap-1">
                                                                {file.issues.iter().map(|issue| view! {
                                                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                                                                        {issue.clone()}
                                                                    </span>
                                                                }).collect::<Vec<_>>()}
                                                            </div>
                                                        </div>
                                                    }
                                                } else {
                                                    view! { <div></div> }
                                                }}
                                            </div>
                                        }).collect::<Vec<_>>()}
                                    </div>
                                </div>
                            }
                        } else {
                            view! {
                                <div class="bg-green-50 rounded-xl border border-green-200 p-6 text-center">
                                    <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
                                    <h3 class="text-lg font-semibold text-green-800 mb-2">"Clean Codebase!"</h3>
                                    <p class="text-green-700">"No suspicious characters found in any files."</p>
                                </div>
                            }
                        }}
                    </div>                })}
            </div>
        </div>
    }
}
