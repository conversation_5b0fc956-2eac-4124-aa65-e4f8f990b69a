# Developer Handbook - Bad Character Scanner by <PERSON><PERSON>

**Your complete guide to developing, extending, and maintaining the Bad Character Scanner - A Leptos + Tauri v2 desktop application for Unicode security analysis.**

---

## 🚀 **Quick Setup (15 Minutes)**

### **Prerequisites**
| Tool | Version | Installation |
|------|---------|--------------|
| **Rust** | Latest stable | `winget install Rustlang.Rustup` |
| **Node.js** | 16+ | `winget install OpenJS.NodeJS` |
| **Tauri CLI** | 2.5+ | `cargo install tauri-cli --version "^2.5"` |
| **Trunk** | Latest | `cargo install trunk --locked` |

### **Development Setup**
```powershell
# 1. Clone and navigate
git clone <repository-url>
cd Leptos_TaurieV2_BCS

# 2. Install dependencies
npm install

# 3. Verify setup
cargo tauri info

# 4. Start development (Recommended)
.\dev_startup.ps1

# Alternative: Manual startup
# Terminal 1: Frontend
trunk serve --port 1420
# Terminal 2: Backend (new terminal)
cargo tauri dev
```

### **✅ Success Verification**
- **Frontend**: http://localhost:1420 loads in browser
- **Desktop App**: Tauri window opens automatically
- **No Errors**: Clean compilation and startup logs

---

## 🏗️ **Architecture Overview**

### **Technology Stack**
- **Frontend**: Leptos (Rust-based reactive UI framework)
- **Backend**: Tauri v2 (Cross-platform desktop framework)
- **Styling**: Tailwind CSS with Apple-inspired design system
- **Build Tools**: Trunk (frontend), Cargo (backend)
- **IPC**: Tauri's type-safe command system

### **Project Structure**
```
Leptos_TaurieV2_BCS/
├── src/                           # Frontend (Leptos/WASM)
│   ├── components/                # UI components
│   │   ├── analyze.rs            # Text analysis interface
│   │   ├── codebase/             # Codebase analysis components
│   │   ├── settings_button.rs    # Settings panel
│   │   └── theme.rs              # Theme management
│   ├── context.rs                # Shared state management
│   └── lib.rs                    # Frontend entry point
├── src-tauri/                    # Backend (Tauri + Rust)
│   ├── src/
│   │   ├── modules/              # Core analysis engines
│   │   │   ├── character_analyzer.rs  # Unicode analysis
│   │   │   ├── ai_detection.rs        # AI content detection
│   │   │   └── enhanced_analysis.rs   # Codebase scanning
│   │   ├── commands/             # Tauri IPC commands
│   │   └── main.rs              # Backend entry point
├── assets/                       # Static resources
│   ├── Advanced_AI_Patterns.json # AI detection patterns
│   └── icons/                   # Application icons
├── docs/                        # Documentation
└── style.css                   # Global styles
```

### **Key Components**

#### **Frontend Components**
- **`App`**: Main application wrapper with theme provider
- **`HomePage`**: Two-mode interface (text/codebase analysis)
- **`AnalyzeComponent`**: Text analysis with real-time feedback
- **`CodebaseComponent`**: File system analysis with drag & drop
- **`SettingsButton`**: Floating settings panel with tabbed interface
- **`ThemeProvider`**: Dark/light/system theme management

#### **Backend Modules**
- **`character_analyzer`**: Core Unicode security analysis engine
- **`ai_detection`**: AI-generated content pattern matching
- **`enhanced_analysis`**: Comprehensive codebase scanning
- **`pattern_matching`**: Threat pattern recognition system

---

## 💻 **Development Workflow**

### **Daily Development Process**
```powershell
# 1. Start development environment
.\dev_startup.ps1

# 2. Development cycle
# - Edit frontend files in src/ (auto-reloads)
# - Edit backend files in src-tauri/src/ (auto-recompiles)
# - Changes appear immediately in browser and desktop app

# 3. Testing
# - Frontend: Browser DevTools at http://localhost:1420
# - Backend: Terminal output from cargo tauri dev
# - Integration: Test in desktop app window

# 4. Commit changes
git add .
git commit -m "feat: your feature description"
```

### **Code Style Guidelines**
- **Formatting**: Run `cargo fmt` before committing
- **Linting**: Use `cargo clippy` for code quality
- **Naming**: Follow Rust conventions (snake_case, PascalCase)
- **Documentation**: Document all public APIs with `///` comments
- **Testing**: Write unit tests for new functionality

---

## 🔧 **Adding New Features**

### **1. Adding a New Tauri Command**
```rust
// src-tauri/src/commands/your_feature.rs
#[tauri::command]
pub async fn analyze_new_threat(input: String) -> Result<AnalysisResult, String> {
    // Implementation
    let result = perform_analysis(&input)?;
    Ok(result)
}

// Register in src-tauri/src/main.rs
.invoke_handler(tauri::generate_handler![
    // ... existing commands
    commands::your_feature::analyze_new_threat,
])
```

### **2. Adding a Frontend Component**
```rust
// src/components/your_component.rs
use leptos::*;

#[component]
pub fn YourComponent() -> impl IntoView {
    let (state, set_state) = create_signal(String::new());
    
    view! {
        <div class="your-component">
            // Your UI implementation
        </div>
    }
}

// Export in src/components/mod.rs
pub mod your_component;
pub use your_component::*;
```

### **3. Adding Detection Patterns**
```json
// assets/Advanced_AI_Patterns.json
{
  "name": "Your Pattern Name",
  "regex": "your-regex-pattern",
  "severity": "Low|Medium|High|Critical",
  "description": "What this pattern detects",
  "category": "ai_detection|security|unicode"
}
```

### **4. Frontend-Backend Communication**
```rust
// Frontend: Call backend command
use tauri::invoke;

let result = invoke("analyze_new_threat", &args).await
    .map_err(|e| format!("Analysis failed: {}", e))?;
```

---

## 🎨 **UI Development Guidelines**

### **Apple-Inspired Design Principles**
- **Clean Hierarchy**: Clear visual hierarchy with consistent spacing
- **Minimal Interface**: Hide complexity behind progressive disclosure
- **Smooth Animations**: Use CSS transitions for state changes
- **Consistent Icons**: Standardized SVG icons (16px, 20px, 24px)
- **Professional Typography**: System fonts with proper weight and sizing

### **Component Patterns**
```rust
// Standard component structure
#[component]
pub fn YourComponent() -> impl IntoView {
    // State management
    let (state, set_state) = create_signal(initial_value);
    
    // Event handlers
    let handle_click = move |_| {
        // Handle user interaction
    };
    
    // Render with Tailwind classes
    view! {
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">"Component Title"</h2>
            // Component content
        </div>
    }
}
```

### **Theme Integration**
```rust
// Use theme context in components
let theme_context = expect_context::<ThemeContext>();
let is_dark = theme_context.is_dark;

view! {
    <div class="bg-white dark:bg-gray-900" class:dark-mode=move || is_dark.get()>
        // Theme-aware content
    </div>
}
```

---

## 🧪 **Testing & Quality Assurance**

### **Testing Commands**
```powershell
# Unit tests
cargo test

# Integration tests
cargo test --test integration

# Frontend tests (if implemented)
npm test

# Build verification
cargo tauri build --debug
```

### **Manual Testing Checklist**
- [ ] **Text Analysis**: Paste text with Unicode threats
- [ ] **Codebase Analysis**: Drag & drop folders
- [ ] **Export Functionality**: Test all export formats
- [ ] **Settings Panel**: Verify all configuration options
- [ ] **Theme Switching**: Test dark/light/system modes
- [ ] **Responsive Design**: Test different window sizes
- [ ] **Error Handling**: Test with invalid inputs

### **Performance Testing**
- **Large Files**: Test with files >1MB
- **Many Files**: Test with codebases >1000 files
- **Memory Usage**: Monitor RAM consumption during analysis
- **Response Time**: Ensure UI remains responsive

---

## 🐛 **Debugging & Troubleshooting**

### **Common Build Issues**
```powershell
# Missing WASM target
rustup target add wasm32-unknown-unknown

# Port conflicts
Get-Process | Where-Object {$_.ProcessName -like "*trunk*"} | Stop-Process
Get-Process | Where-Object {$_.ProcessName -like "*tauri*"} | Stop-Process

# Clean build
cargo clean && trunk clean
rm -rf node_modules && npm install
```

### **Development Issues**
| Issue | Location | Solution |
|-------|----------|----------|
| **Frontend errors** | Browser console (F12) | Check Leptos component logic |
| **Backend errors** | Terminal output | Check Tauri command implementation |
| **IPC errors** | Both frontend/backend | Verify command signatures match |
| **Styling issues** | Browser DevTools | Check Tailwind classes and CSS |

### **Debugging Tools**
```rust
// Backend debugging
eprintln!("Debug: {:#?}", variable);

// Frontend debugging  
log!("Frontend debug: {:#?}", variable);
console_log!("Browser console: {}", message);

// Performance profiling
let start = std::time::Instant::now();
// ... operation
eprintln!("Operation took: {:?}", start.elapsed());
```

---

## 🚀 **Build & Release Process**

### **Development Builds**
```powershell
# Debug build (faster compilation)
cargo tauri build --debug

# Release build (optimized)
cargo tauri build
```

### **Release Checklist**
1. **Update Versions**: Increment in `Cargo.toml` and `package.json`
2. **Run Tests**: `cargo test && npm test`
3. **Build Release**: `cargo tauri build`
4. **Test Installer**: Install on clean system
5. **Create Release**: Tag and publish on GitHub
6. **Update Documentation**: Reflect any API changes

### **Distribution**
- **Windows**: `.msi` installer in `target/release/bundle/msi/`
- **macOS**: `.dmg` file in `target/release/bundle/dmg/`
- **Linux**: `.deb` and `.AppImage` in respective bundle directories

---

## 📚 **Advanced Topics**

### **Performance Optimization**
- **Lazy Loading**: Implement for large file operations
- **Web Workers**: Use for CPU-intensive analysis
- **Caching**: Cache analysis results for repeated operations
- **Memory Management**: Proper cleanup of large data structures

### **Security Considerations**
- **Input Validation**: Sanitize all user inputs
- **File System Access**: Validate file paths and permissions
- **IPC Security**: Ensure command parameters are validated
- **Dependency Updates**: Regularly update dependencies

### **Extensibility**
- **Plugin System**: Design for third-party extensions
- **Configuration**: Externalize settings and patterns
- **API Design**: Create stable interfaces for integrations
- **Documentation**: Maintain comprehensive API docs

---

## 🤝 **Contributing Guidelines**

### **Code Contribution Process**
1. **Fork Repository**: Create your own fork
2. **Create Branch**: `git checkout -b feature/your-feature`
3. **Implement Changes**: Follow coding standards
4. **Write Tests**: Add appropriate test coverage
5. **Submit PR**: Create pull request with clear description

### **Documentation Updates**
- Update relevant documentation for any API changes
- Include examples for new features
- Maintain consistency with existing documentation style
- Test all code examples before submitting

---

*Ready to contribute? You now have everything needed to develop and extend the Bad Character Scanner!*

**Bad Character Scanner by J.Shoy - 2025**
