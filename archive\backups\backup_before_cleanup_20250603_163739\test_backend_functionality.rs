use std::collections::HashMap;

// Simple test to verify our main_module functions work
// This simulates what the Tauri frontend would do

fn main() {
    println!("=== BACKEND FUNCTIONALITY TEST ===");
    
    // Test file path
    let test_file = r"c:\Users\<USER>\Documents\Software\Laptos_TaurieV2_HelloWorld\test_files\sample_dirty_code.js";
    
    println!("Testing with file: {}", test_file);
    
    // Check if file exists
    if std::path::Path::new(test_file).exists() {
        println!("✅ Test file exists");
        
        // Read file content
        match std::fs::read_to_string(test_file) {
            Ok(content) => {
                println!("✅ File read successfully");
                println!("Content preview: {:?}", &content[..std::cmp::min(100, content.len())]);
                
                // Count problematic characters manually
                let mut count = 0;
                for ch in content.chars() {
                    match ch {
                        '\u{200B}' | '\u{200C}' | '\u{200D}' | '\u{202E}' | '\u{202C}' | '\u{FEFF}' => {
                            count += 1;
                            println!("Found problematic char: U+{:04X}", ch as u32);
                        },
                        '\u{0410}' | '\u{043E}' | '\u{0440}' => {
                            count += 1;
                            println!("Found Cyrillic homograph: U+{:04X} ({})", ch as u32, ch);
                        },
                        _ => {}
                    }
                }
                
                println!("Total problematic characters found: {}", count);
                
                if count > 0 {
                    println!("✅ Test file contains problematic characters as expected");
                } else {
                    println!("⚠️  No problematic characters detected - check file encoding");
                }
            },
            Err(e) => {
                println!("❌ Failed to read file: {}", e);
            }
        }
    } else {
        println!("❌ Test file not found");
    }
    
    println!("\n=== FOLDER ANALYSIS TEST ===");
    let test_folder = r"c:\Users\<USER>\Documents\Software\Laptos_TaurieV2_HelloWorld\test_files";
    
    if std::path::Path::new(test_folder).exists() {
        println!("✅ Test folder exists");
        
        // List files in test folder
        match std::fs::read_dir(test_folder) {
            Ok(entries) => {
                let mut file_count = 0;
                for entry in entries {
                    if let Ok(entry) = entry {
                        let path = entry.path();
                        if path.is_file() {
                            file_count += 1;
                            println!("  Found file: {}", path.file_name().unwrap().to_string_lossy());
                        }
                    }
                }
                println!("Total files for testing: {}", file_count);
                
                if file_count > 0 {
                    println!("✅ Test folder ready for batch processing");
                } else {
                    println!("⚠️  No files found in test folder");
                }
            },
            Err(e) => {
                println!("❌ Failed to read folder: {}", e);
            }
        }
    } else {
        println!("❌ Test folder not found");
    }
    
    println!("\n✅ Backend functionality test complete!");
    println!("Ready for live application testing.");
}
