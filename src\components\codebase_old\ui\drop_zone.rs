use leptos::*;
use web_sys::{DragEvent, Event, HtmlInputElement};
use wasm_bindgen::JsCast;
// Removed unused wasm_bindgen prelude import
use serde::{Deserialize, Serialize};
// Removed unused Rc import
use super::file_types_display::FileTypesDisplay;
use crate::components::codebase::{types::BasicProgress, handlers::analyze_codebase};

// Enhanced drag & drop structures
#[derive(Debug, Clone, Serialize, Deserialize)]
struct DroppedFile {
    path: String,
    name: String,
    size: u64,
    is_directory: bool,
    file_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct DragDropValidationResult {
    valid_files: Vec<DroppedFile>,
    invalid_files: Vec<String>,
    total_files: usize,
    total_size: u64,
    validation_errors: Vec<String>,
}

#[component]
pub fn DropZone(
    on_select_folder: impl Fn(web_sys::MouseEvent) + 'static,
    is_analyzing: ReadSignal<bool>,
    // Parameters for direct path analysis
    set_is_analyzing: WriteSignal<bool>,
    set_progress: WriteSignal<Option<BasicProgress>>,
    set_selected_path: WriteSignal<String>,
    set_analysis_result: WriteSignal<Option<String>>,
    analysis_context: Option<crate::context::AnalysisContextValue>,
) -> impl IntoView {
    let (path_input, set_path_input) = create_signal(String::new());
    let (drag_over, set_drag_over) = create_signal(false);

    // Handle path input changes
    let on_path_input = move |ev: Event| {
        let target = ev.target().unwrap();
        let input = target.dyn_into::<HtmlInputElement>().unwrap();
        set_path_input.set(input.value());
    };

    // Function to trigger analysis from path input
    let trigger_path_analysis = {
        let set_is_analyzing = set_is_analyzing.clone();
        let set_progress = set_progress.clone();
        let set_selected_path = set_selected_path.clone();
        let set_analysis_result = set_analysis_result.clone();
        let analysis_context = analysis_context.clone();

        move |path: String| {
            web_sys::console::log_1(&format!("Starting analysis for path: {}", path).into());

            // Call the same analyze_codebase function used by other methods
            analyze_codebase(
                path,
                set_is_analyzing,
                set_progress,
                set_selected_path,
                set_analysis_result,
                analysis_context.clone(),
            );
        }
    };

    // Handle Enter key in path input
    let on_path_keydown = {
        let trigger_analysis = trigger_path_analysis.clone();
        let path_input = path_input.clone();
        move |ev: web_sys::KeyboardEvent| {
            if ev.key() == "Enter" && !path_input.get().trim().is_empty() {
                // Trigger analysis when Enter is pressed
                trigger_analysis(path_input.get().trim().to_string());
            }
        }
    };

    // Store the analysis function in a way that can be called multiple times
    let analyze_fn = store_value({
        let set_is_analyzing = set_is_analyzing.clone();
        let set_progress = set_progress.clone();
        let set_selected_path = set_selected_path.clone();
        let set_analysis_result = set_analysis_result.clone();
        let analysis_context = analysis_context.clone();
        let path_input = path_input.clone();

        move || {
            let path = path_input.get().trim().to_string();
            if !path.is_empty() {
                web_sys::console::log_1(&format!("Starting analysis for path: {}", path).into());

                // Call the same analyze_codebase function used by other methods
                analyze_codebase(
                    path,
                    set_is_analyzing,
                    set_progress,
                    set_selected_path,
                    set_analysis_result,
                    analysis_context.clone(),
                );
            }
        }
    });

    // Enhanced Drag & Drop handlers with Tauri support
    let (_drop_status, set_drop_status) = create_signal(String::new());
    let (_is_validating, set_is_validating) = create_signal(false);

    // Enhanced drop handler with validation
    let on_drop = {
        let set_drag_over = set_drag_over.clone();
        let set_path_input = set_path_input.clone();
        let set_drop_status = set_drop_status.clone();
        let set_is_validating = set_is_validating.clone();
        let trigger_analysis = trigger_path_analysis.clone();

        move |ev: DragEvent| {
            ev.prevent_default();
            ev.stop_propagation();
            set_drag_over.set(false);
            set_is_validating.set(true);
            set_drop_status.set("Processing dropped files...".to_string());

            web_sys::console::log_1(&"Enhanced drop event detected".into());

            // Try to get dropped files from the DataTransfer
            if let Some(data_transfer) = ev.data_transfer() {
                if let Some(files) = data_transfer.files() {
                    let file_count = files.length();
                    web_sys::console::log_1(&format!("Detected {} dropped files", file_count).into());

                    if file_count > 0 {
                        // Get the first file/folder
                        if let Some(file) = files.get(0) {
                            let file_name = file.name();
                            web_sys::console::log_1(&format!("First dropped item: {}", file_name).into());

                            // For web mode, we'll simulate a path based on the file name
                            // In a real Tauri app, the actual file paths would come from the backend
                            let simulated_path = format!("Dropped: {}", file_name);

                            // Update the path input field
                            set_path_input.set(simulated_path.clone());
                            set_drop_status.set(format!("✅ Ready to analyze: {}", file_name));
                            set_is_validating.set(false);

                            // Auto-trigger analysis for dropped files
                            trigger_analysis(simulated_path);

                            web_sys::console::log_1(&"Path input field updated with dropped file".into());
                        }
                    }
                } else {
                    set_drop_status.set("❌ No files detected in drop".to_string());
                    set_is_validating.set(false);
                }
            } else {
                set_drop_status.set("❌ Could not access dropped data".to_string());
                set_is_validating.set(false);
            }
        }
    };

    let on_drag_over = {
        let set_drag_over = set_drag_over.clone();
        move |ev: DragEvent| {
            ev.prevent_default();
            ev.stop_propagation();
            set_drag_over.set(true);
        }
    };

    let on_drag_leave = {
        let set_drag_over = set_drag_over.clone();
        move |ev: DragEvent| {
            ev.prevent_default();
            ev.stop_propagation();
            set_drag_over.set(false);
        }
    };

    view! {
        <div class="space-y-6">
            // Section Header
            <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <svg class="icon-md text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v0M8 5a2 2 0 000 4h8a2 2 0 000-4M8 5v0"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800">"1. Select Source Code Folder"</h3>
                </div>
            </div>

            // Direct Path Input
            <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">"Direct Path Input"</label>
                <input
                    type="text"
                    class="input-field"
                    placeholder="C:\\Users\\<USER>\\Projects\\MyApp or /home/<USER>/projects/myapp"
                    prop:value=move || path_input.get()
                    on:input=on_path_input
                    on:keydown=on_path_keydown
                />
                <p class="text-xs text-gray-500">
                    "Click here and paste (Ctrl+V) your folder path, then press Enter"
                </p>

                // Analyze button - shows when path is entered
                <Show when=move || !path_input.get().trim().is_empty()>
                    <div class="mt-3">
                        <button
                            class="btn-primary w-full"
                            on:click=move |_| analyze_fn.with_value(|f| f())
                            disabled=move || is_analyzing.get()
                        >
                            <svg class="btn-icon mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Codebase" }}
                        </button>
                    </div>
                </Show>
            </div>

            // Quick Actions
            <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">"Quick Actions"</label>
                <div class="flex flex-wrap gap-2">
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        "Browse..."
                    </button>
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        "Recent"
                    </button>
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        "Folder"
                    </button>
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        "Folder"
                    </button>
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        "Folder"
                    </button>
                    <button class="quick-action-btn">
                        <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        "Folder"
                    </button>
                </div>
            </div>

            // Drag & Drop Area
            <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">"Drag & Drop"</label>
                <div
                    class=move || {
                        format!(
                            "drag-drop-area {}",
                            if drag_over.get() { "drag-over" } else { "" }
                        )
                    }
                    on:drop=on_drop
                    on:dragover=on_drag_over
                    on:dragleave=on_drag_leave
                >
                    <div class="flex flex-col items-center">
                        <svg class="icon-2xl text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        <p class="text-lg font-medium text-gray-700 mb-2">
                            "Drag a folder here or use the browse button above"
                        </p>
                        <p class="text-sm text-blue-600 mb-4">
                            "⚠ Native drag & drop support - works perfectly"
                        </p>
                        <button
                            class="btn-primary"
                            on:click=on_select_folder
                            disabled=move || is_analyzing.get()
                        >
                            {move || if is_analyzing.get() { "Analyzing..." } else { "Browse for Folder" }}
                        </button>
                    </div>
                </div>
            </div>

            // Supported file types with expandable display
            <FileTypesDisplay show_help_link=true />
        </div>
    }
}