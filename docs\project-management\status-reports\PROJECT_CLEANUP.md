# Project Cleanup Guide

## Files to Keep

### Core Application Files
- `src/` - Frontend source code
  - `lib.rs` - Main application entry
  - `components/` - UI components
    - `analyze.rs` - Text analysis
    - `clean.rs` - Text cleaning
    - `export.rs` - Export functionality
    - `codebase.rs` - Codebase analysis
    - `controls/` - UI controls
    - `mod.rs` - Module exports

- `src-tauri/` - Backend source code
  - `src/`
    - `main.rs` - Tauri entry point
    - `lib.rs` - Tauri configuration
    - `main_module.rs` - Command handlers
    - `enhanced_analysis.rs` - Advanced analysis
    - `modules/` - Core functionality
      - `character_analyzer.rs` - Unicode analysis
      - `ai_detection.rs` - AI content detection
      - `pattern_matching.rs` - Pattern detection
      - `data_structures.rs` - Shared types

### Configuration Files
- `Cargo.toml` - Rust dependencies
- `package.json` - Node dependencies
- `tauri.conf.json` - Tauri configuration
- `tailwind.config.js` - Tailwind CSS config
- `index.html` - HTML entry point

### Documentation
- `README.md` - Project overview
- `docs/`
  - `DEVELOPER_GUIDE.md` - Developer documentation
  - `BugFixes_*.md` - Bug fix history

### Assets
- `assets/` - Data files
  - `Advanced_AI_Patterns.json`
  - `Bad_Characters.json`
  - `FileTypesSummary.json`

## Files to Remove

### Test Files (can be moved to tests/ directory)
- `test_*.rs` files in root
- `test_*.txt` files
- `test_*.js` files
- `test_*.pdb` files
- `test_*.exe` files

### Temporary/Generated Files
- `temp_output.txt`
- `results_clean.json`
- `test_analysis.json`
- `.pdb` files (debug symbols)
- `.exe` files (compiled binaries)

### Old/Backup Files
- `src/components_old.rs`
- `src/lib_simple.rs`
- `src/style_simple.css`
- `src-tauri/src/lib_backup.rs`
- `src-tauri/src/lib_clean.rs`
- `src-tauri/src/main_module_backup_before_cleanup.rs`
- `src-tauri/src/main_new.rs`

### Directories to Clean
- `archived_gui_files/` - Move to backup location
- `archived_lib_files/` - Move to backup location
- `backup_before_cleanup_*/` - Move to backup location
- `test_*/` directories - Move to tests/ directory
- `cli_test_*/` - Move to tests/ directory

## Cleanup Commands

```bash
# Create backup directory
mkdir -p backup/$(date +%Y%m%d)

# Move test files
mkdir -p tests
mv test_*.* tests/
mv test_*/ tests/

# Move old files
mv archived_*/ backup/$(date +%Y%m%d)/
mv backup_before_*/ backup/$(date +%Y%m%d)/
mv src/*_old.* backup/$(date +%Y%m%d)/
mv src/*_simple.* backup/$(date +%Y%m%d)/
mv src-tauri/src/*_backup.* backup/$(date +%Y%m%d)/

# Remove compiled files
rm -f *.exe *.pdb

# Clean build artifacts
cargo clean
rm -rf node_modules
rm -rf dist
```

## Code Cleanup Tasks

### 1. Remove Unused Imports
```bash
# Fix Rust warnings
cargo fix --allow-dirty
cargo fmt
```

### 2. Remove Dead Code
- Remove `greet` function in `main_module.rs`
- Remove `analyze_file` function if not used
- Remove `tauri_invoke_with_args` in frontend

### 3. Consolidate Duplicate Types
- Review data structures between frontend and backend
- Use shared types where possible

### 4. Update Dependencies
```bash
cargo update
npm update
```

## Project Structure After Cleanup

```
bad-character-scanner/
├── src/                    # Frontend (Leptos)
│   ├── components/         # UI components
│   └── lib.rs             # Main app
├── src-tauri/             # Backend (Tauri)
│   └── src/
│       ├── modules/       # Core modules
│       └── main.rs        # Entry point
├── assets/                # Data files
├── docs/                  # Documentation
├── tests/                 # Test files
├── reports/               # Generated reports
├── Cargo.toml
├── package.json
└── README.md
```

## Maintenance Tips

1. **Regular Cleanup**: Run cleanup monthly
2. **Test Organization**: Keep tests in `tests/` directory
3. **Documentation**: Update docs with major changes
4. **Version Control**: Tag releases before major cleanups
5. **Backup**: Always backup before cleanup