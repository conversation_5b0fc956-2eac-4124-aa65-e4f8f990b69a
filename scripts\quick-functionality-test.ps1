<#
.SYNOPSIS
    Quick functionality test for Bad Character Scanner
    
.DESCRIPTION
    Tests basic functionality across interfaces without building
#>

Write-Host "=== Bad Character Scanner - Quick Functionality Test ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if test data can be created
Write-Host "[Test 1] Creating test data with bad characters..." -ForegroundColor Yellow
$testContent = @"
Normal text here.
Zero-width characters: Hello$([char]0x200C)World$([char]0x200D)Test
Bidirectional override: User $([char]0x202E)Oliver$([char]0x202C) sent
Homoglyphs: Α<PERSON><PERSON> (Greek A) vs <PERSON> (Latin A)
"@

$testFile = "test-bad-chars.txt"
$testContent | Set-Content $testFile -Encoding UTF8
Write-Host "[OK] Test file created" -ForegroundColor Green

# Test 2: PowerShell detection
Write-Host ""
Write-Host "[Test 2] Testing PowerShell bad character detection..." -ForegroundColor Yellow
$content = Get-Content $testFile -Raw
$hasZeroWidth = $content -match '\u200C|\u200D'
$hasRTL = $content -match '\u202E|\u202C'

if ($hasZeroWidth) {
    Write-Host "[OK] Zero-width characters detected" -ForegroundColor Green
} else {
    Write-Host "[FAIL] Zero-width characters NOT detected" -ForegroundColor Red
}

if ($hasRTL) {
    Write-Host "[OK] RTL override characters detected" -ForegroundColor Green
} else {
    Write-Host "[FAIL] RTL override characters NOT detected" -ForegroundColor Red
}

# Test 3: Check GUI is running
Write-Host ""
Write-Host "[Test 3] Checking if GUI is accessible..." -ForegroundColor Yellow
$guiUrl = "http://localhost:1420"
try {
    $response = Invoke-WebRequest -Uri $guiUrl -TimeoutSec 2 -ErrorAction Stop
    Write-Host "[OK] GUI is running at $guiUrl" -ForegroundColor Green
} catch {
    Write-Host "[WARN] GUI is not running (expected if not in dev mode)" -ForegroundColor Yellow
}

# Test 4: Check CLI binary exists
Write-Host ""
Write-Host "[Test 4] Checking for CLI binary..." -ForegroundColor Yellow
$releaseBinary = "./target/release/analyzer_cli.exe"
$debugBinary = "./target/debug/analyzer_cli.exe"

if (Test-Path $releaseBinary) {
    Write-Host "[OK] Release CLI binary found" -ForegroundColor Green
    
    # Try running it
    Write-Host "  Testing CLI with bad character file..." -ForegroundColor Cyan
    & $releaseBinary analyze $testFile
} elseif (Test-Path $debugBinary) {
    Write-Host "[OK] Debug CLI binary found" -ForegroundColor Yellow
    
    # Try running it
    Write-Host "  Testing CLI with bad character file..." -ForegroundColor Cyan
    & $debugBinary analyze $testFile
} else {
    Write-Host "[FAIL] CLI binary not found (needs to be built)" -ForegroundColor Red
    Write-Host "  Run: cd src-tauri && cargo build --bin analyzer_cli" -ForegroundColor Gray
}

# Test 5: Check PowerShell wrapper scripts
Write-Host ""
Write-Host "[Test 5] Checking PowerShell wrapper scripts..." -ForegroundColor Yellow
$scripts = @(
    "./scripts/analyze-text.ps1",
    "./scripts/clean-file.ps1",
    "./scripts/scan-codebase.ps1"
)

foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "[OK] Found: $script" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Missing: $script" -ForegroundColor Red
    }
}

# Cleanup
Write-Host ""
Write-Host "Cleaning up test files..." -ForegroundColor Gray
Remove-Item $testFile -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Basic functionality tests completed." -ForegroundColor White
Write-Host "Check above for any X marks indicating issues." -ForegroundColor White