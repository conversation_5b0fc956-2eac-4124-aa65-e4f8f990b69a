# TICKET: Fix Export Button Application Restart Issue

**Ticket ID**: BUG-EXPORT-001  
**Title**: Export Button Causes Unwanted Application Restart Due to File Watcher  
**Type**: Bug Fix  
**Priority**: P1 - Critical  
**Severity**: High  
**Status**: Open  
**Assignee**: Backend Team  
**Reporter**: Development Team  
**Created**: June 15, 2025  

## Problem Statement

**User Impact**: When users click Export buttons in the GUI, the export succeeds but the application immediately restarts, causing users to lose their current analysis session and creating the impression of application instability.

**Root Cause**: Export files are saved to `src-tauri/reports/` directory, which is monitored by <PERSON><PERSON>'s development file watcher. New file creation triggers automatic application rebuild and restart.

## User Story

**As a** user performing character analysis  
**I want to** export my results without losing my current session  
**So that** I can save multiple export formats and continue working seamlessly  

## Acceptance Criteria

### Must Have:
- [ ] Export buttons work without causing application restart
- [ ] Export files are saved to appropriate user-accessible location
- [ ] All export formats (JSON, HTML, CSV) work correctly
- [ ] Success messages display properly after export
- [ ] User session persists after export operations

### Should Have:
- [ ] Export directory is outside source code tree
- [ ] File paths are easily accessible to users
- [ ] Proper error handling for file permission issues
- [ ] Cross-platform compatibility (Windows, macOS, Linux)

### Could Have:
- [ ] User-configurable export directory
- [ ] Export history/management interface
- [ ] File watcher exclusion configuration

## Technical Requirements

### Implementation Details:

**Current Problematic Code** (src-tauri/src/main_module.rs, line ~340):
```rust
let reports_dir = project_root.join("src-tauri").join("reports");
```

**Proposed Fix**:
```rust
// Use user's Documents directory or fallback to temp
let reports_dir = dirs::document_dir()
    .unwrap_or_else(|| std::env::temp_dir())
    .join("BadCharacterScanner")
    .join("Reports");
```

### Dependencies Required:
- Add `dirs` crate to Cargo.toml for cross-platform directory handling
- Ensure proper permissions for directory creation

### Files to Modify:
1. **src-tauri/src/main_module.rs** - Update export directory logic
2. **src-tauri/Cargo.toml** - Add dirs dependency
3. **Documentation** - Update file location references

## Definition of Done

### Functional:
- [ ] All export formats work without application restart
- [ ] Files are saved to user-accessible location outside source tree
- [ ] Export success messages display correctly
- [ ] No regression in export functionality

### Technical:
- [ ] Code follows project standards and patterns
- [ ] Cross-platform compatibility verified
- [ ] Proper error handling implemented
- [ ] Unit tests updated/added for export functionality

### Testing:
- [ ] Manual testing in development mode (`cargo tauri dev`)
- [ ] Manual testing in production build
- [ ] Automated tests for export directory logic
- [ ] Cross-platform testing (Windows/macOS/Linux)

### Documentation:
- [ ] User documentation updated with new export file location
- [ ] Developer documentation updated
- [ ] README updated if necessary

## Testing Strategy

### Manual Testing:
1. **Pre-fix Verification**: Confirm issue exists in current version
2. **Post-fix Testing**: Verify export works without restart
3. **Format Testing**: Test all export formats (JSON, HTML, CSV)
4. **Path Testing**: Verify files are created in correct location
5. **Permission Testing**: Test on systems with restricted permissions

### Automated Testing:
1. **Unit Tests**: Test export directory logic
2. **Integration Tests**: Test full export workflow
3. **Platform Tests**: Verify behavior across operating systems

## Implementation Plan

### Phase 1: Core Fix (Sprint 1)
- [ ] Add `dirs` dependency to Cargo.toml
- [ ] Update export directory logic in main_module.rs
- [ ] Test basic functionality in development mode
- [ ] Verify no application restart occurs

### Phase 2: Polish & Testing (Sprint 1)
- [ ] Add proper error handling for directory creation
- [ ] Implement cross-platform path handling
- [ ] Add unit tests for new directory logic
- [ ] Update user-facing documentation

### Phase 3: Enhancement (Sprint 2)
- [ ] Consider user-configurable export paths
- [ ] Add export history/management features
- [ ] Implement file watcher exclusions
- [ ] Performance optimization if needed

## Risk Assessment

### Low Risk:
- **Directory permissions**: Standard user Documents access should work
- **Cross-platform**: `dirs` crate handles platform differences well
- **Backward compatibility**: No breaking changes to export API

### Medium Risk:
- **User confusion**: Export location change may confuse existing users
- **File access**: Users may not know where files are saved

### Mitigation:
- Clear success messages with file paths
- Documentation updates
- Consider showing file location in GUI

## Related Issues

- **File Watcher Configuration**: May want to exclude export directories
- **Export Management**: Future enhancement for better file organization
- **Session Persistence**: Consider preserving state across restarts

## Success Metrics

### User Experience:
- **Zero application restarts** during export operations
- **100% export success rate** without session loss
- **Positive user feedback** on export reliability

### Technical:
- **No file watcher conflicts** with export operations
- **Cross-platform compatibility** verified
- **Proper error handling** for edge cases

## Notes

### Technical Considerations:
- Current issue only affects development mode (`cargo tauri dev`)
- Production builds may not exhibit this behavior
- File watcher is essential for development workflow

### User Communication:
- Consider release notes explaining export location change
- Update help documentation with new file paths
- Provide clear success messages with file locations

---

**Estimated Effort**: 2-3 days  
**Sprint**: Current  
**Epic**: Core Functionality Stability  
**Labels**: bug, P1, backend, export, file-system  

**Next Action**: Assign to backend developer for immediate implementation
