# 🚨 Emergency Diagnostic Script for Bad Character Scanner
# Run this when things go wrong to get a comprehensive system health check

Write-Host "🔍 BAD CHARACTER SCANNER - EMERGENCY DIAGNOSTIC" -ForegroundColor Red -BackgroundColor White
Write-Host "================================================" -ForegroundColor Red
Write-Host "Starting comprehensive system analysis..." -ForegroundColor Yellow
Write-Host ""

# Initialize counters
$critical_issues = 0
$warnings = 0
$successes = 0

# Function to report issues
function Report-Issue {
    param($Level, $Message, $Solution = "")
    
    switch ($Level) {
        "CRITICAL" { 
            Write-Host "❌ CRITICAL: $Message" -ForegroundColor Red
            if ($Solution) { Write-Host "   💡 Solution: $Solution" -ForegroundColor Yellow }
            $script:critical_issues++
        }
        "WARNING" { 
            Write-Host "⚠️  WARNING: $Message" -ForegroundColor Yellow
            if ($Solution) { Write-Host "   💡 Suggestion: $Solution" -ForegroundColor Cyan }
            $script:warnings++
        }
        "SUCCESS" { 
            Write-Host "✅ SUCCESS: $Message" -ForegroundColor Green
            $script:successes++
        }
    }
}

# 1. CRITICAL FILES CHECK
Write-Host "📋 1. CRITICAL FILES CHECK" -ForegroundColor Cyan
Write-Host "--------------------------" -ForegroundColor Cyan

$critical_files = @(
    @{Path="assets/Bad_Characters.json"; MinSize=10000; Description="Main threat database"},
    @{Path="assets/Advanced_AI_Patterns.json"; MinSize=5000; Description="AI detection patterns"},
    @{Path="assets/file-types-schema.json"; MinSize=1000; Description="File type definitions"},
    @{Path="assets/FileTypesSummary.json"; MinSize=1000; Description="File processing metadata"},
    @{Path="src-tauri/Cargo.toml"; MinSize=500; Description="Rust project configuration"},
    @{Path="package.json"; MinSize=200; Description="Node.js project configuration"},
    @{Path="tauri.config.json"; MinSize=1000; Description="Tauri application configuration"}
)

foreach ($file in $critical_files) {
    if (Test-Path $file.Path) {
        $size = (Get-Item $file.Path).Length
        if ($size -ge $file.MinSize) {
            Report-Issue "SUCCESS" "$($file.Path) exists ($size bytes) - $($file.Description)"
        } else {
            Report-Issue "WARNING" "$($file.Path) exists but is small ($size bytes)" "Check if file is complete"
        }
    } else {
        Report-Issue "CRITICAL" "$($file.Path) missing - $($file.Description)" "Restore from git or recreate"
    }
}

Write-Host ""

# 2. ASSET INTEGRITY CHECK
Write-Host "📄 2. ASSET INTEGRITY CHECK" -ForegroundColor Cyan
Write-Host "---------------------------" -ForegroundColor Cyan

$json_files = Get-ChildItem "assets/*.json" -ErrorAction SilentlyContinue
if ($json_files) {
    foreach ($json in $json_files) {
        try {
            $content = Get-Content $json.FullName -Raw | ConvertFrom-Json -ErrorAction Stop
            $size = (Get-Item $json.FullName).Length
            
            # Special validation for Bad_Characters.json
            if ($json.Name -eq "Bad_Characters.json") {
                if ($content.forbidden_chars -and $content.invisible_chars) {
                    Report-Issue "SUCCESS" "$($json.Name) - Valid JSON with required fields ($size bytes)"
                } else {
                    Report-Issue "CRITICAL" "$($json.Name) - Missing required fields (forbidden_chars, invisible_chars)" "Restore from backup"
                }
            } else {
                Report-Issue "SUCCESS" "$($json.Name) - Valid JSON ($size bytes)"
            }
        }
        catch {
            Report-Issue "CRITICAL" "$($json.Name) - CORRUPTED JSON!" "Run assets/fix_json.ps1 or restore from git"
        }
    }
} else {
    Report-Issue "CRITICAL" "No JSON files found in assets folder" "Check if assets folder exists and contains JSON files"
}

Write-Host ""

# 3. DEVELOPMENT ENVIRONMENT CHECK
Write-Host "🛠️  3. DEVELOPMENT ENVIRONMENT CHECK" -ForegroundColor Cyan
Write-Host "------------------------------------" -ForegroundColor Cyan

# Rust
try {
    $rust_version = rustc --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Report-Issue "SUCCESS" "Rust installed: $rust_version"
    } else {
        Report-Issue "CRITICAL" "Rust not found" "Install from https://rustup.rs/"
    }
} catch {
    Report-Issue "CRITICAL" "Rust not accessible" "Install from https://rustup.rs/"
}

# Cargo
try {
    $cargo_version = cargo --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Report-Issue "SUCCESS" "Cargo installed: $cargo_version"
    } else {
        Report-Issue "CRITICAL" "Cargo not found" "Cargo comes with Rust installation"
    }
} catch {
    Report-Issue "CRITICAL" "Cargo not accessible" "Reinstall Rust from https://rustup.rs/"
}

# Node.js
try {
    $node_version = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        $version_num = [version]($node_version -replace 'v','')
        if ($version_num -ge [version]"18.0.0") {
            Report-Issue "SUCCESS" "Node.js installed: $node_version"
        } else {
            Report-Issue "WARNING" "Node.js version $node_version is old" "Update to Node.js 18+ from https://nodejs.org/"
        }
    } else {
        Report-Issue "CRITICAL" "Node.js not found" "Install from https://nodejs.org/"
    }
} catch {
    Report-Issue "CRITICAL" "Node.js not accessible" "Install from https://nodejs.org/"
}

# Tauri CLI
try {
    $tauri_version = cargo tauri --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Report-Issue "SUCCESS" "Tauri CLI installed: $tauri_version"
    } else {
        Report-Issue "CRITICAL" "Tauri CLI not found" "Install with: cargo install tauri-cli"
    }
} catch {
    Report-Issue "CRITICAL" "Tauri CLI not accessible" "Install with: cargo install tauri-cli"
}

Write-Host ""

# 4. BUILD SYSTEM CHECK
Write-Host "🔧 4. BUILD SYSTEM CHECK" -ForegroundColor Cyan
Write-Host "------------------------" -ForegroundColor Cyan

# Check if code compiles
Write-Host "Checking if Rust code compiles..." -ForegroundColor White
try {
    $build_output = cargo check --quiet 2>&1
    if ($LASTEXITCODE -eq 0) {
        Report-Issue "SUCCESS" "Rust code compiles without errors"
    } else {
        Report-Issue "CRITICAL" "Rust compilation errors detected" "Run 'cargo check' to see detailed errors"
        Write-Host "Build errors preview:" -ForegroundColor Red
        Write-Host ($build_output | Select-Object -First 5) -ForegroundColor Red
    }
} catch {
    Report-Issue "CRITICAL" "Cannot run cargo check" "Check Rust installation"
}

# Check for target directory bloat
if (Test-Path "target/") {
    try {
        $target_size = (Get-ChildItem "target/" -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
        if ($target_size -gt 2000) {
            Report-Issue "WARNING" "Target directory is very large" "Consider running cargo clean"
        } else {
            Report-Issue "SUCCESS" "Target directory size is reasonable"
        }
    } catch {
        Report-Issue "WARNING" "Cannot check target directory size" "This is usually not critical"
    }
}

# Check for package.json and node_modules
if (Test-Path "package.json") {
    if (Test-Path "node_modules/") {
        Report-Issue "SUCCESS" "Node.js dependencies installed"
    } else {
        Report-Issue "WARNING" "Node.js dependencies not installed" "Run 'npm install'"
    }
} else {
    Report-Issue "WARNING" "No package.json found" "This may be normal for pure Rust projects"
}

Write-Host ""

# 5. RUNTIME ENVIRONMENT CHECK
Write-Host "🚀 5. RUNTIME ENVIRONMENT CHECK" -ForegroundColor Cyan
Write-Host "-------------------------------" -ForegroundColor Cyan

# Check port availability
try {
    $port_1420 = Get-NetTCPConnection -LocalPort 1420 -ErrorAction SilentlyContinue
    if ($port_1420) {
        Report-Issue "WARNING" "Port 1420 is in use" "Kill the existing process or change port in tauri.config.json"
    } else {
        Report-Issue "SUCCESS" "Port 1420 is available"
    }
} catch {
    Report-Issue "SUCCESS" "Port check completed (PowerShell limitation on some systems)"
}

# Check current directory
$current_dir = Get-Location
$expected_files = @("Cargo.toml", "assets", "src-tauri")
$found_files = 0
foreach ($file in $expected_files) {
    if (Test-Path $file) { $found_files++ }
}

if ($found_files -eq $expected_files.Count) {
    Report-Issue "SUCCESS" "Working directory appears correct: $current_dir"
} else {
    Report-Issue "WARNING" "Working directory may be incorrect" "Make sure you're in the project root directory"
}

# Check available memory
try {
    $memory = Get-CimInstance -ClassName Win32_OperatingSystem
    $free_memory_gb = [math]::Round($memory.FreePhysicalMemory / 1MB, 1)
    if ($free_memory_gb -gt 1) {
        Report-Issue "SUCCESS" "Sufficient free memory: ${free_memory_gb}GB"
    } else {
        Report-Issue "WARNING" "Low free memory: ${free_memory_gb}GB" "Close other applications"
    }
} catch {
    Report-Issue "WARNING" "Cannot check memory status" "Ensure you have at least 1GB free RAM"
}

Write-Host ""

# 6. SUMMARY AND RECOMMENDATIONS
Write-Host "📊 6. SUMMARY AND RECOMMENDATIONS" -ForegroundColor Cyan
Write-Host "---------------------------------" -ForegroundColor Cyan

Write-Host "Results Summary:" -ForegroundColor White
Write-Host "• ✅ Successes: $successes" -ForegroundColor Green
Write-Host "• ⚠️  Warnings: $warnings" -ForegroundColor Yellow  
Write-Host "• ❌ Critical Issues: $critical_issues" -ForegroundColor Red

Write-Host ""

if ($critical_issues -eq 0 -and $warnings -eq 0) {
    Write-Host "🎉 EXCELLENT! System appears healthy." -ForegroundColor Green -BackgroundColor Black
    Write-Host "You should be able to run the application successfully." -ForegroundColor Green
    Write-Host ""
    Write-Host "Suggested next steps:" -ForegroundColor Cyan
    Write-Host "1. Try running: cargo tauri dev" -ForegroundColor White
    Write-Host "2. Or use the startup script: .\dev_startup.ps1" -ForegroundColor White
}
elseif ($critical_issues -eq 0) {
    Write-Host "👍 GOOD! No critical issues found." -ForegroundColor Green
    Write-Host "You have $warnings warnings that should be addressed but are not blocking." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Suggested next steps:" -ForegroundColor Cyan
    Write-Host "1. Address the warnings above" -ForegroundColor White
    Write-Host "2. Try running: cargo tauri dev" -ForegroundColor White
}
else {
    Write-Host "🚨 ISSUES DETECTED! $critical_issues critical issues must be fixed." -ForegroundColor Red -BackgroundColor Yellow
    Write-Host "The application will not work until these are resolved." -ForegroundColor Red
    Write-Host ""
    Write-Host "Suggested next steps:" -ForegroundColor Cyan
    Write-Host "1. Fix all critical issues listed above" -ForegroundColor White
    Write-Host "2. Re-run this diagnostic script" -ForegroundColor White
    Write-Host "3. Check the comprehensive debugging guide: docs/COMPREHENSIVE_DEBUGGING_GUIDE.md" -ForegroundColor White
}

Write-Host ""
Write-Host "📝 For detailed troubleshooting help, see:" -ForegroundColor Cyan
Write-Host "• docs/COMPREHENSIVE_DEBUGGING_GUIDE.md" -ForegroundColor White
Write-Host "• docs/guides/QUICK_FIX_GUIDE.md" -ForegroundColor White
Write-Host "• docs/ASSET_FOLDER_CRITICAL_GUIDE.md" -ForegroundColor White

Write-Host ""
Write-Host "Diagnostic completed at $(Get-Date)" -ForegroundColor Gray
Write-Host "Script location: $PSScriptRoot" -ForegroundColor Gray
