# Rustfmt configuration
edition = "2021"
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Max"

# Imports formatting
imports_layout = "Vertical"
imports_granularity = "Crate"
reorder_imports = true

# Control where the ... in a closure is put
fn_args_layout = "Compressed"

# Control where the + is put in a bound
type_punctuation_density = "Wide"

# Control where the + is put in a where clause
where_single_line = false

# Control how the match arm block is formatted
match_block_trailing_comma = true
match_arm_blocks_preserve_parens = true

# Control how the if-else is formatted
control_brace_style = "AlwaysSameLine"

# Control how the chain is formatted
chain_width = 60

# Control how the match is formatted
match_arm_leading_pipes = "Never"

# Control how the where clause is formatted
where_pred_indent = "Block"

# Control how the struct is formatted
struct_lit_single_line = true

# Control how the function is formatted
fn_call_style = "Block"

# Control how the comment is formatted
wrap_comments = true
comment_width = 80
normalize_comments = true
normalize_doc_attributes = true
format_code_in_doc_comments = true
