fn main() {
    let x = "Contains a potentially problematic character: ©";
    println!("{}", x);
    // TODO: Add more complex code here
    // FIXME: This needs fixing
    // NOTE: A note for consideration
    // REVIEW: Review this section
    // HACK: A temporary hack
    // BUG: Known bug here
    // UNDONE: Not yet finished
    // XXX: Risky code section
}

struct MyStruct {
    field_with_bad_char: String, // Example: field containing 'µ'
}

fn another_function() {
    // This function uses a zero-width space: ​
    let y = "hello​world";
    println!("Text with zero-width space: {}", y);
}
