#!/usr/bin/env powershell
# Simple ticket manager for accessibility project

Write-Host "Accessibility Project Ticket Manager" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent
$tickets_dir = Join-Path $project_root "docs\project-management\tickets"

function Show-TicketSummary {
    Write-Host "`nTicket System Overview" -ForegroundColor Yellow
    Write-Host "=====================" -ForegroundColor Yellow
    
    if (-not (Test-Path $tickets_dir)) {
        Write-Host "Tickets directory not found: $tickets_dir" -ForegroundColor Red
        return
    }
    
    $tickets = Get-ChildItem $tickets_dir -Filter "*.md" | Sort-Object Name
    $categories = @{}
    
    foreach ($ticket in $tickets) {
        $content = Get-Content $ticket.FullName -Raw
        $category = "OTHER"
        if ($ticket.BaseName -match "^([A-Z]+)") {
            $category = $matches[1]
        }
        $categories[$category] = ($categories[$category] + 1)
    }
    
    Write-Host "Total Tickets: $($tickets.Count)" -ForegroundColor Green
    
    Write-Host "`nBy Category:" -ForegroundColor Yellow
    foreach ($cat in $categories.Keys | Sort-Object) {
        Write-Host "   $cat : $($categories[$cat])" -ForegroundColor White
    }
}

function Show-HighPriorityTickets {
    Write-Host "`nHigh Priority Tickets" -ForegroundColor Red
    Write-Host "====================" -ForegroundColor Red
    
    $tickets = Get-ChildItem $tickets_dir -Filter "*.md" | Sort-Object Name
    $found = $false
    
    foreach ($ticket in $tickets) {
        $content = Get-Content $ticket.FullName -Raw
        if ($content -match "Priority.*HIGH" -and $content -notmatch "Status.*RESOLVED") {
            $found = $true
            Write-Host "`n$($ticket.BaseName)" -ForegroundColor Red
            
            if ($content -match "^#\s*(.+)") {
                Write-Host "   $($matches[1])" -ForegroundColor White
            }
        }
    }
    
    if (-not $found) {
        Write-Host "No high priority tickets found!" -ForegroundColor Green
    }
}

# Command line interface
param(
    [string]$Action = "summary"
)

switch ($Action.ToLower()) {
    "summary" { 
        Show-TicketSummary 
    }
    "high" { 
        Show-HighPriorityTickets 
    }
    "help" {
        Write-Host "Ticket Manager Commands" -ForegroundColor Cyan
        Write-Host "======================" -ForegroundColor Cyan
        Write-Host ".\scripts\ticket-manager-simple.ps1                    # Show summary" -ForegroundColor White
        Write-Host ".\scripts\ticket-manager-simple.ps1 -Action high       # Show high priority" -ForegroundColor White
    }
    default {
        Write-Host "Unknown action: $Action" -ForegroundColor Red
        Write-Host "Use -Action help for available commands" -ForegroundColor Yellow
    }
}

Write-Host "`nFighting for accessibility!" -ForegroundColor Green
