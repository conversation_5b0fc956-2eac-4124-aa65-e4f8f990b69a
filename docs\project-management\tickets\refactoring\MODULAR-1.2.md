# MODULAR-1.2 - Extract Services and Business Logic

**Status:** 🟡 Ready  
**Priority:** P2 (Medium)  
**Type:** 🔧 Refactoring  
**Created:** 2025-06-12  
**Estimated Effort:** 2-3 hours  
**Parent Ticket:** MODULAR-1 (Overall Modularization)

## 🎯 Problem Statement

Business logic and Tauri command wrappers are mixed with UI components in lib.rs. Extracting these into service modules will improve separation of concerns and testability.

## 🔍 Current Issues

- Business logic mixed with UI components
- Tauri command invocations scattered throughout components
- Difficult to unit test business logic
- No clear separation between data access and UI

## ✅ Acceptance Criteria

- [ ] Create `src/services/` module structure
- [ ] Extract all Tauri command wrappers
- [ ] Extract business logic from UI components
- [ ] Maintain all existing functionality
- [ ] Improve testability of business logic

## 🔧 Implementation Tasks

### 1. Create Service Module Structure
```
src/services/
├── mod.rs               # Module declarations
├── tauri_commands.rs    # Tauri command wrappers
└── analysis_service.rs  # Analysis business logic
```

### 2. Extract Tauri Command Wrappers

#### services/tauri_commands.rs
```rust
use crate::types::{AnalysisResults, CodeBaseAnalysisResult};
use leptos::*;
use wasm_bindgen::prelude::*;

#[wasm_bindgen(module = "/public/tauri.js")]
extern "C" {
    #[wasm_bindgen(js_name = "invoke")]
    fn invoke(command: &str, args: JsValue) -> js_sys::Promise;
}

pub async fn analyze_characters(text: String) -> Result<AnalysisResults, String> {
    // Wrapper for analyze_characters command
}

pub async fn analyze_codebase(path: String) -> Result<CodeBaseAnalysisResult, String> {
    // Wrapper for analyze_codebase command
}

pub async fn select_folder() -> Result<Option<String>, String> {
    // Wrapper for folder selection
}
```

### 3. Extract Analysis Business Logic

#### services/analysis_service.rs
```rust
use crate::types::{AnalysisResults, CharacterInfo};

pub struct AnalysisService;

impl AnalysisService {
    pub fn format_results(results: &AnalysisResults) -> String {
        // Format analysis results for display
    }
    
    pub fn filter_bad_characters(results: &AnalysisResults, severity: &str) -> Vec<CharacterInfo> {
        // Filter characters by severity
    }
    
    pub fn generate_summary(results: &AnalysisResults) -> String {
        // Generate summary text
    }
}
```

### 4. Update Components to Use Services
- [ ] Update HomePage to use service layer
- [ ] Remove direct Tauri invocations from components
- [ ] Update all components to use analysis service
- [ ] Maintain existing component interfaces

## 🧪 Testing Plan

- [ ] **Service Unit Tests**: Test service methods independently
- [ ] **Integration Tests**: Test service integration with components
- [ ] **Command Tests**: Test Tauri command wrappers
- [ ] **Functionality Tests**: All features work as before

## 📊 Success Metrics

- Components focus only on UI concerns
- Business logic is easily testable
- Tauri commands are centralized and consistent
- lib.rs is significantly smaller and cleaner

## 🔗 Related Tickets

- **Parent**: MODULAR-1 (Overall modularization plan)
- **Depends On**: MODULAR-1.1 (Extract Data Types)
- **Blocks**: MODULAR-1.3 (Extract UI Components)

## 💡 Implementation Notes

### Service Design Principles
- **Single Responsibility**: Each service has one clear purpose
- **Testability**: Services are easy to unit test
- **Consistency**: All Tauri interactions go through services
- **Error Handling**: Services handle errors gracefully

### Migration Strategy
1. Create service modules
2. Move one service at a time
3. Update components incrementally
4. Test after each migration step

---

**Created**: 2025-06-12  
**Focus**: Clean separation of business logic and data access  
**Impact**: Improves testability and maintainability
