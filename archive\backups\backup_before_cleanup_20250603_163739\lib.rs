use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::spawn_local;
use chrono::{DateTime, Utc};

// Console error panic hook for better debugging
use console_error_panic_hook::set_once as set_panic_hook;

// Tauri command bindings
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

// Helper functions for Tauri commands
async fn tauri_invoke_no_args(cmd: &str) -> Result<JsValue, JsValue> {
    let args = JsValue::NULL;
    Ok(invoke(cmd, args).await)
}

async fn tauri_invoke_with_args<T: Serialize>(cmd: &str, args: &T) -> Result<JsValue, JsValue> {
    let args_js = serde_wasm_bindgen::to_value(args)?;
    Ok(invoke(cmd, args_js).await)
}

// Data structures matching the backend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub unicode_block: String,
    pub category: String,
    pub codepoint: u32,
    pub utf8_bytes: Vec<u8>,
    pub utf16_units: Vec<u16>,
    pub is_suspicious: bool,
    pub suspicion_reasons: Vec<String>,
    pub recommendations: Vec<String>,
    pub visual_width: usize,
    pub is_combining: bool,
    pub is_emoji: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub total_characters: usize,
    pub total_bytes: usize,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub encoding_info: EncodingInfo,
    pub security_analysis: SecurityAnalysis,
    pub recommendations: Vec<String>,
    pub analysis_timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub health_score: f64,
    pub file_details: Vec<FileAnalysisDetail>,
    pub analysis_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisDetail {
    pub file_path: String,
    pub relative_path: String,
    pub file_size: u64,
    pub total_characters: usize,
    pub suspicious_characters: usize,
    pub issues: Vec<String>,
    pub file_type: String,
    pub encoding: String,
    pub analysis_status: String, // "success", "error", "skipped"
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub risk_level: String,
    pub potential_threats: Vec<String>,
    pub mitigation_suggestions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingInfo {
    pub detected_encoding: String,
    pub confidence: f32,
    pub bom_detected: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ProgressPayload {
    pub current: u32,
    pub total: u32,
    pub message: Option<String>,
    pub operation_id: String,
    pub percentage: f32,
    pub stage: Option<String>,
}

// Post-Cleaning Verification structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostCleaningVerification {
    pub original_analysis: CodeBaseAnalysisResult,
    pub post_cleaning_analysis: CodeBaseAnalysisResult,
    pub cleaning_effectiveness: CleaningEffectiveness,
    pub remaining_issues: Vec<RemainingIssue>,
    pub verification_time_ms: u64,
    pub cleaning_summary: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CleaningEffectiveness {
    pub total_original_issues: u32,
    pub total_cleaned_issues: u32,
    pub success_rate_percentage: f32,
    pub files_fully_cleaned: u32,
    pub files_partially_cleaned: u32,
    pub files_with_errors: u32,
    pub total_files_processed: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemainingIssue {
    pub file_path: String,
    pub line_number: usize,
    pub column_number: usize,
    pub character: char,
    pub character_name: String,
    pub unicode_point: String,
    pub reason_not_cleaned: String,
    pub code_snippet: String,
    pub suggested_action: String,
}

// Warning popup user preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WarningPopupPreferences {
    pub show_post_cleaning_warnings: bool,
    pub show_security_disclaimers: bool,
    pub auto_close_after_seconds: u32,
    pub warning_level_threshold: String,
}

// Enhanced UI structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFolder {
    pub path: String,
    pub name: String,
    pub last_accessed: String,
    pub file_count: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FolderInfo {
    pub path: String,
    pub exists: bool,
    pub readable: bool,
    pub file_count: Option<usize>,
    pub total_size: Option<u64>,
    pub error_message: Option<String>,
}

// Interface state machine for dynamic UX
#[derive(Clone, Debug, PartialEq)]
enum InterfaceState {
    SelectionMode,  // Default state - all selection methods visible
    ActionsMode,    // Folder selected - show actions, collapse selection methods
    ProcessingMode, // Operation in progress - show progress, disable actions
}

#[component]
pub fn App() -> impl IntoView {    // Initialize console error panic hook
    set_panic_hook();

    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Leptos Tauri v2 - Bad Character Scanner"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>

        <Router>
            <Routes>
                <Route path="" view=HomePage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    // State management
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_results, set_analysis_results) = create_signal(Option::<AnalysisResults>::None);
    let (codebase_results, set_codebase_results) = create_signal(Option::<CodeBaseAnalysisResult>::None);
    let (selected_folder, set_selected_folder) = create_signal(Option::<String>::None);
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    let (error_message, set_error_message) = create_signal(Option::<String>::None);
    let (current_tab, set_current_tab) = create_signal("text".to_string());
    let (analysis_progress, set_analysis_progress) = create_signal(Option::<ProgressPayload>::None);
    let (cleaning_progress, set_cleaning_progress) = create_signal(Option::<ProgressPayload>::None); // Added for cleaning

    // New state for post-cleaning verification
    let (post_cleaning_verification, set_post_cleaning_verification) = create_signal(Option::<PostCleaningVerification>::None);
    let (verification_progress, set_verification_progress) = create_signal(Option::<ProgressPayload>::None);
    let (is_cleaning, set_is_cleaning) = create_signal(false);
    let (cleaning_in_progress, set_cleaning_in_progress) = create_signal(false);
    
    // Warning popup state
    let (show_warning_popup, set_show_warning_popup) = create_signal(false);
    let (warning_popup_preferences, set_warning_popup_preferences) = create_signal(WarningPopupPreferences {
        show_post_cleaning_warnings: true,
        show_security_disclaimers: true,
        auto_close_after_seconds: 10,
        warning_level_threshold: "medium".to_string(),
    });
    let (warning_popup_content, set_warning_popup_content) = create_signal(Option::<String>::None);

    // Export functionality state
    let (export_in_progress, set_export_in_progress) = create_signal(false);
    let (export_success_message, set_export_success_message) = create_signal(Option::<String>::None);

    // Enhanced interface state signals
    let (interface_state, set_interface_state) = create_signal(InterfaceState::SelectionMode);
    let (path_input, set_path_input) = create_signal(String::new());
    let (path_validation, set_path_validation) = create_signal(Option::<FolderInfo>::None);
    let (recent_folders, set_recent_folders) = create_signal(Vec::<RecentFolder>::new());
    let (quick_access_folders, set_quick_access_folders) = create_signal(Vec::<FolderInfo>::new());
    let (show_recent_dropdown, set_show_recent_dropdown) = create_signal(false);
    let (validating_path, set_validating_path) = create_signal(false);
    
    // Drag & Drop state
    let (is_drag_over, set_is_drag_over) = create_signal(false);
    let (drag_counter, set_drag_counter) = create_signal(0);    // Effect for handling Tauri progress events (only for non-WASM builds)
    // Note: For WASM builds, we'll handle progress updates through direct command responses
    #[cfg(not(target_arch = "wasm32"))]
    create_effect(move |_| {
        // Event listening would go here for native Tauri builds
        // For now, we'll use polling or direct command responses for progress updates
        log::info!("Progress tracking initialized for non-WASM builds");
    });

    // Analyze text using Tauri backend
    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_error_message.set(Some("Please enter some text to analyze.".to_string()));
            return;
        }

        set_is_analyzing.set(true);
        set_error_message.set(None);

        spawn_local(async move {
            let args = serde_json::json!({ "text": text });
            match tauri_invoke_with_args("analyze_characters", &args).await {
                Ok(result) => {
                    match serde_wasm_bindgen::from_value::<AnalysisResults>(result) {
                        Ok(analysis) => {
                            set_analysis_results.set(Some(analysis));
                        }
                        Err(e) => {
                            set_error_message.set(Some(format!("Failed to parse analysis results: {:?}", e)));
                        }
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Analysis failed: {:?}", e)));
                }
            }
            set_is_analyzing.set(false);
        });
    };

    // Select folder for codebase analysis
    let select_folder = move |_| {
        spawn_local(async move {
            match tauri_invoke_no_args("select_folder").await {
                Ok(result) => {
                    if let Ok(folder_path) = serde_wasm_bindgen::from_value::<Option<String>>(result) {
                        set_selected_folder.set(folder_path);
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Failed to select folder: {:?}", e)));
                }
            }
        });
    };

    // Analyze codebase
    let analyze_codebase = move |_| {
        if let Some(folder_path) = selected_folder.get() {
            set_is_analyzing.set(true);
            set_error_message.set(None);
            set_analysis_progress.set(None);            spawn_local(async move {
                let args = serde_json::json!({ "folder_path": folder_path });
                match tauri_invoke_with_args("analyze_codebase", &args).await {
                    Ok(result) => {
                        match serde_wasm_bindgen::from_value::<CodeBaseAnalysisResult>(result) {
                            Ok(analysis) => {
                                set_codebase_results.set(Some(analysis));
                            }
                            Err(e) => {
                                set_error_message.set(Some(format!("Failed to parse codebase analysis: {:?}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Codebase analysis failed: {:?}", e)));
                    }
                }
                set_is_analyzing.set(false);
            });
        }
    };    // Clean codebase with verification
    let clean_codebase = move |_| {
        if let Some(folder_path) = selected_folder.get() {
            set_is_cleaning.set(true);
            set_cleaning_in_progress.set(true);
            set_error_message.set(None);
            set_cleaning_progress.set(None); // Reset cleaning progress
            set_verification_progress.set(None);
            set_post_cleaning_verification.set(None);

            // Generate output path for cleaned copy
            let folder_name = std::path::Path::new(&folder_path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("cleaned_folder");
            let parent_dir = std::path::Path::new(&folder_path).parent()
                .map(|p| p.to_string_lossy().to_string())
                .unwrap_or_else(|| ".".to_string());
            let output_path = format!("{}/{}_cleaned", parent_dir, folder_name);

            spawn_local(async move {                let args = serde_json::json!({ 
                    "folder_path": folder_path,
                    "output_path": output_path 
                });
                match tauri_invoke_with_args("clean_codebase_with_verification", &args).await {
                    Ok(result) => {
                        match serde_wasm_bindgen::from_value::<PostCleaningVerification>(result) {
                            Ok(verification) => {
                                set_post_cleaning_verification.set(Some(verification.clone()));
                                
                                // Show warning popup if enabled
                                if warning_popup_preferences.get().show_post_cleaning_warnings {
                                    let warning_content = format!(
                                        "Cleaning completed! {} out of {} issues were successfully cleaned ({:.1}% success rate).\n\n⚠️ Security Disclaimer: This tool provides basic character detection and cleaning. Always review changes manually and use additional security measures for sensitive code.",
                                        verification.cleaning_effectiveness.total_cleaned_issues,
                                        verification.cleaning_effectiveness.total_original_issues,
                                        verification.cleaning_effectiveness.success_rate_percentage
                                    );
                                    set_warning_popup_content.set(Some(warning_content));
                                    set_show_warning_popup.set(true);
                                }
                            }
                            Err(e) => {
                                set_error_message.set(Some(format!("Failed to parse cleaning verification: {:?}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Cleaning failed: {:?}", e)));
                    }
                }
                set_is_cleaning.set(false);
                set_cleaning_in_progress.set(false);
            });
        }
    };

    // Export codebase report
    let export_report = move |format: &str| {
        if let Some(results) = codebase_results.get() {
            let format = format.to_string();
            set_export_in_progress.set(true);
            set_export_success_message.set(None);
            set_error_message.set(None);

            spawn_local(async move {                let args = serde_json::json!({
                    "analysis_result": results,
                    "format": format,
                    "output_path": ""
                });
                match tauri_invoke_with_args("export_codebase_report", &args).await {
                    Ok(_) => {
                        set_export_success_message.set(Some(format!("Report exported successfully as {}", format.to_uppercase())));
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Export failed: {:?}", e)));
                    }
                }
                set_export_in_progress.set(false);
            });
        }
    };

    // Enhanced folder selection functions
    let select_folder_from_path = {
        let set_selected_folder = set_selected_folder.clone();
        let set_interface_state = set_interface_state.clone();
        let set_path_input = set_path_input.clone();
        let set_path_validation = set_path_validation.clone();
        let set_error_message = set_error_message.clone();
        move |path: String| {
            set_selected_folder.set(Some(path.clone()));
            set_interface_state.set(InterfaceState::ActionsMode);
            set_path_input.set(path.clone());
            set_path_validation.set(Some(FolderInfo {
                path: path.clone(),
                exists: true,
                readable: true,
                file_count: None,
                total_size: None,
                error_message: None,
            }));
            set_error_message.set(None);
            
            // Save to recent folders
            spawn_local(async move {
                let _ = tauri_invoke_with_args("save_recent_folder", &serde_json::json!({
                    "path": path
                })).await;
            });
        }
    };    let validate_path = {
        let set_validating_path = set_validating_path.clone();
        let set_path_validation = set_path_validation.clone();
        let set_error_message = set_error_message.clone();
        let select_folder_from_path = select_folder_from_path.clone();
        move |path: String| {
            if !path.trim().is_empty() {
                set_validating_path.set(true);
                let path_clone = path.clone();
                let select_folder_clone = select_folder_from_path.clone();
                spawn_local(async move {
                    match tauri_invoke_with_args("validate_folder_path", &serde_json::json!({
                        "path": path_clone
                    })).await {
                        Ok(result) => {
                            if let Ok(folder_info) = serde_wasm_bindgen::from_value::<FolderInfo>(result) {
                                set_path_validation.set(Some(folder_info.clone()));
                                // Auto-select if path is valid and readable
                                if folder_info.exists && folder_info.readable {
                                    select_folder_clone(path_clone);
                                }
                            }
                        }
                        Err(e) => {
                            set_error_message.set(Some(format!("Path validation failed: {:?}", e)));
                        }
                    }
                    set_validating_path.set(false);
                });
            }
        }
    };

    // Drag & Drop handlers
    let on_drag_over = move |ev: web_sys::DragEvent| {
        ev.prevent_default();
    };

    let on_drag_enter = {
        let set_drag_counter = set_drag_counter.clone();
        let set_is_drag_over = set_is_drag_over.clone();
        move |ev: web_sys::DragEvent| {
            ev.prevent_default();
            set_drag_counter.update(|c| *c += 1);
            set_is_drag_over.set(true);
        }
    };

    let on_drag_leave = {
        let set_drag_counter = set_drag_counter.clone();
        let set_is_drag_over = set_is_drag_over.clone();
        let drag_counter = drag_counter.clone();
        move |ev: web_sys::DragEvent| {
            ev.prevent_default();
            set_drag_counter.update(|c| *c -= 1);
            if drag_counter.get_untracked() == 0 {
                set_is_drag_over.set(false);
            }
        }
    };    let on_drop = {
        let set_drag_counter = set_drag_counter.clone();
        let set_is_drag_over = set_is_drag_over.clone();
        let select_folder_from_path = select_folder_from_path.clone();
        let set_error_message = set_error_message.clone();
        move |ev: web_sys::DragEvent| {
            ev.prevent_default();
            set_drag_counter.set(0);
            set_is_drag_over.set(false);
            
            // Try to get folder/file paths from the drag event
            if let Some(data_transfer) = ev.data_transfer() {
                if let Some(files) = data_transfer.files() {
                    if files.length() > 0 {
                        if let Some(file) = files.get(0) {
                            // Get the file path - for Tauri this should work differently than web
                            let file_name = file.name();
                            
                            // Try to use the existing handle_file_drop command
                            let select_folder_clone = select_folder_from_path.clone();
                            let set_error_clone = set_error_message.clone();
                            spawn_local(async move {                                // For now, try to extract directory from file path
                                // This is a workaround - proper implementation should use Tauri's native drag & drop
                                if let Some(parent_path) = std::path::Path::new(&file_name).parent() {
                                    if let Some(parent_str) = parent_path.to_str() {
                                        select_folder_clone(parent_str.to_string());
                                        return;
                                    }
                                }
                                
                                // Fallback: show guidance message instead of error
                                set_error_clone.set(Some(
                                    "Drag & drop detected! For best results, please use the 'Browse Folders' button or type the folder path directly.".to_string()
                                ));
                            });
                        }
                    }
                }
            }
        }
    };

    let return_to_selection = {
        let set_interface_state = set_interface_state.clone();
        let set_selected_folder = set_selected_folder.clone();
        let set_path_input = set_path_input.clone();
        let set_path_validation = set_path_validation.clone();
        let set_error_message = set_error_message.clone();
        move |_| {
            set_interface_state.set(InterfaceState::SelectionMode);
            set_selected_folder.set(None);
            set_path_input.set(String::new());
            set_path_validation.set(None);
            set_error_message.set(None);
        }
    };

    // Interface state management effects
    create_effect(move |_| {
        // Switch to processing mode when analysis or cleaning starts
        if is_analyzing.get() || cleaning_in_progress.get() {
            set_interface_state.set(InterfaceState::ProcessingMode);
        } else if selected_folder.get().is_some() {
            set_interface_state.set(InterfaceState::ActionsMode);
        }
    });

    create_effect(move |_| {
        // Load recent folders on mount
        spawn_local(async move {
            if let Ok(result) = tauri_invoke_no_args("get_recent_folders").await {
                if let Ok(folders) = serde_wasm_bindgen::from_value::<Vec<RecentFolder>>(result) {
                    set_recent_folders.set(folders);
                }
            }
        });
    });

    view! {
        <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="container mx-auto px-4 py-8">
                <div class="max-w-6xl mx-auto">
                    // Header
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-gray-900 mb-4">
                            "🔍 Bad Character Scanner"
                        </h1>
                        <p class="text-xl text-gray-600">
                            "Detect and analyze suspicious characters in text and codebases"
                        </p>
                    </div>

                    // Error message display
                    {move || error_message.get().map(|msg| view! {
                        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                            <span class="font-medium">"Error: "</span>
                            {msg}
                        </div>
                    })}

                    // Main card with tabs
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        // Tab navigation
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8 px-6">
                                <button
                                    class=move || format!("py-4 px-1 border-b-2 font-medium text-sm transition-colors {}",
                                        if current_tab.get() == "text" {
                                            "border-indigo-500 text-indigo-600"
                                        } else {
                                            "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                        }
                                    )
                                    on:click=move |_| set_current_tab.set("text".to_string())
                                >
                                    "📝 Text Analysis"
                                </button>
                                <button
                                    class=move || format!("py-4 px-1 border-b-2 font-medium text-sm transition-colors {}",
                                        if current_tab.get() == "codebase" {
                                            "border-indigo-500 text-indigo-600"
                                        } else {
                                            "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                        }
                                    )
                                    on:click=move |_| set_current_tab.set("codebase".to_string())
                                >
                                    "📁 Codebase Analysis"
                                </button>
                            </nav>
                        </div>

                        // Tab content
                        <div class="p-6">
                            {move || match current_tab.get().as_str() {
                                "text" => view! {
                                    <div>
                                        <label for="text-input" class="block text-sm font-medium text-gray-700 mb-2">
                                            "Enter text to analyze:"
                                        </label>
                                        <textarea
                                            id="text-input"
                                            rows="8"
                                            class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                                            placeholder="Paste your text here... (supports Unicode, control characters, and invisible characters)"
                                            on:input=move |ev| {
                                                set_input_text.set(event_target_value(&ev));
                                            }
                                            prop:value=input_text
                                        />
                                        
                                        <div class="mt-4 flex justify-between items-center">
                                            <div class="text-sm text-gray-500">
                                                "Characters: " {move || input_text.get().chars().count()}
                                                " | Bytes: " {move || input_text.get().len()}
                                            </div>
                                            <button
                                                on:click=analyze_text
                                                disabled=is_analyzing
                                                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50"
                                            >
                                                {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Text" }}
                                            </button>
                                        </div>
                                    </div>
                                }.into_view(),                                "codebase" => view! {
                                    <div class="space-y-6">
                                        // Dynamic interface based on state
                                        {move || {
                                            match interface_state.get() {
                                                InterfaceState::SelectionMode => view! {
                                                    <div class="bg-white rounded-xl shadow-lg p-6">
                                                        <h2 class="text-xl font-semibold mb-6 text-gray-800">
                                                            <i class="fas fa-folder-open mr-2 text-blue-500"></i>
                                                            "Select Folder for Analysis"
                                                        </h2>
                                                        
                                                        // Selection methods grid
                                                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                            // Traditional folder selection
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                                    "Browse for Folder"
                                                                </label>
                                                                <button
                                                                    on:click=select_folder
                                                                    class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors flex items-center justify-center"
                                                                >
                                                                    <i class="fas fa-folder mr-2"></i>
                                                                    "Browse Folders"
                                                                </button>
                                                            </div>

                                                            // Manual path input
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                                    "Enter Path Manually"
                                                                </label>
                                                                <div class="relative">
                                                                    <input
                                                                        type="text"
                                                                        placeholder="C:\\path\\to\\your\\folder"
                                                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                        prop:value=path_input
                                                                        on:input=move |ev| {
                                                                            let value = event_target_value(&ev);
                                                                            set_path_input.set(value.clone());
                                                                            validate_path(value);
                                                                        }
                                                                    />
                                                                    {move || validating_path.get().then(|| view! {
                                                                        <div class="absolute right-3 top-3">
                                                                            <i class="fas fa-spinner fa-spin text-blue-500"></i>
                                                                        </div>
                                                                    })}
                                                                </div>
                                                                
                                                                // Path validation result
                                                                {move || path_validation.get().map(|validation| {
                                                                    if validation.exists && validation.readable {
                                                                        view! {
                                                                            <div class="mt-2 flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                                                                <div class="flex items-center text-green-700">
                                                                                    <i class="fas fa-check-circle mr-2"></i>
                                                                                    "Valid folder path"
                                                                                    {validation.file_count.map(|count| view! {
                                                                                        <span class="ml-2 text-sm text-green-600">
                                                                                            "(" {count} " files)"
                                                                                        </span>
                                                                                    })}
                                                                                </div>
                                                                                <button
                                                                                    on:click=move |_| {
                                                                                        if let Some(folder_info) = path_validation.get() {
                                                                                            if folder_info.exists && folder_info.readable {
                                                                                                let path = folder_info.path.clone();
                                                                                                select_folder_from_path(path);
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                                                                                >
                                                                                    "Select"
                                                                                </button>
                                                                            </div>
                                                                        }
                                                                    } else {
                                                                        view! {
                                                                            <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                                                <div class="flex items-center text-red-700">
                                                                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                                                                    {validation.error_message.clone().unwrap_or_else(|| "Invalid folder path".to_string())}
                                                                                </div>
                                                                            </div>
                                                                        }
                                                                    }
                                                                })}
                                                            </div>
                                                        </div>

                                                        // Enhanced Drag & Drop Zone
                                                        <div class="mt-6">
                                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                                "Drag & Drop"
                                                            </label>
                                                            <div 
                                                                class=move || {
                                                                    let base_classes = "border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer";
                                                                    if is_drag_over.get() {
                                                                        format!("{} border-purple-500 bg-purple-50 border-solid scale-105", base_classes)
                                                                    } else {
                                                                        format!("{} border-gray-300 hover:border-purple-400 hover:bg-gray-50", base_classes)
                                                                    }
                                                                }
                                                                on:dragover=on_drag_over
                                                                on:dragenter=on_drag_enter
                                                                on:dragleave=on_drag_leave
                                                                on:drop=on_drop
                                                            >
                                                                <i class=move || {
                                                                    if is_drag_over.get() {
                                                                        "fas fa-folder-plus text-purple-500 text-4xl mb-3 animate-bounce"
                                                                    } else {
                                                                        "fas fa-upload text-gray-400 text-3xl mb-3"
                                                                    }
                                                                }></i>
                                                                <p class=move || {
                                                                    if is_drag_over.get() {
                                                                        "text-purple-700 text-lg font-medium"
                                                                    } else {
                                                                        "text-gray-600 text-base"
                                                                    }
                                                                }>
                                                                    {move || if is_drag_over.get() {
                                                                        "Drop folder here to analyze"
                                                                    } else {
                                                                        "Drag and drop a folder here"
                                                                    }}
                                                                </p>
                                                                <p class="text-gray-500 text-sm mt-2">
                                                                    "Supports project folders, source code directories, and document collections"
                                                                </p>
                                                            </div>
                                                        </div>

                                                        // Recent folders (if any)
                                                        {move || {
                                                            let folders = recent_folders.get();
                                                            if !folders.is_empty() {
                                                                view! {
                                                                    <div class="mt-6">
                                                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                                                            "Recent Folders"
                                                                        </label>
                                                                        <div class="flex flex-wrap gap-2">
                                                                            {folders.into_iter().take(3).map(|folder| {
                                                                                let folder_clone = folder.clone();
                                                                                view! {
                                                                                    <button
                                                                                        on:click=move |_| select_folder_from_path(folder_clone.path.clone())
                                                                                        class="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm flex items-center"
                                                                                    >
                                                                                        <i class="fas fa-history mr-2"></i>
                                                                                        {folder.name}
                                                                                    </button>
                                                                                }
                                                                            }).collect_view()}
                                                                        </div>
                                                                    </div>
                                                                }
                                                            } else {
                                                                view! { <div></div> }
                                                            }
                                                        }}
                                                    </div>
                                                }.into_view(),

                                                InterfaceState::ActionsMode => view! {
                                                    <div class="bg-white rounded-xl shadow-lg p-6">
                                                        // Selected folder display with collapse option
                                                        <div class="flex items-center justify-between mb-6 p-4 bg-blue-50 rounded-lg">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-folder-open text-blue-500 text-xl mr-3"></i>
                                                                <div>
                                                                    <div class="font-medium text-gray-900">
                                                                        "Selected Folder"
                                                                    </div>
                                                                    <div class="text-sm text-gray-600 font-mono break-all">
                                                                        {move || selected_folder.get().unwrap_or_default()}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <button
                                                                on:click=return_to_selection
                                                                class="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors"
                                                            >
                                                                "Change"
                                                            </button>
                                                        </div>

                                                        // Action buttons
                                                        <div class="space-y-4">
                                                            <div class="flex flex-wrap gap-3">
                                                                <button
                                                                    on:click=analyze_codebase
                                                                    disabled=move || is_analyzing.get()
                                                                    class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50 flex items-center"
                                                                >
                                                                    <i class="fas fa-search mr-2"></i>
                                                                    {move || if is_analyzing.get() { "Analyzing..." } else { "🔍 Analyze Codebase" }}
                                                                </button>
                                                                
                                                                {move || codebase_results.get().map(|_| view! {
                                                                    <button
                                                                        on:click=clean_codebase
                                                                        disabled=move || is_cleaning.get() || cleaning_in_progress.get()
                                                                        class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors disabled:opacity-50 flex items-center"
                                                                    >
                                                                        <i class="fas fa-broom mr-2"></i>
                                                                        {move || if is_cleaning.get() { "Cleaning..." } else { "🧹 Clean & Verify" }}
                                                                    </button>
                                                                })}
                                                            </div>
                                                            
                                                            // Export options
                                                            {move || codebase_results.get().map(|_| view! {
                                                                <div>
                                                                    <h3 class="text-sm font-medium text-gray-700 mb-2">"Export Results"</h3>
                                                                    <div class="flex flex-wrap gap-2">
                                                                        <button
                                                                            on:click=move |_| export_report("json")
                                                                            disabled=export_in_progress
                                                                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors disabled:opacity-50 text-sm"
                                                                        >
                                                                            {move || if export_in_progress.get() { "Exporting..." } else { "📄 JSON" }}
                                                                        </button>
                                                                        <button
                                                                            on:click=move |_| export_report("html")
                                                                            disabled=export_in_progress
                                                                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50 text-sm"
                                                                        >
                                                                            {move || if export_in_progress.get() { "Exporting..." } else { "🌐 HTML" }}
                                                                        </button>
                                                                        <button
                                                                            on:click=move |_| export_report("markdown")
                                                                            disabled=export_in_progress
                                                                            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors disabled:opacity-50 text-sm"
                                                                        >
                                                                            {move || if export_in_progress.get() { "Exporting..." } else { "📝 Markdown" }}
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            })}
                                                        </div>
                                                    </div>
                                                }.into_view(),

                                                InterfaceState::ProcessingMode => {
                                                    let analysis_active = analysis_progress.get().is_some() && is_analyzing.get();
                                                    let cleaning_active = cleaning_progress.get().is_some() && cleaning_in_progress.get();

                                                    view! {
                                                        <div class="bg-white rounded-xl shadow-lg p-6">
                                                            <h2 class="text-xl font-semibold mb-4 text-gray-800 text-center">
                                                                {
                                                                    if analysis_active { "Codebase Analysis in Progress" }
                                                                    else if cleaning_active { "Codebase Cleaning in Progress" }
                                                                    // else if verification_active { "Verification in Progress" } // Placeholder for future
                                                                    else { "Processing Operation" } // Fallback
                                                                }
                                                            </h2>
                                                            // Generic spinner for initial phase before first progress event
                                                            {if !analysis_active && !cleaning_active && (is_analyzing.get() || cleaning_in_progress.get()) {
                                                                view! {
                                                                    <div class="text-center my-4">
                                                                        <i class="fas fa-spinner fa-spin text-3xl text-blue-500"></i>
                                                                        <p class="text-gray-600 mt-2">"Initializing operation, please wait..."</p>
                                                                    </div>
                                                                }                                                            } else {
                                                                view! { <div></div> } // Empty div for when progress bars will be shown by their own conditional logic below
                                                            }}
                                                        </div>
                                                    }.into_view()
                                                },
                                            }
                                        }}

                                        // Status messages
                                        {move || export_success_message.get().map(|msg| view! {
                                            <div class="p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                                                <span class="font-medium">"✅ "</span>
                                                {msg}
                                            </div>
                                        })}
                                        
                                        // Progress indicators
                                        {move || verification_progress.get().map(|progress| view! {
                                            <div class="p-4 bg-purple-50 rounded-lg">
                                                <div class="text-sm text-purple-600 mb-2">{progress.message}</div>
                                                <div class="w-full bg-purple-200 rounded-full h-2">
                                                    <div 
                                                        class="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                                                        style=format!("width: {}%", (progress.current as f32 / progress.total as f32) * 100.0)
                                                    ></div>
                                                </div>
                                                <div class="text-xs text-purple-500 mt-1">
                                                    {format!("{}/{}", progress.current, progress.total)}
                                                </div>
                                            </div>
                                        })}
                                        
                                        {move || analysis_progress.get().map(|progress| {
                                            let message_text = progress.message.clone().unwrap_or_else(|| "Analyzing...".to_string());
                                            let stage_text = progress.stage.clone().map(|s| format!(" - {}", s)).unwrap_or_default();
                                            let percentage_text = format!(" ({:.0}%)", progress.percentage);
                                            view! {
                                                <div class="p-4 bg-blue-50 rounded-lg my-3 shadow">
                                                    <div class="text-sm text-blue-700 mb-1 font-semibold">
                                                        {message_text}{stage_text}{percentage_text}
                                                    </div>
                                                    <div class="w-full bg-blue-200 rounded-full h-2.5">
                                                        <div
                                                            class="bg-blue-600 h-2.5 rounded-full transition-all duration-150 ease-linear"
                                                            style=format!("width: {}%", progress.percentage)
                                                        ></div>
                                                    </div>
                                                    <div class="text-xs text-blue-600 mt-1 text-right">
                                                        {format!("{} / {}", progress.current, progress.total)}
                                                    </div>
                                                </div>
                                            }
                                        })}
                                        {move || cleaning_progress.get().map(|progress| {
                                            let message_text = progress.message.clone().unwrap_or_else(|| "Cleaning...".to_string());
                                            let stage_text = progress.stage.clone().map(|s| format!(" - {}", s)).unwrap_or_default();
                                            let percentage_text = format!(" ({:.0}%)", progress.percentage);
                                            view! {
                                                <div class="p-4 bg-teal-50 rounded-lg my-3 shadow">
                                                    <div class="text-sm text-teal-700 mb-1 font-semibold">
                                                        {message_text}{stage_text}{percentage_text}
                                                    </div>
                                                    <div class="w-full bg-teal-200 rounded-full h-2.5">
                                                        <div
                                                            class="bg-teal-600 h-2.5 rounded-full transition-all duration-150 ease-linear"
                                                            style=format!("width: {}%", progress.percentage)
                                                        ></div>
                                                    </div>
                                                    <div class="text-xs text-teal-600 mt-1 text-right">
                                                        {format!("{} / {}", progress.current, progress.total)}
                                                    </div>
                                                </div>
                                            }
                                        })}
                                    </div>
                                }.into_view(),
                                _ => view! { <div>"Unknown tab"</div> }.into_view()
                            }}
                        </div>
                    </div>

                    // Text Analysis Results display
                    {move || analysis_results.get().map(|results| view! {
                        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">"📊 Analysis Results"</h2>
                            
                            // Summary statistics
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{results.total_characters}</div>
                                    <div class="text-sm text-blue-800">"Total Characters"</div>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-red-600">{results.suspicious_characters.len()}</div>
                                    <div class="text-sm text-red-800">"Suspicious Characters"</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{results.total_bytes}</div>
                                    <div class="text-sm text-green-800">"Total Bytes"</div>
                                </div>
                            </div>

                            // Suspicious characters details
                            {(!results.suspicious_characters.is_empty()).then(|| view! {
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"🚨 Suspicious Characters Found"</h3>
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <div class="space-y-3">
                                            {results.suspicious_characters.iter().map(|char_info| view! {
                                                <div class="flex items-start space-x-3 p-3 bg-white rounded border">
                                                    <div class="font-mono text-lg">
                                                        {if char_info.character.is_control() { 
                                                            format!("\\u{{{:04X}}}", char_info.character as u32)
                                                        } else { 
                                                            char_info.character.to_string() 
                                                        }}
                                                    </div>                                                    <div class="flex-1">
                                                        <div class="font-medium text-gray-900">{&char_info.unicode_name}</div>
                                                        <div class="text-sm text-gray-600">
                                                            {if !char_info.suspicion_reasons.is_empty() {
                                                                char_info.suspicion_reasons.join(", ")
                                                            } else {
                                                                "No issues detected".to_string()
                                                            }}
                                                        </div>
                                                        <div class="text-xs text-gray-500">
                                                            "Position: " {char_info.position} " | Code: U+" {format!("{:04X}", char_info.codepoint)}
                                                        </div>
                                                    </div>
                                                    <span class=format!("px-2 py-1 text-xs rounded {}", 
                                                        if char_info.is_suspicious {
                                                            "bg-red-100 text-red-800"
                                                        } else {
                                                            "bg-green-100 text-green-800"
                                                        })>
                                                        {if char_info.is_suspicious { "Suspicious" } else { "Clean" }}
                                                    </span>
                                                </div>
                                            }).collect::<Vec<_>>()}
                                        </div>
                                    </div>
                                </div>
                            })}

                            // Recommendations
                            {(!results.recommendations.is_empty()).then(|| view! {
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"💡 Recommendations"</h3>
                                    <ul class="space-y-2">
                                        {results.recommendations.iter().map(|rec| view! {
                                            <li class="flex items-start space-x-2">
                                                <span class="text-indigo-500 mt-1">"•"</span>
                                                <span class="text-gray-700">{rec}</span>
                                            </li>
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            })}
                        </div>
                    })}

                    // Codebase Results display
                    {move || codebase_results.get().map(|results| view! {
                        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">"📁 Codebase Analysis Results"</h2>
                            
                            // Summary statistics
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{results.total_files}</div>
                                    <div class="text-sm text-blue-800">"Total Files"</div>
                                </div>                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{results.files_with_issues}</div>
                                    <div class="text-sm text-green-800">"Files with Issues"</div>
                                </div>                                <div class="bg-red-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-red-600">{results.total_suspicious_chars}</div>
                                    <div class="text-sm text-red-800">"Suspicious Characters"</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">{format!("{:.1}%", results.health_score)}</div>
                                    <div class="text-sm text-purple-800">"Health Score"</div>
                                </div>
                            </div>

                            // Files with issues
                            {let files_with_issues: Vec<_> = results.file_details.iter()
                                .filter(|file| file.suspicious_characters > 0)
                                .collect();
                            
                            (!files_with_issues.is_empty()).then(|| view! {
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"⚠️ Files with Issues"</h3>
                                    <div class="space-y-3">
                                        {files_with_issues.into_iter().map(|file| view! {
                                            <div class="border border-gray-200 rounded-lg p-4">
                                                <div class="flex justify-between items-start mb-2">
                                                    <div class="font-mono text-sm text-gray-900">{&file.file_path}</div>
                                                    <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                                                        {format!("{} issues", file.suspicious_characters)}
                                                    </span>
                                                </div>
                                                <div class="text-sm text-gray-600 mb-2">
                                                    "Size: " {file.file_size} " bytes | Encoding: " {&file.encoding}
                                                </div>
                                                {(!file.issues.is_empty()).then(|| view! {
                                                    <div class="space-y-1">
                                                        <div class="text-sm font-medium text-gray-700">"Issues:"</div>
                                                        <ul class="text-sm text-gray-600 space-y-1">
                                                            {file.issues.iter().map(|issue| view! {
                                                                <li class="flex items-start space-x-2">
                                                                    <span class="text-red-500 mt-0.5">"•"</span>
                                                                    <span>{issue}</span>
                                                                </li>
                                                            }).collect::<Vec<_>>()}
                                                        </ul>
                                                    </div>
                                                })}
                                            </div>
                                        }).collect::<Vec<_>>()}
                                    </div>
                                </div>
                            })}
                        </div>
                    })}

                    // Post-Cleaning Verification Results display
                    {move || post_cleaning_verification.get().map(|verification| view! {
                        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">"✅ Post-Cleaning Verification Results"</h2>
                            
                            // Summary statistics                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{verification.original_analysis.total_suspicious_chars}</div>
                                    <div class="text-sm text-blue-800">"Original Suspicious Characters"</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{verification.post_cleaning_analysis.total_suspicious_chars}</div>
                                    <div class="text-sm text-green-800">"Post-Cleaning Suspicious Characters"</div>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-red-600">{verification.cleaning_effectiveness.total_cleaned_issues}</div>
                                    <div class="text-sm text-red-800">"Cleaned Issues"</div>
                                </div>
                            </div>

                            // Cleaning effectiveness details
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">"🛠️ Cleaning Effectiveness"</h3>
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm text-gray-600">
                                            <span>"Success Rate:"</span>
                                            <span class="font-medium text-gray-900">{format!("{:.2}%", verification.cleaning_effectiveness.success_rate_percentage)}</span>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600">
                                            <span>"Files Fully Cleaned:"</span>
                                            <span class="font-medium text-gray-900">{verification.cleaning_effectiveness.files_fully_cleaned}</span>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600">
                                            <span>"Files Partially Cleaned:"</span>
                                            <span class="font-medium text-gray-900">{verification.cleaning_effectiveness.files_partially_cleaned}</span>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600">
                                            <span>"Files with Errors:"</span>
                                            <span class="font-medium text-gray-900">{verification.cleaning_effectiveness.files_with_errors}</span>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600">
                                            <span>"Total Files Processed:"</span>
                                            <span class="font-medium text-gray-900">{verification.cleaning_effectiveness.total_files_processed}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            // Remaining issues details
                            {(!verification.remaining_issues.is_empty()).then(|| view! {
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"🔍 Remaining Issues"</h3>
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <div class="space-y-3">                                            {verification.remaining_issues.iter().map(|issue| view! {
                                                <div class="flex items-start space-x-3 p-3 bg-white rounded border">
                                                    <div class="font-mono text-lg">
                                                        {if issue.character.is_control() { 
                                                            format!("\\u{{{:04X}}}", issue.character as u32)
                                                        } else { 
                                                            issue.character.to_string() 
                                                        }}
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="font-medium text-gray-900">{&issue.character_name}</div>
                                                        <div class="text-sm text-gray-600">{&issue.reason_not_cleaned}</div>
                                                        <div class="text-xs text-gray-500">
                                                            "File: " {&issue.file_path} " | Line: " {issue.line_number} " | Column: " {issue.column_number}
                                                        </div>
                                                        <div class="text-xs text-gray-500 mt-1">
                                                            "Suggested Action: " {&issue.suggested_action}
                                                        </div>
                                                    </div>
                                                </div>
                                            }).collect::<Vec<_>>()}
                                        </div>
                                    </div>
                                </div>
                            })}

                    })}

                    // Warning popup
                    {move || show_warning_popup.get().then(|| view! {
                        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div class="bg-white rounded-lg shadow-2xl p-6 max-w-lg w-full mx-4">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900">"⚠️ Post-Cleaning Warning"</h3>
                                    <button
                                        on:click=move |_| set_show_warning_popup.set(false)
                                        class="text-gray-500 hover:text-gray-700 text-xl"
                                    >
                                        "✖"
                                    </button>
                                </div>
                                <div class="text-sm text-gray-700 mb-6 whitespace-pre-line">
                                    {move || warning_popup_content.get().unwrap_or_default()}
                                </div>
                                <div class="flex justify-end space-x-3">
                                    <button
                                        on:click=move |_| {
                                            set_show_warning_popup.set(false);
                                            set_warning_popup_preferences.update(|prefs| {
                                                prefs.show_post_cleaning_warnings = false;
                                            });
                                        }
                                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                                    >
                                        "Don't Show Again"
                                    </button>
                                    <button
                                        on:click=move |_| set_show_warning_popup.set(false)
                                        class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors"
                                    >
                                        "OK"
                                    </button>
                                </div>
                            </div>
                        </div>
                    })}

                    // Footer with additional information
                    <div class="mt-12 text-center text-gray-500 text-sm">
                        <div class="bg-white rounded-lg p-6 shadow-md">
                            <h3 class="font-semibold text-gray-700 mb-4">"🛡️ What We Detect"</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">"Invisible Characters:"</h4>
                                    <ul class="space-y-1">
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Zero-width spaces and joiners"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Bidirectional text override characters"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Non-breaking spaces and soft hyphens"
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">"Control Characters:"</h4>
                                    <ul class="space-y-1">
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "ASCII control characters (0x00-0x1F)"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Byte order marks (BOM) and line endings"
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

// Main entry point for WASM
#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
    mount_to_body(|| view! { <App/> })
}
