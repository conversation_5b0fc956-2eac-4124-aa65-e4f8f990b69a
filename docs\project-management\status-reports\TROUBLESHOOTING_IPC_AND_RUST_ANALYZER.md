# Troubleshooting Guide: Tauri IPC and Rust Analyzer Issues

## Issue 1: Tauri IPC Protocol Connection Refused

### Problem
- Error: `net::ERR_CONNECTION_REFUSED` when posting to `http://ipc.localhost/analyze_codebase_advanced`
- Application falls back to postMessage interface

### Possible Causes & Solutions

1. **Tauri Dev Server Not Running Properly**
   - Ensure Tauri dev server is running: `cargo tauri dev`
   - Check if the Tauri window is properly initialized

2. **IPC Protocol Configuration**
   - Check `tauri.conf.json` for proper security configuration
   - Ensure `dangerousRemoteDomainIpcAccess` is not blocking localhost

3. **Port Conflicts**
   - The IPC protocol might be conflicting with other services
   - Try restarting the development server

4. **Windows Firewall/Antivirus**
   - Windows Defender or antivirus might be blocking the IPC connection
   - Add exception for your development folder

### Quick Fix
```bash
# Kill all Tauri processes
taskkill /f /im "laptos-tauri.exe" 2>$null

# Clear Tauri cache
cargo clean

# Rebuild and run
cargo tauri dev
```

## Issue 2: Rust Analyzer Flycheck Error

### Problem
- Error: "no such command: cargo"
- Despite cargo.exe existing at `C:\Users\<USER>\.cargo\bin\cargo.exe`

### Solutions

1. **Add Cargo to System PATH**
   ```powershell
   # Check if cargo is in PATH
   where cargo
   
   # If not found, add to PATH
   $env:Path += ";C:\Users\<USER>\.cargo\bin"
   ```

2. **VS Code Settings**
   Add to `.vscode/settings.json`:
   ```json
   {
     "rust-analyzer.cargo.buildScripts.enable": true,
     "rust-analyzer.procMacro.enable": true,
     "rust-analyzer.cargo.features": "all",
     "rust-analyzer.server.path": "rust-analyzer",
     "rust-analyzer.checkOnSave.command": "check",
     "rust-analyzer.cargo.target": null,
     "rust-analyzer.runnables.command": null,
     "rust-analyzer.runnables.extraEnv": {
       "PATH": "${env:PATH};C:\\Users\\<USER>\\.cargo\\bin"
     }
   }
   ```

3. **Restart VS Code**
   - Close VS Code completely
   - Open PowerShell as Administrator
   - Run: `code .` from project directory

4. **Reinstall Rust Analyzer**
   ```bash
   # Uninstall
   rustup component remove rust-analyzer
   
   # Reinstall
   rustup component add rust-analyzer
   ```

5. **Check Workspace Configuration**
   Ensure `Cargo.toml` in root directory has proper workspace configuration:
   ```toml
   [workspace]
   members = [".", "src-tauri"]
   resolver = "2"
   ```

## Verification Steps

1. **Test Cargo**
   ```bash
   cargo --version
   cargo check
   ```

2. **Test Rust Analyzer**
   - Open any `.rs` file
   - Check for syntax highlighting
   - Hover over types to see if tooltips appear

3. **Test Tauri IPC**
   - Open browser DevTools
   - Check Network tab for IPC requests
   - Look for console errors

## Additional Debugging

1. **Enable Tauri Debug Logging**
   ```bash
   RUST_LOG=debug cargo tauri dev
   ```

2. **Check VS Code Output**
   - View → Output → Select "Rust Analyzer Language Server"
   - Look for specific error messages

3. **Windows Event Viewer**
   - Check for application errors
   - Look for blocked connections

If issues persist after trying these solutions, consider:
- Reinstalling Rust toolchain
- Creating a fresh Tauri project to test
- Checking for Windows updates