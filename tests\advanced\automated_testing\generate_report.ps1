# Advanced Test Report Generator
# Processes test results and generates comprehensive reports

param(
    [string]$ResultsFile = "advanced_test_results.json",
    [string]$OutputFormat = "html",  # html, markdown, pdf
    [string]$OutputFile = "advanced_test_report.html"
)

if (-not (Test-Path $ResultsFile)) {
    Write-Host "❌ Results file not found: $ResultsFile" -ForegroundColor Red
    exit 1
}

# Load test results
$results = Get-Content $ResultsFile | ConvertFrom-Json

# Generate HTML report
$htmlReport = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Bad Character Scanner Test Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header .subtitle { color: #7f8c8d; font-size: 18px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .metric { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .metric .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .metric .label { font-size: 0.9em; opacity: 0.9; }
        .test-results { margin-top: 40px; }
        .test-item { background: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .test-item.passed { border-left-color: #28a745; }
        .test-item.failed { border-left-color: #dc3545; }
        .test-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .test-name { font-size: 1.2em; font-weight: bold; color: #2c3e50; }
        .test-status { padding: 5px 10px; border-radius: 20px; font-size: 0.8em; font-weight: bold; }
        .status-passed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .test-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px; }
        .detail-item { text-align: center; }
        .detail-value { font-size: 1.5em; font-weight: bold; color: #495057; }
        .detail-label { font-size: 0.8em; color: #6c757d; margin-top: 5px; }
        .threat-types { margin-top: 15px; }
        .threat-tag { display: inline-block; background: #e9ecef; color: #495057; padding: 3px 8px; margin: 2px; border-radius: 12px; font-size: 0.8em; }
        .performance { background: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%); color: white; padding: 20px; border-radius: 8px; margin-top: 30px; }
        .chart-container { margin: 20px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Advanced Bad Character Scanner Test Report</h1>
            <div class="subtitle">Comprehensive Unicode Threat Detection Analysis</div>
            <div style="color: #95a5a6; margin-top: 10px;">Generated: $($results.timestamp)</div>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="value">$($results.summary.totalTests)</div>
                <div class="label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="value">$($results.summary.passed)</div>
                <div class="label">Passed</div>
            </div>
            <div class="metric">
                <div class="value">$($results.summary.failed)</div>
                <div class="label">Failed</div>
            </div>
            <div class="metric">
                <div class="value">$($results.summary.detectionAccuracy)%</div>
                <div class="label">Detection Accuracy</div>
            </div>
            <div class="metric">
                <div class="value">$($results.summary.totalThreats)</div>
                <div class="label">Threats Detected</div>
            </div>
            <div class="metric">
                <div class="value">$([math]::Round($results.performance.totalExecutionTime, 0))ms</div>
                <div class="label">Total Execution Time</div>
            </div>
        </div>

        <div class="performance">
            <h3 style="margin-top: 0;">⚡ Performance Metrics</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                    <div style="font-size: 1.5em; font-weight: bold;">$($results.performance.averageFileProcessingTime)ms</div>
                    <div style="opacity: 0.9;">Average Processing Time</div>
                </div>
                <div>
                    <div style="font-size: 1.5em; font-weight: bold;">$($results.performance.threatsPerSecond)</div>
                    <div style="opacity: 0.9;">Threats Per Second</div>
                </div>
            </div>
        </div>

        <div class="test-results">
            <h2>📋 Detailed Test Results</h2>
"@

foreach ($test in $results.results) {
    $status = if ($test.analysis.success) { "passed" } else { "failed" }
    $statusClass = if ($test.analysis.success) { "status-passed" } else { "status-failed" }
    $statusText = if ($test.analysis.success) { "PASSED" } else { "FAILED" }
    
    $threatTypes = if ($test.analysis.detectedThreatTypes) {
        ($test.analysis.detectedThreatTypes | ForEach-Object { "<span class='threat-tag'>$_</span>" }) -join ""
    } else { "<span class='threat-tag'>None</span>" }
    
    $cleaningInfo = ""
    if ($test.cleaning -and $test.cleaning.success) {
        $cleaningInfo = @"
                <div class="detail-item">
                    <div class="detail-value">$($test.cleaning.cleaningEfficiency)%</div>
                    <div class="detail-label">Cleaning Efficiency</div>
                </div>
                <div class="detail-item">
                    <div class="detail-value">$($test.cleaning.remainingThreats)</div>
                    <div class="detail-label">Remaining Threats</div>
                </div>
"@
    }
    
    $htmlReport += @"
            <div class="test-item $status">
                <div class="test-header">
                    <span class="test-name">$($test.testName)</span>
                    <span class="test-status $statusClass">$statusText</span>
                </div>
                <div style="margin: 10px 0;">
                    <strong>Attack Vector:</strong> $($test.attackVector) | 
                    <strong>Severity:</strong> $($test.severity)
                </div>
                <div class="test-details">
                    <div class="detail-item">
                        <div class="detail-value">$($test.analysis.threats)/$($test.analysis.expectedThreats)</div>
                        <div class="detail-label">Threats Detected</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">$($test.analysis.accuracy)%</div>
                        <div class="detail-label">Accuracy</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">$($test.analysis.healthScore)%</div>
                        <div class="detail-label">Health Score</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">$([math]::Round($test.analysis.executionTime, 0))ms</div>
                        <div class="detail-label">Execution Time</div>
                    </div>
                    $cleaningInfo
                </div>
                <div class="threat-types">
                    <strong>Detected Threat Types:</strong><br>
                    $threatTypes
                </div>
            </div>
"@
}

$htmlReport += @"
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #155724;">🎯 Test Conclusions</h3>
            <ul style="color: #155724;">
                <li><strong>Overall Success Rate:</strong> $($results.summary.detectionAccuracy)% of tests passed</li>
                <li><strong>Threat Detection:</strong> Successfully identified $($results.summary.totalThreats) malicious Unicode characters</li>
                <li><strong>Performance:</strong> Processed files at an average rate of $($results.performance.threatsPerSecond) threats per second</li>
                <li><strong>Attack Vector Coverage:</strong> Tested invisible characters, homographs, bidirectional text, and mixed attacks</li>
                <li><strong>Scalability:</strong> Successfully handled large-scale performance testing scenarios</li>
            </ul>
        </div>

        <div style="margin-top: 30px; text-align: center; color: #6c757d; font-size: 0.9em;">
            <p>Report generated by Advanced Bad Character Scanner Test Suite</p>
            <p>Test Environment: Windows 11, PowerShell, Rust CLI</p>
        </div>
    </div>
</body>
</html>
"@

# Save the report
$htmlReport | Out-File -FilePath $OutputFile -Encoding UTF8

Write-Host "📊 Advanced test report generated: $OutputFile" -ForegroundColor Green
Write-Host "🌐 Open the HTML file in your browser to view the detailed report" -ForegroundColor Cyan
