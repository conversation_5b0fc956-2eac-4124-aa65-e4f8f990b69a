// Advanced Security Analysis UI Results Component
// Critical bug fix for JSON parsing mismatch - By <PERSON> - 2025
// Advanced Security Analysis Results Parser - By <PERSON> - 2025
// This file was fixed to use correct JSON nested structure for threat analysis
// Critical fix: Changed flat field parsing to nested JSON paths to prevent false security reporting

use leptos::*;
use crate::components::codebase::types::AnalysisStats;

#[component]
pub fn AnalysisResults(result: String) -> impl IntoView {
    match serde_json::from_str::<serde_json::Value>(&result) {
        Ok(json) => {            // Extract comprehensive analysis data from ComprehensiveAnalysisResult structure
            let stats = AnalysisStats {
                analysis_id: json.get("analysis_metadata")
                    .and_then(|meta| meta.get("analysis_id"))
                    .and_then(|v| v.as_str())
                    .map(|s| s.to_string())
                    .unwrap_or_else(|| "Unknown".to_string()),
                
                total_files: json.get("analysis_metadata")
                    .and_then(|meta| meta.get("total_files_scanned"))
                    .and_then(|v| v.as_u64())
                    .unwrap_or(0),
                
                files_analyzed: json.get("file_analyses")
                    .and_then(|files| files.as_array())
                    .map(|arr| arr.len() as u64)
                    .unwrap_or(0),
                
                overall_risk_score: json.get("risk_assessment")
                    .and_then(|risk| risk.get("overall_risk_score"))
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.0),
                
                // Count threats from the threat arrays and executive summary
                homoglyph_threats: json.get("homoglyph_threats")
                    .and_then(|threats| threats.as_array())
                    .map(|arr| arr.len())
                    .or_else(|| {
                        // Fallback to executive summary if direct threats array is empty
                        json.get("executive_summary")
                            .and_then(|summary| summary.get("total_threats"))
                            .and_then(|v| v.as_u64())
                            .map(|v| v as usize)
                    })
                    .unwrap_or(0),

                pattern_threats: json.get("pattern_threats")
                    .and_then(|threats| threats.as_array())
                    .map(|arr| arr.len())
                    .or_else(|| {
                        // Check risk assessment for pattern threats
                        json.get("risk_assessment")
                            .and_then(|risk| risk.get("threat_breakdown"))
                            .and_then(|breakdown| breakdown.get("pattern_threats"))
                            .and_then(|v| v.as_u64())
                            .map(|v| v as usize)
                    })
                    .unwrap_or(0),

                security_threats: json.get("security_threats")
                    .and_then(|threats| threats.as_array())
                    .map(|arr| arr.len())
                    .or_else(|| {
                        // Check risk assessment for security threats
                        json.get("risk_assessment")
                            .and_then(|risk| risk.get("threat_breakdown"))
                            .and_then(|breakdown| breakdown.get("security_threats"))
                            .and_then(|v| v.as_u64())
                            .map(|v| v as usize)
                    })
                    .unwrap_or(0),
                
                unicode_threats: {
                    let mut unicode_count = 0;

                    // Count invisible characters
                    if let Some(invisible) = json.get("unicode_analysis")
                        .and_then(|unicode| unicode.get("invisible_chars"))
                        .and_then(|chars| chars.as_array()) {
                        unicode_count += invisible.len();
                    }

                    // Count bidirectional overrides
                    if let Some(bidi) = json.get("unicode_analysis")
                        .and_then(|unicode| unicode.get("bidirectional_overrides"))
                        .and_then(|overrides| overrides.as_array()) {
                        unicode_count += bidi.len();
                    }

                    // Count script mixing (this is what's actually found in your data)
                    if let Some(script_mixing) = json.get("unicode_analysis")
                        .and_then(|unicode| unicode.get("script_mixing"))
                        .and_then(|mixing| mixing.as_array()) {
                        unicode_count += script_mixing.len();
                    }

                    // Count encoding anomalies
                    if let Some(encoding) = json.get("unicode_analysis")
                        .and_then(|unicode| unicode.get("encoding_anomalies"))
                        .and_then(|anomalies| anomalies.as_array()) {
                        unicode_count += encoding.len();
                    }

                    // Count normalization issues
                    if let Some(normalization) = json.get("unicode_analysis")
                        .and_then(|unicode| unicode.get("normalization_issues"))
                        .and_then(|norm_issues| norm_issues.as_array()) {
                        unicode_count += normalization.len();
                    }

                    unicode_count
                },
            };
            
            view! {
                <>
                    <crate::components::codebase::ui::SummaryStats stats=stats />

                    // Detailed results (expandable)
                    <details class="bg-white border rounded-lg mt-4">
                        <summary class="cursor-pointer p-4 font-semibold text-gray-700 hover:bg-gray-50">
                            "📊 Detailed Analysis Results"
                        </summary>
                        <div class="p-4 border-t bg-gray-50">
                            <pre class="whitespace-pre-wrap text-xs overflow-auto max-h-96 bg-white p-3 rounded border">
                                {result}
                            </pre>
                        </div>
                    </details>
                </>
            }.into_view()
        }
        Err(_) => view! {
            <>
                <div class="mt-4 p-4 bg-gray-50 rounded-md">
                    <h3 class="text-lg font-semibold mb-2">"Analysis Results"</h3>
                    <pre class="whitespace-pre-wrap text-sm">{result}</pre>
                </div>
            </>
        }.into_view()
    }
}