// Advanced Security Analysis Summary Stats Display
// UI component for displaying security analysis statistics - By <PERSON> - 2025
// Security Analysis Summary UI Component - By <PERSON> - 2025
// Displays threat analysis statistics and risk assessment

use leptos::*;
use crate::components::codebase::types::AnalysisStats;

#[component]
pub fn SummaryStats(stats: AnalysisStats) -> impl IntoView {
    let (risk_level, risk_text_color, risk_bg_color) = stats.risk_level();
    let total_threats = stats.total_threats();
    
    view! {
        <div class="mt-4 space-y-6">
            // Header with analysis ID and risk level
            <div class="flex justify-between items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border">
                <div>
                    <h3 class="text-xl font-bold text-gray-800">"Advanced Security Analysis"</h3>
                    <p class="text-sm text-gray-600">{"Analysis ID: "}{stats.analysis_id}</p>
                </div>
                <div class=format!(
                    "px-4 py-2 rounded-full text-sm font-bold {} {}",
                    risk_text_color,
                    risk_bg_color,
                )>{format!("{} Risk", risk_level)}</div>
            </div>

            // Summary statistics
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                    <div class="text-3xl font-bold text-blue-600">{stats.total_files}</div>
                    <div class="text-sm text-gray-600">"Total Files"</div>
                </div>
                <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                    <div class="text-3xl font-bold text-green-600">{stats.files_analyzed}</div>
                    <div class="text-sm text-gray-600">"Files Analyzed"</div>
                </div>
                <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                    <div class="text-3xl font-bold text-red-600">{total_threats}</div>
                    <div class="text-sm text-gray-600">"Total Threats"</div>
                </div>
                <div class="text-center p-4 bg-white rounded-lg shadow-sm border">
                    <div class=format!(
                        "text-3xl font-bold {}",
                        risk_text_color,
                    )>{format!("{:.1}", stats.overall_risk_score)}</div>
                    <div class="text-sm text-gray-600">"Risk Score"</div>
                </div>
            </div>

            // Threat breakdown
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="p-4 bg-white rounded-lg shadow-sm border">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-700">"Homoglyph Attacks"</h4>
                        <span class="text-2xl">{"🔤"}</span>
                    </div>
                    <div class="text-2xl font-bold text-purple-600">{stats.homoglyph_threats}</div>
                    <div class="text-xs text-gray-500">"Suspicious character substitutions"</div>
                </div>

                <div class="p-4 bg-white rounded-lg shadow-sm border">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-700">"Pattern Threats"</h4>
                        <span class="text-2xl">{"🔍"}</span>
                    </div>
                    <div class="text-2xl font-bold text-orange-600">{stats.pattern_threats}</div>
                    <div class="text-xs text-gray-500">"Malicious code patterns"</div>
                </div>

                <div class="p-4 bg-white rounded-lg shadow-sm border">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-700">"Security Issues"</h4>
                        <span class="text-2xl">{"🛡️"}</span>
                    </div>
                    <div class="text-2xl font-bold text-red-600">{stats.security_threats}</div>
                    <div class="text-xs text-gray-500">"Security vulnerabilities"</div>
                </div>

                <div class="p-4 bg-white rounded-lg shadow-sm border">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-gray-700">"Unicode Threats"</h4>
                        <span class="text-2xl">{"⚠️"}</span>
                    </div>
                    <div class="text-2xl font-bold text-indigo-600">{stats.unicode_threats}</div>
                    <div class="text-xs text-gray-500">"Unicode-based attacks"</div>
                </div>
            </div>
        </div>
    }
}