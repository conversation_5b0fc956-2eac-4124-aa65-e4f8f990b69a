#!/usr/bin/env powershell
# Run development server and monitor for compilation status

Write-Host "`n🚀 STARTING BAD CHARACTER SCANNER" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent

# Function to check if compilation is successful
function Test-Compilation {
    Push-Location $project_root
    $output = cargo check 2>&1
    $success = $LASTEXITCODE -eq 0
    Pop-Location
    return $success
}

# Initial compilation check
Write-Host "`n🔍 Checking compilation status..." -ForegroundColor Yellow
if (Test-Compilation) {
    Write-Host "✅ Code compiles successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Compilation errors detected" -ForegroundColor Red
    Write-Host "Running fix script..." -ForegroundColor Yellow
    & "$PSScriptRoot\fix-compiler-errors.ps1"
}

# Start the development server
Write-Host "`n🌐 Starting development server..." -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:1421" -ForegroundColor Gray
Write-Host "Backend logs are using structured JSON format" -ForegroundColor Gray
Write-Host "Log location: %LOCALAPPDATA%\bad-character-scanner\logs" -ForegroundColor Gray

Write-Host "`n📊 Server Output:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

# Run cargo tauri dev
cargo tauri dev