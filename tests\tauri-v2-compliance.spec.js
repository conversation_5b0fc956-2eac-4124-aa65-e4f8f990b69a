import { test, expect } from '@playwright/test';
import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

test.describe('Tauri v2.5 Compliance Verification', () => {
  
  test('should verify no legacy Tauri imports in Rust source files', async () => {
    const srcDir = 'src';
    const rustFiles = getAllRustFiles(srcDir);
    
    for (const file of rustFiles) {
      const content = readFileSync(file, 'utf-8');
      
      // Check for correct v2.5 patterns
      expect(content).not.toMatch(/@tauri-apps\/api(?!\/)/); // No main package imports
      expect(content).not.toMatch(/invoke\(['"]tauri['"].*?\)/); // No legacy invoke patterns
      
      // If file contains Tauri invocations, verify they use correct v2.5 syntax
      if (content.includes('wasm_bindgen') && content.includes('invoke')) {
        expect(content).toMatch(/js_namespace = \["window", "__TAURI__", "core"\]/);
      }
    }
  });
  
  test('should verify Cargo.toml uses correct Tauri v2.5 dependencies', async () => {
    const cargoToml = readFileSync('src-tauri/Cargo.toml', 'utf-8');
    
    // Verify main Tauri dependency is v2.5.x
    expect(cargoToml).toMatch(/tauri = \{ version = "~2\.5\.\d+"/);
    
    // Verify plugins use correct v2.x versions
    expect(cargoToml).toMatch(/tauri-plugin-shell = "~2\.\d+\.\d+"/);
    expect(cargoToml).toMatch(/tauri-plugin-dialog = "~2\.\d+\.\d+"/);
    expect(cargoToml).toMatch(/tauri-plugin-fs = "~2\.\d+\.\d+"/);
    
    // Verify build dependency
    expect(cargoToml).toMatch(/tauri-build = \{ version = "~2\.\d+\.\d+"/);
  });
  
  test('should verify tauri.config.json uses v2.0.0 schema', async () => {
    const tauriConfig = readFileSync('src-tauri/tauri.config.json', 'utf-8');
    const config = JSON.parse(tauriConfig);
    
    // Verify schema version
    expect(config.$schema).toBe('https://schema.tauri.app/config/2.0.0');
    
    // Verify v2 structure
    expect(config).toHaveProperty('app');
    expect(config).toHaveProperty('build');
    expect(config).toHaveProperty('bundle');
    
    // Verify v2 app configuration
    expect(config.app).toHaveProperty('windows');
    expect(config.app).toHaveProperty('security');
  });
  
  test('should verify package.json uses correct Tauri v2 packages', async () => {
    const packageJson = readFileSync('package.json', 'utf-8');
    const pkg = JSON.parse(packageJson);
    
    // Verify dependencies use v2.x versions
    if (pkg.dependencies) {
      if (pkg.dependencies['@tauri-apps/api']) {
        expect(pkg.dependencies['@tauri-apps/api']).toMatch(/^~?2\.\d+\.\d+/);
      }
      if (pkg.dependencies['@tauri-apps/plugin-dialog']) {
        expect(pkg.dependencies['@tauri-apps/plugin-dialog']).toMatch(/^~?2\.\d+\.\d+/);
      }
      if (pkg.dependencies['@tauri-apps/plugin-fs']) {
        expect(pkg.dependencies['@tauri-apps/plugin-fs']).toMatch(/^~?2\.\d+\.\d+/);
      }
      if (pkg.dependencies['@tauri-apps/plugin-shell']) {
        expect(pkg.dependencies['@tauri-apps/plugin-shell']).toMatch(/^~?2\.\d+\.\d+/);
      }
    }
    
    if (pkg.devDependencies) {
      if (pkg.devDependencies['@tauri-apps/cli']) {
        expect(pkg.devDependencies['@tauri-apps/cli']).toMatch(/^~?2\.\d+\.\d+/);
      }
    }
  });
  
  test('should verify no JavaScript/TypeScript files use legacy Tauri imports', async () => {
    const jsFiles = getAllJSFiles('src');
    const testFiles = getAllJSFiles('tests');
    const allFiles = [...jsFiles, ...testFiles];
    
    for (const file of allFiles) {
      const content = readFileSync(file, 'utf-8');
      
      // Check for legacy import patterns
      expect(content).not.toMatch(/from ['"]@tauri-apps\/api['"](?!\/)/);
      expect(content).not.toMatch(/from ['"]@tauri-apps\/api\/tauri['"]/);
      expect(content).not.toMatch(/require\(['"]@tauri-apps\/api['"]\)/);
      
      // If file imports Tauri modules, verify they use specific v2 modules
      if (content.includes('@tauri-apps/')) {
        const lines = content.split('\\n');
        for (const line of lines) {
          if (line.includes('@tauri-apps/') && (line.includes('import') || line.includes('require'))) {
            // Should import from specific modules like @tauri-apps/api/core
            expect(line).toMatch(/@tauri-apps\/(api\/(core|window|event|fs|dialog|shell|cli|app|os)|plugin-)/);
          }
        }
      }
    }
  });
  
  test('should verify Rust backend uses correct command patterns', async () => {
    const backendFiles = getAllRustFiles('src-tauri/src');
    
    for (const file of backendFiles) {
      const content = readFileSync(file, 'utf-8');
      
      // If file defines Tauri commands, verify they use correct v2 patterns
      if (content.includes('#[tauri::command]')) {
        // Should use proper async/await patterns
        const commandLines = content.split('\\n').filter(line => 
          line.includes('#[tauri::command]') || 
          line.includes('async fn') ||
          line.includes('-> Result<')
        );
        
        // Basic verification that command structure looks correct
        expect(commandLines.length).toBeGreaterThan(0);
      }
    }
  });
  
  test('should verify frontend uses correct Tauri invoke patterns', async () => {
    const content = readFileSync('src/lib.rs', 'utf-8');
    
    // Verify correct wasm_bindgen external declaration
    expect(content).toMatch(/js_namespace = \["window", "__TAURI__", "core"\]/);
    
    // Verify invoke function is properly declared
    expect(content).toMatch(/async fn invoke\(cmd: &str, args: JsValue\) -> JsValue/);
  });
});

// Helper functions
function getAllRustFiles(dir) {
  const files = [];
  try {
    const entries = readdirSync(dir);
    for (const entry of entries) {
      const fullPath = join(dir, entry);
      const stat = statSync(fullPath);
      if (stat.isDirectory()) {
        files.push(...getAllRustFiles(fullPath));
      } else if (entry.endsWith('.rs')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.warn(`[getAllRustFiles] Directory not found or unreadable: "${dir}"`, error);
    return [];
  }
  return files;
}

function getAllJSFiles(dir) {
  const files = [];
  try {
    const entries = readdirSync(dir);
    for (const entry of entries) {
      const fullPath = join(dir, entry);
      const stat = statSync(fullPath);
      if (stat.isDirectory()) {
        files.push(...getAllJSFiles(fullPath));
      } else if (entry.endsWith('.js') || entry.endsWith('.ts') || entry.endsWith('.jsx') || entry.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.warn(`[getAllJSFiles] Directory not found or unreadable: "${dir}"`, error);
    return [];
  }
  return files;
}
