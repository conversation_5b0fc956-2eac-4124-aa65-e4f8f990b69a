# PowerShell Script to Analyze Frontend Issues (v4)

# --- CONFIGURATION ---
$projectRoot = "C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1"
$assetsPath = Join-Path $projectRoot "assets\images"
$searchPaths = @(
    (Join-Path $projectRoot "src"),
    (Join-Path $projectRoot "public"),
    (Join-Path $projectRoot "index.html"),
    (Join-Path $projectRoot "style.css")
)
$excludeDirs = @("node_modules", "dist", "target", "archive")
$imageExtensions = @("*.png", "*.svg", "*.ico", "*.jpg", "*.jpeg")

# --- SCRIPT BODY ---

# 1. Find all image and icon files
Write-Host "--- Image Usage Analysis ---"
Write-Host "--- Finding all image and icon files... ---"
$imageFiles = Get-ChildItem -Path $assetsPath -Recurse -Include $imageExtensions

Write-Host "Found $($imageFiles.Count) image files."

# 2. Search for image usage and identify unused files
Write-Host "`n--- Analyzing image usage... ---"
$unusedFiles = @()
foreach ($image in $imageFiles) {
    $found = $false
    $pattern = $image.Name

    foreach ($searchPath in $searchPaths) {
        if (Test-Path $searchPath) {
            if ((Get-Item $searchPath).PSIsContainer) {
                Get-ChildItem -Path $searchPath -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
                    if ($_.FullName -notmatch ($excludeDirs -join '|')) {
                        if (Select-String -Path $_.FullName -Pattern $pattern -SimpleMatch -Quiet) {
                            $found = $true
                        }
                    }
                }
            } else {
                if (Select-String -Path $searchPath -Pattern $pattern -SimpleMatch -Quiet) {
                    $found = $true
                }
            }
        }
        if ($found) { break }
    }

    if (-not $found) {
        $unusedFiles += $image
    }
}

# Move unused images to unused_images folder
$unusedDir = Join-Path $assetsPath "unused_images"
if (-not (Test-Path $unusedDir)) {
    New-Item -ItemType Directory -Path $unusedDir | Out-Null
}
foreach ($unused in $unusedFiles) {
    $target = Join-Path $unusedDir $unused.Name
    if ($unused.FullName -ne $target) {
        Move-Item -Path $unused.FullName -Destination $target -Force
    }
}

# Export unused image list to markdown
$mdPath = Join-Path $projectRoot "docs/FRONTEND_UNUSED_IMAGES_REPORT.md"
"# Unused Images Report (`$(Get-Date -Format yyyy-MM-dd)`)

## Unused Images
" | Set-Content $mdPath
foreach ($unused in $unusedFiles) {
    "- $($unused.Name)" | Add-Content $mdPath
}

if ($unusedFiles.Count -gt 0) {
    Write-Host "`nFound $($unusedFiles.Count) potentially unused image files:"
    $unusedFiles | ForEach-Object { Write-Host $_.FullName }
} else {
    Write-Host "`nAll image files appear to be used."
}

# 3. Analyze CSS for sizing rules
Write-Host "`n`n--- CSS Sizing Rule Analysis ---"
$cssFiles = Get-ChildItem -Path $projectRoot -Recurse -Include "*.css" -File -Exclude $excludeDirs

foreach ($cssFile in $cssFiles) {
    Write-Host "`n`n--- Analyzing: $($cssFile.FullName) ---"
    $content = Get-Content $cssFile.FullName -Raw
    $regex = '([^{}]+){([^}]+(?:width|height|max-width|max-height|flex-basis|background-size)[^}]+)}'
    $matches = $content | Select-String -Pattern $regex -AllMatches

    if ($matches) {
        $matches.Matches | ForEach-Object {
            Write-Host "Rule: $($_.Value.Trim())"
        }
    } else {
        Write-Host "No relevant sizing rules found in this file."
    }
}

Write-Host "`n--- Analysis complete. ---"