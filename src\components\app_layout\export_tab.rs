use leptos::*;
use crate::AnalysisResults;
use wasm_bindgen::JsValue;
use wasm_bindgen::JsCast;

#[component]
pub fn ExportTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
    let export_json = move |_| {
        if let Some(res) = results.get() {
            let json = serde_json::to_string_pretty(&res).unwrap_or_default();
            // Create download link
            let array = js_sys::Array::of1(&JsValue::from_str(&json));
            let blob = web_sys::Blob::new_with_str_sequence(&array).unwrap();
            
            let url = web_sys::Url::create_object_url_with_blob(&blob).unwrap();
            let document = web_sys::window().unwrap().document().unwrap();
            let a = document.create_element("a").unwrap();
            a.set_attribute("href", &url).unwrap();
            a.set_attribute("download", "bad-character-analysis.json").unwrap();
            a.dyn_ref::<web_sys::HtmlElement>().unwrap().click();
            web_sys::Url::revoke_object_url(&url).unwrap();
        }
    };
    
    view! {
        <div class="tab-content-inner">
            <h4>"💾 Export Results"</h4>
            <p>"Export your analysis results in various formats:"</p>
            
            <div class="export-options">
                <button class="btn btn-primary" on:click=export_json>
                    "📄 Export as JSON"
                </button>
                
                <button class="btn btn-secondary" disabled=true>
                    "📊 Export as CSV (Coming Soon)"
                </button>
                
                <button class="btn btn-secondary" disabled=true>
                    "📑 Export as PDF Report (Coming Soon)"
                </button>
                
                <button class="btn btn-secondary" disabled=true>
                    "🔍 Export as SARIF (Coming Soon)"
                </button>
            </div>
            
            <div class="export-preview mt-lg">
                <h5>"Preview:"</h5>
                <pre class="code-preview">
                    {move || results.get().map(|res| {
                        serde_json::to_string_pretty(&res).unwrap_or_default()
                    }).unwrap_or_else(|| "No data to export".to_string())}
                </pre>
            </div>
        </div>
    }
}
