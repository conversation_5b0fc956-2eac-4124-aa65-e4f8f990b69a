# DOC-3 - Format limitationAct.md documentation

**Status:** 🟢 Open  
**Priority:** Medium  
**Created:** 2025-06-10  
**Updated:** 2025-06-10  
**Assigned To:** @shoy  
**Related Issues:** N/A

## Description

The current `limitationAct.md` is lengthy and difficult to read. This ticket covers re-formatting the document to be more human-readable while preserving all technical detail.

Key goals:

1. Convert long JSON-style listings into well-structured Markdown tables.
2. Add short, friendly notes for every bad-character entry and for every supported file-type entry.
3. Split the work into manageable chunks to keep diffs small and review simple.
4. Ensure the final document passes the Leptos formatter and renders cleanly in common Markdown viewers.
5. Maintain explicit mention of Tauri v2 throughout the documentation where relevant, per project guidelines.

## Acceptance Criteria

- [ ] A new, table-based layout is introduced for each bad-character category.
- [ ] Each character row contains: character/name, Unicode code-point, category, and a concise human-readable note.
- [ ] File-type lists are converted to tables with columns: extension, category, scan-ability probability, scan approach note.
- [ ] No content loss from the original document.
- [ ] Document is broken into logical commit chunks (< ~400 lines per chunk).
- [ ] Result passes Leptos formatting and renders correctly in GitHub preview.
- [ ] Ticket status updated to ✅ when merged.

## Technical Details

- Use standard GitHub-flavoured Markdown tables.
- Keep tables narrow enough for horizontal scroll avoidance where practical.
- Where probability values are speculative, use qualitative scale (High / Medium / Low) unless numeric data exists.
- Formatting work must not break existing links or anchors referenced elsewhere in the docs.

## Dependencies

- None.

## Progress

- [ ] Create initial table templates.
- [ ] Migrate first 300 lines (bad-character overview).
- [ ] Migrate remaining bad-character sections.
- [ ] Migrate file-type sections.
- [ ] Proof-read & run Leptos formatter.

## Testing

- Manual inspection in GitHub preview.
- Validate with Markdown linter if available in CI.

## Notes

This ticket precedes actual formatting work. Once approved, subsequent PRs will reference DOC-3.

---
*Last updated: 2025-06-10*
