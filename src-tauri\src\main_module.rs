// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// Main Tauri Backend Module - Command Handlers and Core Logic
// Advanced Security Analysis Backend - By <PERSON> - 2025

// Don't declare modules here, they're declared in lib.rs

// Removed unused HashMap import
// Removed unused imports - keeping only what's needed
use tauri::Emitter;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
// Removed unused chrono::Utc import
// Removed unused sha2::Digest import
// Removed unused uuid::Uuid import

// Import modular components
use crate::modules::{
    data_structures::*,
    character_analyzer::CharacterAnalyzer,
};
use crate::modules::cleaning_operations::CleaningOperations;

// Re-export CharacterAnalyzer for easy access in this module
// pub use crate::modules::character_analyzer::CharacterAnalyzer; // This can be removed if CharacterAnalyzer is always qualified or brought in scope by `use`

// Structs for commands moved/created here
#[derive(Debug, Serialize, Deserialize)]
pub struct CleaningRequest {
    pub folder_path: String,
    pub output_path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CodebaseAnalysisRequest {
    pub path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExportCodebaseReportRequest {
    pub report_id: String, // Example field
    pub format: String,
}

// Drag & Drop related structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DroppedFile {
    pub path: String,
    pub name: String,
    pub size: u64,
    pub is_directory: bool,
    pub file_type: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DragDropValidationResult {
    pub valid_files: Vec<DroppedFile>,
    pub invalid_files: Vec<String>,
    pub total_files: usize,
    pub total_size: u64,
    pub validation_errors: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchProcessingRequest {
    pub file_paths: Vec<String>,
    pub options: ProcessingOptions,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingOptions {
    pub max_file_size_mb: Option<u64>,
    pub supported_extensions: Option<Vec<String>>,
    pub recursive: bool,
    pub create_individual_reports: bool,
    pub create_summary_report: bool,
}

impl Default for ProcessingOptions {
    fn default() -> Self {
        Self {
            max_file_size_mb: Some(50),
            supported_extensions: Some(vec![
                "txt".to_string(), "js".to_string(), "ts".to_string(), 
                "py".to_string(), "rs".to_string(), "md".to_string(),
                "json".to_string(), "xml".to_string(), "html".to_string(), 
                "css".to_string()
            ]),
            recursive: true,
            create_individual_reports: true,
            create_summary_report: true,
        }
    }
}

// Tauri command handlers
// Removed unused greet function

#[tauri::command]
pub async fn analyze_characters(text: String) -> Result<AnalysisResults, String> {
    eprintln!("Tauri Backend: Entered analyze_characters function.");
    let truncated_text = text.chars().take(100).collect::<String>();
    eprintln!("Tauri Backend: Received text for analysis in analyze_characters. Length: {}. First 100 chars: \"{}\"", text.len(), truncated_text);
    let analyzer = CharacterAnalyzer::new().map_err(|e| e.to_string())?;
    let results = analyzer.analyze_text(&text);

    // Debug: Print the JSON being sent to the frontend
    match serde_json::to_string_pretty(&results) {
        Ok(json_string) => {
            eprintln!("Tauri Backend: Sending AnalysisResults JSON from analyze_characters:\\\\n{}", json_string);
        }
        Err(e) => {
            eprintln!("Tauri Backend: Error serializing AnalysisResults for logging in analyze_characters: {}", e);
        }
    }

    Ok(results)
}

// Add alias for frontend compatibility
#[tauri::command]
pub async fn analyze_text(text: String) -> Result<AnalysisResults, String> {
    analyze_characters(text).await
}

// Removed unused analyze_file function

#[tauri::command]
pub async fn batch_analyze(texts: Vec<String>) -> Result<Vec<AnalysisResults>, String> {
    let analyzer = CharacterAnalyzer::new().map_err(|e| e.to_string())?;
    let mut results = Vec::new();
    
    for text in texts {
        let analysis = analyzer.analyze_text(&text);
        results.push(analysis);
    }
    
    Ok(results)
}

// Assuming clean_text command is this one, add map_err
#[tauri::command]
pub async fn clean_text(text: String) -> Result<String, String> {
    let analyzer = CharacterAnalyzer::new().map_err(|e| e.to_string())?;
    let cleaned_text = analyzer.clean_text(&text);
    Ok(cleaned_text)
}

// Add clean_codebase command (moved from modules/commands.rs)
#[tauri::command]
pub async fn clean_codebase(request: CleaningRequest) -> Result<String, String> {
    let _cleaner = CleaningOperations::new()
        .map_err(|e| e.to_string())?;
    // Placeholder implementation - actual cleaning logic would go here
    // For example, iterating through files in request.folder_path, cleaning them,
    // and saving to request.output_path
    eprintln!("Laptos_Tauri Backend: clean_codebase called with path: {}", request.folder_path);
    Ok(format!("Cleaning process initiated for {}. Output to {}. (Placeholder)", request.folder_path, request.output_path))
}

// Add placeholder for export_codebase_report command
#[tauri::command]
pub async fn export_codebase_report(request: ExportCodebaseReportRequest) -> Result<String, String> {
    // Placeholder implementation
    eprintln!("Laptos_Tauri Backend: export_codebase_report called for report {} in format {}", request.report_id, request.format);
    Ok(format!("Codebase report {} would be exported in {} format. (Placeholder)", request.report_id, request.format))
}

// Basic progress tracking structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BasicProgress {
    pub current: u32,
    pub total: u32,
    pub percentage: f32,
    pub current_file: String,
    pub status: String,
}

// Enhanced codebase analysis command
#[tauri::command]
pub async fn analyze_codebase(request: CodebaseAnalysisRequest, app_handle: tauri::AppHandle) -> Result<CodeBaseAnalysisResult, String> {
    eprintln!("Laptos_Tauri Backend: analyze_codebase called with path: {}", request.path);
    
    let analyzer = CharacterAnalyzer::new().map_err(|e| e.to_string())?;
    
    // Check if it's a file or directory
    let path = std::path::Path::new(&request.path);
    if !path.exists() {
        return Err(format!("Path does not exist: {}", request.path));
    }

    // Count total files for progress tracking
    let total_files = if path.is_file() {
        1u32
    } else if path.is_dir() {
        count_files_in_directory(&request.path)?
    } else {
        return Err(format!("Invalid path type: {}", request.path));
    };

    // Emit initial progress
    let initial_progress = BasicProgress {
        current: 0,
        total: total_files,
        percentage: 0.0,
        current_file: "Starting analysis...".to_string(),
        status: "Starting".to_string(),
    };
    app_handle.emit("analysis-progress", &initial_progress).ok();
    
    let mut files_analyzed = 0;
    let mut total_suspicious_chars = 0;
    let mut all_file_results = Vec::new();
    let mut processed_count = 0u32;
      if path.is_file() {
        // Analyze single file
        processed_count += 1;
          // Emit progress for single file
        let progress = BasicProgress {
            current: processed_count,
            total: total_files,
            percentage: (processed_count as f32 / total_files as f32) * 100.0,
            current_file: request.path.clone(),
            status: "Analyzing".to_string(),
        };
        app_handle.emit("analysis-progress", &progress).ok();
        
        match std::fs::read_to_string(path) {
            Ok(content) => {
                let analysis = analyzer.analyze_text(&content);
                total_suspicious_chars += analysis.suspicious_characters.len();
                files_analyzed = 1;
                
                all_file_results.push(FileAnalysisDetail {
                    file_path: request.path.clone(),
                    relative_path: request.path.clone(),
                    file_size: content.len() as u64,
                    total_characters: analysis.total_characters,
                    suspicious_characters: analysis.suspicious_characters.len(),
                    issues: analysis.suspicious_characters.iter()
                        .map(|c| format!("Suspicious character '{}' at position {}", c.character, c.position))
                        .collect(),
                    file_type: "text".to_string(),
                    encoding: "UTF-8".to_string(),
                    analysis_status: "success".to_string(),
                    error_message: None,
                });
                  // Emit completion progress
                let completion_progress = BasicProgress {
                    current: processed_count,
                    total: total_files,
                    percentage: 100.0,
                    current_file: "✅ Analysis complete!".to_string(),
                    status: "Complete".to_string(),
                };
                app_handle.emit("analysis-progress", &completion_progress).ok();
            }
            Err(e) => {
                eprintln!("Warning: Failed to read file {}: {}", request.path, e);                // Still emit progress for failed file
                let error_progress = BasicProgress {
                    current: processed_count,
                    total: total_files,
                    percentage: 100.0,
                    current_file: format!("❌ Failed to read: {}", e),
                    status: "Error".to_string(),
                };
                app_handle.emit("analysis-progress", &error_progress).ok();
                return Err(format!("Failed to read file: {}", e));
            }
        }    } else if path.is_dir() {
        // Analyze directory recursively with progress
        match analyze_directory_recursive_with_progress(path, &analyzer, &app_handle, total_files, &mut processed_count) {
            Ok((files, suspicious_count, file_details)) => {
                files_analyzed = files;
                total_suspicious_chars = suspicious_count;
                all_file_results = file_details;
                  // Emit final completion progress
                let completion_progress = BasicProgress {
                    current: total_files,
                    total: total_files,
                    percentage: 100.0,
                    current_file: "✅ Analysis complete!".to_string(),
                    status: "Complete".to_string(),
                };
                app_handle.emit("analysis-progress", &completion_progress).ok();
            }
            Err(e) => return Err(format!("Failed to analyze directory: {}", e)),
        }
    }
    
    // Create summary result
    let result = CodeBaseAnalysisResult {
        total_files: files_analyzed,
        files_with_issues: all_file_results.iter().filter(|f| f.suspicious_characters > 0).count(),
        total_suspicious_chars: total_suspicious_chars,
        health_score: if files_analyzed > 0 {
            ((files_analyzed - all_file_results.iter().filter(|f| f.suspicious_characters > 0).count()) as f64 / files_analyzed as f64) * 100.0
        } else {
            100.0
        },
        file_details: all_file_results,
        analysis_time_ms: 0, // Placeholder
        backdoor_count: 0, // TODO: Replace with real value
        frontdoor_count: 0, // TODO: Replace with real value
    };
    
    eprintln!("Laptos_Tauri Backend: analyze_codebase completed. Files analyzed: {}, Suspicious chars: {}", files_analyzed, total_suspicious_chars);    Ok(result)
}

// NEW: Advanced codebase analysis using the new modular analysis system
#[tauri::command]
pub async fn analyze_codebase_advanced(request: CodebaseAnalysisRequest, app_handle: tauri::AppHandle) -> Result<crate::analysis::ComprehensiveAnalysisResult, String> {
    eprintln!("Laptos_Tauri Backend: analyze_codebase_advanced called with path: {}", request.path);
    
    // Import the advanced analysis system
    use crate::analysis::CodebaseAnalyzer;
    
    // Check if path exists
    let path = std::path::Path::new(&request.path);
    if !path.exists() {
        return Err(format!("Path does not exist: {}", request.path));
    }    // Create the advanced analyzer
    let analyzer = CodebaseAnalyzer::new().map_err(|e| e.to_string())?;
    
    // Emit initial progress
    let initial_progress = BasicProgress {
        current: 0,
        total: 100,
        percentage: 0.0,
        current_file: "Initializing advanced analysis...".to_string(),
        status: "Starting".to_string(),
    };
    app_handle.emit("analysis-progress", &initial_progress).unwrap_or_else(|e| {
        eprintln!("Failed to emit initial progress: {}", e);
    });
    
    // Run the comprehensive analysis with progress tracking
    let app_handle_clone = app_handle.clone();
    let progress_callback = move |current: u32, total: u32, percentage: f32, current_file: &str, status: &str| {
        let progress = BasicProgress {
            current,
            total,
            percentage,
            current_file: current_file.to_string(),
            status: status.to_string(),
        };
        let _ = app_handle_clone.emit("analysis-progress", &progress);
    };

    match analyzer.analyze_codebase_with_progress(&request.path, crate::analysis::ScanDepth::Deep, Some(progress_callback)) {
        Ok(report) => {
            // Emit completion progress
            let completion_progress = BasicProgress {
                current: 100,
                total: 100,
                percentage: 100.0,
                current_file: "✅ Advanced analysis complete!".to_string(),
                status: "Complete".to_string(),
            };
            app_handle.emit("analysis-progress", &completion_progress).unwrap_or_else(|e| {
                eprintln!("Failed to emit completion progress: {}", e);
            });
            
            eprintln!("Laptos_Tauri Backend: analyze_codebase_advanced completed successfully");
            Ok(report)
        }
        Err(e) => {
            // Emit error progress
            let error_progress = BasicProgress {
                current: 0,
                total: 100,
                percentage: 0.0,
                current_file: format!("❌ Analysis failed: {}", e),
                status: "Error".to_string(),
            };
            app_handle.emit("analysis-progress", &error_progress).unwrap_or_else(|emit_err| {
                eprintln!("Failed to emit error progress: {}", emit_err);
            });
            
            Err(format!("Advanced analysis failed: {}", e))
        }
    }
}

// Helper function to count files in directory for progress tracking
fn count_files_in_directory(dir_path: &str) -> Result<u32, String> {
    use std::fs;
    let mut count = 0u32;
    
    fn count_recursive(path: &std::path::Path, counter: &mut u32) -> std::io::Result<()> {
        if path.is_dir() {
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                let path = entry.path();
                if path.is_dir() {
                    count_recursive(&path, counter)?;
                } else if path.is_file() {
                    // Only count text files that we would analyze
                    if let Some(ext) = path.extension() {
                        if let Some(ext_str) = ext.to_str() {
                            if matches!(ext_str.to_lowercase().as_str(), 
                                "rs" | "js" | "ts" | "py" | "txt" | "md" | "json" | "toml" | "yaml" | "yml" | 
                                "html" | "css" | "scss" | "less" | "jsx" | "tsx" | "vue" | "go" | "java" | 
                                "cpp" | "c" | "h" | "hpp" | "php" | "rb" | "swift" | "kt" | "cs" | "xml") {
                                *counter += 1;
                            }
                        }
                    }
                }
            }
        }
        Ok(())
    }
    
    count_recursive(std::path::Path::new(dir_path), &mut count)
        .map_err(|e| format!("Failed to count files: {}", e))?;
    
    Ok(count)
}

// Helper function for recursive directory analysis with progress
fn analyze_directory_recursive_with_progress(
    dir_path: &std::path::Path, 
    analyzer: &CharacterAnalyzer,
    app_handle: &tauri::AppHandle,
    total_files: u32,
    processed_count: &mut u32,
) -> Result<(usize, usize, Vec<FileAnalysisDetail>), Box<dyn std::error::Error>> {
    let mut files_count = 0;
    let mut suspicious_count = 0;
    let mut file_details = Vec::new();
    
    fn visit_dir_with_progress(
        dir: &std::path::Path, 
        analyzer: &CharacterAnalyzer,
        app_handle: &tauri::AppHandle,
        total_files: u32,
        processed_count: &mut u32,
        files_count: &mut usize,
        suspicious_count: &mut usize,
        file_details: &mut Vec<FileAnalysisDetail>
    ) -> std::io::Result<()> {
        use std::fs;
        
        if dir.is_dir() {
            for entry in fs::read_dir(dir)? {
                let entry = entry?;
                let path = entry.path();
                
                // Skip hidden files and common non-text directories
                if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                    if name.starts_with('.') || name == "node_modules" || name == "target" {
                        continue;
                    }
                }
                
                if path.is_dir() {
                    visit_dir_with_progress(&path, analyzer, app_handle, total_files, processed_count, files_count, suspicious_count, file_details)?;
                } else if is_text_file(&path) {
                    *processed_count += 1;
                    
                    // Emit progress
                    let progress = BasicProgress {
                        current: *processed_count,
                        total: total_files,
                        percentage: (*processed_count as f32 / total_files as f32) * 100.0,
                        current_file: path.to_string_lossy().to_string(),
                        status: "Analyzing".to_string(),
                    };
                    let _ = app_handle.emit("analysis-progress", &progress);
                    
                    if let Ok(content) = fs::read_to_string(&path) {
                        let analysis = analyzer.analyze_text(&content);
                        *suspicious_count += analysis.suspicious_characters.len();
                        *files_count += 1;
                        
                        file_details.push(FileAnalysisDetail {
                            file_path: path.to_string_lossy().to_string(),
                            relative_path: path.to_string_lossy().to_string(),
                            file_size: content.len() as u64,
                            total_characters: analysis.total_characters,
                            suspicious_characters: analysis.suspicious_characters.len(),
                            issues: analysis.suspicious_characters.iter()
                                .map(|c| format!("Suspicious character '{}' at position {}", c.character, c.position))
                                .collect(),
                            file_type: path.extension()
                                .and_then(|e| e.to_str())
                                .unwrap_or("unknown")
                                .to_string(),
                            encoding: "UTF-8".to_string(),
                            analysis_status: "success".to_string(),
                            error_message: None,
                        });
                    }
                }
            }
        }
        Ok(())
    }
    
    visit_dir_with_progress(dir_path, analyzer, app_handle, total_files, processed_count, &mut files_count, &mut suspicious_count, &mut file_details)?;
    Ok((files_count, suspicious_count, file_details))
}

// Helper function to check if a file is likely a text file
fn is_text_file(path: &std::path::Path) -> bool {
    if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
        matches!(extension.to_lowercase().as_str(), 
            "txt" | "rs" | "js" | "ts" | "html" | "css" | "json" | "xml" | "md" | 
            "py" | "java" | "cpp" | "c" | "h" | "hpp" | "go" | "php" | "rb" | 
            "yml" | "yaml" | "toml" | "ini" | "cfg" | "conf" | "log" | "sh" | "bat"
        )
    } else {
        false
    }
}

// Add missing Tauri commands that are referenced in lib.rs

#[tauri::command]
pub async fn export_analysis(format: String, filename: String, _analysis_data: Option<String>) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: export_analysis called with format: {}, filename: {}", format, filename);
    Ok(format!("Analysis exported in {} format to {}", format, filename))
}

#[tauri::command]
pub async fn clean_codebase_with_verification(_request: CleaningRequest) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: clean_codebase_with_verification called");
    Ok("Cleaning with verification completed".to_string())
}

#[tauri::command]
pub async fn get_character_details(character: char) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: get_character_details called");
    Ok(format!("Character details for: {}", character))
}

#[tauri::command]
pub async fn detect_encoding(_text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: detect_encoding called");
    Ok("UTF-8".to_string())
}

#[tauri::command]
pub async fn check_homographs(text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: check_homographs called");
    Ok(format!("Homograph analysis for {} characters", text.len()))
}

#[tauri::command]
pub async fn normalize_text(text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: normalize_text called");
    Ok(text.chars().filter(|c| !c.is_control()).collect())
}

#[tauri::command]
pub async fn get_script_info(text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: get_script_info called");
    Ok(format!("Script info for {} characters", text.len()))
}

#[tauri::command]
pub async fn clean_text_detailed(text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: clean_text_detailed called");
    let analyzer = CharacterAnalyzer::new().map_err(|e| e.to_string())?;
    Ok(analyzer.clean_text(&text))
}

#[tauri::command]
pub async fn generate_report(_data: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: generate_report called");
    Ok("Report generated successfully".to_string())
}

#[tauri::command]
pub async fn get_file_types_summary(path: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: get_file_types_summary called");
    Ok(format!("File types summary for: {}", path))
}

#[tauri::command]
pub async fn detect_ai_content(text: String) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: detect_ai_content called");
    Ok(format!("AI content analysis for {} characters", text.len()))
}

#[tauri::command]
pub async fn new_file() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: new_file called");
    Ok("New file created".to_string())
}

#[tauri::command]
pub async fn open_file() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: open_file called");
    Ok("File opened".to_string())
}

#[tauri::command]
pub async fn save_file() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: save_file called");
    Ok("File saved".to_string())
}

#[tauri::command]
pub async fn open_terminal() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: open_terminal called");
    Ok("Terminal opened".to_string())
}

#[tauri::command]
pub async fn save_settings() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: save_settings called");
    Ok("Settings saved".to_string())
}

#[tauri::command]
pub async fn exit_app(app_handle: tauri::AppHandle) -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: exit_app called");
    app_handle.exit(0);
    Ok("Exiting application".to_string())
}

#[tauri::command]
pub async fn open_reports_folder() -> Result<String, String> {
    eprintln!("Laptos_Tauri Backend: open_reports_folder called");
    Ok("Reports folder opened".to_string())
}

#[tauri::command]
pub async fn validate_dropped_files(paths: Vec<String>) -> Result<DragDropValidationResult, String> {
    eprintln!("Laptos_Tauri Backend: validate_dropped_files called with {} paths", paths.len());

    let mut valid_files = Vec::new();
    let mut invalid_files = Vec::new();
    let mut total_size = 0u64;
    let mut validation_errors = Vec::new();

    for path in paths {
        match std::fs::metadata(&path) {
            Ok(metadata) => {
                if metadata.is_file() {
                    // Check if it's a text file we can analyze
                    let extension = std::path::Path::new(&path)
                        .extension()
                        .and_then(|ext| ext.to_str())
                        .unwrap_or("")
                        .to_lowercase();

                    let supported_extensions = vec![
                        "txt", "rs", "js", "ts", "py", "java", "cpp", "c", "h", "hpp",
                        "cs", "php", "rb", "go", "swift", "kt", "scala", "clj", "hs",
                        "ml", "fs", "elm", "dart", "lua", "r", "m", "pl", "sh", "bash",
                        "zsh", "fish", "ps1", "bat", "cmd", "html", "htm", "xml", "css",
                        "scss", "sass", "less", "json", "yaml", "yml", "toml", "ini",
                        "cfg", "conf", "config", "md", "rst", "tex", "sql", "dockerfile"
                    ];

                    if supported_extensions.contains(&extension.as_str()) || extension.is_empty() {
                        total_size += metadata.len();
                        valid_files.push(DroppedFile {
                            path: path.clone(),
                            name: std::path::Path::new(&path).file_name()
                                .and_then(|n| n.to_str())
                                .unwrap_or("unknown")
                                .to_string(),
                            size: metadata.len(),
                            is_directory: false,
                            file_type: Some(extension),
                        });
                    } else {
                        invalid_files.push(path.clone());
                        validation_errors.push(format!("Unsupported file type: {}", path));
                    }
                } else if metadata.is_dir() {
                    // For directories, we'll accept them and scan recursively
                    valid_files.push(DroppedFile {
                        path: path.clone(),
                        name: std::path::Path::new(&path).file_name()
                            .and_then(|n| n.to_str())
                            .unwrap_or("unknown")
                            .to_string(),
                        size: 0, // Directories don't have a meaningful size
                        is_directory: true,
                        file_type: None,
                    });
                } else {
                    invalid_files.push(path.clone());
                    validation_errors.push(format!("Not a regular file or directory: {}", path));
                }
            }
            Err(e) => {
                invalid_files.push(path.clone());
                validation_errors.push(format!("Cannot access {}: {}", path, e));
            }
        }
    }

    let total_files_count = valid_files.len() + invalid_files.len();

    Ok(DragDropValidationResult {
        valid_files,
        invalid_files,
        total_files: total_files_count,
        total_size,
        validation_errors,
    })
}

#[tauri::command]
pub async fn process_dropped_files(request: BatchProcessingRequest) -> Result<Vec<AnalysisResults>, String> {
    eprintln!("Laptos_Tauri Backend: process_dropped_files called with {} files", request.file_paths.len());

    let mut results = Vec::new();

    for (index, file_path) in request.file_paths.iter().enumerate() {
        eprintln!("Processing file {}/{}: {}", index + 1, request.file_paths.len(), file_path);

        match std::fs::metadata(file_path) {
            Ok(metadata) => {
                if metadata.is_file() {
                    // Process individual file
                    match std::fs::read_to_string(file_path) {
                        Ok(content) => {
                            // Use the existing character analyzer
                            match crate::modules::CharacterAnalyzer::new() {
                                Ok(analyzer) => {
                                    let analysis = analyzer.analyze_text(&content);
                                    results.push(analysis);
                                }
                                Err(e) => {
                                    eprintln!("Failed to create analyzer: {}", e);
                                    // Create a basic error result
                                    results.push(AnalysisResults {
                                        id: format!("error_{}", index),
                                        timestamp: chrono::Utc::now().to_rfc3339(),
                                        input_text: content.clone(),
                                        text_hash: format!("{:x}", md5::compute(&content)),
                                        total_characters: content.chars().count(),
                                        total_bytes: content.len(),
                                        total_graphemes: content.chars().count(), // Simplified
                                        visual_width: content.chars().count(), // Simplified
                                        suspicious_characters: Vec::new(),
                                        character_breakdown: std::collections::HashMap::new(),
                                        script_breakdown: std::collections::HashMap::new(),
                                        encoding_info: crate::modules::EncodingInfo {
                                            detected_encoding: "Unknown".to_string(),
                                            confidence: 0.0,
                                            is_valid_utf8: false,
                                            bom_detected: None,
                                            line_endings: "Unknown".to_string(),
                                        },
                                        security_analysis: crate::modules::SecurityAnalysis {
                                            risk_level: "Unknown".to_string(),
                                            phishing_indicators: Vec::new(),
                                            homograph_attacks: Vec::new(),
                                            script_mixing: Vec::new(),
                                            steganography_potential: false,
                                        },
                                        patterns_found: Vec::new(),
                                        recommendations: vec![format!("Failed to analyze file: {}", e)],
                                        analysis_duration_ms: 0,
                                        confidence_score: 0.0,
                                    });
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to read file {}: {}", file_path, e);
                            // Create an error result
                            results.push(AnalysisResults {
                                id: format!("read_error_{}", index),
                                timestamp: chrono::Utc::now().to_rfc3339(),
                                input_text: String::new(),
                                text_hash: format!("{:x}", md5::compute("")),
                                total_characters: 0,
                                total_bytes: 0,
                                total_graphemes: 0,
                                visual_width: 0,
                                suspicious_characters: Vec::new(),
                                character_breakdown: std::collections::HashMap::new(),
                                script_breakdown: std::collections::HashMap::new(),
                                encoding_info: crate::modules::EncodingInfo {
                                    detected_encoding: "Unknown".to_string(),
                                    confidence: 0.0,
                                    is_valid_utf8: false,
                                    bom_detected: None,
                                    line_endings: "Unknown".to_string(),
                                },
                                security_analysis: crate::modules::SecurityAnalysis {
                                    risk_level: "Unknown".to_string(),
                                    phishing_indicators: Vec::new(),
                                    homograph_attacks: Vec::new(),
                                    script_mixing: Vec::new(),
                                    steganography_potential: false,
                                },
                                patterns_found: Vec::new(),
                                recommendations: vec![format!("Could not read file: {}", e)],
                                analysis_duration_ms: 0,
                                confidence_score: 0.0,
                            });
                        }
                    }
                } else if metadata.is_dir() {
                    // For directories, use the existing codebase analysis
                    let _request = CodebaseAnalysisRequest { path: file_path.clone() };
                    // We need an AppHandle for the analysis, but we don't have one in this context
                    // For now, let's use a simpler approach or skip directory analysis in batch processing
                    eprintln!("Directory processing in batch mode not yet implemented: {}", file_path);
                    results.push(AnalysisResults {
                        id: format!("dir_skip_{}", index),
                        timestamp: chrono::Utc::now().to_rfc3339(),
                        input_text: String::new(),
                        text_hash: format!("{:x}", md5::compute("")),
                        total_characters: 0,
                        total_bytes: 0,
                        total_graphemes: 0,
                        visual_width: 0,
                        suspicious_characters: Vec::new(),
                        character_breakdown: HashMap::new(),
                        script_breakdown: HashMap::new(),
                        encoding_info: crate::modules::EncodingInfo {
                            detected_encoding: "UTF-8".to_string(),
                            confidence: 1.0,
                            is_valid_utf8: true,
                            bom_detected: None,
                            line_endings: "LF".to_string(),
                        },
                        security_analysis: crate::modules::SecurityAnalysis {
                            risk_level: "Low".to_string(),
                            phishing_indicators: Vec::new(),
                            homograph_attacks: Vec::new(),
                            script_mixing: Vec::new(),
                            steganography_potential: false,
                        },
                        patterns_found: Vec::new(),
                        recommendations: vec![format!("Directory analysis skipped in batch mode: {}", file_path)],
                        analysis_duration_ms: 0,
                        confidence_score: 0.0,
                    });
                    continue;
                }
            }
            Err(e) => {
                eprintln!("Failed to get metadata for {}: {}", file_path, e);
                results.push(AnalysisResults {
                    id: format!("metadata_error_{}", index),
                    timestamp: chrono::Utc::now().to_rfc3339(),
                    input_text: String::new(),
                    text_hash: format!("{:x}", md5::compute("")),
                    total_characters: 0,
                    total_bytes: 0,
                    total_graphemes: 0,
                    visual_width: 0,
                    suspicious_characters: Vec::new(),
                    character_breakdown: HashMap::new(),
                    script_breakdown: HashMap::new(),
                    encoding_info: crate::modules::EncodingInfo {
                        detected_encoding: "Unknown".to_string(),
                        confidence: 0.0,
                        is_valid_utf8: false,
                        bom_detected: None,
                        line_endings: "Unknown".to_string(),
                    },
                    security_analysis: crate::modules::SecurityAnalysis {
                        risk_level: "Unknown".to_string(),
                        phishing_indicators: Vec::new(),
                        homograph_attacks: Vec::new(),
                        script_mixing: Vec::new(),
                        steganography_potential: false,
                    },
                    patterns_found: Vec::new(),
                    recommendations: vec![format!("Could not access file: {}", e)],
                    analysis_duration_ms: 0,
                    confidence_score: 0.0,
                });
            }
        }
    }

    Ok(results)
}
