<#
.SYNOPSIS
    Cleans bad characters from files using the Bad Character Scanner
    
.DESCRIPTION
    This script provides a PowerShell interface to clean bad characters from files
    
.PARAMETER InputPath
    Path to the input file
    
.PARAMETER OutputPath
    Path for the cleaned output file
    
.PARAMETER Backup
    Create a backup of the original file
    
.EXAMPLE
    .\clean-file.ps1 -InputPath "dirty.txt" -OutputPath "clean.txt"
    
.EXAMPLE
    .\clean-file.ps1 -InputPath "code.js" -Backup
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputPath,
    
    [string]$OutputPath,
    
    [switch]$Backup
)

$projectRoot = Split-Path -Parent $PSScriptRoot
$cliPath = Join-Path $projectRoot "src-tauri"

# Build the CLI if needed
if (-not (Test-Path (Join-Path $projectRoot "target\release\analyzer_cli.exe"))) {
    Write-Host "Building analyzer CLI..." -ForegroundColor Yellow
    Push-Location $cliPath
    cargo build --bin analyzer_cli --release
    Pop-Location
}

# Create backup if requested
if ($Backup) {
    $backupPath = "$InputPath.bak"
    Copy-Item $InputPath $backupPath -Force
    Write-Host "Backup created: $backupPath" -ForegroundColor Green
}

# If no output path specified, use default
if (-not $OutputPath) {
    $OutputPath = [System.IO.Path]::ChangeExtension($InputPath, ".cleaned" + [System.IO.Path]::GetExtension($InputPath))
}

# Convert to absolute paths
$InputPath = [System.IO.Path]::GetFullPath($InputPath)
$OutputPath = [System.IO.Path]::GetFullPath($OutputPath)

# Prepare command
$args = @("run", "--bin", "analyzer_cli", "--", "clean", $InputPath, "-o", $OutputPath)

# Run cleaner
Push-Location $cliPath
try {
    $output = & cargo $args 2>&1
    
    if (Test-Path $OutputPath) {
        Write-Host "File cleaned successfully: $OutputPath" -ForegroundColor Green
        
        # Show statistics
        $originalSize = (Get-Item $InputPath).Length
        $cleanedSize = (Get-Item $OutputPath).Length
        $removed = $originalSize - $cleanedSize
        
        Write-Host "Original size: $originalSize bytes" -ForegroundColor Cyan
        Write-Host "Cleaned size: $cleanedSize bytes" -ForegroundColor Cyan
        Write-Host "Removed: $removed bytes" -ForegroundColor Yellow
    } else {
        Write-Host "Error: Failed to clean file" -ForegroundColor Red
        Write-Host $output
    }
} finally {
    Pop-Location
}