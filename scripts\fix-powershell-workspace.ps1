#!/usr/bin/env powershell
# Fix PowerShell Extension workspace warnings for archived scripts

Write-Host "Fixing PowerShell Extension Workspace Issues" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$scripts_dir = Join-Path $project_root "scripts"
$archive_dir = Join-Path $scripts_dir "Script Archives"

# List of missing scripts that PowerShell Extension is looking for
$missing_scripts = @(
    "setup-simple.ps1",
    "fix-folder-selection.ps1", 
    "fix-cleaning-functionality.ps1",
    "fix-folder-selection-clean.ps1",
    "fix-cleaning-functionality-clean.ps1"
)

Write-Host "`nAnalyzing PowerShell workspace issues..." -ForegroundColor Yellow

# Check if archived scripts exist
$archived_scripts = @()
$truly_missing = @()

foreach ($script in $missing_scripts) {
    $archive_path = Join-Path $archive_dir $script
    $root_path = Join-Path $scripts_dir $script
    
    if (Test-Path $archive_path) {
        $archived_scripts += @{
            name = $script
            archive_path = $archive_path
            root_path = $root_path
        }
        Write-Host "  Found in archive: $script" -ForegroundColor Green
    } else {
        $truly_missing += $script
        Write-Host "  Truly missing: $script" -ForegroundColor Red
    }
}

Write-Host "`nApplying Automatic Fix: Clean PowerShell workspace cache" -ForegroundColor Magenta

# Clean PowerShell Extension workspace cache
Write-Host "`nCleaning PowerShell workspace cache..." -ForegroundColor Cyan

$vscode_dir = Join-Path $project_root ".vscode"
if (Test-Path $vscode_dir) {
    $ps_cache_patterns = @(
        "*.ps1.cache",
        "powershell.cache",
        ".powershell"
    )
    
    foreach ($pattern in $ps_cache_patterns) {
        $cache_files = Get-ChildItem -Path $vscode_dir -Filter $pattern -Recurse -ErrorAction SilentlyContinue
        foreach ($file in $cache_files) {
            Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
            Write-Host "  Removed cache: $($file.Name)" -ForegroundColor Gray
        }
    }
}

Write-Host "  PowerShell workspace cache cleared" -ForegroundColor Green

Write-Host "`nManual Instructions:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

Write-Host "`n1. Restart VS Code completely" -ForegroundColor Yellow
Write-Host "2. If warnings persist, disable and re-enable PowerShell Extension" -ForegroundColor Yellow
Write-Host "3. The archived scripts are available in: scripts/Script Archives/" -ForegroundColor Yellow

Write-Host "`nArchived script locations:" -ForegroundColor White
foreach ($script_info in $archived_scripts) {
    Write-Host "  $($script_info.name) -> scripts/Script Archives/$($script_info.name)" -ForegroundColor Gray
}

Write-Host "`nPowerShell workspace fix completed!" -ForegroundColor Green
Write-Host "Note: These scripts were archived because they're for an older codebase structure." -ForegroundColor Yellow
Write-Host "Current active scripts are in the main scripts/ directory." -ForegroundColor Yellow

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Restart VS Code to clear any cached references" -ForegroundColor White
Write-Host "2. Check that PowerShell Extension warnings are resolved" -ForegroundColor White
Write-Host "3. Use current active scripts for development tasks" -ForegroundColor White
