use std::sync::atomic::{AtomicBool, Ordering};

/// Log levels for the logging system
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord)]
pub enum LogLevel {
    Debug = 0,
    Info = 1,
    Warn = 2,
    Error = 3,
}

impl LogLevel {
    pub fn as_str(&self) -> &'static str {
        match self {
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
        }
    }
}

/// Global logging configuration
static LOGGING_ENABLED: AtomicBool = AtomicBool::new(true);
static MIN_LOG_LEVEL: AtomicBool = AtomicBool::new(false); // false = Debug, true = Info+

/// Logger struct for the Bad Character Scanner
pub struct Logger;

impl Logger {
    /// Enable or disable logging globally
    pub fn set_enabled(enabled: bool) {
        LOGGING_ENABLED.store(enabled, Ordering::Relaxed);
    }

    /// Check if logging is enabled
    pub fn is_enabled() -> bool {
        LOGGING_ENABLED.load(Ordering::Relaxed)
    }

    /// Set minimum log level (false = Debug+, true = Info+)
    pub fn set_production_mode(production: bool) {
        MIN_LOG_LEVEL.store(production, Ordering::Relaxed);
    }

    /// Check if we should log at this level
    fn should_log(level: LogLevel) -> bool {
        if !Self::is_enabled() {
            return false;
        }

        let min_level = if MIN_LOG_LEVEL.load(Ordering::Relaxed) {
            LogLevel::Info
        } else {
            LogLevel::Debug
        };

        level >= min_level
    }

    /// Log a debug message
    pub fn debug(message: &str) {
        Self::log(LogLevel::Debug, message);
    }

    /// Log an info message
    pub fn info(message: &str) {
        Self::log(LogLevel::Info, message);
    }

    /// Log a warning message
    #[allow(dead_code)]
    pub fn warn(message: &str) {
        Self::log(LogLevel::Warn, message);
    }

    /// Log an error message
    #[allow(dead_code)]
    pub fn error(message: &str) {
        Self::log(LogLevel::Error, message);
    }

    /// Internal logging function
    fn log(level: LogLevel, message: &str) {
        if !Self::should_log(level) {
            return;
        }

        // Create a formatted log message with timestamp and level
        let timestamp = js_sys::Date::new_0().to_iso_string();
        let timestamp_str = timestamp.as_string().unwrap_or_else(|| "unknown".to_string());
        
        let formatted_message = format!(
            "[{}] [BCS:{}] {}",
            timestamp_str,
            level.as_str(),
            message
        );

        // Use appropriate console method based on log level
        // SAFETY: These console methods are safe to call from web context
        // They're provided by the browser's console API and don't access raw memory
        match level {
            LogLevel::Debug => {
                web_sys::console::log_1(&formatted_message.into());
            }
            LogLevel::Info => {
                web_sys::console::log_1(&formatted_message.into());
            }
            LogLevel::Warn => {
                web_sys::console::log_1(&formatted_message.into());
            }
            LogLevel::Error => {
                web_sys::console::log_1(&formatted_message.into());
            }
        }
    }
}

/// Convenience macros for logging
#[macro_export]
macro_rules! log_debug {
    ($msg:expr) => {
        $crate::utils::logging::Logger::debug($msg)
    };
}

#[macro_export]
macro_rules! log_info {
    ($msg:expr) => {
        $crate::utils::logging::Logger::info($msg)
    };
}

#[macro_export]
macro_rules! log_warn {
    ($msg:expr) => {
        $crate::utils::logging::Logger::warn($msg)
    };
}

#[macro_export]
macro_rules! log_error {
    ($msg:expr) => {
        $crate::utils::logging::Logger::error($msg)
    };
}

/// Initialize logging for the application
pub fn init_logging() {
    // Check if we're in production mode (you can customize this logic)
    let is_production = cfg!(not(debug_assertions));
    
    Logger::set_enabled(true);
    Logger::set_production_mode(is_production);
    
    if is_production {
        Logger::info("BCS Logging initialized (Production mode - Info+ only)");
    } else {
        Logger::debug("BCS Logging initialized (Development mode - All levels)");
    }
}

/// Callback management for preventing memory leaks
pub mod callback_manager {
    use std::collections::HashMap;
    use wasm_bindgen::closure::Closure;

    /// Global callback registry to track active callbacks
    /// Note: Using thread_local! instead of static for WASM compatibility
    use std::cell::RefCell;

    thread_local! {
        static CALLBACK_REGISTRY: RefCell<HashMap<String, Box<dyn std::any::Any>>> = RefCell::new(HashMap::new());
    }

    /// Register a callback with a unique identifier
    #[allow(dead_code)]
    pub fn register_callback<F>(id: String, callback: Closure<F>) -> Result<(), String>
    where
        F: ?Sized + 'static,
    {
        CALLBACK_REGISTRY.with(|registry| {
            registry.borrow_mut().insert(id.clone(), Box::new(callback));
            super::Logger::debug(&format!("Registered callback: {}", id));
        });
        Ok(())
    }
}
