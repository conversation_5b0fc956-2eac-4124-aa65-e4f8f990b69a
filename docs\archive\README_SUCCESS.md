# Bad Character Scanner - Leptos + Tauri v2 ✅

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Rust](https://github.com/IBIYP/Rust_Test/actions/workflows/rust.yml/badge.svg)](https://github.com/IBIYP/Rust_Test/actions/workflows/rust.yml)
[![Status: Working](https://img.shields.io/badge/Status-Working-success)](.)

🔍 **A powerful desktop application for detecting suspicious Unicode characters, invisible characters, and control codes in text.**

Built with **Leptos** (frontend) + **Tauri v2** (desktop framework) for blazing-fast performance and cross-platform compatibility.

## ✅ Status: Successfully Implemented!

This application is **WORKING** and fully functional! The integration between Leptos frontend and Tauri v2 backend has been successfully completed.

## ✨ Features

- 🚀 **Blazing Fast**: Built with Rust and WebAssembly for maximum performance
- 🖥️ **Cross-Platform**: Works on Windows, macOS, and Linux  
- 🔍 **Character Analysis**: Detects invisible characters, control codes, and suspicious Unicode sequences
- 🎨 **Modern UI**: Beautiful responsive interface built with Leptos and Tailwind CSS
- 🔥 **Hot Reloading**: Fast development cycle with hot module replacement
- 📦 **Single Binary**: Package your entire application into a single executable
- 🔒 **Privacy-Focused**: All processing happens locally on your machine

## 🛡️ What This Scanner Detects

- **Control Characters**: Invisible formatting characters (except common ones like `\n`, `\r`, `\t`)
- **Zero-Width Characters**: ZWSP (`\u{200B}`), ZWNJ (`\u{200C}`), ZWJ (`\u{200D}`) that can hide malicious content
- **Byte Order Marks (BOM)**: Unicode BOM characters (`\u{FEFF}`) that can cause issues
- **Bidirectional Override**: Characters (`\u{202A}`-`\u{202E}`) that can manipulate text direction for spoofing

## 🚀 Quick Start

### Prerequisites

- [Rust](https://www.rust-lang.org/tools/install) (latest stable version, 1.70 or later)
- [Tauri v2.x](https://tauri.app/v2/guides/getting-started/prerequisites) (v2.0.0 or later required)
- [Trunk](https://trunkrs.dev/) (for building the frontend)
  ```bash
  cargo install trunk wasm-bindgen-cli
  ```

### Development

1. **Clone the repository**
   ```bash
   git clone <repo-url>
   cd Laptos_TaurieV2_HelloWorld
   ```

2. **Start the development server**
   ```bash
   cargo tauri dev
   ```
   
   This will:
   - Start Trunk development server on `http://localhost:1420`
   - Compile and run the Tauri desktop application
   - Enable hot reloading for both frontend and backend

3. **Open the application** - The desktop window will open automatically with the Bad Character Scanner interface.

### Production Build

```bash
# Build the frontend for production
trunk build --release

# Build the desktop application
cargo tauri build
```

This creates a standalone executable in `src-tauri/target/release/bundle/`.

## 🏗️ Architecture

### Frontend (Leptos)
- **Framework**: Leptos 0.6 with CSR (Client-Side Rendering)
- **Build Tool**: Trunk for WASM compilation and bundling
- **Styling**: Tailwind CSS for responsive design
- **API Communication**: JavaScript bindings to call Tauri backend commands

### Backend (Tauri v2)
- **Framework**: Tauri v2.0+ for desktop application wrapper
- **Language**: Rust for high-performance character analysis
- **Commands**: Custom `analyze_characters` command for Unicode processing
- **Security**: Local processing with no network requests

### Project Structure
```
├── src/                    # Leptos frontend source
│   └── lib.rs             # Main application component with UI and API calls
├── src-tauri/             # Tauri backend
│   ├── src/
│   │   ├── main.rs        # Backend entry point & analyze_characters command
│   │   └── lib.rs         # Library configuration
│   ├── tauri.conf.json    # Tauri configuration
│   └── Cargo.toml        # Backend dependencies
├── index.html             # HTML template
├── styles/                # Tailwind CSS styles
├── Cargo.toml            # Frontend dependencies
└── tauri.config.json     # Root Tauri configuration
```

## 🔧 Configuration

### Tauri Configuration
- **Dev Server**: `trunk serve --port 1420`
- **Build Command**: `trunk build --release`  
- **API Access**: `withGlobalTauri: true` for frontend API access
- **Security**: CSP disabled for development (`csp: null`)

### Development vs Production
- **Development**: Hot reloading with `cargo tauri dev`
- **Production**: Single executable with `cargo tauri build`
- **Web Preview**: Available at `http://localhost:1420` during development

## 🧪 Testing the Application

### Sample Test Cases

1. **Invisible Characters**:
   ```
   Hello​World
   ```
   *(Contains zero-width space between Hello and World)*

2. **Control Characters**:
   ```
   Text with control chars
   ```
   *(Paste text with embedded control characters)*

3. **Unicode Spoofing**:
   ```
   Bidirectional override characters
   ```

### Expected Results
The application will display:
- Total character and byte count
- Whether non-ASCII characters are present
- Count of suspicious characters found
- Detailed list of each suspicious character with:
  - Position in text
  - Unicode codepoint (e.g., U+200B)
  - Visual representation
  - Reason for flagging

## 🚀 Deployment

### Building for Distribution

1. **Create release build**:
   ```bash
   cargo tauri build
   ```

2. **Locate executable**:
   - **Windows**: `src-tauri/target/release/bundle/msi/Bad Character Scanner_0.1.0_x64_en-US.msi`
   - **macOS**: `src-tauri/target/release/bundle/dmg/Bad Character Scanner_0.1.0_x64.dmg`
   - **Linux**: `src-tauri/target/release/bundle/deb/bad-character-scanner_0.1.0_amd64.deb`

### Signing (Optional)
For production releases, consider code signing:
- **Windows**: Use `signtool` with a code signing certificate
- **macOS**: Use Apple Developer certificate with `codesign`
- **Linux**: Use GPG signing for package integrity

## 🔧 Development Tools

### VS Code Extensions (Recommended)
- [Rust Analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer) - For Rust language support
- [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) - For Tauri framework support
- [Leptos](https://marketplace.visualstudio.com/items?itemName=leptos.leptos-vscode) - For Leptos framework support
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) - For Tailwind CSS support

## 🐛 Troubleshooting

### Common Issues

1. **"Tauri API not available" error**:
   - Make sure you're running in the desktop window, not just the browser
   - Verify `withGlobalTauri: true` is set in configuration
   - Check that the development server is using `trunk serve` not `trunk build`

2. **Build errors**:
   - Ensure Rust target `wasm32-unknown-unknown` is installed: `rustup target add wasm32-unknown-unknown`
   - Update to latest Trunk version: `cargo install trunk --force`

3. **Hot reloading not working**:
   - Check that port 1420 is not blocked by firewall
   - Restart the development server with `cargo tauri dev`

## ✅ Success Metrics

- ✅ **Frontend**: Leptos application compiles and runs in WASM
- ✅ **Backend**: Tauri v2 desktop application launches successfully
- ✅ **Integration**: Frontend can call backend commands via Tauri API
- ✅ **Functionality**: Character analysis works with real-time results
- ✅ **UI**: Responsive design with Tailwind CSS styling
- ✅ **Hot Reload**: Development workflow with live updates

## 📚 Learn More

- [Tauri v2 Documentation](https://tauri.app/v2/guides/)
- [Leptos Framework](https://leptos-rs.github.io/leptos/)
- [Trunk Build Tool](https://trunkrs.dev/)
- [Unicode Character Database](https://unicode.org/ucd/)

## 🤝 Contributing

Contributions are welcome! This project demonstrates a successful integration of modern Rust web technologies.

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Tauri](https://tauri.app/) - Build smaller, faster, and more secure desktop applications
- [Leptos](https://github.com/leptos-rs/leptos) - A full-stack, isomorphic Rust web framework
- [Unicode Consortium](https://home.unicode.org/) - For the Unicode Standard

---

**🎉 Congratulations! You now have a fully working Leptos + Tauri v2 desktop application!** 🎉
