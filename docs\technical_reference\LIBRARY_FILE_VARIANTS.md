# 📚 Library File Variants Reference

**Technical reference for all library files in the `src/` directory of the Bad Character Scanner project**

---

## 📋 **File Overview**

| File Name | Purpose | Status | Integration | Size | Usage |
|-----------|---------|--------|-------------|------|-------|
| **`lib.rs`** | Production application | ✅ Active | Full Tauri v2 | ~3,000 lines | Main build |
| **`lib_simple.rs`** | Minimal demo/prototype | 🔄 Reference | Frontend only | ~500 lines | Development |
| **`lib_new.rs`** | Future experiments | 📝 Placeholder | None | Empty | Reserved |

---

## 🚀 **Production Library: `lib.rs`**

### **Purpose**
Primary frontend implementation for the Bad Character Scanner desktop application.

### **Key Features**
- **Complete UI System** - Full Leptos component architecture
- **Tauri Integration** - All backend command bindings
- **State Management** - Comprehensive reactive state handling
- **Analysis Engine** - Frontend logic for Unicode analysis
- **Export System** - Multi-format data export capabilities
- **Error Handling** - Robust error management and user feedback

### **Architecture**
```rust
// Main components structure
App
├── Router                    // Page routing
├── NavigationBar            // Top navigation
├── Pages/
│   ├── Dashboard           // Main analysis interface
│   ├── BatchAnalysis       // Multiple file processing
│   ├── Settings            // Configuration
│   └── Help               // User assistance
└── Components/
    ├── FileUpload         // File selection UI
    ├── AnalysisResults    // Results display
    ├── ProgressIndicator  // Processing feedback
    └── ExportOptions      // Data export UI
```

### **Tauri v2 Commands**
| Command | Purpose | Returns |
|---------|---------|---------|
| `analyze_text` | Core text analysis | `AnalysisResults` |
| `analyze_file` | File-based analysis | `FileAnalysisResults` |
| `export_results` | Data export | `ExportStatus` |
| `get_settings` | Configuration retrieval | `AppSettings` |
| `save_settings` | Configuration persistence | `SaveStatus` |

### **Dependencies**
```toml
# Key frontend dependencies used in lib.rs
leptos = "0.6"              # Reactive UI framework
wasm-bindgen = "0.2"        # WASM bindings
serde = "1.0"               # Serialization
unicode-segmentation = "1.10" # Text processing
regex = "1.8"               # Pattern matching
```

### **Build Integration**
- **Primary Build Target** - Used by `trunk serve` and `trunk build`
- **Cargo.toml Entry** - Configured as main library crate
- **WASM Compilation** - Compiles to WebAssembly for frontend
- **Hot Reload** - Supports development hot reloading

---

## 🧪 **Development Library: `lib_simple.rs`**

### **Purpose**
Minimal, standalone demonstration and prototyping environment.

### **Key Features**
- **Simplified UI** - Basic Leptos components
- **No Backend Dependency** - Frontend-only implementation
- **Quick Testing** - Rapid prototyping environment
- **Reference Implementation** - Clean, minimal code example
- **Educational Value** - Easy to understand for learning

### **Use Cases**
- **Prototyping** - Test new UI concepts
- **Learning** - Understand basic Leptos patterns
- **Debugging** - Isolate frontend issues
- **Demos** - Simple demonstrations
- **Fallback** - Alternative if main library has issues

### **Limitations**
- **No File Operations** - Cannot access filesystem
- **No Advanced Analysis** - Basic character detection only
- **No Data Persistence** - In-memory only
- **No Export** - Limited output options

### **Code Structure**
```rust
// Simplified structure
SimpleApp
├── TextInput               // Basic text input
├── BasicAnalysis          // Simple character checks
└── ResultsDisplay         // Minimal results view
```

---

## 📝 **Future Library: `lib_new.rs`**

### **Current Status**
- **Content**: Empty placeholder file
- **Purpose**: Reserved for future development
- **Integration**: None currently

### **Planned Usage**
- **Experimental Features** - Test new functionality
- **Alternative Implementations** - Different architectural approaches  
- **Temporary Development** - Isolated feature development
- **Backup Workspace** - Safe development environment

### **Guidelines for Use**
1. **Keep Isolated** - Don't integrate with build system until stable
2. **Document Changes** - Update this reference when adding content
3. **Maintain Independence** - Avoid dependencies on main library
4. **Version Control** - Commit experimental work with clear messages

---

## 🔄 **File Management Strategy**

### **Development Workflow**
```bash
# Primary development (always)
trunk serve                    # Uses lib.rs

# Simple testing/prototyping
# 1. Temporarily rename lib.rs to lib_main.rs
# 2. Rename lib_simple.rs to lib.rs
# 3. Run trunk serve
# 4. Restore original names when done

# Experimental work
# Use lib_new.rs for isolated development
# No build integration until ready
```

### **Build Configuration**
```toml
# Cargo.toml - Library configuration
[lib]
name = "bad_character_scanner"
crate-type = ["cdylib"]        # For WASM compilation

# Only lib.rs is used in actual builds
# Other lib_*.rs files are development tools only
```

---

## 🛠️ **Maintenance Guidelines**

### **Code Synchronization**
- **Keep Features Aligned** - When adding features to `lib.rs`, consider if `lib_simple.rs` needs updates
- **Version Compatibility** - Ensure all library files work with current dependency versions
- **Documentation Updates** - Update this document when file purposes change

### **Quality Standards**
| File | Code Quality | Testing | Documentation |
|------|-------------|---------|---------------|
| **lib.rs** | Production-grade | Comprehensive | Full |
| **lib_simple.rs** | Clean, readable | Basic | Minimal |
| **lib_new.rs** | Experimental | Optional | As needed |

### **File Lifecycle**
1. **Creation** - Start with clear purpose and scope
2. **Development** - Maintain independence from other files
3. **Integration** - Only integrate when stable and tested
4. **Deprecation** - Remove or archive when no longer needed
5. **Documentation** - Update this reference throughout lifecycle

---

## 🔍 **Usage Examples**

### **Standard Development**
```bash
# Normal development workflow
trunk serve                    # Always uses lib.rs
cargo tauri dev               # Desktop app with lib.rs
```

### **Simple Prototyping**
```bash
# To use lib_simple.rs temporarily:
# Option 1: Rename files
mv lib.rs lib_main_backup.rs
mv lib_simple.rs lib.rs
trunk serve
# Restore when done
mv lib.rs lib_simple.rs
mv lib_main_backup.rs lib.rs

# Option 2: Modify Cargo.toml temporarily (advanced)
```

### **Experimental Development**
```bash
# Work in lib_new.rs
# Copy sections to/from other files as needed
# No build integration until ready for production
```

---

## ⚠️ **Important Notes**

### **Build System Integration**
- **Only `lib.rs` is integrated** with the build system
- **Other files are development tools** and not part of production builds
- **Trunk configuration** expects `lib.rs` as the main entry point
- **Tauri configuration** is designed for `lib.rs` integration

### **Dependency Management**
- **All library files** should use compatible dependency versions
- **Feature flags** may differ between files based on their purpose
- **Update dependencies** in all files when upgrading versions

### **Version Control Best Practices**
- **Commit all library files** even if they're not part of builds
- **Use descriptive commit messages** when changing file purposes
- **Tag releases** to maintain version history for all files
- **Document architectural decisions** that affect multiple files

---

## 📊 **File Statistics**

### **Current Metrics** (as of v0.3.1)
| Metric | lib.rs | lib_simple.rs | lib_new.rs |
|--------|--------|---------------|------------|
| **Lines of Code** | ~3,000 | ~500 | 0 |
| **Components** | 15+ | 3 | 0 |
| **Dependencies** | 20+ | 5 | 0 |
| **Tauri Commands** | 8+ | 0 | 0 |
| **Test Coverage** | 90%+ | Basic | N/A |

### **Evolution History**
- **v0.1.0**: Single `lib.rs` file (~500 lines)
- **v0.2.0**: Added `lib_simple.rs` for prototyping (~300 lines)
- **v0.3.0**: Expanded `lib.rs` to production scale (~3,000 lines)
- **v0.3.1**: Added `lib_new.rs` placeholder, updated documentation

---

*This reference document should be updated whenever library files are added, removed, or their purposes change. Keep it synchronized with the actual codebase for accurate development guidance.*
