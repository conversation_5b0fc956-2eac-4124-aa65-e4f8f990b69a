// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// Don't declare modules here, they're declared in lib.rs

use std::collections::HashMap;
use std::time::{SystemTime, Duration};
use std::path::Path;
use std::fs;
use tauri::{Emitter, Manager};
use serde::{Deserialize, Serialize};
use unicode_segmentation::UnicodeSegmentation;
use unicode_width::UnicodeWidthStr;
use encoding_rs::*;
use regex::Regex;
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};
use uuid::Uuid;

// Import report_generator module
use crate::report_generator;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressPayload {
    pub current: u32,
    pub total: u32,
    pub message: Option<String>,
    pub operation_id: String,
    pub percentage: f32,
    pub stage: Option<String>, // Changed from String and reordered
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub unicode_block: String,
    pub category: String,
    pub codepoint: u32,
    pub utf8_bytes: Vec<u8>,
    pub utf16_units: Vec<u16>,
    pub is_suspicious: bool,
    pub suspicion_reasons: Vec<String>,
    pub recommendations: Vec<String>,
    pub visual_width: usize,
    pub is_combining: bool,
    pub is_emoji: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingInfo {
    pub detected_encoding: String,
    pub confidence: f32,
    pub is_valid_utf8: bool,
    pub bom_detected: Option<String>,
    pub line_endings: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub risk_level: String, // Low, Medium, High, Critical
    pub phishing_indicators: Vec<String>,
    pub homograph_attacks: Vec<String>,
    pub steganography_potential: bool,
    pub script_mixing: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub input_text: String,
    pub text_hash: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub total_graphemes: usize,
    pub visual_width: usize,
    pub encoding_info: EncodingInfo,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub character_breakdown: HashMap<String, usize>,
    pub script_breakdown: HashMap<String, usize>,
    pub analysis_duration_ms: u64,
    pub confidence_score: f32,
    pub security_analysis: SecurityAnalysis,
    pub patterns_found: Vec<PatternMatch>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternMatch {
    pub pattern_name: String,
    pub description: String,
    pub start_position: usize,
    pub end_position: usize,
    pub matched_text: String,
    pub severity: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisRequest {
    pub content: String,
    pub filename: Option<String>,
    pub encoding: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisDetail {
    pub file_path: String,
    pub relative_path: String,
    pub file_size: u64,
    pub total_characters: usize,
    pub suspicious_characters: usize,
    pub issues: Vec<String>,
    pub file_type: String,
    pub encoding: String,
    pub analysis_status: String, // "success", "error", "skipped"
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub health_score: f64,
    pub file_details: Vec<FileAnalysisDetail>,
    pub analysis_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseStats {
    pub total_files_processed: usize,
    pub files_with_issues: usize,
    pub total_suspicious_characters: usize,
    pub most_common_issues: Vec<String>,
    pub file_type_breakdown: HashMap<String, usize>,
    pub overall_health_score: f64,
}

// Data structures for JSON assets integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterEntry {
    pub name: Option<String>,
    pub hex: Option<String>,
    pub description: String,
    pub char: Option<String>,
    pub example: Option<String>,
    pub range: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterCategory {
    #[serde(rename = "displayName")]
    pub display_name: String,
    pub description: String,
    pub characters: Vec<BadCharacterEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharacterSubCategories {
    #[serde(rename = "extremelyBigProblems")]
    pub extremely_big_problems: BadCharacterCategory,
    #[serde(rename = "highProblems")]
    pub high_problems: BadCharacterCategory,
    #[serde(rename = "mediumProblems")]
    pub medium_problems: BadCharacterCategory,
    #[serde(rename = "lowProblems")]
    pub low_problems: BadCharacterCategory,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InvisibleAndFormattingCharacters {
    pub description: String,
    #[serde(rename = "subCategories")]
    pub sub_categories: BadCharacterSubCategories,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentSpecificIssues {
    #[serde(rename = "invisibleAndFormattingCharacters")]
    pub invisible_and_formatting_characters: InvisibleAndFormattingCharacters,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilenameIssues {
    #[serde(rename = "forbiddenWindowsCharacters")]
    pub forbidden_windows_characters: Vec<BadCharacterEntry>,
    #[serde(rename = "generalControlCharactersForFilenames")]
    pub general_control_characters_for_filenames: Vec<BadCharacterEntry>,
    #[serde(rename = "c1ControlCharactersForFilenames")]
    pub c1_control_characters_for_filenames: Vec<BadCharacterEntry>,
    #[serde(rename = "problematicUnicodeForFilenames")]
    pub problematic_unicode_for_filenames: Vec<BadCharacterEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BadCharactersConfig {
    #[serde(rename = "filenameSpecificIssues")]
    pub filename_specific_issues: FilenameIssues,
    #[serde(rename = "contentSpecificIssues")]
    pub content_specific_issues: ContentSpecificIssues,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgrammingSourceCodeCategories {
    #[serde(rename = "C Family")]
    pub c_family: Vec<String>,
    #[serde(rename = "Java/JVM")]
    pub java_jvm: Vec<String>,
    #[serde(rename = "Python")]
    pub python: Vec<String>,
    #[serde(rename = "JavaScript/TypeScript")]
    pub javascript_typescript: Vec<String>,
    #[serde(rename = "Other")]
    pub other: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTypeCategories {
    #[serde(rename = "System Files")]
    pub system_files: Vec<String>,
    #[serde(rename = "Virtualization Formats")]
    pub virtualization_formats: Vec<String>,
    #[serde(rename = "Image Formats")]
    pub image_formats: Vec<String>,
    #[serde(rename = "Configuration Files")]
    pub configuration_files: Vec<String>,
    #[serde(rename = "Log Files")]
    pub log_files: Vec<String>,
    #[serde(rename = "Document Formats")]
    pub document_formats: Vec<String>,
    #[serde(rename = "Archive Formats")]
    pub archive_formats: Vec<String>,
    #[serde(rename = "Executable Formats")]
    pub executable_formats: Vec<String>,
    #[serde(rename = "Database Formats")]
    pub database_formats: Vec<String>,
    #[serde(rename = "Network Formats")]
    pub network_formats: Vec<String>,
    #[serde(rename = "Audio Formats")]
    pub audio_formats: Vec<String>,
    #[serde(rename = "Video Formats")]
    pub video_formats: Vec<String>,
    #[serde(rename = "CAD Formats")]
    pub cad_formats: Vec<String>,
    #[serde(rename = "Virtual Machines")]
    pub virtual_machines: Vec<String>,
    #[serde(rename = "Game Formats")]
    pub game_formats: Vec<String>,
    #[serde(rename = "E-Book Formats")]
    pub e_book_formats: Vec<String>,
    #[serde(rename = "BIOS/UEFI Formats")]
    pub bios_uefi_formats: Vec<String>,
    #[serde(rename = "Forensic Formats")]
    pub forensic_formats: Vec<String>,
    #[serde(rename = "Container Formats")]
    pub container_formats: Vec<String>,
    #[serde(rename = "Project Files")]
    pub project_files: Vec<String>,
    #[serde(rename = "Programming Source Code")]
    pub programming_source_code: ProgrammingSourceCodeCategories,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTypesSummary {
    #[serde(rename = "$schema")]
    pub schema: String,
    pub version: String,
    #[serde(rename = "lastUpdated")]
    pub last_updated: String,
    pub categories: FileTypeCategories,
}

#[derive(Debug, Clone)]
pub struct AssetManager {
    pub bad_characters: BadCharactersConfig,
    pub file_types: FileTypesSummary,
    pub supported_extensions: std::collections::HashSet<String>,
}

impl AssetManager {
    pub fn new() -> Result<Self, String> {
        // Try to load assets from multiple possible locations
        let bad_characters = Self::load_bad_characters()?;
        let file_types = Self::load_file_types()?;

        // Build supported extensions set from Programming Source Code category
        let mut supported_extensions = std::collections::HashSet::new();
        
        // Add C Family extensions
        for ext in &file_types.categories.programming_source_code.c_family {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Java/JVM extensions
        for ext in &file_types.categories.programming_source_code.java_jvm {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Python extensions
        for ext in &file_types.categories.programming_source_code.python {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add JavaScript/TypeScript extensions
        for ext in &file_types.categories.programming_source_code.javascript_typescript {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }
        
        // Add Other extensions
        for ext in &file_types.categories.programming_source_code.other {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }

        // Add Configuration Files (commonly analyzed)
        for ext in &file_types.categories.configuration_files {
            supported_extensions.insert(ext.trim_start_matches('.').to_lowercase());
        }

        Ok(Self {
            bad_characters,
            file_types,
            supported_extensions,
        })
    }

    pub fn is_supported_extension(&self, extension: &str) -> bool {
        self.supported_extensions.contains(&extension.to_lowercase())
    }

    pub fn get_character_severity(&self, codepoint: u32) -> Option<&str> {
        let hex_code = format!("U+{:04X}", codepoint);
        
        // Check extremely big problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.extremely_big_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("extremely_big_problems");
            }
        }
        
        // Check high problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.high_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("high_problems");
            }
        }
        
        // Check medium problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.medium_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("medium_problems");
            }
        }
        
        // Check low problems
        for char_entry in &self.bad_characters.content_specific_issues.invisible_and_formatting_characters.sub_categories.low_problems.characters {
            if char_entry.hex.as_ref() == Some(&hex_code) {
                return Some("low_problems");
            }
        }
        
        None
    }

    pub fn get_file_type_documentation(&self, extension: &str) -> String {
        let ext_with_dot = format!(".{}", extension.trim_start_matches('.'));
        
        // Check in Programming Source Code categories
        if self.file_types.categories.programming_source_code.c_family.contains(&ext_with_dot) {
            return "C/C++ source code file - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.java_jvm.contains(&ext_with_dot) {
            return "Java/JVM language source code - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.python.contains(&ext_with_dot) {
            return "Python source code file - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.javascript_typescript.contains(&ext_with_dot) {
            return "JavaScript/TypeScript source code - read as text with UTF-8 encoding".to_string();
        }
        if self.file_types.categories.programming_source_code.other.contains(&ext_with_dot) {
            return "Programming source code file - read as text with UTF-8 encoding".to_string();
        }
        
        // Check in Configuration Files
        if self.file_types.categories.configuration_files.contains(&ext_with_dot) {
            return "Configuration file - read as text with UTF-8 encoding".to_string();
        }
        
        "Text-based file - read as text with UTF-8 encoding".to_string()
    }
}

// Helper methods for robust asset loading
impl AssetManager {
    fn load_bad_characters() -> Result<BadCharactersConfig, String> {
        println!("🔍 Loading Bad_Characters.json...");
        
        // Try embedded assets first (always works)
        match Self::load_embedded_bad_characters() {
            Ok(config) => {
                println!("✅ Successfully loaded Bad_Characters.json from embedded data");
                return Ok(config);
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load embedded Bad_Characters.json: {}", e);
                println!("🔄 Trying file system paths...");
            }
        }
        
        // Fallback to file system paths
        let possible_paths = [
            "assets/Bad_Characters.json",           // Development
            "./assets/Bad_Characters.json",         // Alternative development
            "../assets/Bad_Characters.json",        // Relative to binary
            "Bad_Characters.json",                  // Direct in working dir
            // Add Windows-specific paths
            r".\assets\Bad_Characters.json",
            r"..\assets\Bad_Characters.json",
            // Add more specific development paths
            r"C:\Users\<USER>\Documents\Software\Laptos_TaurieV2_HelloWorld\assets\Bad_Characters.json",
        ];
        
        let mut last_error = String::new();
        
        for path_str in &possible_paths {
            let path = Path::new(path_str);
            println!("🔍 Trying path: {}", path_str);
            
            match fs::read_to_string(path) {
                Ok(content) => {
                    println!("📄 File read successfully, {} bytes", content.len());
                    match serde_json::from_str::<BadCharactersConfig>(&content) {
                        Ok(config) => {
                            println!("✅ Successfully loaded Bad_Characters.json from: {}", path_str);
                            return Ok(config);
                        }
                        Err(e) => {
                            last_error = format!("Failed to parse Bad_Characters.json from {}: {}", path_str, e);
                            eprintln!("❌ {}", last_error);
                        }
                    }
                }
                Err(e) => {
                    last_error = format!("Failed to read Bad_Characters.json from {}: {}", path_str, e);
                    println!("❌ {}", last_error);
                }
            }
        }
        
        Err(format!("Failed to load Bad_Characters.json from any location. Last error: {}", last_error))
    }
    
    fn load_file_types() -> Result<FileTypesSummary, String> {
        println!("🔍 Loading FileTypesSummary.json...");
        
        // Try embedded assets first (always works)
        match Self::load_embedded_file_types() {
            Ok(config) => {
                println!("✅ Successfully loaded FileTypesSummary.json from embedded data");
                return Ok(config);
            }
            Err(e) => {
                eprintln!("⚠️ Failed to load embedded FileTypesSummary.json: {}", e);
                println!("🔄 Trying file system paths...");
            }
        }
        
        // Fallback to file system paths
        let possible_paths = [
            "assets/FileTypesSummary.json",         // Development
            "./assets/FileTypesSummary.json",       // Alternative development
            "../assets/FileTypesSummary.json",      // Relative to binary
            "FileTypesSummary.json",                // Direct in working dir
            // Add Windows-specific paths
            r".\assets\FileTypesSummary.json",
            r"..\assets\FileTypesSummary.json",
            // Add more specific development paths
            r"C:\Users\<USER>\Documents\Software\Laptos_TaurieV2_HelloWorld\assets\FileTypesSummary.json",
        ];
        
        let mut last_error = String::new();
        
        for path_str in &possible_paths {
            let path = Path::new(path_str);
            println!("🔍 Trying path: {}", path_str);
            
            match fs::read_to_string(path) {
                Ok(content) => {
                    println!("📄 File read successfully, {} bytes", content.len());
                    match serde_json::from_str::<FileTypesSummary>(&content) {
                        Ok(config) => {
                            println!("✅ Successfully loaded FileTypesSummary.json from: {}", path_str);
                            return Ok(config);
                        }
                        Err(e) => {
                            last_error = format!("Failed to parse FileTypesSummary.json from {}: {}", path_str, e);
                            eprintln!("❌ {}", last_error);
                        }
                    }
                }
                Err(e) => {
                    last_error = format!("Failed to read FileTypesSummary.json from {}: {}", path_str, e);
                    println!("❌ {}", last_error);
                }
            }
        }
        
        Err(format!("Failed to load FileTypesSummary.json from any location. Last error: {}", last_error))
    }
    
    // Enhanced fallback method: embed the JSON as a string in the binary for production builds
    fn load_embedded_bad_characters() -> Result<BadCharactersConfig, String> {
        // Include the JSON file as a string at compile time
        const BAD_CHARACTERS_JSON: &str = include_str!("../../assets/Bad_Characters.json");
        
        serde_json::from_str(BAD_CHARACTERS_JSON)
            .map_err(|e| format!("Failed to parse embedded Bad_Characters.json: {}", e))
    }
    
    fn load_embedded_file_types() -> Result<FileTypesSummary, String> {
        // Include the JSON file as a string at compile time
        const FILE_TYPES_JSON: &str = include_str!("../../assets/FileTypesSummary.json");
        
        serde_json::from_str(FILE_TYPES_JSON)
            .map_err(|e| format!("Failed to parse embedded FileTypesSummary.json: {}", e))
    }
}

#[derive(Debug, Clone)]
pub struct CharacterAnalyzer {
    homograph_db: HashMap<char, Vec<char>>,
    pattern_rules: Vec<(String, Regex, String)>, // name, regex, severity
    script_detector: ScriptDetector,
    asset_manager: AssetManager,
}

impl CharacterAnalyzer {
    pub fn new() -> Result<Self, String> {
        let asset_manager = AssetManager::new()?;
        Ok(Self {
            homograph_db: Self::build_homograph_database(),
            pattern_rules: Self::build_pattern_rules(),
            script_detector: ScriptDetector::new(),
            asset_manager,
        })
    }

    fn build_homograph_database() -> HashMap<char, Vec<char>> {
        let mut db = HashMap::new();
        
        // Common lookalike characters
        db.insert('а', vec!['a']); // Cyrillic 'a' looks like Latin 'a'
        db.insert('е', vec!['e']); // Cyrillic 'e' looks like Latin 'e'
        db.insert('о', vec!['o']); // Cyrillic 'o' looks like Latin 'o'
        db.insert('р', vec!['p']); // Cyrillic 'p' looks like Latin 'p'
        db.insert('с', vec!['c']); // Cyrillic 'c' looks like Latin 'c'
        db.insert('х', vec!['x']); // Cyrillic 'x' looks like Latin 'x'
        db.insert('у', vec!['y']); // Cyrillic 'y' looks like Latin 'y'
        
        // Greek lookalikes
        db.insert('α', vec!['a']); // Greek alpha
        db.insert('ο', vec!['o']); // Greek omicron
        db.insert('ρ', vec!['p']); // Greek rho
        
        // More sophisticated lookalikes
        db.insert('Α', vec!['A']); // Greek capital alpha
        db.insert('Β', vec!['B']); // Greek capital beta
        db.insert('Ε', vec!['E']); // Greek capital epsilon
        db.insert('Ζ', vec!['Z']); // Greek capital zeta
        db.insert('Η', vec!['H']); // Greek capital eta
        db.insert('Ι', vec!['I']); // Greek capital iota
        db.insert('Κ', vec!['K']); // Greek capital kappa
        db.insert('Μ', vec!['M']); // Greek capital mu
        db.insert('Ν', vec!['N']); // Greek capital nu
        db.insert('Ο', vec!['O']); // Greek capital omicron
        db.insert('Ρ', vec!['P']); // Greek capital rho
        db.insert('Τ', vec!['T']); // Greek capital tau
        db.insert('Υ', vec!['Y']); // Greek capital upsilon
        db.insert('Χ', vec!['X']); // Greek capital chi
        
        db
    }

    fn build_pattern_rules() -> Vec<(String, Regex, String)> {
        let mut rules = Vec::new();
        
        // Suspicious patterns
        rules.push((
            "Multiple Zero-Width Characters".to_string(),
            Regex::new(r"[\u{200B}\u{200C}\u{200D}]{3,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Mixed Script Text".to_string(),
            Regex::new(r"[\p{Latin}][\p{Cyrillic}]|[\p{Cyrillic}][\p{Latin}]").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Right-to-Left Override".to_string(),
            Regex::new(r"\u{202E}").unwrap(),
            "Critical".to_string(),
        ));
        
        rules.push((
            "Suspicious Unicode Blocks".to_string(),
            Regex::new(r"[\u{FFF0}-\u{FFFF}]").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Combining Character Abuse".to_string(),
            Regex::new(r"[\u{0300}-\u{036F}]{5,}").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Hidden Text Pattern".to_string(),
            Regex::new(r"[a-zA-Z][\u{200B}\u{200C}\u{200D}]+[a-zA-Z]").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Bidirectional Override Sequence".to_string(),
            Regex::new(r"[\u{202A}-\u{202E}]").unwrap(),
            "High".to_string(),
        ));
        
        rules.push((
            "Mixed Scripts".to_string(),
            Regex::new(r"[a-zA-Z].*[\u{0400}-\u{04FF}]|[\u{0400}-\u{04FF}].*[a-zA-Z]").unwrap(),
            "Medium".to_string(),
        ));
        
        rules.push((
            "Punycode Pattern".to_string(),
            Regex::new(r"xn--[a-z0-9]+").unwrap(),
            "Medium".to_string(),
        ));
        
        rules
    }

    pub fn analyze_text(&self, text: &str) -> AnalysisResults {
        let start_time = SystemTime::now();
        let id = Uuid::new_v4().to_string();
        let timestamp = Utc::now();
        
        // Create hash of the text
        let mut hasher = Sha256::new();
        hasher.update(text.as_bytes());
        let text_hash = format!("{:x}", hasher.finalize());
        
        let graphemes: Vec<&str> = text.graphemes(true).collect();
        let total_graphemes = graphemes.len();
        let visual_width = text.width();
        
        let mut suspicious_chars = Vec::new();
        let mut char_breakdown = HashMap::new();
        let mut script_breakdown = HashMap::new();
        
        // Analyze each character
        for (pos, ch) in text.char_indices() {
            let char_info = self.analyze_character(ch, pos);
            
            // Update statistics
            let category = char_info.category.clone();
            *char_breakdown.entry(category).or_insert(0) += 1;
            
            // Detect script
            let script = self.script_detector.detect_script(ch);
            *script_breakdown.entry(script).or_insert(0) += 1;
            
            if char_info.is_suspicious {
                suspicious_chars.push(char_info);
            }
        }
        
        // Encoding analysis
        let encoding_info = self.analyze_encoding(text);
        
        // Security analysis
        let security_analysis = self.perform_security_analysis(text, &suspicious_chars);
        
        // Pattern matching
        let patterns_found = self.find_patterns(text);
        
        // Generate recommendations
        let recommendations = self.generate_recommendations(&suspicious_chars, &security_analysis, &patterns_found);
        
        let duration = start_time.elapsed().unwrap_or_default();
        let confidence_score = self.calculate_confidence_score(&suspicious_chars, &patterns_found);
        
        AnalysisResults {
            id,
            timestamp,
            input_text: text.to_string(),
            text_hash,
            total_characters: text.chars().count(),
            total_bytes: text.len(),
            total_graphemes,
            visual_width,
            encoding_info,
            suspicious_characters: suspicious_chars,
            character_breakdown: char_breakdown,
            script_breakdown,
            analysis_duration_ms: duration.as_millis() as u64,
            confidence_score,
            security_analysis,
            patterns_found,
            recommendations,
        }
    }

    fn analyze_character(&self, ch: char, position: usize) -> CharacterInfo {
        let mut suspicion_reasons = Vec::new();
        let mut recommendations = Vec::new();
        
        // Basic character properties
        let codepoint = ch as u32;
        let utf8_bytes = ch.to_string().into_bytes();
        let mut utf16_buffer = [0u16; 2];
        let utf16_units = ch.encode_utf16(&mut utf16_buffer).to_vec();
        
        // Unicode properties
        let category = self.get_unicode_category(ch);
        let unicode_name = self.get_unicode_name(ch);
        let unicode_block = self.get_unicode_block(ch);
        let visual_width = ch.to_string().width();
        let is_combining = unicode_normalization::char::is_combining_mark(ch);
        let is_emoji = self.is_emoji(ch);
        
        // Suspicious character detection
        if ch.is_control() && !matches!(ch, '\n' | '\r' | '\t') {
            suspicion_reasons.push("Control character".to_string());
            recommendations.push("Remove or replace with visible equivalent".to_string());
        }
        
        if matches!(ch, '\u{200B}'..='\u{200D}') {
            suspicion_reasons.push("Zero-width character".to_string());
            recommendations.push("Consider if zero-width character is necessary".to_string());
        }
        
        if self.homograph_db.contains_key(&ch) {
            suspicion_reasons.push("Homograph character".to_string());
            recommendations.push("May be confused with similar-looking characters".to_string());
        }
        
        if matches!(ch, '\u{202A}'..='\u{202E}') {
            suspicion_reasons.push("Bidirectional text control".to_string());
            recommendations.push("Remove unless bidirectional text is intended".to_string());
        }
        
        if codepoint >= 0xFFF0 {
            suspicion_reasons.push("Private use or non-character".to_string());
            recommendations.push("Verify character is valid and necessary".to_string());
        }
        
        let is_suspicious = !suspicion_reasons.is_empty();
        
        CharacterInfo {
            character: ch,
            position,
            unicode_name,
            unicode_block,
            category,
            codepoint,
            utf8_bytes,
            utf16_units,
            is_suspicious,
            suspicion_reasons,
            recommendations,
            visual_width,
            is_combining,
            is_emoji,
        }
    }
    
    fn get_unicode_category(&self, ch: char) -> String {
        match ch {
            c if c.is_alphabetic() => "Letter".to_string(),
            c if c.is_numeric() => "Number".to_string(),
            c if c.is_whitespace() => "Whitespace".to_string(),
            c if c.is_control() => "Control".to_string(),
            c if c.is_ascii_punctuation() => "Punctuation".to_string(),
            _ => "Other".to_string(),
        }
    }
    
    fn get_unicode_name(&self, ch: char) -> String {
        // Simplified Unicode name mapping - in a real implementation, 
        // you'd use a proper Unicode database
        match ch {
            '\u{200B}' => "ZERO WIDTH SPACE".to_string(),
            '\u{200C}' => "ZERO WIDTH NON-JOINER".to_string(),
            '\u{200D}' => "ZERO WIDTH JOINER".to_string(),
            '\u{202A}' => "LEFT-TO-RIGHT EMBEDDING".to_string(),
            '\u{202B}' => "RIGHT-TO-LEFT EMBEDDING".to_string(),
            '\u{202C}' => "POP DIRECTIONAL FORMATTING".to_string(),
            '\u{202D}' => "LEFT-TO-RIGHT OVERRIDE".to_string(),
            '\u{202E}' => "RIGHT-TO-LEFT OVERRIDE".to_string(),
            '\u{FEFF}' => "ZERO WIDTH NO-BREAK SPACE".to_string(),
            c if c.is_ascii() => format!("LATIN {} {}", 
                if c.is_uppercase() { "CAPITAL" } else { "SMALL" },
                format!("LETTER {}", c.to_uppercase())
            ),
            _ => format!("U+{:04X}", ch as u32),
        }
    }
    
    fn get_unicode_block(&self, ch: char) -> String {
        let codepoint = ch as u32;
        match codepoint {
            0x0000..=0x007F => "Basic Latin".to_string(),
            0x0080..=0x00FF => "Latin-1 Supplement".to_string(),
            0x0100..=0x017F => "Latin Extended-A".to_string(),
            0x0180..=0x024F => "Latin Extended-B".to_string(),
            0x0250..=0x02AF => "IPA Extensions".to_string(),
            0x02B0..=0x02FF => "Spacing Modifier Letters".to_string(),
            0x0300..=0x036F => "Combining Diacritical Marks".to_string(),
            0x0370..=0x03FF => "Greek and Coptic".to_string(),
            0x0400..=0x04FF => "Cyrillic".to_string(),
            0x0500..=0x052F => "Cyrillic Supplement".to_string(),
            0x0530..=0x058F => "Armenian".to_string(),
            0x0590..=0x05FF => "Hebrew".to_string(),
            0x0600..=0x06FF => "Arabic".to_string(),
            0x0700..=0x074F => "Syriac".to_string(),
            0x0780..=0x07BF => "Thaana".to_string(),
            0x0900..=0x097F => "Devanagari".to_string(),
            0x1F600..=0x1F64F => "Emoticons".to_string(),
            0x1F300..=0x1F5FF => "Miscellaneous Symbols and Pictographs".to_string(),
            0x1F680..=0x1F6FF => "Transport and Map Symbols".to_string(),
            0x1F700..=0x1F77F => "Alchemical Symbols".to_string(),
            0x1F780..=0x1F7FF => "Geometric Shapes Extended".to_string(),
            0x1F800..=0x1F8FF => "Supplemental Arrows-C".to_string(),
            0x1F900..=0x1F9FF => "Supplemental Symbols and Pictographs".to_string(),
            0x2000..=0x206F => "General Punctuation".to_string(),
            0x20A0..=0x20CF => "Currency Symbols".to_string(),
            0x2100..=0x214F => "Letterlike Symbols".to_string(),
            0x2190..=0x21FF => "Arrows".to_string(),
            0x2200..=0x22FF => "Mathematical Operators".to_string(),
            0x2300..=0x23FF => "Miscellaneous Technical".to_string(),
            0x2400..=0x243F => "Control Pictures".to_string(),
            0x2440..=0x245F => "Optical Character Recognition".to_string(),
            0x2460..=0x24FF => "Enclosed Alphanumerics".to_string(),
            0x2500..=0x257F => "Box Drawing".to_string(),
            0x2580..=0x259F => "Block Elements".to_string(),
            0x25A0..=0x25FF => "Geometric Shapes".to_string(),
            0x2600..=0x26FF => "Miscellaneous Symbols".to_string(),
            0x2700..=0x27BF => "Dingbats".to_string(),
            0x4E00..=0x9FFF => "CJK Unified Ideographs".to_string(),
            0x3040..=0x309F => "Hiragana".to_string(),
            0x30A0..=0x30FF => "Katakana".to_string(),
            0xFFF0..=0xFFFF => "Specials".to_string(),
            _ => "Other".to_string(),
        }
    }
    
    fn is_emoji(&self, ch: char) -> bool {
        let codepoint = ch as u32;
        matches!(codepoint,
            0x1F600..=0x1F64F | // Emoticons
            0x1F300..=0x1F5FF | // Miscellaneous Symbols and Pictographs
            0x1F680..=0x1F6FF | // Transport and Map Symbols
            0x1F700..=0x1F77F | // Alchemical Symbols
            0x1F780..=0x1F7FF | // Geometric Shapes Extended
            0x1F800..=0x1F8FF | // Supplemental Arrows-C
            0x1F900..=0x1F9FF | // Supplemental Symbols and Pictographs
            0x2600..=0x26FF |   // Miscellaneous Symbols
            0x2700..=0x27BF     // Dingbats
        )
    }
    
    fn analyze_encoding(&self, text: &str) -> EncodingInfo {
        let bytes = text.as_bytes();
        let (_decoded, encoding, malformed) = WINDOWS_1252.decode(bytes);
        
        let detected_encoding = if std::str::from_utf8(bytes).is_ok() {
            "UTF-8".to_string()
        } else {
            encoding.name().to_string()
        };
        
        let confidence = if malformed { 0.5 } else { 0.95 };
        let is_valid_utf8 = std::str::from_utf8(bytes).is_ok();
        
        // Check for BOM
        let bom_detected = if bytes.starts_with(&[0xEF, 0xBB, 0xBF]) {
            Some("UTF-8 BOM".to_string())
        } else if bytes.starts_with(&[0xFF, 0xFE]) {
            Some("UTF-16 LE BOM".to_string())
        } else if bytes.starts_with(&[0xFE, 0xFF]) {
            Some("UTF-16 BE BOM".to_string())
        } else {
            None
        };
        
        // Detect line endings
        let line_endings = if text.contains("\r\n") {
            "CRLF (Windows)".to_string()
        } else if text.contains('\n') {
            "LF (Unix)".to_string()
        } else if text.contains('\r') {
            "CR (Classic Mac)".to_string()
        } else {
            "None".to_string()
        };
        
        EncodingInfo {
            detected_encoding,
            confidence,
            is_valid_utf8,
            bom_detected,
            line_endings,
        }
    }
    
    fn perform_security_analysis(&self, text: &str, suspicious_chars: &[CharacterInfo]) -> SecurityAnalysis {
        let mut phishing_indicators = Vec::new();
        let mut homograph_attacks = Vec::new();
        let mut script_mixing = Vec::new();
        let mut steganography_potential = false;
        
        // Check for homograph attacks
        for ch in text.chars() {
            if self.homograph_db.contains_key(&ch) {
                let lookalikes = &self.homograph_db[&ch];
                homograph_attacks.push(format!("Character '{}' (U+{:04X}) looks like {:?}", 
                    ch, ch as u32, lookalikes));
            }
        }
        
        // Check for script mixing
        let scripts = self.script_detector.detect_all_scripts(text);
        if scripts.len() > 1 {
            script_mixing = scripts;
        }
        
        // Check for steganography indicators
        let zero_width_count = text.chars().filter(|&c| matches!(c, '\u{200B}'..='\u{200D}')).count();
        if zero_width_count > 5 {
            steganography_potential = true;
        }
        
        // Check for phishing indicators
        if text.to_lowercase().contains("paypal") || text.to_lowercase().contains("amazon") {
            phishing_indicators.push("Contains common phishing target names".to_string());
        }
        
        if text.chars().any(|c| matches!(c, '\u{202A}'..='\u{202E}')) {
            phishing_indicators.push("Contains bidirectional text overrides".to_string());
        }
        
        // Calculate risk level
        let risk_level = if !homograph_attacks.is_empty() && !script_mixing.is_empty() {
            "Critical".to_string()
        } else if steganography_potential || !phishing_indicators.is_empty() {
            "High".to_string()
        } else if !suspicious_chars.is_empty() {
            "Medium".to_string()
        } else {
            "Low".to_string()
        };
        
        SecurityAnalysis {
            risk_level,
            phishing_indicators,
            homograph_attacks,
            steganography_potential,
            script_mixing,
        }
    }
    
    fn find_patterns(&self, text: &str) -> Vec<PatternMatch> {
        let mut matches = Vec::new();
        
        for (name, regex, severity) in &self.pattern_rules {
            for m in regex.find_iter(text) {
                matches.push(PatternMatch {
                    pattern_name: name.clone(),
                    description: format!("Pattern '{}' detected", name),
                    start_position: m.start(),
                    end_position: m.end(),
                    matched_text: m.as_str().to_string(),
                    severity: severity.clone(),
                });
            }
        }
        
        matches
    }
    
    fn generate_recommendations(&self, suspicious_chars: &[CharacterInfo], 
                               security_analysis: &SecurityAnalysis, 
                               patterns: &[PatternMatch]) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if !suspicious_chars.is_empty() {
            recommendations.push(format!("Found {} suspicious characters that may need review", 
                suspicious_chars.len()));
        }
        
        if !security_analysis.homograph_attacks.is_empty() {
            recommendations.push("Consider replacing lookalike characters with standard equivalents".to_string());
        }
        
        if security_analysis.steganography_potential {
            recommendations.push("High number of zero-width characters detected - check for hidden content".to_string());
        }
        
        if !security_analysis.script_mixing.is_empty() {
            recommendations.push("Multiple scripts detected - verify this is intentional".to_string());
        }
        
        for pattern in patterns {
            if pattern.severity == "Critical" || pattern.severity == "High" {
                recommendations.push(format!("High-risk pattern detected: {}", pattern.pattern_name));
            }
        }
        
        if recommendations.is_empty() {
            recommendations.push("Text appears to be clean and safe".to_string());
        }
        
        recommendations
    }
    
    fn calculate_confidence_score(&self, suspicious_chars: &[CharacterInfo], patterns: &[PatternMatch]) -> f32 {
        let base_score = 1.0;
        let suspicious_penalty = suspicious_chars.len() as f32 * 0.1;
        let pattern_penalty = patterns.iter()
            .map(|p| match p.severity.as_str() {
                "Critical" => 0.3,
                "High" => 0.2,
                "Medium" => 0.1,
                _ => 0.05,
            })
            .sum::<f32>();
        
        (base_score - suspicious_penalty - pattern_penalty).max(0.0).min(1.0)
    }
    
    pub fn clean_text(&self, text: &str) -> String {
        let mut cleaned = String::new();
        
        for ch in text.chars() {
            // Check if character should be removed based on comprehensive bad character list
            if self.should_remove_character(ch) {
                continue;
            }
            
            // Replace homograph characters with their Latin equivalents
            if let Some(replacements) = self.homograph_db.get(&ch) {
                if let Some(&replacement) = replacements.first() {
                    cleaned.push(replacement);
                } else {
                    cleaned.push(ch);
                }
            } else {
                cleaned.push(ch);
            }
        }
        
        // DO NOT normalize whitespace for code files - preserve original formatting
        cleaned
    }
    
    /// Enhanced character removal check using AssetManager data and comprehensive Bad_Characters.json
    fn should_remove_character(&self, ch: char) -> bool {
        let code = ch as u32;
        
        // First check using AssetManager data for severity-based analysis
        if let Some(severity) = self.asset_manager.get_character_severity(code) {
            // Remove characters based on severity levels from Bad_Characters.json
            match severity {
                "extremely_big_problems" | "high_problems" => return true,
                "medium_problems" => return true, // For comprehensive cleaning
                "low_problems" => return true,    // For maximum safety
                _ => {}
            }
        }
        
        // Fallback to hardcoded comprehensive list (for any characters not in JSON)
        
        // Extremely Big Problems - Zero-width and invisible characters
        if matches!(ch, 
            '\u{200B}' |  // Zero Width Space
            '\u{FEFF}' |  // Zero Width No-Break Space / BOM
            '\u{2060}'    // Word Joiner / Zero Width No-Break Space
        ) {
            return true;
        }
        
        // High Problems - Problematic space characters
        if matches!(ch,
            '\u{00A0}' |  // No-Break Space
            '\u{2000}' |  // En Quad
            '\u{2001}' |  // Em Quad
            '\u{2002}' |  // En Space
            '\u{2003}' |  // Em Space
            '\u{2004}' |  // Three-Per-Em Space
            '\u{2005}' |  // Four-Per-Em Space
            '\u{2006}' |  // Six-Per-Em Space
            '\u{2007}' |  // Figure Space
            '\u{2008}' |  // Punctuation Space
            '\u{2009}' |  // Thin Space
            '\u{200A}' |  // Hair Space
            '\u{202F}' |  // Narrow No-Break Space
            '\u{205F}' |  // Medium Mathematical Space
            '\u{3000}'    // Ideographic Space
        ) {
            return true;
        }
        
        // High Problems - Line and paragraph separators
        if matches!(ch,
            '\u{2028}' |  // Line Separator
            '\u{2029}'    // Paragraph Separator
        ) {
            return true;
        }
        
        // High Problems - Bidirectional text controls
        if matches!(ch,
            '\u{200E}' |  // Left-to-Right Mark
            '\u{200F}' |  // Right-to-Left Mark
            '\u{202A}' |  // Left-to-Right Embedding
            '\u{202B}' |  // Right-to-Left Embedding
            '\u{202C}' |  // Pop Directional Formatting
            '\u{202D}' |  // Left-to-Right Override
            '\u{202E}' |  // Right-to-Left Override
            '\u{2066}' |  // Left-to-Right Isolate
            '\u{2067}' |  // Right-to-Left Isolate
            '\u{2068}' |  // First Strong Isolate
            '\u{2069}'    // Pop Directional Isolate
        ) {
            return true;
        }
        
        // High Problems - NEL (Next Line)
        if ch == '\u{0085}' {
            return true;
        }
        
        // Medium Problems - Additional zero-width and invisible characters
        if matches!(ch,
            '\u{00AD}' |  // Soft Hyphen
            '\u{200C}' |  // Zero Width Non-Joiner
            '\u{200D}' |  // Zero Width Joiner
            '\u{FFFC}'    // Object Replacement Character
        ) {
            return true;
        }
        
        // C0 Control Characters (0x00-0x1F) except standard whitespace
        if code <= 0x1F && !matches!(ch, '\n' | '\r' | '\t') {
            return true;
        }
        
        // DEL character
        if ch == '\u{007F}' {
            return true;
        }
        
        // C1 Control Characters (0x80-0x9F)
        if code >= 0x80 && code <= 0x9F {
            return true;
        }
        
        // Low Problems - Mathematical notation characters
        if matches!(ch,
            '\u{2061}' |  // Function Application
            '\u{2062}' |  // Invisible Times
            '\u{2063}' |  // Invisible Separator
            '\u{2064}'    // Invisible Plus
        ) {
            return true;
        }
        
        // Variation Selectors (FE00-FE0F)
        if code >= 0xFE00 && code <= 0xFE0F {
            return true;
        }
        
        // Interlinear annotation characters
        if matches!(ch,
            '\u{FFF9}' |  // Interlinear Annotation Anchor
            '\u{FFFA}' |  // Interlinear Annotation Separator
            '\u{FFFB}'    // Interlinear Annotation Terminator
        ) {
            return true;
        }
        
        // Replacement Character (usually indicates corruption)
        if ch == '\u{FFFD}' {
            return true;
        }
        
        // Additional problematic characters not in Bad_Characters.json
        
        // Noncharacter code points (should never appear in text)
        if ch == '\u{FFFF}' {
            return true;
        }
        
        // Kaithi script characters (often problematic in mixed text)
        if ch == '\u{110BD}' {  // Kaithi Number Sign
            return true;
        }
        
        // Mongolian script characters (can cause rendering issues)
        if code >= 0x1BCA0 && code <= 0x1BCA3 {  // Mongolian cluster characters
            return true;
        }
        
        // Musical Symbol characters (notation marks, not for regular text)
        if code >= 0x1D173 && code <= 0x1D17A {  // Musical Symbol characters
            return true;
        }
        
        // Tag characters (language/script tags, invisible formatting)
        if matches!(ch,
            '\u{E0000}' |  // Language Tag
            '\u{E0001}' |  // Language Tag
            '\u{E0020}' |  // Tag Space
            '\u{E007F}'    // Cancel Tag
        ) {
            return true;
        }
        
        false
    }
}

// Script detection helper
#[derive(Debug, Clone)]
pub struct ScriptDetector;

impl ScriptDetector {
    pub fn new() -> Self {
        Self
    }

    pub fn detect_script(&self, ch: char) -> String {
        let codepoint = ch as u32;
        match codepoint {
            0x0000..=0x007F => "Basic Latin".to_string(),
            0x0080..=0x00FF => "Latin-1".to_string(),
            0x0100..=0x017F => "Latin Extended-A".to_string(),
            0x0180..=0x024F => "Latin Extended-B".to_string(),
            0x0400..=0x04FF => "Cyrillic".to_string(),
            0x0370..=0x03FF => "Greek".to_string(),
            0x0590..=0x05FF => "Hebrew".to_string(),
            0x0600..=0x06FF => "Arabic".to_string(),
            0x4E00..=0x9FFF => "CJK Unified Ideographs".to_string(),
            0x3040..=0x309F => "Hiragana".to_string(),
            0x30A0..=0x30FF => "Katakana".to_string(),
            _ => "Other".to_string(),
        }
    }

    pub fn detect_all_scripts(&self, text: &str) -> Vec<String> {
        let mut scripts = std::collections::HashSet::new();
        for ch in text.chars() {
            scripts.insert(self.detect_script(ch));
        }
        scripts.into_iter().collect()
    }
}

// Tauri command handlers
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
pub async fn analyze_characters(text: String) -> Result<AnalysisResults, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);

    // Debug: Print the JSON being sent to the frontend
    match serde_json::to_string_pretty(&results) {
        Ok(json_string) => {
            println!("Laptos_Tauri Backend: Sending AnalysisResults JSON from analyze_characters:\n{}", json_string);
        }
        Err(e) => {
            eprintln!("Laptos_Tauri Backend: Error serializing AnalysisResults for logging in analyze_characters: {}", e);
        }
    }

    Ok(results)
}

#[tauri::command]
async fn analyze_file(request: FileAnalysisRequest) -> Result<AnalysisResults, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&request.content);
    Ok(results)
}

#[tauri::command]
pub async fn batch_analyze(texts: Vec<String>) -> Result<Vec<AnalysisResults>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut results = Vec::new();
    
    for text in texts {
        let analysis = analyzer.analyze_text(&text);
        results.push(analysis);
    }
    
    Ok(results)
}

#[tauri::command]
pub async fn export_analysis(results: AnalysisResults, format: String) -> Result<String, String> {
    match format.as_str() {
        "json" => {
            serde_json::to_string_pretty(&results)
                .map_err(|e| format!("Failed to serialize to JSON: {}", e))
        },
        "csv" => {
            let mut csv_content = String::new();
            csv_content.push_str("Position,Character,Unicode,Category,Suspicious,Reasons\n");
            
            for char_info in &results.suspicious_characters {
                csv_content.push_str(&format!(
                    "{},{},U+{:04X},{},{},{}\n",
                    char_info.position,
                    char_info.character,
                    char_info.codepoint,
                    char_info.category,
                    char_info.is_suspicious,
                    char_info.suspicion_reasons.join("; ")
                ));
            }
            
            Ok(csv_content)
        },
        "txt" => {
            let mut report = String::new();
            report.push_str(&format!("Character Analysis Report\n"));
            report.push_str(&format!("Generated: {}\n", results.timestamp));
            report.push_str(&format!("Analysis ID: {}\n\n", results.id));
            
            report.push_str(&format!("Summary:\n"));
            report.push_str(&format!("- Total Characters: {}\n", results.total_characters));
            report.push_str(&format!("- Total Bytes: {}\n", results.total_bytes));
            report.push_str(&format!("- Visual Width: {}\n", results.visual_width));
            report.push_str(&format!("- Suspicious Characters: {}\n", results.suspicious_characters.len()));
            report.push_str(&format!("- Risk Level: {}\n\n", results.security_analysis.risk_level));
            
            if !results.suspicious_characters.is_empty() {
                report.push_str("Suspicious Characters:\n");
                for char_info in &results.suspicious_characters {
                    report.push_str(&format!(
                        "- Position {}: '{}' (U+{:04X}) - {}\n",
                        char_info.position,
                        char_info.character,
                        char_info.codepoint,
                        char_info.suspicion_reasons.join(", ")
                    ));
                }
                report.push_str("\n");
            }
            
            if !results.recommendations.is_empty() {
                report.push_str("Recommendations:\n");
                for rec in &results.recommendations {
                    report.push_str(&format!("- {}\n", rec));
                }
            }
            
            Ok(report)
        },
        _ => Err("Unsupported export format".to_string()),
    }
}

#[tauri::command]
pub async fn get_character_details(character: char) -> Result<CharacterInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let char_info = analyzer.analyze_character(character, 0);
    Ok(char_info)
}

#[tauri::command]
pub async fn detect_encoding(bytes: Vec<u8>) -> Result<EncodingInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    
    // Try to convert bytes to string
    let text = match std::str::from_utf8(&bytes) {
        Ok(s) => s.to_string(),
        Err(_) => {
            // Try different encodings
            let (decoded, _encoding, _) = WINDOWS_1252.decode(&bytes);
            decoded.to_string()
        }
    };
    
    let encoding_info = analyzer.analyze_encoding(&text);
    Ok(encoding_info)
}

#[tauri::command]
pub async fn check_homographs(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut homographs = Vec::new();
    
    for ch in text.chars() {
        if analyzer.homograph_db.contains_key(&ch) {
            let lookalikes = &analyzer.homograph_db[&ch];
            homographs.push(format!("'{}' (U+{:04X}) looks like: {:?}", 
                ch, ch as u32, lookalikes));
        }
    }
    
    Ok(homographs)
}

#[tauri::command]
pub async fn normalize_text(text: String, form: String) -> Result<String, String> {
    use unicode_normalization::UnicodeNormalization;
    
    let normalized = match form.as_str() {
        "nfc" => text.nfc().collect::<String>(),
        "nfd" => text.nfd().collect::<String>(),
        "nfkc" => text.nfkc().collect::<String>(),
        "nfkd" => text.nfkd().collect::<String>(),
        _ => return Err("Invalid normalization form. Use: nfc, nfd, nfkc, nfkd".to_string()),
    };
    
    Ok(normalized)
}

#[tauri::command]
pub async fn get_script_info(text: String) -> Result<HashMap<String, usize>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut script_counts = HashMap::new();
    
    for ch in text.chars() {
        let script = analyzer.script_detector.detect_script(ch);
        *script_counts.entry(script).or_insert(0) += 1;
    }
    
    Ok(script_counts)
}

#[derive(Serialize, Deserialize)]
struct CleaningChange {
    start: usize,
    end: usize,
    original: String,
    cleaned: String,
    change_type: String,
}

#[derive(Serialize, Deserialize)]
pub struct CleaningResult {
    original: String,
    cleaned: String,
    changes: Vec<CleaningChange>,
    stats: HashMap<String, u32>,
}

#[tauri::command]
pub async fn clean_text(text: String, options: HashMap<String, bool>) -> Result<String, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut cleaned = text;
    
    // Remove zero-width characters (comprehensive set)
    if *options.get("remove_zero_width").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !matches!(c, 
                '\u{200B}' |  // Zero Width Space
                '\u{FEFF}' |  // Zero Width No-Break Space / BOM
                '\u{2060}' |  // Word Joiner / Zero Width No-Break Space
                '\u{200C}' |  // Zero Width Non-Joiner
                '\u{200D}'    // Zero Width Joiner
            ))
            .collect();
    }
    
    // Remove control characters (comprehensive set)
    if *options.get("remove_control").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| {
                let code = c as u32;
                // Keep standard whitespace, remove other control characters
                !((code <= 0x1F && !matches!(c, '\n' | '\r' | '\t')) ||
                  c == '\u{007F}' ||
                  (code >= 0x80 && code <= 0x9F) ||
                  c == '\u{0085}')
            })
            .collect();
    }
    
    // Remove bidirectional overrides (comprehensive set)
    if *options.get("remove_bidi").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !matches!(c,
                '\u{200E}' |  // Left-to-Right Mark
                '\u{200F}' |  // Right-to-Left Mark
                '\u{202A}' |  // Left-to-Right Embedding
                '\u{202B}' |  // Right-to-Left Embedding
                '\u{202C}' |  // Pop Directional Formatting
                '\u{202D}' |  // Left-to-Right Override
                '\u{202E}' |  // Right-to-Left Override
                '\u{2066}' |  // Left-to-Right Isolate
                '\u{2067}' |  // Right-to-Left Isolate
                '\u{2068}' |  // First Strong Isolate
                '\u{2069}'    // Pop Directional Isolate
            ))
            .collect();
    }
    
    // Comprehensive cleaning using all bad characters from Bad_Characters.json
    if *options.get("comprehensive_clean").unwrap_or(&false) {
        cleaned = cleaned.chars()
            .filter(|&c| !analyzer.should_remove_character(c))
            .collect();
    }
    
    // Normalize whitespace
    if *options.get("normalize_whitespace").unwrap_or(&false) {
        cleaned = cleaned.split_whitespace().collect::<Vec<_>>().join(" ");
    }
    
    Ok(cleaned)
}

#[tauri::command]
pub async fn clean_text_detailed(text: String, options: HashMap<String, bool>) -> Result<CleaningResult, String> {
    let original = text.clone();
    let mut changes = Vec::new();
    let mut stats = HashMap::new();
    let mut current_text = text;
    let char_offset = 0;
    
    let analyzer = CharacterAnalyzer::new()?;
    
    // Track each cleaning operation with changes using comprehensive character removal
    if *options.get("remove_zero_width").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            // Use comprehensive zero-width character detection
            if matches!(ch, 
                '\u{200B}' |  // Zero Width Space
                '\u{FEFF}' |  // Zero Width No-Break Space / BOM
                '\u{2060}' |  // Word Joiner / Zero Width No-Break Space
                '\u{200C}' |  // Zero Width Non-Joiner
                '\u{200D}'    // Zero Width Joiner
            ) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: ch.to_string(),
                    cleaned: String::new(),
                    change_type: "zero_width_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("zero_width_removed".to_string(), removed_count);
    }
    
    if *options.get("remove_control").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            let code = ch as u32;
            // Comprehensive control character removal
            let is_control_to_remove = 
                // C0 Control Characters (0x00-0x1F) except standard whitespace
                (code <= 0x1F && !matches!(ch, '\n' | '\r' | '\t')) ||
                // DEL character
                ch == '\u{007F}' ||
                // C1 Control Characters (0x80-0x9F)
                (code >= 0x80 && code <= 0x9F) ||
                // NEL (Next Line)
                ch == '\u{0085}';
                
            if is_control_to_remove {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "control_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("control_removed".to_string(), removed_count);
    }
    
    if *options.get("remove_bidi").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            // Comprehensive bidirectional control characters
            if matches!(ch,
                '\u{200E}' |  // Left-to-Right Mark
                '\u{200F}' |  // Right-to-Left Mark
                '\u{202A}' |  // Left-to-Right Embedding
                '\u{202B}' |  // Right-to-Left Embedding
                '\u{202C}' |  // Pop Directional Formatting
                '\u{202D}' |  // Left-to-Right Override
                '\u{202E}' |  // Right-to-Left Override
                '\u{2066}' |  // Left-to-Right Isolate
                '\u{2067}' |  // Right-to-Left Isolate
                '\u{2068}' |  // First Strong Isolate
                '\u{2069}'    // Pop Directional Isolate
            ) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "bidi_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("bidi_removed".to_string(), removed_count);
    }
    
    // Add comprehensive cleaning option that removes ALL problematic characters
    if *options.get("comprehensive_clean").unwrap_or(&false) {
        let mut new_text = String::new();
        let mut removed_count = 0;
        let mut pos = 0;
        
        for ch in current_text.chars() {
            if analyzer.should_remove_character(ch) {
                changes.push(CleaningChange {
                    start: pos + char_offset,
                    end: pos + char_offset + ch.len_utf8(),
                    original: format!("\\u{{{:04X}}}", ch as u32),
                    cleaned: String::new(),
                    change_type: "comprehensive_removed".to_string(),
                });
                removed_count += 1;
            } else {
                new_text.push(ch);
            }
            pos += ch.len_utf8();
        }
        current_text = new_text;
        stats.insert("comprehensive_removed".to_string(), removed_count);
    }
    
    if *options.get("normalize_whitespace").unwrap_or(&false) {
        let normalized = current_text.split_whitespace().collect::<Vec<_>>().join(" ");
        if normalized != current_text {
            changes.push(CleaningChange {
                start: 0,
                end: current_text.len(),
                original: current_text.clone(),
                cleaned: normalized.clone(),
                change_type: "whitespace_normalized".to_string(),
            });
            stats.insert("whitespace_normalized".to_string(), 1);
        }
        current_text = normalized;
    }
    
    Ok(CleaningResult {
        original,
        cleaned: current_text,
        changes,
        stats,
    })
}

#[tauri::command]
pub async fn generate_report(results: AnalysisResults) -> Result<String, String> {
    let mut report = String::new();
    
    // HTML report
    report.push_str("<!DOCTYPE html>\n<html>\n<head>\n");
    report.push_str("<title>Character Analysis Report</title>\n");
    report.push_str("<style>\n");
    report.push_str("body { font-family: Arial, sans-serif; margin: 20px; }\n");
    report.push_str(".header { background: #f5f5f5; padding: 15px; border-radius: 5px; }\n");
    report.push_str(".section { margin: 20px 0; }\n");
    report.push_str(".suspicious { color: #d32f2f; font-weight: bold; }\n");
    report.push_str(".safe { color: #388e3c; }\n");
    report.push_str("table { border-collapse: collapse; width: 100%; }\n");
    report.push_str("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
    report.push_str("th { background-color: #f2f2f2; }\n");
    report.push_str("</style>\n</head>\n<body>\n");
    
    report.push_str(&format!("<div class='header'>\n"));
    report.push_str(&format!("<h1>Character Analysis Report</h1>\n"));
    report.push_str(&format!("<p><strong>Analysis ID:</strong> {}</p>\n", results.id));
    report.push_str(&format!("<p><strong>Generated:</strong> {}</p>\n", results.timestamp));
    report.push_str(&format!("<p><strong>Risk Level:</strong> <span class='{}'>{}</span></p>\n", 
        if results.security_analysis.risk_level == "Low" { "safe" } else { "suspicious" },
        results.security_analysis.risk_level));
    report.push_str("</div>\n");
    
    report.push_str(&format!("<div class='section'>\n"));
    report.push_str(&format!("<h2>Summary</h2>\n"));
    report.push_str(&format!("<ul>\n"));
    report.push_str(&format!("<li>Total Characters: {}</li>\n", results.total_characters));
    report.push_str(&format!("<li>Total Bytes: {}</li>\n", results.total_bytes));
    report.push_str(&format!("<li>Visual Width: {}</li>\n", results.visual_width));
    report.push_str(&format!("<li>Suspicious Characters: {}</li>\n", results.suspicious_characters.len()));
    report.push_str(&format!("<li>Confidence Score: {:.2}%</li>\n", results.confidence_score * 100.0));
    report.push_str(&format!("</ul>\n"));
    report.push_str("</div>\n");
    
    if !results.suspicious_characters.is_empty() {
        report.push_str(&format!("<div class='section'>\n"));
        report.push_str(&format!("<h2>Suspicious Characters</h2>\n"));
        report.push_str("<table>\n");
        report.push_str("<tr><th>Position</th><th>Character</th><th>Unicode</th><th>Category</th><th>Reasons</th></tr>\n");
        
        for char_info in &results.suspicious_characters {
            report.push_str(&format!(
                "<tr><td>{}</td><td>{}</td><td>U+{:04X}</td><td>{}</td><td>{}</td></tr>\n",
                char_info.position,
                char_info.character,
                char_info.codepoint,
                char_info.category,
                char_info.suspicion_reasons.join(", ")
            ));
        }
        
        report.push_str("</table>\n</div>\n");
    }
    
    if !results.recommendations.is_empty() {
        report.push_str(&format!("<div class='section'>\n"));
        report.push_str(&format!("<h2>Recommendations</h2>\n"));
        report.push_str("<ul>\n");
        for rec in &results.recommendations {
            report.push_str(&format!("<li>{}</li>\n", rec));
        }
        report.push_str("</ul>\n</div>\n");
    }
    
    report.push_str("</body>\n</html>");
    
    Ok(report)
}

#[tauri::command]
async fn analyze_text(text: String) -> Result<AnalysisResults, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results)
}

#[tauri::command]
async fn get_security_analysis(text: String) -> Result<SecurityAnalysis, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results.security_analysis)
}

#[tauri::command]
async fn get_pattern_analysis(text: String) -> Result<Vec<PatternMatch>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let results = analyzer.analyze_text(&text);
    Ok(results.patterns_found)
}

#[tauri::command]
async fn get_encoding_analysis(text: String) -> Result<EncodingInfo, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let encoding_info = analyzer.analyze_encoding(&text);
    Ok(encoding_info)
}

#[tauri::command]
async fn detect_mixed_scripts(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let scripts = analyzer.script_detector.detect_all_scripts(&text);
    Ok(scripts)
}

#[tauri::command]
async fn analyze_homoglyphs(text: String) -> Result<Vec<String>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let mut homographs = Vec::new();
    
    for ch in text.chars() {
        if analyzer.homograph_db.contains_key(&ch) {
            let lookalikes = &analyzer.homograph_db[&ch];
            homographs.push(format!("'{}' (U+{:04X}) looks like: {:?}", 
                ch, ch as u32, lookalikes));
        }
    }
    
    Ok(homographs)
}

#[tauri::command]
async fn detect_suspicious_patterns(text: String) -> Result<Vec<PatternMatch>, String> {
    let analyzer = CharacterAnalyzer::new()?;
    let patterns = analyzer.find_patterns(&text);
    Ok(patterns)
}

#[tauri::command]
pub async fn select_folder() -> Result<Option<String>, String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        
        // Use a more robust PowerShell command with better error handling
        let powershell_script = r#"
            Add-Type -AssemblyName System.Windows.Forms
            $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
            $folderBrowser.Description = 'Select source code folder to analyze for suspicious characters'
            $folderBrowser.ShowNewFolderButton = $false
            $folderBrowser.RootFolder = [System.Environment+SpecialFolder]::MyComputer
            
            $result = $folderBrowser.ShowDialog()
            if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
                Write-Output $folderBrowser.SelectedPath
            } else {
                Write-Output ""
            }
        "#;
        
        let output = Command::new("powershell")
            .args(&["-ExecutionPolicy", "Bypass", "-Command", powershell_script])
            .output();
            
        match output {
            Ok(result) => {
                if result.status.success() {
                    let path_string = String::from_utf8_lossy(&result.stdout);
                    let path = path_string.trim();
                    if path.is_empty() {
                        Ok(None) // User cancelled
                    } else {
                        // Validate the path exists
                        if std::path::Path::new(path).exists() {
                            Ok(Some(path.to_string()))
                        } else {
                            Err(format!("Selected path does not exist: {}", path))
                        }
                    }
                } else {
                    let error = String::from_utf8_lossy(&result.stderr);
                    Err(format!("PowerShell error: {}", error))
                }
            }
            Err(e) => Err(format!("Failed to open folder dialog: {}", e))
        }
    }
    
    #[cfg(target_os = "macos")]
    {
        use std::process::Command;
        
        let output = Command::new("osascript")
            .args(&[
                "-e",
                "tell application \"Finder\" to return POSIX path of (choose folder with prompt \"Select source code folder to analyze\")"
            ])
            .output();
            
        match output {
            Ok(result) => {
                if result.status.success() {
                    let path_string = String::from_utf8_lossy(&result.stdout);
                    let path = path_string.trim();
                    if path.is_empty() {
                        Ok(None)
                    } else {
                        Ok(Some(path.to_string()))
                    }
                } else {
                    Ok(None) // User cancelled
                }
            }
            Err(e) => Err(format!("Failed to open folder dialog: {}", e))
        }
    }
    
    #[cfg(target_os = "linux")]
    {
        use std::process::Command;
        
        
        // Try zenity first, then kdialog
        let zenity_output = Command::new("zenity")
            .args(&["--file-selection", "--directory", "--title=Select source code folder to analyze"])
            .output();
            
        match zenity_output {
            Ok(result) => {
                if result.status.success() {
                    let path_string = String::from_utf8_lossy(&result.stdout);
                    let path = path_string.trim();
                    if path.is_empty() {
                        Ok(None)
                    } else {
                        Ok(Some(path.to_string()))
                    }
                } else {
                    Ok(None) // User cancelled
                }
            }
            Err(_) => {
                // Try kdialog as fallback
                let kdialog_output = Command::new("kdialog")
                    .args(&["--getexistingdirectory", ".", "--title", "Select source code folder to analyze"])
                    .output();
                    
                match kdialog_output {
                    Ok(result) => {
                        if result.status.success() {
                            let path_string = String::from_utf8_lossy(&result.stdout);
                            let path = path_string.trim();
                            if path.is_empty() {
                                Ok(None)
                            } else {
                                Ok(Some(path.to_string()))
                            }
                        } else {
                            Ok(None) // User cancelled
                        }
                    }
                    Err(_) => Err("No suitable file dialog found. Please install zenity or kdialog.".to_string())
                }
            }
        }
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        Err("Folder selection not implemented for this platform.".to_string())
    }
}

#[tauri::command]
pub async fn analyze_codebase(folder_path: String, app_handle: tauri::AppHandle) -> Result<CodeBaseAnalysisResult, String> {
    let start_time = SystemTime::now();
    let analyzer = CharacterAnalyzer::new()?;
    
    let folder_path = Path::new(&folder_path);
    if !folder_path.exists() {
        return Err("Folder does not exist".to_string());
    }
    
    if !folder_path.is_dir() {
        return Err("Path is not a directory".to_string());
    }
    
    let mut file_details = Vec::new();
    let mut total_files = 0;
    let mut files_with_issues = 0;
    let mut total_suspicious_chars = 0;
    
    // Get supported file extensions from AssetManager
    let code_extensions: Vec<&str> = analyzer.asset_manager.supported_extensions
        .iter()
        .map(|s| s.as_str())
        .collect();
    
    // First pass: count total files for progress calculation
    let total_file_count = count_code_files(folder_path, &code_extensions)?;
    let operation_id = format!("analyze_{}", SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis());
    
    // Emit initial progress
    let _ = app_handle.emit("analysis-progress", ProgressPayload {
        current: 0,
        total: total_file_count as u32,
        percentage: 0.0,
        message: Some("Starting analysis...".to_string()),
        operation_id: operation_id.clone(),
        stage: Some("initialization".to_string()),
    });
    
    let mut processed_files = 0;
    
    // Helper function to count code files
    fn count_code_files(dir: &Path, code_extensions: &[&str]) -> Result<usize, String> {
        let mut count = 0;
        let entries = fs::read_dir(dir).map_err(|e| format!("Failed to read directory: {}", e))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
            let path = entry.path();
            
            if path.is_dir() {
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if !matches!(dir_name, "node_modules" | "target" | ".git" | "build" | "dist" | "vendor" | "__pycache__") {
                        count += count_code_files(&path, code_extensions)?;
                    }
                }
            } else if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
                if code_extensions.contains(&extension) {
                    count += 1;
                }
            }
        }
        Ok(count)
    }
    
    // Recursively walk through directory
    fn walk_dir(
        dir: &Path, 
        analyzer: &CharacterAnalyzer, 
        file_details: &mut Vec<FileAnalysisDetail>,
        total_files: &mut usize,
        files_with_issues: &mut usize,
        total_suspicious_chars: &mut usize,
        code_extensions: &[&str],
        base_path: &Path,
        app_handle: &tauri::AppHandle,
        processed_files: &mut usize,
        total_file_count: usize,
        operation_id: &str,
    ) -> Result<(), String> {
        let entries = fs::read_dir(dir).map_err(|e| format!("Failed to read directory: {}", e))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
            let path = entry.path();
            
            if path.is_dir() {
                // Skip common directories that shouldn't be analyzed
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if matches!(dir_name, "node_modules" | "target" | ".git" | "build" | "dist" | "vendor" | "__pycache__") {
                        continue;
                    }
                }
                walk_dir(&path, analyzer, file_details, total_files, files_with_issues, total_suspicious_chars, code_extensions, base_path, app_handle, processed_files, total_file_count, operation_id)?;
            } else {
                // Check if file has a supported extension
                if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
                    if code_extensions.contains(&extension) {
                        *total_files += 1;
                        
                        let relative_path = path.strip_prefix(base_path)
                            .unwrap_or(&path)
                            .to_string_lossy()
                            .to_string();
                        
                        // Analyze file
                        let file_detail = analyze_single_file(&path, &relative_path, analyzer)?;
                        
                        if file_detail.suspicious_characters > 0 {
                            *files_with_issues += 1;
                            *total_suspicious_chars += file_detail.suspicious_characters;
                        }
                        
                        file_details.push(file_detail);
                        
                        // Update progress
                        *processed_files += 1;
                        let progress_percentage = (*processed_files as f32 / total_file_count as f32 * 100.0).min(100.0);
                        
                        // Emit progress update
                        let file_name = path.file_name()
                            .and_then(|n| n.to_str())
                            .unwrap_or("unknown");
                        
                        let _ = app_handle.emit("analysis-progress", ProgressPayload {
                            current: *processed_files as u32,
                            total: total_file_count as u32,
                            percentage: progress_percentage,
                            message: Some(format!("Analyzing: {}", file_name)),
                            operation_id: operation_id.to_string(),
                            stage: Some("analysis".to_string()),
                        });
                    }
                }
            }
        }
        Ok(())
    }
    
    fn analyze_single_file(
        file_path: &Path, 
        relative_path: &str, 
        analyzer: &CharacterAnalyzer
    ) -> Result<FileAnalysisDetail, String> {
        let file_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => {
                return Ok(FileAnalysisDetail {
                    file_path: file_path.to_string_lossy().to_string(),
                    relative_path: relative_path.to_string(),
                    file_size: 0,
                    total_characters: 0,
                    suspicious_characters: 0,
                    issues: vec![],
                    file_type: file_path.extension()
                        .and_then(|e| e.to_str())
                        .unwrap_or("unknown")
                        .to_string(),
                    encoding: "unknown".to_string(),
                    analysis_status: "error".to_string(),
                    error_message: Some(format!("Failed to read file: {}", e)),
                });
            }
        };
        
        let file_size = file_content.len() as u64;
        let total_characters = file_content.chars().count();
        
        // Analyze the file content
        let analysis_results = analyzer.analyze_text(&file_content);
        let suspicious_characters = analysis_results.suspicious_characters.len();
        
        let mut issues = Vec::new();
        for char_info in &analysis_results.suspicious_characters {
            for reason in &char_info.suspicion_reasons {
                if !issues.contains(reason) {
                    issues.push(reason.clone());
                }
            }
        }
        
        // Add pattern-based issues
        for pattern in &analysis_results.patterns_found {
            let issue = format!("{}: {}", pattern.pattern_name, pattern.description);
            if !issues.contains(&issue) {
                issues.push(issue);
            }
        }
        
        Ok(FileAnalysisDetail {
            file_path: file_path.to_string_lossy().to_string(),
            relative_path: relative_path.to_string(),
            file_size,
            total_characters,
            suspicious_characters,
            issues,
            file_type: file_path.extension()
                .and_then(|e| e.to_str())
                .unwrap_or("unknown")
                .to_string(),
            encoding: analysis_results.encoding_info.detected_encoding,
            analysis_status: "success".to_string(),
            error_message: None,
        })
    }
    
    // Start the analysis
    walk_dir(
        folder_path, 
        &analyzer, 
        &mut file_details, 
        &mut total_files, 
        &mut files_with_issues, 
        &mut total_suspicious_chars, 
        &code_extensions,
        folder_path,
        &app_handle,
        &mut processed_files,
        total_file_count,
        &operation_id,
    )?;
    
    // Calculate health score (0-100, where 100 is perfect)
    let health_score = if total_files == 0 {
        100.0
    } else {
        let issue_ratio = files_with_issues as f64 / total_files as f64;
        ((1.0 - issue_ratio) * 100.0).max(0.0)
    };
    
    let analysis_time_ms = start_time.elapsed()
        .map_err(|e| format!("Failed to calculate analysis time: {}", e))?
        .as_millis() as u64;

    // Emit final progress update
    let _ = app_handle.emit("analysis-progress", ProgressPayload {
        current: total_file_count as u32, // All files processed
        total: total_file_count as u32,
        percentage: 100.0,
        message: Some("Analysis complete.".to_string()),
        operation_id: operation_id.clone(),
        stage: Some("completion".to_string()),
    });

    Ok(CodeBaseAnalysisResult {
        total_files,
        files_with_issues,
        total_suspicious_chars,
        health_score,
        file_details,
        analysis_time_ms,
    })
}

#[tauri::command]
pub async fn clean_codebase(folder_path: String, output_path: String, app_handle: tauri::AppHandle) -> Result<String, String> {
    let analyzer = CharacterAnalyzer::new()?;
    
    let source_path = Path::new(&folder_path);
    let dest_path = Path::new(&output_path);
    
    if !source_path.exists() {
        return Err("Source folder does not exist".to_string());
    }
    
    if !source_path.is_dir() {
        return Err("Source path is not a directory".to_string());
    }
    
    // Create destination directory
    fs::create_dir_all(dest_path)
        .map_err(|e| format!("Failed to create destination directory: {}", e))?;
    
    let mut cleaned_files = 0;
    let mut skipped_files = 0;
    let mut total_files = 0;
    
    // First pass: count total files for progress calculation
    count_files_recursive(source_path, &mut total_files)?;
    
    let operation_id = format!("clean_{}", SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis());

    // Emit initial progress
    let _ = app_handle.emit("cleaning-progress", ProgressPayload {
        current: 0,
        total: total_files as u32,
        percentage: 0.0,
        message: Some("Starting cleanup...".to_string()),
        operation_id: operation_id.clone(),
        stage: Some("initialization".to_string()),
    });
    
    // Get supported file extensions from AssetManager
    let code_extensions: Vec<&str> = analyzer.asset_manager.supported_extensions
        .iter()
        .map(|s| s.as_str())
        .collect();
    
    let mut processed_files = 0;
    
    fn count_files_recursive(dir: &Path, count: &mut usize) -> Result<(), String> {
        let entries = fs::read_dir(dir)
            .map_err(|e| format!("Failed to read directory: {}", e))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
            let path = entry.path();
            
            if path.is_dir() {
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if !matches!(dir_name, "node_modules" | "target" | ".git" | "build" | "dist" | "vendor" | "__pycache__") {
                        count_files_recursive(&path, count)?;
                    }
                }
            } else {
                *count += 1;
            }
        }
        Ok(())
    }
    
    fn copy_and_clean_dir(
        source_dir: &Path,
        dest_dir: &Path,
        analyzer: &CharacterAnalyzer,
        cleaned_files: &mut usize,
        skipped_files: &mut usize,
        code_extensions: &[&str],
        app_handle: &tauri::AppHandle, // Changed from window
        processed_files: &mut usize,
        total_files: usize,
        operation_id: &str, // Added operation_id
    ) -> Result<(), String> {
        let entries = fs::read_dir(source_dir)
            .map_err(|e| format!("Failed to read source directory: {}", e))?;
        
        for entry in entries {
            let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
            let source_path = entry.path();
            let file_name = source_path.file_name()
                .ok_or("Invalid file name")?;
            let dest_path = dest_dir.join(file_name);
            
            if source_path.is_dir() {
                // Skip certain directories
                if let Some(dir_name) = file_name.to_str() {
                    if matches!(dir_name, "node_modules" | "target" | ".git" | "build" | "dist" | "vendor" | "__pycache__") {
                        continue;
                    }
                }
                
                fs::create_dir_all(&dest_path)
                    .map_err(|e| format!("Failed to create directory: {}", e))?;
                
                copy_and_clean_dir(&source_path, &dest_path, analyzer, cleaned_files, skipped_files, code_extensions, app_handle, processed_files, total_files, operation_id)?;
            } else {
                *processed_files += 1;
                let progress_percent = (*processed_files * 100 / total_files).min(100);
                
                // Emit progress update
                let file_name = source_path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown");
                
                let _ = app_handle.emit("cleaning-progress", ProgressPayload {
                    current: *processed_files as u32,
                    total: total_files as u32,
                    percentage: progress_percent as f32,
                    message: Some(format!("Cleaning: {}", file_name)),
                    operation_id: operation_id.to_string(),
                    stage: Some("cleaning".to_string()),
                });
                
                // Check if file should be processed
                if let Some(extension) = source_path.extension().and_then(|e| e.to_str()) {
                    if code_extensions.contains(&extension) {
                        // Read, clean, and write file
                        match fs::read_to_string(&source_path) {
                            Ok(content) => {
                                let cleaned_content = analyzer.clean_text(&content);
                                fs::write(&dest_path, cleaned_content)
                                    .map_err(|e| format!("Failed to write cleaned file: {}", e))?;
                                *cleaned_files += 1;
                            }
                            Err(_) => {
                                // If we can't read as text, copy as binary
                                fs::copy(&source_path, &dest_path)
                                    .map_err(|e| format!("Failed to copy file: {}", e))?;
                                *skipped_files += 1;
                            }
                        }
                    } else {
                        // Copy non-code files as-is
                        fs::copy(&source_path, &dest_path)
                            .map_err(|e| format!("Failed to copy file: {}", e))?;
                        *skipped_files += 1;
                    }
                } else {
                    // Copy files without extensions as-is
                    fs::copy(&source_path, &dest_path)
                        .map_err(|e| format!("Failed to copy file: {}", e))?;
                    *skipped_files += 1;
                }
            }
        }
        Ok(())
    }
    
    copy_and_clean_dir(
        source_path, 
        dest_path, 
        &analyzer, 
        &mut cleaned_files, 
        &mut skipped_files, 
        &code_extensions,
        &app_handle, // Changed from window
        &mut processed_files,
        total_files,
        &operation_id // Added operation_id
    )?;
    
    // Final progress update
    let _ = app_handle.emit("cleaning-progress", ProgressPayload {
        current: total_files as u32,
        total: total_files as u32,
        percentage: 100.0,
        message: Some("Cleaning complete.".to_string()),
        operation_id: operation_id.clone(),
        stage: Some("completion".to_string()),
    });
    
    Ok(format!(
        "Successfully created cleaned codebase. Cleaned {} files, copied {} files unchanged.",
        cleaned_files, skipped_files
    ))
}

// Enhanced clean_codebase function with verification
#[tauri::command]
pub async fn clean_codebase_with_verification(
    folder_path: String, 
    output_path: String, 
    app_handle: tauri::AppHandle
) -> Result<PostCleaningVerification, String> {
    let start_time = SystemTime::now();
    
    // Phase 1: Pre-cleaning analysis
    let _ = app_handle.emit("verification-progress", serde_json::json!({
        "phase": "pre-analysis",
        "message": "Analyzing original codebase...",
        "progress_percent": 0
    }));
    
    let original_analysis = analyze_codebase(folder_path.clone(), app_handle.clone()).await?;
    
    // Phase 2: Cleaning operation (use existing function)
    let _ = app_handle.emit("verification-progress", serde_json::json!({
        "phase": "cleaning",
        "message": "Starting cleaning process...",
        "progress_percent": 25
    }));
    
    // Get window handle for the existing clean_codebase function
    let window = app_handle.get_webview_window("main")
        .ok_or("Could not get main window handle")?;
    
    let cleaning_summary = clean_codebase(folder_path.clone(), output_path.clone(), window.app_handle().clone()).await?;
    
    // Phase 3: Post-cleaning verification analysis
    let _ = app_handle.emit("verification-progress", serde_json::json!({
        "phase": "verification",
        "message": "Verifying cleaning effectiveness...",
        "progress_percent": 75
    }));
    
    let post_cleaning_analysis = analyze_codebase(output_path, app_handle.clone()).await?;
    
    // Phase 4: Generate verification report
    let _ = app_handle.emit("verification-progress", serde_json::json!({
        "phase": "reporting",
        "message": "Generating verification report...",
        "progress_percent": 95
    }));
    
    let verification_result = PostCleaningVerification::generate_verification_report(
        original_analysis,
        post_cleaning_analysis,
        cleaning_summary,
    );
    
    // Final progress update
    let _ = app_handle.emit("verification-progress", serde_json::json!({
        "phase": "complete",
        "message": format!(
            "✅ Verification complete! {:.1}% of issues cleaned successfully",
            verification_result.cleaning_effectiveness.success_rate_percentage
        ),
        "progress_percent": 100
    }));
    
    Ok(verification_result)
}

// Post-Cleaning Verification System Data Structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostCleaningVerification {
    pub original_analysis: CodeBaseAnalysisResult,
    pub post_cleaning_analysis: CodeBaseAnalysisResult,
    pub cleaning_effectiveness: CleaningEffectiveness,
    pub remaining_issues: Vec<RemainingIssue>,
    pub verification_time_ms: u64,
    pub cleaning_summary: String, // Original cleaning result message
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CleaningEffectiveness {
    pub total_original_issues: u32,
    pub total_cleaned_issues: u32,
    pub success_rate_percentage: f32,
    pub files_fully_cleaned: u32,
    pub files_partially_cleaned: u32,
    pub files_with_errors: u32,
    pub total_files_processed: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemainingIssue {
    pub file_path: String,
    pub line_number: u32,
    pub column_number: u32,
    pub character: char,
    pub character_name: String,
    pub unicode_point: String,
    pub reason_not_cleaned: String,
    pub code_snippet: String,
    pub suggested_action: String,
}

impl PostCleaningVerification {
    fn generate_verification_report(
        original_analysis: CodeBaseAnalysisResult,
        post_cleaning_analysis: CodeBaseAnalysisResult,
        cleaning_summary: String,
    ) -> Self {
        let start_time = SystemTime::now();
        
        let cleaning_effectiveness = Self::calculate_cleaning_effectiveness(
            &original_analysis,
            &post_cleaning_analysis,
        );
        
        let remaining_issues = Self::identify_remaining_issues(
            &original_analysis,
            &post_cleaning_analysis,
        );
        
        let verification_time_ms = start_time
            .elapsed()
            .unwrap_or(Duration::from_millis(0))
            .as_millis() as u64;
        
        PostCleaningVerification {
            original_analysis,
            post_cleaning_analysis,
            cleaning_effectiveness,
            remaining_issues,
            verification_time_ms,
            cleaning_summary,
        }
    }
    
    fn calculate_cleaning_effectiveness(
        original: &CodeBaseAnalysisResult,
        post_cleaning: &CodeBaseAnalysisResult,
    ) -> CleaningEffectiveness {
        let total_original_issues = original.total_suspicious_chars;
        let total_remaining_issues = post_cleaning.total_suspicious_chars;
        let total_cleaned_issues = total_original_issues.saturating_sub(total_remaining_issues);
        
        let success_rate_percentage = if total_original_issues > 0 {
            (total_cleaned_issues as f32 / total_original_issues as f32) * 100.0
        } else {
            100.0
        };
        
        let mut files_fully_cleaned = 0;
        let mut files_partially_cleaned = 0;
        let mut files_with_errors = 0;
        
        // Compare file-by-file results
        for original_file in &original.file_details {
            if let Some(post_file) = post_cleaning.file_details.iter()
                .find(|f| f.file_path == original_file.file_path) {
                
                if post_file.suspicious_characters == 0 && original_file.suspicious_characters > 0 {
                    files_fully_cleaned += 1;
                } else if post_file.suspicious_characters < original_file.suspicious_characters {
                    files_partially_cleaned += 1;
                } else if post_file.suspicious_characters > 0 {
                    files_with_errors += 1;
                }
            }
        }
        
        CleaningEffectiveness {
            total_original_issues: total_original_issues.try_into().unwrap_or(0),
            total_cleaned_issues: total_cleaned_issues.try_into().unwrap_or(0),
            success_rate_percentage,
            files_fully_cleaned,
            files_partially_cleaned,
            files_with_errors,
            total_files_processed: original.total_files.try_into().unwrap_or(0),
        }
    }
    
    fn identify_remaining_issues(
        original: &CodeBaseAnalysisResult,
        post_cleaning: &CodeBaseAnalysisResult,
    ) -> Vec<RemainingIssue> {
        let mut remaining_issues = Vec::new();
        
        // Compare file-level suspicious character counts
        for post_file in &post_cleaning.file_details {
            if post_file.suspicious_characters > 0 {
                // Find corresponding original file
                if let Some(original_file) = original.file_details.iter()
                    .find(|f| f.file_path == post_file.file_path) {
                    
                    // If there are still suspicious characters after cleaning
                    if post_file.suspicious_characters > 0 {
                        // Create a generic remaining issue entry since we don't have character-level details
                        remaining_issues.push(RemainingIssue {
                            file_path: post_file.file_path.clone(),
                            line_number: 0, // Generic - would need detailed analysis to get exact line
                            column_number: 0, // Generic - would need detailed analysis to get exact column
                            character: '\u{FFFD}', // Replacement character as placeholder
                            character_name: "Suspicious character(s) remaining".to_string(),
                            unicode_point: "Various".to_string(),
                            reason_not_cleaned: format!(
                                "{} suspicious characters remain after cleaning - may be in protected contexts",
                                post_file.suspicious_characters
                            ),
                            code_snippet: "Context would require detailed file analysis".to_string(),
                            suggested_action: "Manual review recommended for remaining issues".to_string(),
                        });
                    }
                }
            }
        }
        
        remaining_issues
    }
    
    fn determine_cleaning_failure_reason(character: &char) -> String {
        match *character {
            '\u{200B}' => "Zero-width space in string literal or comment".to_string(),
            '\u{FEFF}' => "BOM character at start of file".to_string(),
            '\u{2028}' | '\u{2029}' => "Line/paragraph separator in code structure".to_string(),
            c if c.is_control() => "Control character in critical code context".to_string(),
            _ => "Character may be functionally required in this context".to_string(),
        }
    }
    
    fn generate_suggested_action(character: &char) -> String {
        match *character {
            '\u{200B}' => "Manually review and remove if not needed".to_string(),
            '\u{FEFF}' => "Save file with UTF-8 encoding without BOM".to_string(),
            '\u{2028}' | '\u{2029}' => "Replace with standard newline characters".to_string(),
            c if c.is_control() => "Review context and replace with appropriate alternative".to_string(),
            _ => "Manual review recommended to determine if character is necessary".to_string(),
        }
    }
}

// Additional Tauri Commands

#[tauri::command]
pub async fn export_codebase_report(
    analysis_result: CodeBaseAnalysisResult,
    format: String,
    output_path: String,
) -> Result<String, String> {
    crate::report_generator::export_report(&analysis_result, &format, &output_path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn validate_folder_path(path: String) -> Result<bool, String> {
    use std::path::Path;
    let path = Path::new(&path);
    Ok(path.exists() && path.is_dir())
}

#[tauri::command]
pub async fn get_recent_folders() -> Result<Vec<String>, String> {
    // Return empty list for now - can be enhanced with persistent storage later
    Ok(vec![])
}

#[tauri::command]
pub async fn save_recent_folder(folder_path: String) -> Result<(), String> {
    // No-op for now - can be enhanced with persistent storage later
    Ok(())
}

#[tauri::command]
pub async fn get_quick_access_folders() -> Result<Vec<String>, String> {
    // Return common system folders
    Ok(vec![
        "C:\\Users".to_string(),
        "C:\\Program Files".to_string(),
        "C:\\".to_string(),
    ])
}
