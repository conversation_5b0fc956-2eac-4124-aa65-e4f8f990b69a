#!/bin/bash

# Enhanced Codebase Analyzer - Advanced Bash Interface
# Advanced command-line interface for the Laptos TauriV2 Bad Character Scanner
# 
# NEW ENHANCED FEATURES:
# - Multi-format report generation (JSON, Markdown, HTML, CSV, XML)
# - Advanced analysis with severity rankings and pattern detection
# - Complex statistical analysis and trend reporting
# - Bulk processing and batch analysis
# - Risk assessment and security scoring
# - Detailed vulnerability reporting
# - Custom filtering and search capabilities
# - Interactive dashboard mode
# - Performance profiling and optimization reports

# Enable strict error handling
set -euo pipefail

# Configuration and Environment Setup
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEMP_DIR="/tmp/laptos_analyzer_enhanced_$$"
LOG_FILE="$TEMP_DIR/analyzer_enhanced.log"
REPORTS_DIR="$TEMP_DIR/reports"

# Enhanced Color Definitions with more variety
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BOLD='\033[1m'
DIM='\033[2m'
NC='\033[0m' # No Color

# Enhanced Unicode Emoji Characters
CHECKMARK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
ROCKET="🚀"
FOLDER="📁"
FILE="📄"
EXPORT="📤"
CHART="📊"
SHIELD="🛡️"
MAGNIFY="🔍"
TARGET="🎯"
FIRE="🔥"
LIGHTNING="⚡"
STAR="⭐"
TROPHY="🏆"

# Enhanced Configuration Values
DEFAULT_OUTPUT_FORMAT="json"
DEFAULT_OUTPUT_DIR="./reports"
DEFAULT_SEVERITY_LEVEL="medium"
DEFAULT_REPORT_TEMPLATE="comprehensive"
VERBOSE=false
DRY_RUN=false
QUIET=false
INTERACTIVE=false
PROFILING=false

# Advanced Analysis Configuration
ENABLE_PATTERN_DETECTION=true
ENABLE_RISK_ASSESSMENT=true
ENABLE_TREND_ANALYSIS=true
ENABLE_PERFORMANCE_PROFILING=false
ENABLE_SECURITY_SCORING=true
BATCH_SIZE=100
MAX_FILE_SIZE="10MB"

# Report Templates
declare -A REPORT_TEMPLATES=(
    ["minimal"]="Basic analysis with essential findings"
    ["standard"]="Standard analysis with detailed findings"
    ["comprehensive"]="Full analysis with all features enabled"
    ["security"]="Security-focused analysis with risk assessment"
    ["performance"]="Performance-focused analysis with optimization tips"
    ["compliance"]="Compliance-focused analysis for standards"
    ["custom"]="User-defined custom analysis parameters"
)

# Export Formats
declare -A EXPORT_FORMATS=(
    ["json"]="JavaScript Object Notation - Machine readable"
    ["markdown"]="Markdown format - Human readable documentation"
    ["html"]="HTML with interactive charts and visualizations" 
    ["csv"]="Comma-separated values - Spreadsheet compatible"
    ["xml"]="Extensible Markup Language - Structured data"
    ["pdf"]="Portable Document Format - Print-ready reports"
    ["txt"]="Plain text - Simple human readable"
    ["yaml"]="YAML format - Configuration friendly"
)

# Severity Levels
declare -A SEVERITY_LEVELS=(
    ["low"]="Detect only critical security issues"
    ["medium"]="Detect medium and high severity issues"
    ["high"]="Detect all issues including potential problems"
    ["paranoid"]="Detect everything including false positives"
)

# Enhanced Usage Information
show_usage() {
    cat << EOF
${BLUE}${ROCKET} Enhanced Laptos TauriV2 Bad Character Scanner - Advanced Bash Interface${NC}

${CYAN}USAGE:${NC}
    $0 [OPTIONS] <COMMAND> [ARGS...]

${CYAN}COMMANDS:${NC}
    ${GREEN}analyze${NC} <directory>        - Perform advanced analysis on directory
    ${GREEN}batch${NC} <pattern>           - Batch analyze multiple directories
    ${GREEN}export${NC} <analysis_file>     - Export analysis to multiple formats
    ${GREEN}report${NC} <template>          - Generate comprehensive reports
    ${GREEN}scan${NC} <file>               - Deep scan individual file
    ${GREEN}compare${NC} <file1> <file2>    - Compare two analysis results
    ${GREEN}trend${NC} <directory>          - Analyze trends over time
    ${GREEN}dashboard${NC}                  - Interactive analysis dashboard
    ${GREEN}profile${NC} <directory>        - Performance profiling analysis
    ${GREEN}security${NC} <directory>       - Security-focused deep analysis
    ${GREEN}clean${NC} <directory>          - Clean and fix detected issues
    ${GREEN}validate${NC} <directory>       - Validate fixes and improvements
    ${GREEN}benchmark${NC}                  - Performance benchmarking
    ${GREEN}test${NC}                      - Run comprehensive test suite
    ${GREEN}demo${NC}                      - Interactive demonstration
    ${GREEN}health${NC}                    - System health and dependency check

${CYAN}ANALYSIS OPTIONS:${NC}
    ${YELLOW}-s, --severity LEVEL${NC}       Severity level: low, medium, high, paranoid
    ${YELLOW}-t, --template TEMPLATE${NC}    Report template: minimal, standard, comprehensive, security, performance
    ${YELLOW}-p, --pattern PATTERN${NC}      File pattern filter (e.g., "*.js,*.ts")
    ${YELLOW}-r, --recursive DEPTH${NC}      Recursion depth (default: unlimited)
    ${YELLOW}-e, --exclude PATTERN${NC}      Exclude files/directories matching pattern
    ${YELLOW}-m, --max-size SIZE${NC}        Maximum file size to analyze (e.g., 10MB)

${CYAN}EXPORT OPTIONS:${NC}
    ${YELLOW}-f, --format FORMAT${NC}        Export format: json, markdown, html, csv, xml, pdf, txt, yaml
    ${YELLOW}-o, --output DIR${NC}           Output directory (default: ./reports)
    ${YELLOW}--template-file FILE${NC}       Custom report template file
    ${YELLOW}--include-charts${NC}           Include charts in HTML/PDF reports
    ${YELLOW}--include-recommendations${NC}  Include fix recommendations
    ${YELLOW}--include-trends${NC}           Include trend analysis

${CYAN}PROCESSING OPTIONS:${NC}
    ${YELLOW}-j, --parallel JOBS${NC}        Number of parallel processing jobs
    ${YELLOW}-b, --batch-size SIZE${NC}      Batch processing size (default: 100)
    ${YELLOW}--cache-dir DIR${NC}            Cache directory for faster re-analysis
    ${YELLOW}--no-cache${NC}                 Disable caching
    ${YELLOW}--profile${NC}                  Enable performance profiling

${CYAN}OUTPUT OPTIONS:${NC}
    ${YELLOW}-v, --verbose${NC}              Enable verbose logging
    ${YELLOW}-q, --quiet${NC}                Suppress non-error output
    ${YELLOW}-d, --dry-run${NC}              Show what would be done without executing
    ${YELLOW}-i, --interactive${NC}          Interactive mode with prompts
    ${YELLOW}--no-color${NC}                 Disable colored output
    ${YELLOW}--json-log${NC}                 Output logs in JSON format

${CYAN}ADVANCED FEATURES:${NC}
    ${YELLOW}--enable-ai-detection${NC}      Enable AI content detection
    ${YELLOW}--enable-pattern-learning${NC}  Enable pattern learning and adaptation
    ${YELLOW}--enable-auto-fix${NC}          Enable automatic fixing of issues
    ${YELLOW}--risk-threshold SCORE${NC}     Risk score threshold (0-100)
    ${YELLOW}--confidence-level LEVEL${NC}   Confidence level for detection (0.0-1.0)

${CYAN}EXAMPLES:${NC}
    ${GREEN}# Comprehensive security analysis${NC}
    $0 --template security --severity high --format html security /path/to/codebase

    ${GREEN}# Batch analysis of multiple projects${NC}
    $0 --parallel 4 batch "/projects/*/src"

    ${GREEN}# Generate trend analysis report${NC}
    $0 --include-trends --format pdf trend /path/to/codebase

    ${GREEN}# Interactive dashboard mode${NC}
    $0 --interactive dashboard

    ${GREEN}# Performance profiling with charts${NC}
    $0 --profile --include-charts --format html profile /path/to/codebase

    ${GREEN}# Export existing analysis to multiple formats${NC}
    $0 export analysis.json --format html,pdf,csv

    ${GREEN}# Security-focused analysis with auto-fix${NC}
    $0 --template security --enable-auto-fix --risk-threshold 75 analyze /path/to/codebase

    ${GREEN}# Compare two analysis results${NC}
    $0 compare old_analysis.json new_analysis.json --format markdown

EOF
}

# Enhanced Logging Functions with timestamps and structured output
log_with_timestamp() {
    local level="$1"
    local message="$2"
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

log_info() {
    local force="${1:-false}"
    local message="${2:-$1}"
    if [[ "$force" == "--force" ]]; then
        force="true"
        message="$2"
    fi
    
    if [[ "$VERBOSE" == "true" ]] || [[ "$force" == "true" ]]; then
        if [[ "$QUIET" != "true" ]]; then
            echo -e "${INFO} ${BLUE}[INFO]${NC} $message" >&2
        fi
    fi
    log_with_timestamp "INFO" "$message"
}

log_success() {
    if [[ "$QUIET" != "true" ]]; then
        echo -e "${CHECKMARK} ${GREEN}[SUCCESS]${NC} $1" >&2
    fi
    log_with_timestamp "SUCCESS" "$1"
}

log_warning() {
    if [[ "$QUIET" != "true" ]]; then
        echo -e "${WARNING} ${YELLOW}[WARNING]${NC} $1" >&2
    fi
    log_with_timestamp "WARNING" "$1"
}

log_error() {
    echo -e "${CROSS} ${RED}[ERROR]${NC} $1" >&2
    log_with_timestamp "ERROR" "$1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        if [[ "$QUIET" != "true" ]]; then
            echo -e "${GEAR} ${PURPLE}[DEBUG]${NC} $1" >&2
        fi
    fi
    log_with_timestamp "DEBUG" "$1"
}

log_progress() {
    local current="$1"
    local total="$2"
    local message="$3"
    local percentage=$((current * 100 / total))
    
    if [[ "$QUIET" != "true" ]]; then
        printf "\r${LIGHTNING} ${CYAN}[PROGRESS]${NC} $message [$current/$total] $percentage%%"
        if [[ "$current" -eq "$total" ]]; then
            echo ""
        fi
    fi >&2
}

# Enhanced Setup Functions
setup_enhanced_environment() {
    mkdir -p "$TEMP_DIR" "$REPORTS_DIR"
    echo "# Enhanced Laptos Analyzer Log - $(date)" > "$LOG_FILE"
    log_debug "Enhanced temporary directory: $TEMP_DIR"
    log_debug "Reports directory: $REPORTS_DIR"
    
    # Create subdirectories for different types of reports
    mkdir -p "$REPORTS_DIR/analysis" "$REPORTS_DIR/exports" "$REPORTS_DIR/trends" "$REPORTS_DIR/security"
}

# Enhanced Cleanup Function
cleanup_enhanced() {
    local exit_code=$?
    
    if [[ "$PROFILING" == "true" ]]; then
        log_info --force "Saving profiling data..."
        save_profiling_data
    fi
    
    if [[ -d "$TEMP_DIR" ]]; then
        log_debug "Cleaning up enhanced temporary directory: $TEMP_DIR"
        if [[ "$VERBOSE" == "true" ]]; then
            log_info --force "Temporary files used: $(du -sh "$TEMP_DIR" 2>/dev/null | cut -f1)"
        fi
        rm -rf "$TEMP_DIR"
    fi
    
    exit $exit_code
}

# Enhanced Dependency Checking
check_enhanced_dependencies() {
    local missing_deps=()
    local optional_deps=()
    
    # Required dependencies
    local required_tools=("cargo" "jq" "awk" "sed" "sort" "uniq")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_deps+=("$tool")
        fi
    done
    
    # Optional dependencies for enhanced features
    local optional_tools=("pandoc" "wkhtmltopdf" "gnuplot" "parallel" "fzf")
    for tool in "${optional_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            optional_deps+=("$tool")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "Missing required dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo -e "  ${CROSS} $dep"
        done
        echo -e "\n${INFO} Please install missing dependencies and try again."
        exit 6
    fi
    
    if [[ ${#optional_deps[@]} -gt 0 ]] && [[ "$VERBOSE" == "true" ]]; then
        log_warning "Missing optional dependencies (some features may be limited):"
        for dep in "${optional_deps[@]}"; do
            echo -e "  ${WARNING} $dep"
        done
    fi
    
    log_debug "Dependency check completed"
}

# Create a simple Rust CLI binary for analysis (adapted from codebase_analyzer.sh)
create_analyzer_binary() {
    local cli_source_dir="$PROJECT_ROOT/src-tauri/src/bin"
    local cli_source="$cli_source_dir/analyzer_cli.rs"
    
    log_info --force "Ensuring CLI analyzer binary source at $cli_source is up-to-date (will overwrite)..."
    
    mkdir -p "$cli_source_dir" # Ensure the bin directory exists
    cat > "$cli_source" << EOF
use std::error::Error;
use std::path::Path;
use std::env; // Ensure env is imported for argument parsing
use tokio::runtime::Runtime;

// Assuming these are the correct paths after previous fixes
use laptos_tauri::main_module::analyze_codebase;
use laptos_tauri::modules::data_structures::CodeBaseAnalysisResult;

async fn perform_core_analysis(target_path_str: &str) -> Result<String, Box<dyn Error>> {
    eprintln!("[DEBUG] analyzer_cli: Calling analyze_codebase for path: {}", target_path_str);
    let analysis_result = analyze_codebase(target_path_str.to_string()).await?;
    let json_output = serde_json::to_string_pretty(&analysis_result)?;
    Ok(json_output)
}

async fn handle_analyze_command(path_arg: String, _format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'analyze' command received for path: {}", path_arg);
    let analysis_json = perform_core_analysis(&path_arg).await?;
    println!("{}", analysis_json);
    Ok(())
}

fn handle_export_command(path_arg: String, format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'export' command (stub) for path: {}, format: {}", path_arg, format_arg);
    if !Path::new(&path_arg).exists() {
        return Err(format!("File not found for export: {}", path_arg).into());
    }
    eprintln!("[WARN] Export functionality is a stub in analyzer_cli.rs.");
    Ok(())
}

fn main() -> Result<(), Box<dyn Error>> {
    let mut args = std::env::args().skip(1);

    let command = args.next().ok_or_else(|| {
        "No command provided. Usage: analyzer_cli <analyze|export> <path> [format]".to_string()
    })?;

    let path_arg = args.next().ok_or_else(|| {
        format!("Missing path argument for command '{}'", command)
    })?;

    let format_arg = args.next().unwrap_or_else(|| "json".to_string());

    let rt = Runtime::new()?;

    match command.as_str() {
        "analyze" => {
            rt.block_on(handle_analyze_command(path_arg, format_arg))?;
        }
        "export" => {
            handle_export_command(path_arg, format_arg)?;
        }
        _ => {
            eprintln!("Unknown command: '{}'. Supported commands: analyze, export.", command);
            return Err(format!("Unknown command: {}", command).into());
        }
    }
    Ok(())
}
EOF
    
    log_success "CLI analyzer source (over)written successfully at $cli_source"
}

# Build the Rust analyzer if needed (adapted from codebase_analyzer.sh)
build_analyzer() {
    local build_needed=false
    local binary_path="$PROJECT_ROOT/target/release/analyzer_cli" # Assuming release build
    
    if [[ ! -f "$PROJECT_ROOT/Cargo.toml" ]]; then
        log_error "Cargo.toml not found in $PROJECT_ROOT. Cannot build."
        exit 1
    fi

    if [[ ! -f "$binary_path" ]]; then
        build_needed=true
        log_info --force "Analyzer binary not found, building..."
    else
        # Check if any Rust source file in src-tauri/src is newer than the binary
        # Using find correctly for this check.
        local main_lib_file="$PROJECT_ROOT/src-tauri/src/lib.rs" # Check a key file first
        if [[ -f "$main_lib_file" ]] && [[ "$main_lib_file" -nt "$binary_path" ]]; then
             build_needed=true
        elif find "$PROJECT_ROOT/src-tauri/src" -name "*.rs" -newer "$binary_path" -print -quit | grep -q .; then
            build_needed=true
        fi
        
        if [[ "$build_needed" == "true" ]]; then
            log_info --force "Source files updated or binary missing, rebuilding Rust analyzer..."
        fi
    fi
    
    if [[ "$build_needed" == "true" ]]; then
        log_info --force "${GEAR} Building Rust analyzer (analyzer_cli)..."
        if [[ "$DRY_RUN" == "true" ]]; then
            echo "[DRY RUN] Would build: cargo build --release --package laptos-tauri --bin analyzer_cli"
            return 0
        fi
        
        # Ensure we are in the project root for cargo commands
        local current_dir_before_build=$(pwd)
        cd "$PROJECT_ROOT"
        if ! cargo build --release --package laptos-tauri --bin analyzer_cli >> "$LOG_FILE" 2>&1; then
            log_error "Failed to build analyzer_cli. Check log: $LOG_FILE"
            # Attempt to print relevant part of log
            tail -n 50 "$LOG_FILE" >&2
            cd "$current_dir_before_build" # Return to original directory
            exit 4
        fi
        cd "$current_dir_before_build" # Return to original directory
        log_success "Rust analyzer (analyzer_cli) built successfully"
    else
        log_debug "Using existing Rust analyzer (analyzer_cli) binary"
    fi
}


# Enhanced argument parsing with support for all new features
parse_enhanced_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--severity)
                DEFAULT_SEVERITY_LEVEL="$2"
                shift 2
                ;;
            -t|--template)
                DEFAULT_REPORT_TEMPLATE="$2"
                shift 2
                ;;
            -f|--format)
                EXPORT_FORMATS="$2"
                shift 2
                ;;
            -o|--output)
                DEFAULT_OUTPUT_DIR="$2"
                shift 2
                ;;
            -p|--pattern)
                FILE_PATTERN="$2"
                shift 2
                ;;
            -e|--exclude)
                EXCLUDE_PATTERN="$2"
                shift 2
                ;;
            -m|--max-size)
                MAX_FILE_SIZE="$2"
                shift 2
                ;;
            -r|--recursive)
                MAX_DEPTH="$2"
                shift 2
                ;;
            -b|--batch-size)
                BATCH_SIZE="$2"
                shift 2
                ;;
            -j|--parallel)
                PARALLEL_JOBS="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -i|--interactive)
                INTERACTIVE=true
                shift
                ;;
            --profile)
                PROFILING=true
                ENABLE_PERFORMANCE_PROFILING=true
                shift
                ;;
            --no-color)
                RED=''
                GREEN=''
                YELLOW=''
                BLUE=''
                PURPLE=''
                CYAN=''
                WHITE=''
                BOLD=''
                DIM=''
                NC=''
                CHECKMARK="OK"
                CROSS="ERR"
                WARNING="WARN"
                INFO="INFO"
                GEAR="[S]"
                ROCKET="[R]"
                FOLDER="[F]"
                FILE="[D]"
                EXPORT="[E]"
                CHART="[C]"
                SHIELD="[H]"
                MAGNIFY="[M]"
                TARGET="[T]"
                FIRE="[!]"
                LIGHTNING="[*]"
                STAR="(*)"
                TROPHY="(V)"
                shift
                ;;
            --enable-ai-detection)
                ENABLE_AI_DETECTION=true
                shift
                ;;
            --enable-pattern-learning)
                ENABLE_PATTERN_LEARNING=true
                shift
                ;;
            --enable-auto-fix)
                ENABLE_AUTO_FIX=true
                shift
                ;;
            --risk-threshold)
                RISK_THRESHOLD="$2"
                shift 2
                ;;
            --confidence-level)
                CONFIDENCE_LEVEL="$2"
                shift 2
                ;;
            --include-charts)
                INCLUDE_CHARTS=true
                shift
                ;;
            --include-recommendations)
                INCLUDE_RECOMMENDATIONS=true
                shift
                ;;
            --include-trends)
                INCLUDE_TRENDS=true
                ENABLE_TREND_ANALYSIS=true
                shift
                ;;
            --no-cache)
                DISABLE_CACHE=true
                shift
                ;;
            --cache-dir)
                CACHE_DIR="$2"
                shift 2
                ;;
            --json-log)
                JSON_LOG=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            --version)
                echo "Enhanced Laptos Bad Character Scanner v2.0.0"
                exit 0
                ;;
            -*)
                log_error "Unknown option: $1"
                show_usage
                exit 2
                ;;
            *)
                # First non-option argument is the command
                if [[ -z "${COMMAND:-}" ]]; then
                    COMMAND="$1"
                else
                    # Additional arguments
                    ARGS+=("$1")
                fi
                shift
                ;;
        esac
    done
}

# Enhanced Analysis Functions
analyze_directory_enhanced() {
    local target_dir="$1"
    local output_format="${2:-$DEFAULT_OUTPUT_FORMAT}"
    local report_template="${3:-$DEFAULT_REPORT_TEMPLATE}"
    local severity_level="${4:-$DEFAULT_SEVERITY_LEVEL}"

    log_info --force "${ROCKET} Starting enhanced analysis for directory: $target_dir"
    log_info "Output Format: $output_format, Template: $report_template, Severity: $severity_level"

    if [[ ! -d "$target_dir" ]]; then
        log_error "Target directory '$target_dir' does not exist."
        exit 2
    fi

    # Ensure the Rust CLI binary is ready
    create_analyzer_binary
    build_analyzer

    local analyzer_binary_path="$PROJECT_ROOT/target/release/analyzer_cli"
    if [[ ! -x "$analyzer_binary_path" ]]; then
        log_error "Analyzer binary not found or not executable at $analyzer_binary_path"
        exit 5
    fi

    # Prepare report file path
    local report_basename=\"analysis_$(basename "$target_dir")_$(date +%Y%m%d_%H%M%S)\"
    local report_file_json=\"$REPORTS_DIR/analysis/${report_basename}.json\"
    
    mkdir -p "$REPORTS_DIR/analysis" # Ensure analysis report subdir exists

    log_info "Running Rust analyzer: $analyzer_binary_path analyze \\"$target_dir\\""
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would execute: $analyzer_binary_path analyze \\"$target_dir\\" > \\"$report_file_json\\""
        # Create a dummy JSON for dry run to allow subsequent steps to proceed if needed
        echo '{ "dry_run": true, "message": "This is a dry run analysis." }' > "$report_file_json"
        log_success "Dry run: Analysis report would be saved to $report_file_json"
        return 0
    fi

    # Execute the analyzer and capture JSON output
    if ! "$analyzer_binary_path" analyze "$target_dir" > "$report_file_json"; then
        log_error "Rust analyzer failed. Check output above and log: $LOG_FILE"
        # Attempt to show last few lines from the binary's stderr if it wrote to log, or from its direct stderr
        # The analyzer_cli.rs currently prints errors to its stderr, which won't be in $LOG_FILE unless cargo build errors are there.
        # If the binary itself crashes, its stderr might be lost or mixed.
        # For now, we rely on the binary's eprintln messages.
        exit 3
    fi

    if [[ ! -s "$report_file_json" ]]; then
        log_error "Analysis completed but output file $report_file_json is empty or not created."
        # Check if the binary produced any output on stderr that might indicate why
        # This is tricky as stderr is not directly captured here.
        # The binary is designed to output JSON to stdout and logs to stderr.
        exit 3
    fi

    log_success "Analysis successful. Report saved to: $report_file_json"
    log_info "Report content preview (first 10 lines):"
    head -n 10 "$report_file_json" | sed 's/^/    /' >&2 # Indent for readability

    # Placeholder for further processing (e.g., converting to other formats)
    log_info "Further processing (export, advanced reporting) can be added here."

    # Example: If output format is not json, call an export function
    if [[ "$output_format" != "json" ]]; then
        log_info "Exporting to $output_format..."
        # export_analysis_enhanced "$report_file_json" "$output_format" # Assuming such a function exists
        log_warning "Export to formats other than JSON is not yet fully implemented in this command."
    fi
}

# Enhanced Batch Processing
batch_analyze_enhanced() {
    local pattern="$1"

    log_info --force "${ROCKET} Starting batch analysis for pattern: $pattern"

    # Find directories matching the pattern
    local dirs=()
    while IFS= read -r -d '' dir; do
        dirs+=("$dir")
    done < <(find $pattern -type d -print0 2>/dev/null)

    if [[ ${#dirs[@]} -gt 0 ]]; then
        log_info "Found ${#dirs[@]} directories matching pattern."
        local count=0
        for dir in "${dirs[@]}"; do
            ((count++))
            log_progress "$count" "${#dirs[@]}" "Analyzing directory: $dir"
            analyze_directory_enhanced "$dir" "$DEFAULT_OUTPUT_FORMAT" "$DEFAULT_REPORT_TEMPLATE" "$DEFAULT_SEVERITY_LEVEL"
        done
    else
        log_error "No directories found matching pattern: $pattern"
        exit 1
    fi
}

# Enhanced Export Function (Placeholder)
export_analysis_enhanced() {
    local analysis_file="$1"
    local export_format="$2" # Could be a comma-separated list
    local output_dir="${3:-$REPORTS_DIR/exports}"

    log_info --force "${EXPORT} Exporting analysis from $analysis_file to format(s): $export_format"
    mkdir -p "$output_dir"

    if [[ ! -f "$analysis_file" ]]; then
        log_error "Analysis file '$analysis_file' not found for export."
        return 1
    fi

    # This is where you'd call pandoc or other tools based on export_format
    # For now, it's a placeholder
    log_warning "Export functionality for '$export_format' is a placeholder."
    
    # Example for JSON (which is already the input format)
    if [[ "$export_format" == *"json"* ]]; then
        cp "$analysis_file" "$output_dir/$(basename "$analysis_file" .json)_exported.json"
        log_success "Copied JSON to $output_dir/$(basename "$analysis_file" .json)_exported.json"
    fi
    
    # Add more export logic here for other formats (markdown, html, etc.)
    # e.g., if [[ "$export_format" == *"markdown"* ]]; then ... using jq and text processing or pandoc
}

# Enhanced Report Generation (Placeholder)
generate_custom_report() {
    local template_name="$1"
    local analysis_data_file="$2" # Path to the JSON analysis data
    local output_dir="${3:-$REPORTS_DIR/custom_reports}"

    log_info --force "${CHART} Generating custom report using template: $template_name"
    mkdir -p "$output_dir"

    if [[ ! -f "$analysis_data_file" ]]; then
        log_error "Analysis data file '$analysis_data_file' not found."
        return 1
    fi
    
    if [[ -z "${REPORT_TEMPLATES[$template_name]}" ]]; then
        log_error "Unknown report template: $template_name"
        log_info "Available templates: ${!REPORT_TEMPLATES[*]}"
        return 1
    fi

    log_info "Template description: ${REPORT_TEMPLATES[$template_name]}"
    # Placeholder for actual report generation logic
    # This would involve parsing $analysis_data_file (JSON) and formatting it
    # according to the $template_name.
    # For complex reports, dedicated scripts or tools (like Python with Jinja2, or pandoc with templates) would be used.
    
    local report_output_file="$output_dir/report_${template_name}_$(basename "$analysis_data_file" .json).txt" # or .html, .md etc.
    
    echo "Report Title: Laptos Bad Character Analysis - ${template_name}" > "$report_output_file"
    echo "Date: $(date)" >> "$report_output_file"
    echo "Source Data: $analysis_data_file" >> "$report_output_file"
    echo "---" >> "$report_output_file"
    echo "Template Description: ${REPORT_TEMPLATES[$template_name]}" >> "$report_output_file"
    echo "---" >> "$report_output_file"
    
    # Example: Add a summary from the JSON
    if command -v jq &> /dev/null; then
        jq -r '.summary | to_entries | .[] | \"\\(.key): \\(.value)\"' "$analysis_data_file" >> "$report_output_file"
    else
        echo "[Warning] jq not found, cannot include detailed summary in this placeholder report." >> "$report_output_file"
    fi
    
    log_success "Placeholder report generated: $report_output_file"
    log_warning "Custom report generation for '$template_name' is a placeholder."
}


# ... other enhanced functions like scan_file_deep, compare_analyses, etc. ...

# Main command dispatcher for enhanced script
main_enhanced() {
    setup_enhanced_environment
    check_enhanced_dependencies

    # Default command if none provided
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 0
    fi

    # Parse global options first (like -v, -q, --dry-run)
    # This is a simplified parser; a more robust one would handle options before commands.
    # For now, assuming options are mostly for specific commands or handled globally.
    
    # Pre-parse global flags like verbose, quiet, dry-run
    # This loop will consume global options, then the command and its args remain.
    # This is a basic way; getopt or a more advanced parser would be better for complex cases.
    _ARGS=()
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                # If quiet, typically verbose is false, but we'll let explicit -v override if it came after
                # A more robust parser would handle this better.
                if [[ "$VERBOSE" == "true" ]]; then
                    log_warning "Both --quiet and --verbose specified. --verbose takes precedence for debug logs."
                fi
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            --no-color)
                RED=''
                GREEN=''
                YELLOW=''
                BLUE=''
                PURPLE=''
                CYAN=''
                WHITE=''
                BOLD=''
                DIM=''
                NC=''
                CHECKMARK="OK"
                CROSS="ERR"
                WARNING="WARN"
                INFO="INFO"
                GEAR="[S]"
                ROCKET="[R]"
                FOLDER="[F]"
                FILE="[D]"
                EXPORT="[E]"
                CHART="[C]"
                SHIELD="[H]"
                MAGNIFY="[M]"
                TARGET="[T]"
                FIRE="[!]"
                LIGHTNING="[*]"
                STAR="(*)"
                TROPHY="(V)"
                shift
                ;;
            # Add other global options here if any
            *)
                _ARGS+=("$(printf '%q' "$1")") # Quote the argument to handle spaces and special characters safely
                shift
                ;;
        esac
    done
    # Restore processed arguments
    # set -- "${_ARGS[@]}" 
    eval set -- "${_ARGS[@]}"
    # Now $1 should be the command

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info --force "${DIM}Dry run mode enabled. No actual changes will be made.${NC}"
    fi


    local command=\"${1:-help}\" # Default to 'help' if no command
    shift || true # Shift command off, or do nothing if no args left

    case "$command" in
        analyze)
            if [[ $# -lt 1 ]]; then log_error "Missing directory for 'analyze' command."; show_usage; exit 1; fi
            analyze_directory_enhanced "$1" "${2:-$DEFAULT_OUTPUT_FORMAT}" "${3:-$DEFAULT_REPORT_TEMPLATE}" "${4:-$DEFAULT_SEVERITY_LEVEL}"
            ;;
        batch)
            if [[ $# -lt 1 ]]; then log_error "Missing pattern for 'batch' command."; show_usage; exit 1; fi
            batch_analyze_enhanced "$1"
            ;;
        export)
            if [[ $# -lt 1 ]]; then log_error "Missing analysis file for 'export' command."; show_usage; exit 1; fi
            # Export can take multiple formats, e.g., --format html,pdf
            # The parse_enhanced_arguments needs to handle this or this command needs its own option parsing.
            # For now, assuming format is passed as a single string argument if not using global -f.
            local export_formats_arg="$DEFAULT_OUTPUT_FORMAT" # Default
            local output_dir_arg="$DEFAULT_OUTPUT_DIR"
            # Basic option parsing for export command specifically
            # This is a bit ad-hoc; proper solution is full getopt or arg parsing library/function
            TEMP_ARGS=()
            input_file_for_export="$1"; shift
            while [[ $# -gt 0 ]]; do
                case "$1" in
                    -f|--format) export_formats_arg="$2"; shift 2;;
                    -o|--output) output_dir_arg="$2"; shift 2;;
                    *) TEMP_ARGS+=("$1"); shift;; # Unknown, could be an error or positional
                esac
            done
            if [[ ${#TEMP_ARGS[@]} -gt 0 ]]; then
                log_warning "Ignoring unknown arguments for export: ${TEMP_ARGS[*]}"
            fi
            export_analysis_enhanced "$input_file_for_export" "$export_formats_arg" "$output_dir_arg"
            ;;
        report)
            if [[ $# -lt 2 ]]; then log_error "Usage: report <template_name> <analysis_data_file.json>"; show_usage; exit 1; fi
            generate_custom_report "$1" "$2" # template_name, analysis_data_file
            ;;
        health)
            log_info --force "Running system health check..."
            check_enhanced_dependencies # Already checks required, this will re-log optional if verbose
            log_info "Project Root: $PROJECT_ROOT"
            log_info "Script Directory: $SCRIPT_DIR"
            log_info "Temporary Directory Base: /tmp/laptos_analyzer_enhanced_*"
            log_info "Default Reports Directory: $DEFAULT_OUTPUT_DIR (within TEMP_DIR if not overridden)"
            log_info "Log File: $LOG_FILE (within TEMP_DIR)"
            # Check Rust toolchain
            if command -v cargo &> /dev/null && command -v rustc &> /dev/null; then
                log_success "Rust toolchain (cargo, rustc) found."
                log_info "Cargo version: $(cargo --version)"
                log_info "Rustc version: $(rustc --version)"
            else
                log_error "Rust toolchain not fully found. Ensure 'cargo' and 'rustc' are in PATH."
            fi
            log_success "Health check completed."
            ;;
        # ... other command cases ...
        help|--help|-h)
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Trap EXIT signal for cleanup
trap cleanup_enhanced EXIT

# Execute main function for enhanced script
# parse_enhanced_arguments \"$@\" # This was for global options, handled differently now
main_enhanced "$@"
