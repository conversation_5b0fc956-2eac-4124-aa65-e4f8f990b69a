use std::error::Error;
use std::path::{Path, PathBuf};
use std::fs;
use tokio::runtime::Runtime;

// Import the required structures and functions
use laptos_tauri::analysis::{CodebaseA<PERSON>yzer, ScanDepth};
use laptos_tauri::modules::cleaning_operations::clean_content;

// This function performs the core analysis using the modular analysis system directly
async fn perform_core_analysis(target_path_str: &str) -> Result<String, Box<dyn Error>> {
    eprintln!("[DEBUG] analyzer_cli: Performing analysis for path: {}", target_path_str);
    
    // Use the modular analysis system directly for CLI usage
    let analyzer = CodebaseAnalyzer::new()?;
    let analysis_result = analyzer.analyze_codebase(target_path_str, ScanDepth::Deep)?;
    
    let json_output = serde_json::to_string_pretty(&analysis_result)?;
    Ok(json_output)
}

// Wrapper for the 'analyze' command
async fn handle_analyze_command(path_arg: String, _format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'analyze' command received for path: {}", path_arg); // Use eprintln!
    // perform_core_analysis expects &str, path_arg is String
    let analysis_json = perform_core_analysis(&path_arg).await?;
    // The output is printed to stdout. The calling bash script will capture it.
    println!("{}", analysis_json); // This remains println! for the actual JSON output
    Ok(())
}

// Handle 'clean' command - removes bad characters from a file
async fn handle_clean_command(input_path: String, output_path: Option<String>) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'clean' command for path: {}", input_path);
    
    // Read the input file
    let content = fs::read_to_string(&input_path)?;
    
    // Clean the content
    let cleaned_content = clean_content(&content);
    
    // Determine output path
    let output = if let Some(out) = output_path {
        PathBuf::from(out)
    } else {
        let mut path = PathBuf::from(&input_path);
        let stem = path.file_stem().unwrap_or_default().to_string_lossy();
        let ext = path.extension().unwrap_or_default().to_string_lossy();
        path.set_file_name(format!("{}_cleaned.{}", stem, ext));
        path
    };
    
    // Write cleaned content
    fs::write(&output, cleaned_content)?;
    eprintln!("[INFO] Cleaned file written to: {}", output.display());
    
    Ok(())
}

// Handle 'scan' command - scans a directory for bad characters
async fn handle_scan_command(path: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'scan' command for path: {}", path);
    
    let analyzer = CodebaseAnalyzer::new()?;
    let analysis_result = analyzer.analyze_codebase(&path, ScanDepth::Deep)?;
    
    // Print summary instead of full JSON for scan
    println!("Files scanned: {}", analysis_result.analysis_metadata.total_files_scanned);
    println!("Issues found: {}", analysis_result.homoglyph_threats.len() + 
                                analysis_result.pattern_threats.len() + 
                                analysis_result.security_threats.len());
    println!("Risk score: {:.1}", analysis_result.risk_assessment.overall_risk_score);
    
    // List files with issues
    if !analysis_result.file_analyses.is_empty() {
        println!("\nFiles with issues:");
        for file in &analysis_result.file_analyses {
            if file.risk_score > 0.0 {
                println!("  - {} (risk: {:.1})", file.relative_path, file.risk_score);
            }
        }
    }
    
    Ok(())
}

// Placeholder for 'export' command handling
fn handle_export_command(path_arg: String, format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'export' command (stub) for path: {}, format: {}", path_arg, format_arg); // Use eprintln!
    if !Path::new(&path_arg).exists() {
        return Err(format!("File not found for export: {}", path_arg).into());
    }
    eprintln!("[WARN] Export functionality is a stub in analyzer_cli.rs."); // Use eprintln!
    // In a real scenario, this would read the analysis_json and convert to the specified format.
    Ok(())
}

fn main() -> Result<(), Box<dyn Error>> {
    let args: Vec<String> = std::env::args().collect();
    
    if args.len() < 2 {
        eprintln!("Usage: analyzer_cli <command> [options]");
        eprintln!("Commands:");
        eprintln!("  analyze <path>          - Analyze a file or directory for bad characters");
        eprintln!("  clean <input> [-o out]  - Clean bad characters from a file");
        eprintln!("  scan <path>             - Scan a directory and show summary");
        eprintln!("  export <path> [format]  - Export analysis results");
        return Err("No command provided".into());
    }

    let command = &args[1];
    
    // Tokio runtime for async operations
    let rt = Runtime::new()?;

    match command.as_str() {
        "analyze" => {
            if args.len() < 3 {
                return Err("Missing path argument for analyze command".into());
            }
            let path = args[2].clone();
            let format = args.get(3).cloned().unwrap_or_else(|| "json".to_string());
            rt.block_on(handle_analyze_command(path, format))?;
        }
        "clean" => {
            if args.len() < 3 {
                return Err("Missing input path for clean command".into());
            }
            let input_path = args[2].clone();
            let output_path = if args.len() > 4 && args[3] == "-o" {
                Some(args[4].clone())
            } else {
                None
            };
            rt.block_on(handle_clean_command(input_path, output_path))?;
        }
        "scan" => {
            if args.len() < 3 {
                return Err("Missing path argument for scan command".into());
            }
            let path = args[2].clone();
            rt.block_on(handle_scan_command(path))?;
        }
        "export" => {
            if args.len() < 3 {
                return Err("Missing path argument for export command".into());
            }
            let path = args[2].clone();
            let format = args.get(3).cloned().unwrap_or_else(|| "json".to_string());
            handle_export_command(path, format)?;
        }
        _ => {
            eprintln!("Unknown command: '{}'. Supported commands: analyze, clean, scan, export.", command);
            return Err(format!("Unknown command: {}", command).into());
        }
    }
    Ok(())
}
