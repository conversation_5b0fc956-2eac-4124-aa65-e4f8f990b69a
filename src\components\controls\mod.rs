use leptos::*;
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[component]
pub fn FileMenu() -> impl IntoView {
    let (is_open, set_is_open) = create_signal(false);

    let handle_new = move |_| {
        spawn_local(async {
            let _ = invoke("new_file", JsValue::NULL).await;
        });
        set_is_open.set(false);
    };

    let handle_open = move |_| {
        spawn_local(async {
            let _ = invoke("open_file", JsValue::NULL).await;
        });
        set_is_open.set(false);
    };

    let handle_save = move |_| {
        spawn_local(async {
            let _ = invoke("save_file", JsValue::NULL).await;
        });
        set_is_open.set(false);
    };

    let handle_exit = move |_| {
        spawn_local(async {
            let _ = invoke("exit_app", JsValue::NULL).await;
        });
    };

    view! {
        <div class="relative">
            <button
                class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                on:click=move |_| set_is_open.update(|v| *v = !*v)
            >
                "File"
            </button>

            <Show when=move || is_open.get()>
                <div class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    <button
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        on:click=handle_new
                    >
                        "New"
                    </button>
                    <button
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        on:click=handle_open
                    >
                        "Open..."
                    </button>
                    <button
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        on:click=handle_save
                    >
                        "Save"
                    </button>
                    <hr class="my-1 border-gray-200" />
                    <button
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        on:click=handle_exit
                    >
                        "Exit"
                    </button>
                </div>
            </Show>
        </div>
    }
}