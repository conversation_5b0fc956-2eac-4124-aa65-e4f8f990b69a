use leptos::*;

#[component]
pub fn QuickTestSamples(
    set_text_input: WriteSignal<String>,
) -> impl IntoView {
    let samples = vec![
        ("Regular Text", "This is regular text without any issues."),
        ("Zero-Width Characters", "This‌text‍has‌zero-width‍characters"),
        ("Mixed Scripts", "Hello мир 世界 مرحبا"),
        ("Control Characters", "Line1\x00\x01\x02Line2"),
        ("Bidirectional Override", "User \u{202E}revilO\u{202C} sent a message"),
        ("Emoji Test", "Hello 👋 World 🌍"),
        ("Combining Characters", "e̸̗͉x̸̱̋ą̶̏m̷̱̈p̶̰̕l̵̡̏ë̷́"),
    ];
    
    view! {
        <div class="quick-test-section mt-lg">
            <p class="text-muted mb-sm">
                <span class="card-icon">"🧪"</span>
                " Quick Test Samples:"
            </p>
            <div class="quick-test-container">
                {samples.into_iter().map(|(name, text)| {
                    let text = text.to_string();
                    view! {
                        <button 
                            class="quick-test-btn"
                            on:click=move |_| set_text_input.set(text.clone())
                        >
                            {name}
                        </button>
                    }
                }).collect_view()}
            </div>
        </div>
    }
}