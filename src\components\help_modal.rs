use leptos::*;

#[derive(<PERSON><PERSON>, Debug)]
pub enum HelpTopic {
    FileTypes,
    #[allow(dead_code)]
    BadCharacters,
    #[allow(dead_code)]
    Limitations,
    #[allow(dead_code)]
    TechnicalReference,
}

impl HelpTopic {
    pub fn title(&self) -> &'static str {
        match self {
            HelpTopic::FileTypes => "Supported File Types",
            HelpTopic::BadCharacters => "Bad Character Detection",
            HelpTopic::Limitations => "Application Limitations",
            HelpTopic::TechnicalReference => "Technical Reference",
        }
    }
    
    pub fn content(&self) -> &'static str {
        match self {
            HelpTopic::FileTypes => r#"
# Supported File Types

The Bad Character Scanner supports analysis of **2000+ file types** across all major categories.

## Quick Reference - Common Types
.js, .ts, .rs, .py, .java, .cpp, .c, .cs, .html, .xml, .json, .md, .txt, .yaml, .toml

## Major Categories

### Programming Languages
- **C Family**: .c, .h, .cpp, .hpp, .cc, .cxx, .ino
- **Java/JVM**: .java, .kt, .scala, .clj, .cljs
- **Python**: .py, .pyw, .pyi
- **JavaScript/TypeScript**: .js, .ts, .jsx, .tsx, .d.ts
- **Other Languages**: .cs, .php, .swift, .go, .rs, .rb, .hs, .dart, .elm

### Configuration & Data
.json, .yaml, .yml, .xml, .toml, .ini, .cfg, .conf, .env, .properties

### Web Development
.html, .htm, .css, .vue, .svelte, .sass, .scss, .less

### Documents
.md, .txt, .rtf, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx

### Database Files
.sql, .sqlite, .db, .mdb, .accdb, .dbf

### And Many More...
- Archive formats (.zip, .rar, .7z, .tar, .gz)
- Image formats (.jpg, .png, .gif, .svg, .webp)
- Audio/Video (.mp3, .mp4, .avi, .mkv)
- CAD files (.dwg, .dxf, .stl, .obj)
- Scientific data (.fits, .cdf, .nc, .h5)
- Security files (.pem, .crt, .key, .pfx)

## Complete Reference
For the complete list of all 2000+ supported file types, see:
- `assets/FileTypesSummary.json` in the project directory
- Technical documentation in `docs/technical_reference/`

The scanner automatically detects file types and applies appropriate analysis methods for each category.
"#,
            HelpTopic::BadCharacters => r#"
# Bad Character Detection

The Bad Character Scanner detects various types of problematic Unicode characters that can cause issues in code, filenames, and text processing.

## Character Categories

### High Priority Issues
- **Zero-width characters**: Invisible characters that can break parsing
- **Bidirectional overrides**: Characters that can alter text direction
- **Non-standard spaces**: Space-like characters that aren't regular spaces
- **Control characters**: Legacy control codes that can cause system issues

### Medium Priority Issues
- **Special shell characters**: Characters with special meaning in command shells
- **URL-problematic characters**: Characters that can break web links
- **Filename-unsafe characters**: Characters forbidden in filenames

### Low Priority Issues
- **Mathematical operators**: Invisible mathematical notation characters
- **Variation selectors**: Characters that modify glyph appearance

For detailed information about specific characters and their Unicode codes, see the technical documentation.
"#,
            HelpTopic::Limitations => r#"
# Application Limitations

## File Size Limits
- Maximum file size: 100MB per file
- Maximum total scan size: 1GB per operation

## File Type Support
- Text-based files are fully supported
- Binary files are scanned for embedded text
- Some proprietary formats may have limited support

## Performance Considerations
- Large codebases may take several minutes to scan
- Memory usage scales with file count and size
- Progress reporting updates every 100 files processed

## Known Issues
- Very large files (>50MB) may cause memory pressure
- Network drives may have slower scan performance
- Some Unicode normalization edge cases may not be detected

For the most up-to-date limitations and known issues, check the project documentation.
"#,
            HelpTopic::TechnicalReference => r#"
# Technical Reference

## Architecture
The Bad Character Scanner is built with:
- **Frontend**: Leptos (Rust WebAssembly)
- **Backend**: Tauri v2 (Rust native)
- **UI Framework**: Custom CSS with Tailwind-inspired classes

## File Processing Pipeline
1. **File Discovery**: Recursive directory traversal with type filtering
2. **Content Analysis**: Multi-threaded character-by-character scanning
3. **Pattern Matching**: Regex-based detection of problematic patterns
4. **Report Generation**: Structured JSON output with detailed findings

## Configuration Files
- `assets/Bad_Characters.json`: Character detection patterns
- `assets/Advanced_AI_Patterns.json`: AI-generated text detection
- `assets/FileTypesSummary.json`: Supported file type definitions

## API Reference
The application exposes Tauri commands for:
- `analyze_text`: Single text analysis
- `analyze_codebase`: Full directory analysis
- `clean_text`: Character cleaning and normalization

For detailed API documentation, see the source code comments.
"#,
        }
    }
}

#[component]
pub fn HelpModal(
    show: ReadSignal<bool>,
    #[prop(into)] on_close: Callback<()>,
    #[prop(default = HelpTopic::FileTypes)] topic: HelpTopic,
) -> impl IntoView {
    view! {
        <Show when=move || show.get()>
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl max-h-[80vh] w-full mx-4">
                    // Header
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-800">{topic.title()}</h2>
                        <button
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                            on:click=move |_| on_close.call(())
                        >
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    // Content
                    <div class="p-6 overflow-y-auto max-h-[60vh]">
                        <div class="prose prose-sm max-w-none">
                            <pre class="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed">
                                {topic.content()}
                            </pre>
                        </div>
                    </div>
                    
                    // Footer
                    <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
                        <div class="text-sm text-gray-500">
                            "For more information, see the "
                            <code class="bg-gray-200 px-1 rounded">"docs/technical_reference/"</code>
                            " directory."
                        </div>
                        <button
                            class="btn-secondary"
                            on:click=move |_| on_close.call(())
                        >
                            "Close"
                        </button>
                    </div>
                </div>
            </div>
        </Show>
    }
}

#[component]
pub fn HelpButton(
    #[prop(default = HelpTopic::FileTypes)] topic: HelpTopic,
    #[prop(default = "Help")] text: &'static str,
    #[prop(default = "text-blue-600 hover:text-blue-800 underline text-xs")] class: &'static str,
) -> impl IntoView {
    let (show_modal, set_show_modal) = create_signal(false);
    
    view! {
        <>
            <button
                class=class
                on:click=move |_| set_show_modal.set(true)
            >
                {text}
            </button>
            
            <HelpModal
                show=show_modal
                on_close=Callback::new(move |_| set_show_modal.set(false))
                topic=topic
            />
        </>
    }
}
