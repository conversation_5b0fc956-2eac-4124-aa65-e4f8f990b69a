# UI-2: Implement Drag & Drop Functionality

## Status
🔴 **Open** - High Priority

## Type
Enhancement - User Interface

## Description
The drag & drop functionality for folder/file input is currently not working. Users see the drag area with instructions but cannot actually drag and drop files or folders into the application.

## Current Behavior
- Drag & drop area is displayed with text: "Drag a folder here or use the browse button above"
- Shows note: "Due to browser security, folder drag & drop has limitations"
- Area is non-functional - no drag events are being handled
- Browse button functionality needs verification

## Expected Behavior
- Users should be able to drag files/folders onto the designated area
- Visual feedback should be provided during drag operations (hover states, drop zones)
- Files should be processed and analyzed when dropped
- Proper error handling for unsupported file types
- Fallback to file picker when drag & drop fails

## Technical Requirements

### Frontend (Leptos)
- Implement HTML5 drag and drop API integration
- Add event handlers for:
  - `dragenter` - Visual feedback when drag enters area
  - `dragover` - Prevent default and allow drop
  - `dragleave` - Remove visual feedback when drag leaves
  - `drop` - Handle file processing
- Add visual states (hover, active, error)
- Implement file validation before processing

### Backend (Tauri)
- Ensure `handle_file_drop` command is properly exposed
- Verify file path handling and validation
- Add support for both single files and folders
- Implement recursive folder scanning

### Browser Limitations
- Document limitations with folder drag & drop
- Implement fallback file picker for unsupported browsers
- Add user-friendly error messages

## Supported File Types (Current)
Currently displays: `.js, .ts, .rs, .py, .java, .cpp, .c, .h, .css, .html, .xml, .json, .md`

**Note**: This list should be updated as part of UI-3 ticket.

## Implementation Steps
1. [ ] Research HTML5 drag & drop best practices for Leptos
2. [ ] Implement drag event handlers in frontend
3. [ ] Add visual feedback states (CSS/styling)
4. [ ] Connect to existing Tauri `handle_file_drop` command
5. [ ] Test with various file types and folder structures
6. [ ] Add error handling and user feedback
7. [ ] Document browser compatibility and limitations
8. [ ] Implement fallback file picker

## Acceptance Criteria
- [ ] Users can drag files onto the designated area
- [ ] Visual feedback is provided during drag operations
- [ ] Files are successfully processed when dropped
- [ ] Error messages are shown for unsupported file types
- [ ] Browse button works as alternative input method
- [ ] Functionality works in major browsers (Chrome, Firefox, Edge, Safari)
- [ ] Proper handling of both individual files and folder structures

## Related Tickets
- UI-3: Update file type support display
- CORE-2: Enhance file processing capabilities

## Priority
High - Core functionality that affects user experience

## Estimated Effort
Medium (2-3 days)

## Notes
- Consider progressive enhancement approach
- Ensure accessibility compliance
- Test thoroughly across different operating systems
- Document any browser-specific limitations
