use crate::{CodeBaseAnalysisResult, FileAnalysisDetail};
use chrono::Local;
use serde_json;
use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// Format CodeBaseAnalysisResult as pretty-printed JSON with enhanced metadata
pub fn format_as_json(data: &CodeBaseAnalysisResult) -> Result<String, anyhow::Error> {
    // Create enhanced JSON structure with timestamp
    let mut enhanced_data = serde_json::to_value(data)?;
    
    // Add current timestamp for export
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S UTC%z").to_string();
    enhanced_data["export_timestamp"] = serde_json::Value::String(timestamp);
    enhanced_data["export_version"] = serde_json::Value::String("1.0".to_string());
    enhanced_data["tool_version"] = serde_json::Value::String("Bad Character Scanner v0.2.0".to_string());
    
    // Pretty-print with 2-space indentation
    let formatted = serde_json::to_string_pretty(&enhanced_data)?;
    Ok(formatted)
}

/// Format CodeBaseAnalysisResult as structured Markdown with comprehensive analysis
pub fn format_as_markdown(data: &CodeBaseAnalysisResult) -> Result<String, anyhow::Error> {
    let mut markdown = String::new();
    
    // Header and metadata
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
    markdown.push_str("# 📊 Codebase Analysis Report\n\n");
    markdown.push_str(&format!("**Generated:** {}\n", timestamp));
    markdown.push_str(&format!("**Analysis Duration:** {} ms\n\n", data.analysis_time_ms));
    
    // Executive Summary
    markdown.push_str("## 📋 Executive Summary\n\n");
    markdown.push_str("| Metric | Value |\n");
    markdown.push_str("|--------|-------|\n");
    markdown.push_str(&format!("| **Total Files Scanned** | {} |\n", data.total_files));
    markdown.push_str(&format!("| **Files with Issues** | {} |\n", data.files_with_issues));
    markdown.push_str(&format!("| **Total Suspicious Characters** | {} |\n", data.total_suspicious_chars));
    markdown.push_str(&format!("| **Health Score** | {:.1}% |\n", data.health_score));
    
    // Health score interpretation
    let health_interpretation = match data.health_score {
        score if score >= 90.0 => "🟢 Excellent - Very few issues detected",
        score if score >= 75.0 => "🟡 Good - Some issues require attention", 
        score if score >= 50.0 => "🟠 Fair - Multiple issues detected",
        _ => "🔴 Poor - Significant issues require immediate attention"
    };
    markdown.push_str(&format!("| **Health Assessment** | {} |\n\n", health_interpretation));
    
    // Issue Distribution
    if !data.file_details.is_empty() {
        markdown.push_str("## 📈 Issue Distribution\n\n");
        
        // Calculate file type breakdown
        let mut file_type_counts: HashMap<String, usize> = HashMap::new();
        let mut file_type_issues: HashMap<String, usize> = HashMap::new();
        
        for file in &data.file_details {
            let file_type = if file.file_type.is_empty() { "unknown" } else { &file.file_type };
            *file_type_counts.entry(file_type.to_string()).or_insert(0) += 1;
            *file_type_issues.entry(file_type.to_string()).or_insert(0) += file.suspicious_characters;
        }
        
        markdown.push_str("### File Types Analysis\n\n");
        markdown.push_str("| File Type | Files Scanned | Issues Found |\n");
        markdown.push_str("|-----------|---------------|---------------|\n");
        
        for (file_type, count) in file_type_counts.iter() {
            let issues = file_type_issues.get(file_type).unwrap_or(&0);
            markdown.push_str(&format!("| `{}` | {} | {} |\n", file_type, count, issues));
        }
        markdown.push_str("\n");
        
        // Most common issues
        let mut issue_counts: HashMap<String, usize> = HashMap::new();
        for file in &data.file_details {
            for issue in &file.issues {
                *issue_counts.entry(issue.clone()).or_insert(0) += 1;
            }
        }
        
        if !issue_counts.is_empty() {
            markdown.push_str("### Most Common Issues\n\n");
            let mut sorted_issues: Vec<_> = issue_counts.iter().collect();
            sorted_issues.sort_by(|a, b| b.1.cmp(a.1));
            
            markdown.push_str("| Issue Type | Occurrences |\n");
            markdown.push_str("|------------|-------------|\n");
            
            for (issue, count) in sorted_issues.iter().take(10) {
                markdown.push_str(&format!("| {} | {} |\n", issue, count));
            }
            markdown.push_str("\n");
        }
    }
    
    // Detailed File Analysis
    if data.files_with_issues > 0 {
        markdown.push_str("## 🔍 Files Requiring Attention\n\n");
        
        let files_with_issues: Vec<_> = data.file_details.iter()
            .filter(|file| file.suspicious_characters > 0)
            .collect();
            
        for (index, file) in files_with_issues.iter().enumerate() {
            markdown.push_str(&format!("### {}. {}\n\n", index + 1, file.relative_path));
            
            markdown.push_str("| Property | Value |\n");
            markdown.push_str("|----------|-------|\n");
            markdown.push_str(&format!("| **File Path** | `{}` |\n", file.file_path));
            markdown.push_str(&format!("| **File Size** | {} bytes |\n", file.file_size));
            markdown.push_str(&format!("| **Total Characters** | {} |\n", file.total_characters));
            markdown.push_str(&format!("| **Suspicious Characters** | {} |\n", file.suspicious_characters));
            markdown.push_str(&format!("| **File Type** | `{}` |\n", file.file_type));
            markdown.push_str(&format!("| **Encoding** | {} |\n", file.encoding));
            markdown.push_str(&format!("| **Status** | {} |\n", file.analysis_status));
            
            if let Some(error) = &file.error_message {
                markdown.push_str(&format!("| **Error** | {} |\n", error));
            }
            
            if !file.issues.is_empty() {
                markdown.push_str("\n**Issues Detected:**\n");
                for issue in &file.issues {
                    markdown.push_str(&format!("- {}\n", issue));
                }
            }
            
            markdown.push_str("\n");
        }
    } else {
        markdown.push_str("## ✅ No Issues Detected\n\n");
        markdown.push_str("Congratulations! No suspicious characters or issues were found in your codebase.\n\n");
    }
    
    // Clean Files Summary
    let clean_files: Vec<_> = data.file_details.iter()
        .filter(|file| file.suspicious_characters == 0 && file.analysis_status == "success")
        .collect();
        
    if !clean_files.is_empty() {
        markdown.push_str(&format!("## ✅ Clean Files ({} files)\n\n", clean_files.len()));
        markdown.push_str("The following files were analyzed and found to be clean:\n\n");
        
        for file in clean_files.iter().take(50) { // Limit to first 50 to avoid huge reports
            markdown.push_str(&format!("- `{}` ({} bytes)\n", file.relative_path, file.file_size));
        }
        
        if clean_files.len() > 50 {
            markdown.push_str(&format!("\n... and {} more clean files.\n", clean_files.len() - 50));
        }
        markdown.push_str("\n");
    }
    
    // Analysis Summary Footer
    markdown.push_str("---\n\n");
    markdown.push_str("## 📝 Analysis Details\n\n");
    markdown.push_str(&format!("- **Total Scan Time:** {} ms\n", data.analysis_time_ms));
    markdown.push_str(&format!("- **Files Processed:** {}\n", data.total_files));
    markdown.push_str(&format!("- **Success Rate:** {:.1}%\n", 
        if data.total_files > 0 { 
            (data.file_details.len() as f64 / data.total_files as f64) * 100.0 
        } else { 
            100.0 
        }
    ));
    
    // Tool signature
    markdown.push_str(&format!("\n*Report generated by Bad Character Scanner v0.2.0 on {}*\n", timestamp));
    
    Ok(markdown)
}
/// Format CodeBaseAnalysisResult as plain text for readability
pub fn format_as_text(data: &CodeBaseAnalysisResult) -> Result<String, anyhow::Error> {
    let mut text = String::new();
    
    let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
    text.push_str("CODEBASE ANALYSIS REPORT\n");
    text.push_str("========================\n\n");
    text.push_str(&format!("Generated: {}\n", timestamp));
    text.push_str(&format!("Analysis Duration: {} ms\n\n", data.analysis_time_ms));
    
    // Summary
    text.push_str("EXECUTIVE SUMMARY\n");
    text.push_str("-----------------\n");
    text.push_str(&format!("Total Files Scanned:      {}\n", data.total_files));
    text.push_str(&format!("Files with Issues:        {}\n", data.files_with_issues));
    text.push_str(&format!("Total Suspicious Chars:   {}\n", data.total_suspicious_chars));
    text.push_str(&format!("Health Score:             {:.1}%\n\n", data.health_score));
    
    // Files with issues
    if data.files_with_issues > 0 {
        text.push_str("FILES REQUIRING ATTENTION\n");
        text.push_str("-------------------------\n");
        
        let files_with_issues: Vec<_> = data.file_details.iter()
            .filter(|file| file.suspicious_characters > 0)
            .collect();
            
        for (index, file) in files_with_issues.iter().enumerate() {
            text.push_str(&format!("{}. {}\n", index + 1, file.relative_path));
            text.push_str(&format!("   Path: {}\n", file.file_path));
            text.push_str(&format!("   Size: {} bytes\n", file.file_size));
            text.push_str(&format!("   Issues: {} suspicious characters\n", file.suspicious_characters));
            text.push_str(&format!("   Type: {}\n", file.file_type));
            text.push_str(&format!("   Encoding: {}\n", file.encoding));
            
            if !file.issues.is_empty() {
                text.push_str("   Issues Detected:\n");
                for issue in &file.issues {
                    text.push_str(&format!("     - {}\n", issue));
                }
            }
            text.push_str("\n");
        }
    } else {
        text.push_str("NO ISSUES DETECTED\n");
        text.push_str("------------------\n");
        text.push_str("Congratulations! No suspicious characters were found.\n\n");
    }
    
    text.push_str(&format!("Report generated by Bad Character Scanner v0.2.0 on {}\n", timestamp));
    
    Ok(text)
}

/// Export codebase analysis report to file in specified format
pub fn export_report(
    analysis_result: &CodeBaseAnalysisResult,
    format: &str,
    output_path: &str,
) -> Result<String, String> {
    // Generate content based on format
    let content = match format.to_lowercase().as_str() {
        "json" => format_as_json(analysis_result)
            .map_err(|e| format!("JSON formatting error: {}", e))?,
        "markdown" | "md" => format_as_markdown(analysis_result)
            .map_err(|e| format!("Markdown formatting error: {}", e))?,
        "text" | "txt" => format_as_text(analysis_result)
            .map_err(|e| format!("Text formatting error: {}", e))?,
        _ => return Err(format!("Unsupported format: {}. Supported formats: json, markdown, text", format)),
    };

    // Ensure output directory exists
    if let Some(parent) = Path::new(output_path).parent() {
        fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create output directory: {}", e))?;
    }

    // Write content to file
    fs::write(output_path, content)
        .map_err(|e| format!("Failed to write file: {}", e))?;

    Ok(format!("Report exported successfully to: {}", output_path))
}

#[cfg(test)]
mod tests {
    use super::*;
    
    fn create_test_data() -> CodeBaseAnalysisResult {
        CodeBaseAnalysisResult {
            total_files: 5,
            files_with_issues: 2,
            total_suspicious_chars: 10,
            health_score: 75.5,
            analysis_time_ms: 1500,
            file_details: vec![
                FileAnalysisDetail {
                    file_path: "/path/to/test.js".to_string(),
                    relative_path: "test.js".to_string(),
                    file_size: 1024,
                    total_characters: 500,
                    suspicious_characters: 5,
                    issues: vec!["Zero-width space".to_string(), "Non-breaking space".to_string()],
                    file_type: "js".to_string(),
                    encoding: "UTF-8".to_string(),
                    analysis_status: "success".to_string(),
                    error_message: None,
                },
            ],
        }
    }
    
    #[test]
    fn test_json_formatting() {
        let data = create_test_data();
        let result = format_as_json(&data);
        assert!(result.is_ok());
        
        let json_str = result.unwrap();
        assert!(json_str.contains("total_files"));
        assert!(json_str.contains("export_timestamp"));
    }
    
    #[test]
    fn test_markdown_formatting() {
        let data = create_test_data();
        let result = format_as_markdown(&data);
        assert!(result.is_ok());
        
        let markdown = result.unwrap();
        assert!(markdown.contains("# 📊 Codebase Analysis Report"));
        assert!(markdown.contains("## 📋 Executive Summary"));
        assert!(markdown.contains("75.5%"));
    }
    
    #[test]
    fn test_text_formatting() {
        let data = create_test_data();
        let result = format_as_text(&data);
        assert!(result.is_ok());
        
        let text = result.unwrap();
        assert!(text.contains("CODEBASE ANALYSIS REPORT"));
        assert!(text.contains("EXECUTIVE SUMMARY"));
    }
}
