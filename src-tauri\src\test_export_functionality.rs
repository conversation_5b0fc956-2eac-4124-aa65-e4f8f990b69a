use serde_json;
use crate::report_generator::{format_as_json, format_as_markdown, format_as_text};
use crate::{CodeBaseAnalysisResult, FileAnalysisDetail};

fn create_test_analysis_result() -> CodeBaseAnalysisResult {
    CodeBaseAnalysisResult {
        total_files: 3,
        files_with_issues: 2,
        total_suspicious_chars: 3,
        health_score: 66.7,
        analysis_time_ms: 1250,
        file_details: vec![
            FileAnalysisDetail {
                file_path: "/path/to/src/test.js".to_string(),
                relative_path: "src/test.js".to_string(),
                file_size: 1024,
                total_characters: 500,
                suspicious_characters: 2,
                issues: vec![
                    "Zero-width space detected".to_string(),
                    "Invisible character found".to_string(),
                ],
                file_type: "js".to_string(),
                encoding: "UTF-8".to_string(),
                analysis_status: "success".to_string(),
                error_message: None,
            },
            FileAnalysisDetail {
                file_path: "/path/to/src/clean.py".to_string(),
                relative_path: "src/clean.py".to_string(),
                file_size: 2048,
                total_characters: 750,
                suspicious_characters: 0,
                issues: vec![],
                file_type: "py".to_string(),
                encoding: "UTF-8".to_string(),
                analysis_status: "success".to_string(),
                error_message: None,
            },
            FileAnalysisDetail {
                file_path: "/path/to/README.md".to_string(),
                relative_path: "README.md".to_string(),
                file_size: 1024,
                total_characters: 300,
                suspicious_characters: 1,
                issues: vec![
                    "Right-to-left override character detected".to_string(),
                ],
                file_type: "md".to_string(),
                encoding: "UTF-8".to_string(),
                analysis_status: "success".to_string(),
                error_message: None,
            },
        ],
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_json_export_format() {
        let test_data = create_test_analysis_result();
        let result = format_as_json(&test_data);
        
        assert!(result.is_ok(), "JSON formatting failed: {:?}", result.err());
        
        let json_string = result.unwrap();
        
        // Verify it's valid JSON by parsing it
        let parsed: serde_json::Value = serde_json::from_str(&json_string)
            .expect("Generated JSON should be parseable");
        
        // Check key fields are present
        assert!(parsed["codebase_analysis"].is_object());
        assert!(parsed["metadata"].is_object());
        assert_eq!(parsed["codebase_analysis"]["total_files"], 3);
        assert_eq!(parsed["codebase_analysis"]["files_with_issues"], 2);
        assert_eq!(parsed["codebase_analysis"]["health_score"], 66.7);
        
        println!("✅ JSON export format test passed");
    }

    #[test]
    fn test_markdown_export_format() {
        let test_data = create_test_analysis_result();
        let result = format_as_markdown(&test_data);
        
        assert!(result.is_ok(), "Markdown formatting failed: {:?}", result.err());
        
        let markdown_string = result.unwrap();
        
        // Check for key markdown elements
        assert!(markdown_string.contains("# 📊 Codebase Analysis Report"));
        assert!(markdown_string.contains("## 📋 Executive Summary"));
        assert!(markdown_string.contains("| **Total Files Scanned** | 3 |"));
        assert!(markdown_string.contains("| **Files with Issues** | 2 |"));
        assert!(markdown_string.contains("| **Health Score** | 66.7% |"));
        assert!(markdown_string.contains("🔍 Files Requiring Attention"));
        
        println!("✅ Markdown export format test passed");
    }

    #[test]
    fn test_text_export_format() {
        let test_data = create_test_analysis_result();
        let result = format_as_text(&test_data);
        
        assert!(result.is_ok(), "Text formatting failed: {:?}", result.err());
        
        let text_string = result.unwrap();
        
        // Check for key text elements
        assert!(text_string.contains("CODEBASE ANALYSIS REPORT"));
        assert!(text_string.contains("EXECUTIVE SUMMARY"));
        assert!(text_string.contains("Total Files Scanned:      3"));
        assert!(text_string.contains("Files with Issues:        2"));
        assert!(text_string.contains("Health Score:             66.7%"));
        assert!(text_string.contains("FILES REQUIRING ATTENTION"));
        
        println!("✅ Text export format test passed");
    }

    #[test]
    fn test_export_with_no_issues() {
        let clean_data = CodeBaseAnalysisResult {
            total_files: 2,
            files_with_issues: 0,
            total_suspicious_chars: 0,
            health_score: 100.0,
            analysis_time_ms: 800,
            file_details: vec![
                FileAnalysisDetail {
                    file_path: "/path/to/clean1.py".to_string(),
                    relative_path: "clean1.py".to_string(),
                    file_size: 1024,
                    total_characters: 400,
                    suspicious_characters: 0,
                    issues: vec![],
                    file_type: "py".to_string(),
                    encoding: "UTF-8".to_string(),
                    analysis_status: "success".to_string(),
                    error_message: None,
                },
                FileAnalysisDetail {
                    file_path: "/path/to/clean2.js".to_string(),
                    relative_path: "clean2.js".to_string(),
                    file_size: 2048,
                    total_characters: 600,
                    suspicious_characters: 0,
                    issues: vec![],
                    file_type: "js".to_string(),
                    encoding: "UTF-8".to_string(),
                    analysis_status: "success".to_string(),
                    error_message: None,
                },
            ],
        };

        // Test all formats with clean data
        let json_result = format_as_json(&clean_data);
        let markdown_result = format_as_markdown(&clean_data);
        let text_result = format_as_text(&clean_data);

        assert!(json_result.is_ok());
        assert!(markdown_result.is_ok());
        assert!(text_result.is_ok());

        // Check that "no issues" messaging is present
        let markdown = markdown_result.unwrap();
        assert!(markdown.contains("No Issues Detected") || markdown.contains("✅"));
        
        let text = text_result.unwrap();
        assert!(text.contains("NO ISSUES DETECTED") || text.contains("Congratulations"));

        println!("✅ Clean data export test passed");
    }

    #[test]
    fn test_export_edge_cases() {
        // Test with empty file list
        let empty_data = CodeBaseAnalysisResult {
            total_files: 0,
            files_with_issues: 0,
            total_suspicious_chars: 0,
            health_score: 100.0,
            analysis_time_ms: 50,
            file_details: vec![],
        };

        let json_result = format_as_json(&empty_data);
        let markdown_result = format_as_markdown(&empty_data);
        let text_result = format_as_text(&empty_data);

        assert!(json_result.is_ok());
        assert!(markdown_result.is_ok());
        assert!(text_result.is_ok());

        println!("✅ Edge case export test passed");
    }
}
