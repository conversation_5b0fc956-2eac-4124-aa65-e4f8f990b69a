# This script builds the frontend application and lists the output to verify which files are being used.

# Step 1: Build the Leptos frontend application using Trunk.
# The output will be placed in the `dist` directory as configured in Trunk.toml.
echo "Building the Leptos frontend..."
trunk build --release

# Step 2: List the contents of the `dist` directory.
# This shows us exactly what was packaged for the frontend.
echo "Listing the contents of the project's 'dist' directory..."
Get-ChildItem -Path C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\dist -Recurse