<svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- By <PERSON> 2025 - Style block to import the Lexend font from Google Fonts -->
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Lexend:wght@600&amp;display=swap');
    </style>
    
    <!-- Main shield gradient - Updated to purple -->
    <linearGradient id="shieldMain" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#c86873" />
      <stop offset="30%" stop-color="#a855a7" />
      <stop offset="70%" stop-color="#9333ea" />
      <stop offset="100%" stop-color="#7c3aed" />
    </linearGradient>
    
    <!-- Inner shield gradient - Updated to purple -->
    <linearGradient id="shieldInner" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#d8b4fe" />
      <stop offset="50%" stop-color="#c084fc" />
      <stop offset="100%" stop-color="#a855f7" />
    </linearGradient>
    
    <!-- Scan lines gradient - Updated to purple/cyan -->
    <linearGradient id="scanLines" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#06b6d4" stop-opacity="0.8" />
      <stop offset="50%" stop-color="#0891b2" stop-opacity="1" />
      <stop offset="100%" stop-color="#0e7490" stop-opacity="0.8" />
    </linearGradient>    
    
    <!-- Radar wave gradient -->
    <linearGradient id="radarWave" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#06b6d4" stop-opacity="0.6" />
      <stop offset="50%" stop-color="#0891b2" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#0e7490" stop-opacity="0.1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Pulse effect -->
    <filter id="pulse">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Radar wave filter -->
    <filter id="radarGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- A subtle drop shadow filter for the text for readability -->
    <filter id="textGlow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="0.5" stdDeviation="0.5" flood-color="#000" flood-opacity="0.7"/>
    </filter>
  </defs>
  
  <!-- Outer glow ring -->
  <circle cx="60" cy="60" r="55" fill="none" stroke="url(#shieldMain)" stroke-width="2" opacity="0.3" filter="url(#glow)"/>
  
  <!-- Main shield shape -->
  <path d="M60 10 L20 25 L20 55 C20 75 35 95 60 100 C85 95 100 75 100 55 L100 25 Z" 
        fill="url(#shieldMain)" 
        stroke="url(#shieldInner)" 
        stroke-width="2" 
        filter="url(#glow)"/>
  <!-- Inner shield detail -->
  <path d="M60 20 L30 32 L30 55 C30 70 42 83 60 87 C78 83 90 70 90 55 L90 32 Z" 
        fill="url(#shieldInner)" 
        opacity="0.7"/>
  
  <!-- Radar wave ripples (pebble in pond effect) -->
  <g opacity="0.4">
    <!-- First ripple -->
    <circle cx="60" cy="55" r="15" fill="none" stroke="url(#radarWave)" stroke-width="1" filter="url(#radarGlow)">
      <animate attributeName="r" values="15;25;35" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.3;0" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Second ripple (delayed) -->
    <circle cx="60" cy="55" r="15" fill="none" stroke="url(#radarWave)" stroke-width="1" filter="url(#radarGlow)">
      <animate attributeName="r" values="15;25;35" dur="4s" begin="1.3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.3;0" dur="4s" begin="1.3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Third ripple (delayed more) -->
    <circle cx="60" cy="55" r="15" fill="none" stroke="url(#radarWave)" stroke-width="1" filter="url(#radarGlow)">
      <animate attributeName="r" values="15;25;35" dur="4s" begin="2.6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.3;0" dur="4s" begin="2.6s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Scanning grid -->
  <g stroke="url(#scanLines)" stroke-width="1.5" opacity="0.8">
    <!-- Horizontal scan lines -->
    <line x1="35" y1="40" x2="85" y2="40"/>
    <line x1="35" y1="50" x2="85" y2="50"/>
    <line x1="35" y1="60" x2="85" y2="60"/>
    <line x1="35" y1="70" x2="85" y2="70"/>
    
    <!-- Vertical scan lines -->
    <line x1="45" y1="35" x2="45" y2="75"/>
    <line x1="55" y1="35" x2="55" y2="75"/>
    <line x1="65" y1="35" x2="65" y2="75"/>
    <line x1="75" y1="35" x2="75" y2="75"/>
  </g>
  
  <!-- Central scanner element -->
  <circle cx="60" cy="55" r="8" fill="none" stroke="url(#scanLines)" stroke-width="2" filter="url(#pulse)"/>
  <circle cx="60" cy="55" r="4" fill="url(#scanLines)" opacity="0.9"/>
  <!-- Scanner sweep line (animated) - Extended 20% longer -->
  <line x1="48" y1="43" x2="72" y2="67" stroke="#06b6d4" stroke-width="2" stroke-linecap="round" opacity="0.8">
    <animateTransform
      attributeName="transform"
      type="rotate"
      values="0 60 55;360 60 55"
      dur="3s"
      repeatCount="indefinite"/>
  </line>
    <!-- Digital elements -->
  <g fill="url(#scanLines)" opacity="0.7">
    <!-- Top corners -->
    <rect x="35" y="35" width="3" height="3"/>
    <rect x="82" y="35" width="3" height="3"/>
    <!-- Bottom corners -->
    <rect x="35" y="72" width="3" height="3"/>
    <rect x="82" y="72" width="3" height="3"/>
  </g>

  <!-- Text block (with correct sizing and positioning) -->
  <text 
    x="62" 
    y="46" 
    font-family="Lexend, sans-serif" 
    font-weight="900"
    font-size="12" 
    fill="white" 
    text-anchor="middle" 
    opacity="0.8"
    filter="url(#textGlow)">
      <tspan>Bad</tspan>
      <tspan x="62" dy="9.2">Character</tspan>
      <tspan x="62" dy="9.2">Scanner</tspan>
  </text>
</svg>