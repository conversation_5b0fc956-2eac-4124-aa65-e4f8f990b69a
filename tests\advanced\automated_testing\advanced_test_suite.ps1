dvanced Automated Testing Script for Bad Character Scanner
# This script performs comprehensive testing with detailed reporting

param(
    [string]$TestType = "all",  # all, basic, advanced, p        Write-Host "    ❌ Error during cleaning: $($_.Exception.Message)" -ForegroundColor Redrformance
    [string]$OutputFormat = "json",  # json, detailed, summary
    [string]$ReportFile = "advanced_test_results.json",
    [switch]$Verbose
)

Write-Host "🧪 Advanced Bad Character Scanner Testing Suite" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Test configuration
$TestResults = @{
    timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    testType = $TestType
    results = @()
    summary = @{
        totalTests = 0
        passed = 0
        failed = 0
        totalThreats = 0
        detectionAccuracy = 0
    }
    performance = @{
        totalExecutionTime = 0
        averageFileProcessingTime = 0
        largestFileProcessed = 0
        threatsPerSecond = 0
    }
}

# Test file definitions with expected results
$TestFiles = @(
    @{
        name = "Supply Chain Attack"
        file = "test_advanced_live\sophisticated_attacks\supply_chain_attack.js"
        expectedThreats = 6
        threatTypes = @("ZERO_WIDTH_SPACE", "ZERO_WIDTH_NON_JOINER", "ZERO_WIDTH_JOINER")
        attackVector = "invisible_characters"
        severity = "high"
    },
    @{
        name = "Phishing Homograph Attack"
        file = "test_advanced_live\sophisticated_attacks\phishing_homograph_attack.py"
        expectedThreats = 15
        threatTypes = @("CYRILLIC_SMALL_LETTER_ER", "CYRILLIC_SMALL_LETTER_A", "CYRILLIC_SMALL_LETTER_O", "CYRILLIC_SMALL_LETTER_BYELORUSSIAN_UKRAINIAN_I")
        attackVector = "homograph"
        severity = "critical"
    },
    @{
        name = "Advanced Bidirectional Attack"
        file = "test_advanced_live\sophisticated_attacks\bidirectional_advanced_attack.html"
        expectedThreats = 12
        threatTypes = @("RIGHT_TO_LEFT_OVERRIDE", "LEFT_TO_RIGHT_ISOLATE")
        attackVector = "bidirectional"
        severity = "high"
    },
    @{
        name = "Multi-Vector Attack"
        file = "test_advanced_live\sophisticated_attacks\multi_vector_attack.c"
        expectedThreats = 25
        threatTypes = @("ZERO_WIDTH_SPACE", "ZERO_WIDTH_NON_JOINER", "ZERO_WIDTH_JOINER", "CYRILLIC_SMALL_LETTER_A", "CYRILLIC_SMALL_LETTER_BYELORUSSIAN_UKRAINIAN_I", "RIGHT_TO_LEFT_OVERRIDE")
        attackVector = "mixed"
        severity = "critical"
    },
    @{
        name = "Large Scale Performance Test"
        file = "test_advanced_live\performance_testing\large_scale_attack_test.go"
        expectedThreats = 200
        threatTypes = @("ZERO_WIDTH_SPACE", "ZERO_WIDTH_NON_JOINER", "ZERO_WIDTH_JOINER", "CYRILLIC_SMALL_LETTER_O", "CYRILLIC_SMALL_LETTER_IE", "RIGHT_TO_LEFT_OVERRIDE")
        attackVector = "performance"
        severity = "medium"
    }
)

# Function to run CLI analysis
function Test-FileAnalysis {
    param(
        [hashtable]$TestFile
    )
    
    $startTime = Get-Date
    
    Write-Host "  🔍 Testing: $($TestFile.name)" -ForegroundColor Yellow
    
    # Check if file exists
    if (-not (Test-Path $TestFile.file)) {
        Write-Host "    ❌ File not found: $($TestFile.file)" -ForegroundColor Red
        return @{
            success = $false
            error = "File not found"
            threats = 0
            executionTime = 0
        }
    }
    
    # Run CLI analysis
    try {
        $output = & ".\target\release\analyzer_cli.exe" analyze $TestFile.file json 2>$null
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "    ❌ CLI execution failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{
                success = $false
                error = "CLI execution failed"
                threats = 0
                executionTime = 0
            }
        }
        
        # Parse JSON output
        $analysisResult = $output | ConvertFrom-Json
        $endTime = Get-Date
        $executionTime = ($endTime - $startTime).TotalMilliseconds
        
        # Extract threat information
        $detectedThreats = $analysisResult.threats.Count
        $healthScore = $analysisResult.health_score
        $detectedThreatTypes = $analysisResult.threats | ForEach-Object { $_.character_type } | Sort-Object -Unique
        
        # Calculate accuracy
        $accuracy = if ($TestFile.expectedThreats -gt 0) {
            [math]::Round(($detectedThreats / $TestFile.expectedThreats) * 100, 2)
        } else { 100 }
          Write-Host "    ✅ Detected: $detectedThreats/$($TestFile.expectedThreats) threats ($accuracy%)" -ForegroundColor Green
        Write-Host "    📊 Health Score: $healthScore%" -ForegroundColor Cyan
        Write-Host "    ⏱️  Execution Time: ${executionTime}ms" -ForegroundColor Gray
        
        if ($Verbose) {
            Write-Host "    🔍 Detected Threat Types: $($detectedThreatTypes -join ', ')" -ForegroundColor Gray
        }
        
        return @{
            success = $true
            threats = $detectedThreats
            expectedThreats = $TestFile.expectedThreats
            accuracy = $accuracy
            healthScore = $healthScore
            executionTime = $executionTime
            detectedThreatTypes = $detectedThreatTypes
            analysisResult = $analysisResult
        }
        
    } catch {
        Write-Host "    ❌ Error during analysis: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            success = $false
            error = $_.Exception.Message
            threats = 0
            executionTime = 0
        }
    }
}

# Function to test cleaning functionality
function Test-CleaningFunctionality {
    param(
        [hashtable]$TestFile
    )
    
    Write-Host "  🧹 Testing cleaning: $($TestFile.name)" -ForegroundColor Yellow
    
    $originalFile = $TestFile.file
    $cleanedFile = $originalFile -replace '\.([^.]+)$', '_cleaned.$1'
      try {
        # Clean the file
        & ".\target\release\analyzer_cli.exe" clean $originalFile $cleanedFile 2>$null
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "    ❌ Cleaning failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{ success = $false; error = "Cleaning failed" }
        }
          # Analyze the cleaned file
        $cleanedOutput = & ".\target\release\analyzer_cli.exe" analyze $cleanedFile json 2>$null
        $cleanedResult = $cleanedOutput | ConvertFrom-Json
        
        $remainingThreats = $cleanedResult.threats.Count
        $cleaningEfficiency = if ($TestFile.expectedThreats -gt 0) {
            [math]::Round((($TestFile.expectedThreats - $remainingThreats) / $TestFile.expectedThreats) * 100, 2)
        } else { 100 }
        
        Write-Host "    ✅ Cleaning efficiency: $cleaningEfficiency% ($remainingThreats threats remaining)" -ForegroundColor Green
        
        # Clean up the test file
        if (Test-Path $cleanedFile) {
            Remove-Item $cleanedFile -Force
        }
        
        return @{
            success = $true
            originalThreats = $TestFile.expectedThreats
            remainingThreats = $remainingThreats
            cleaningEfficiency = $cleaningEfficiency
        }
        
    } catch {
        Write-Host "    ❌ Error during cleaning: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            success = $false
            error = $_.Exception.Message
        }
    }
}

# Main testing logic
Write-Host "🚀 Starting comprehensive testing..." -ForegroundColor Green
Write-Host ""

$overallStartTime = Get-Date

foreach ($testFile in $TestFiles) {
    Write-Host "📋 Test Suite: $($testFile.name)" -ForegroundColor Cyan
    Write-Host "   Attack Vector: $($testFile.attackVector)" -ForegroundColor Gray
    Write-Host "   Severity: $($testFile.severity)" -ForegroundColor Gray
    Write-Host ""
    
    # Run analysis test
    $analysisResult = Test-FileAnalysis -TestFile $testFile
    
    # Run cleaning test if analysis was successful
    $cleaningResult = $null
    if ($analysisResult.success -and $TestType -eq "all") {
        $cleaningResult = Test-CleaningFunctionality -TestFile $testFile
    }
    
    # Store results
    $testResult = @{
        testName = $testFile.name
        attackVector = $testFile.attackVector
        severity = $testFile.severity
        analysis = $analysisResult
        cleaning = $cleaningResult
        timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    $TestResults.results += $testResult
    $TestResults.summary.totalTests++
    
    if ($analysisResult.success) {
        $TestResults.summary.passed++
        $TestResults.summary.totalThreats += $analysisResult.threats
    } else {
        $TestResults.summary.failed++
    }
    
    Write-Host ""
}

$overallEndTime = Get-Date
$TestResults.performance.totalExecutionTime = ($overallEndTime - $overallStartTime).TotalMilliseconds

# Calculate summary statistics
if ($TestResults.summary.totalTests -gt 0) {
    $TestResults.summary.detectionAccuracy = [math]::Round(($TestResults.summary.passed / $TestResults.summary.totalTests) * 100, 2)
    $TestResults.performance.averageFileProcessingTime = [math]::Round($TestResults.performance.totalExecutionTime / $TestResults.summary.totalTests, 2)
    $TestResults.performance.threatsPerSecond = if ($TestResults.performance.totalExecutionTime -gt 0) {
        [math]::Round(($TestResults.summary.totalThreats / ($TestResults.performance.totalExecutionTime / 1000)), 2)
    } else { 0 }
}

# Display summary
Write-Host "📊 TEST SUMMARY" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "Total Tests: $($TestResults.summary.totalTests)" -ForegroundColor White
Write-Host "Passed: $($TestResults.summary.passed)" -ForegroundColor Green
Write-Host "Failed: $($TestResults.summary.failed)" -ForegroundColor Red
Write-Host "Detection Accuracy: $($TestResults.summary.detectionAccuracy)%" -ForegroundColor Yellow
Write-Host "Total Threats Detected: $($TestResults.summary.totalThreats)" -ForegroundColor Magenta
Write-Host "Total Execution Time: $([math]::Round($TestResults.performance.totalExecutionTime, 2))ms" -ForegroundColor Gray
Write-Host "Average Processing Time: $($TestResults.performance.averageFileProcessingTime)ms per file" -ForegroundColor Gray
Write-Host "Threat Detection Rate: $($TestResults.performance.threatsPerSecond) threats per second" -ForegroundColor Gray
Write-Host ""

# Save detailed results to file
$TestResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $ReportFile -Encoding UTF8

Write-Host "📄 Detailed results saved to: $ReportFile" -ForegroundColor Green

# Final status
if ($TestResults.summary.failed -eq 0) {
    Write-Host "🎉 ALL TESTS PASSED! The Bad Character Scanner is working correctly." -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠️  Some tests failed. Please review the results above." -ForegroundColor Yellow
    exit 1
}
