# 🚀 Developer Onboarding - Get Started in 15 Minutes
## Bad Character Scanner - Leptos + Tauri v2 Application

**Welcome!** This guide will get you productive with our Leptos + Tauri v2 desktop application for Unicode security analysis.

---

## 🎯 **What You're Building**

**Bad Character Scanner** - A desktop app that detects dangerous Unicode characters, homoglyphs, and security threats in text and codebases.

### **Quick Overview**
- **Frontend**: Leptos (Rust-based reactive UI)
- **Backend**: Tauri v2 (Rust desktop framework)  
- **Purpose**: Unicode security analysis & threat detection
- **Status**: ✅ **Fully operational and production-ready**

---

## ⚡ **15-Minute Setup**

### **Step 1: Prerequisites (5 min)**
```powershell
# Install Rust
winget install Rustlang.Rustup

# Install Node.js  
winget install OpenJS.NodeJS

# Install required tools
cargo install tauri-cli --version "^2.5"
cargo install trunk --locked
```

### **Step 2: Get Code (2 min)**
```powershell
git clone <repository-url>
cd Leptos_TaurieV2_BCS
```

### **Step 3: Setup (3 min)**
```powershell
# Install dependencies
npm install

# Verify setup
cargo tauri info
```

### **Step 4: Run (5 min)**
```powershell
# Option 1: Use startup script (Recommended)
.\dev_startup.ps1

# Option 2: Manual (if script doesn't work)
# Terminal 1: Frontend
trunk serve --port 1420

# Terminal 2: Backend (new terminal)
cargo tauri dev
```

### **✅ Success Check**
You should see:
- **Frontend**: http://localhost:1420 in browser
- **Desktop App**: Tauri window opens automatically
- **No Errors**: Clean compilation and startup

---

## 🏗️ **Project Structure (Need to Know)**

```
Leptos_TaurieV2_BCS/
├── src/                    # Frontend (Leptos/WASM)
│   ├── components/         # UI components
│   └── lib.rs             # Frontend entry
├── src-tauri/             # Backend (Tauri + Rust)
│   ├── src/modules/       # Analysis engines
│   └── src/commands/      # IPC interface
├── docs/                  # Documentation
└── dev_startup.ps1        # Development script
```

### **Key Components You'll Work With**
- **`AnalyzeComponent`**: Main text analysis UI
- **`CodebaseComponent`**: File system analysis  
- **`character_analyzer`**: Core analysis engine
- **Tauri Commands**: Frontend ↔ Backend communication

---

## 🔧 **Your Development Workflow**

### **Daily Development**
```powershell
# 1. Start development environment
.\dev_startup.ps1

# 2. Make changes
# - Frontend: Edit src/ files (auto-reloads)
# - Backend: Edit src-tauri/src/ files (auto-recompiles)

# 3. Test changes
# - Frontend: Browser refreshes automatically
# - Backend: Tauri app restarts

# 4. Commit when ready
git add .
git commit -m "Your changes"
```

### **Adding New Features**
1. **Backend**: Create module in `src-tauri/src/modules/`
2. **Commands**: Add Tauri command in `src-tauri/src/commands/`  
3. **Frontend**: Create component in `src/components/`
4. **Wire Up**: Connect frontend to backend via IPC

---

## 🐛 **Common Issues & Quick Fixes**

### **Build Errors**
```powershell
# Missing WASM target
rustup target add wasm32-unknown-unknown

# Port conflict
# Kill processes on port 1420 or 1421
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"} | Stop-Process
```

### **Development Issues**
- **Frontend errors**: Check browser console at http://localhost:1420
- **Backend errors**: Check `cargo tauri dev` terminal output
- **IPC errors**: Check both frontend and backend logs

### **Quick Solutions**
1. **Clean build**: `cargo clean && trunk clean`
2. **Restart**: Close all terminals, run `.\dev_startup.ps1` again
3. **Check processes**: Ensure trunk and tauri processes are running

---

## 🎯 **What's Next?**

### **After Setup (Choose Your Path)**

**👨‍💻 I Want to Develop Features**
- Read: [🏗️ DEVELOPER_GUIDE.md](DEVELOPER_GUIDE.md)
- Study: [🏗️ project/ARCHITECTURE.md](project/ARCHITECTURE.md)

**🎨 I Want to Improve UI**  
- Read: [🎨 MODERN_GUI_IMPLEMENTATION_GUIDE.md](MODERN_GUI_IMPLEMENTATION_GUIDE.md)
- Study: Components in `src/components/`

**🔧 I Want to Fix Issues**
- Check: [📊 project-management/COMPLETE_SUCCESS_FULL_STACK_WORKING.md](project-management/COMPLETE_SUCCESS_FULL_STACK_WORKING.md)
- Use: [⚡ QUICK_NAVIGATION.md](QUICK_NAVIGATION.md) to find solutions

**📚 I Want to Learn More**
- Explore: [✨ FEATURES.md](FEATURES.md) - See what the app does
- Follow: [📝 contributing/CONTRIBUTING.md](contributing/CONTRIBUTING.md) - Development workflow

---

## 🏆 **You're Ready!**

**Congratulations!** You now have:
- ✅ Fully functional development environment
- ✅ Understanding of project structure  
- ✅ Knowledge of development workflow
- ✅ Quick fixes for common issues

### **Next Steps**
1. **Explore the app**: Try analyzing some text or files
2. **Read the code**: Start with `src/components/analyze_component.rs`
3. **Make a small change**: Add a console.log or modify some text
4. **Check documentation**: Use [⚡ QUICK_NAVIGATION.md](QUICK_NAVIGATION.md) to find what you need

---

**🎯 Ready to contribute? You're now productive with the Bad Character Scanner!**

*Happy coding! 🚀*
