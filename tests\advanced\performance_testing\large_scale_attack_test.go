// Large-scale Performance Testing File
// This file contains 1000+ instances of various Unicode attacks to test performance

package main

import (
    "fmt"
    "strings"
    "crypto/md5"
    "encoding/hex"
)

// Generate 100 functions with zero-width characters
func test‌Function1() string { return "test1" }  // ZWNJ
func test​Function2() string { return "test2" }  // ZWSP  
func test‍Function3() string { return "test3" }  // ZWJ
func test‌Function4() string { return "test4" }  // ZWNJ
func test​Function5() string { return "test5" }  // ZWSP
func test‍Function6() string { return "test6" }  // ZWJ
func test‌Function7() string { return "test7" }  // ZWNJ
func test​Function8() string { return "test8" }  // ZWSP
func test‍Function9() string { return "test9" }  // ZWJ
func test‌Function10() string { return "test10" }  // ZWNJ

// Generate 50 variables with homographic attacks (Cyrillic characters)
var user1 = "legitimate"
var usеr1 = "malicious"  // Cyrillic 'е'
var admin1 = "legitimate"
var аdmin1 = "malicious"  // Cyrillic 'а'
var manager1 = "legitimate"
var mаnager1 = "malicious"  // Cyrillic 'а'
var operator1 = "legitimate"
var оperator1 = "malicious"  // Cyrillic 'о'
var supervisor1 = "legitimate"
var supervisоr1 = "malicious"  // Cyrillic 'о'

// Generate 25 functions with bidirectional text attacks
func processRequest1(data string) string {
    if data == "safe" {
        return "processed"
    } ‮ else {
        return "malicious_processed"
    } ⁦
    return "safe"
}

func processRequest2(data string) string {
    if data == "safe" {
        return "processed"
    } ‮ else {
        return "malicious_processed"
    } ⁦
    return "safe"
}

func processRequest3(data string) string {
    if data == "safe" {
        return "processed"
    } ‮ else {
        return "malicious_processed"
    } ⁦
    return "safe"
}

// Mass generation of variables with mixed attacks
var config‌1 = "safe"    // ZWNJ
var config​2 = "safe"    // ZWSP
var config‍3 = "safe"    // ZWJ
var cоnfig4 = "malicious"  // Cyrillic 'о'
var соnfig5 = "malicious"  // Cyrillic 'о'
var settings‌1 = "safe"  // ZWNJ
var settings​2 = "safe"  // ZWSP
var settings‍3 = "safe"  // ZWJ
var sеttings4 = "malicious"  // Cyrillic 'е'

// Large array with mixed invisible characters
var largeDataSet = []string{
    "item1‌",     // ZWNJ
    "item2​",     // ZWSP
    "item3‍",     // ZWJ
    "item4‌",     // ZWNJ
    "item5​",     // ZWSP
    "item6‍",     // ZWJ
    "item7‌",     // ZWNJ
    "item8​",     // ZWSP
    "item9‍",     // ZWJ
    "item10‌",    // ZWNJ
    "item11​",    // ZWSP
    "item12‍",    // ZWJ
    "item13‌",    // ZWNJ
    "item14​",    // ZWSP
    "item15‍",    // ZWJ
    "item16‌",    // ZWNJ
    "item17​",    // ZWSP
    "item18‍",    // ZWJ
    "item19‌",    // ZWNJ
    "item20​",    // ZWSP
}

// Functions with complex homographic names
func processData() string { return "legitimate" }
func prоcessData() string { return "malicious" }  // Cyrillic 'о'
func procеssData() string { return "malicious" }  // Cyrillic 'е'
func validateInput() string { return "legitimate" }
func vаlidateInput() string { return "malicious" }  // Cyrillic 'а'
func vаlidаteInput() string { return "malicious" }  // Cyrillic 'а' x2
func authenticateUser() string { return "legitimate" }
func аuthenticateUser() string { return "malicious" }  // Cyrillic 'а'
func authentіcateUser() string { return "malicious" }  // Cyrillic 'і'

// Large comment block with bidirectional attacks
/*
This is a large comment block that contains multiple bidirectional text attacks
scattered throughout to test the scanner's ability to detect them efficiently.

Normal text here describing the function behavior.
‮ Hidden malicious instructions that would compromise the system ⁦
More normal text to hide the attack.
‮ Another hidden section with different malicious content ⁦
Even more normal text.
‮ Third hidden attack vector ⁦
Final normal text section.
*/

// Stress test with deeply nested structures
type ComplexStruct struct {
    Field1‌ string   // ZWNJ
    Field2​ string   // ZWSP  
    Field3‍ string   // ZWJ
    Field4‌ string   // ZWNJ
    Field5​ string   // ZWSP
    Field6‍ string   // ZWJ
    Field7‌ string   // ZWNJ
    Field8​ string   // ZWSP
    Field9‍ string   // ZWJ
    Field10‌ string  // ZWNJ
    Nested struct {
        SubField1‌ int  // ZWNJ
        SubField2​ int  // ZWSP
        SubField3‍ int  // ZWJ
        DeepNested struct {
            DeepField1‌ bool  // ZWNJ
            DeepField2​ bool  // ZWSP
            DeepField3‍ bool  // ZWJ
        }
    }
}

// Mass string processing with hidden characters
func massStringProcessing() {
    data := []string{
        "string1‌", "string2​", "string3‍", "string4‌", "string5​",
        "string6‍", "string7‌", "string8​", "string9‍", "string10‌",
        "string11​", "string12‍", "string13‌", "string14​", "string15‍",
        "string16‌", "string17​", "string18‍", "string19‌", "string20​",
        "string21‍", "string22‌", "string23​", "string24‍", "string25‌",
        "string26​", "string27‍", "string28‌", "string29​", "string30‍",
    }
    
    for i, item := range data {
        processed := strings.ToUpper(item)
        hash := md5.Sum([]byte(processed))
        fmt.Printf("Item %d: %s -> %s\n", i, item, hex.EncodeToString(hash[:]))
    }
}

// Large map with homographic keys
var homographicMap = map[string]string{
    "key1": "value1",
    "kеy1": "malicious1",  // Cyrillic 'е'
    "key2": "value2", 
    "kеy2": "malicious2",  // Cyrillic 'е'
    "admin": "legitimate",
    "аdmin": "malicious",  // Cyrillic 'а'
    "user": "legitimate",
    "usеr": "malicious",   // Cyrillic 'е'
    "root": "legitimate",
    "rооt": "malicious",   // Cyrillic 'о' x2
}

// Performance test function
func performanceTest() {
    // Test 1: Function calls with invisible characters
    for i := 0; i < 1000; i++ {
        test‌Function1()  // ZWNJ
        test​Function2()  // ZWSP
        test‍Function3()  // ZWJ
    }
    
    // Test 2: Map access with homographs
    for i := 0; i < 1000; i++ {
        _ = homographicMap["key1"]
        _ = homographicMap["kеy1"]  // Cyrillic 'е'
        _ = homographicMap["admin"]
        _ = homographicMap["аdmin"]  // Cyrillic 'а'
    }
    
    // Test 3: String processing
    massStringProcessing()
    
    // Test 4: Complex struct operations
    var complex ComplexStruct
    complex.Field1‌ = "test1"  // ZWNJ
    complex.Field2​ = "test2"  // ZWSP
    complex.Field3‍ = "test3"  // ZWJ
    
    fmt.Printf("Complex struct processed: %+v\n", complex)
}

// Main function with comprehensive testing
func main() {
    fmt.Println("Starting large-scale Unicode attack performance test...")
    
    // Run performance tests
    performanceTest()
    
    // Test bidirectional processing
    for i := 0; i < 100; i++ {
        processRequest1("test")
        processRequest2("test")
        processRequest3("test")
    }
    
    fmt.Println("Performance test completed")
}
