// Simple, clean text analyzer component - Apple-inspired design
use leptos::*;
use serde::Serialize;
use wasm_bindgen::prelude::*;
use js_sys;
use crate::context::use_analysis_context;
use crate::components::security_analysis_tabs::SecurityAnalysisTabs;
use crate::components::{ProgressOverlay, ProgressManager, ErrorDisplay, ErrorManager, AppError, Toast, ToastType};

// Simple function to check if <PERSON><PERSON> is available
fn is_tauri_available() -> bool {
    js_sys::Reflect::has(&js_sys::global(), &"__TAURI__".into()).unwrap_or(false)
}

#[derive(Debug, <PERSON>lone, Serialize)]
struct AnalyzeRequest {
    text: String,
}

/// Clean problematic Unicode characters from text
fn clean_problematic_unicode(text: &str) -> String {
    text.chars()
        .filter(|&c| {
            let code_point = c as u32;

            // Remove problematic Unicode characters
            match code_point {
                // Zero Width characters
                0x200B | // Zero Width Space
                0x200C | // Zero Width Non-Joiner
                0x200D | // Zero Width Joiner
                0x2060 | // Word Joiner
                0xFEFF   // Zero Width No-Break Space (BOM)
                => false,

                // Directional override characters
                0x202A | // Left-to-Right Embedding
                0x202B | // Right-to-Left Embedding
                0x202C | // Pop Directional Formatting
                0x202D | // Left-to-Right Override
                0x202E | // Right-to-Left Override
                0x2066 | // Left-to-Right Isolate
                0x2067 | // Right-to-Left Isolate
                0x2068 | // First Strong Isolate
                0x2069   // Pop Directional Isolate
                => false,

                // Control characters (except common whitespace)
                0x0000..=0x001F => {
                    // Keep common whitespace characters
                    matches!(c, '\t' | '\n' | '\r')
                },
                0x007F..=0x009F => false, // DEL and C1 control characters

                // Soft hyphen
                0x00AD => false,

                // Replacement character
                0xFFFD => false,

                // Non-breaking spaces (replace with regular space)
                0x00A0 | // No-Break Space
                0x2007 | // Figure Space
                0x202F | // Narrow No-Break Space
                0x3000   // Ideographic Space
                => false, // We'll replace these with regular spaces

                // Keep everything else
                _ => true,
            }
        })
        .collect::<String>()
        // Replace non-breaking spaces with regular spaces
        .replace('\u{00A0}', " ") // No-Break Space
        .replace('\u{2007}', " ") // Figure Space
        .replace('\u{202F}', " ") // Narrow No-Break Space
        .replace('\u{3000}', " ") // Ideographic Space
        // Normalize multiple spaces to single space
        .split_whitespace()
        .collect::<Vec<&str>>()
        .join(" ")
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[component]
pub fn SimpleTextAnalyzer() -> impl IntoView {
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_result, set_analysis_result) = create_signal(None::<String>);
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    let (cleaned_text, set_cleaned_text) = create_signal(None::<String>);
    let (toast_message, set_toast_message) = create_signal(None::<(String, ToastType)>);
    
    // Progress and error management
    let (progress_state, progress_manager) = ProgressManager::new();
    let (errors, error_manager) = ErrorManager::new();
    
    // Get analysis context to store results
    let analysis_context = use_analysis_context();

    let analyze_text = {
        let analysis_context = analysis_context.clone();
        let progress_manager = progress_manager.clone();
        let error_manager = error_manager.clone();
        move |_| {
            let text = input_text.get();
            if text.is_empty() {
                error_manager.add_error(AppError::invalid_input("text".to_string()));
                return;
            }

            if text.len() > 1_000_000 {
                error_manager.add_error(
                    AppError::invalid_input("text".to_string())
                        .with_details("Text is too large (over 1MB). Please reduce the input size.".to_string())
                );
                return;
            }

            set_is_analyzing.set(true);
            set_analysis_result.set(None);
            set_cleaned_text.set(None);
            
            // Start progress tracking
            progress_manager.start_operation("Analyzing Text", 3, true);

            let analysis_context = analysis_context.clone();
            let progress_manager = progress_manager.clone();
            let error_manager = error_manager.clone();
            
            spawn_local(async move {
                progress_manager.update_progress(1, Some("Preparing analysis...".to_string()));
                
                if !is_tauri_available() {
                    // Web mode - provide demo analysis
                    progress_manager.update_progress(2, Some("Running analysis...".to_string()));
                    gloo_timers::future::sleep(std::time::Duration::from_millis(1500)).await;
                    
                    let demo_result = format!(r#"{{
                        "total_characters": {},
                        "suspicious_characters": [
                            {{
                                "character": "\u200B",
                                "position": 15,
                                "codepoint": 8203,
                                "suspicion_reasons": ["Zero-width character", "Potential steganography"]
                            }},
                            {{
                                "character": "а",
                                "position": 42,
                                "codepoint": 1072,
                                "suspicion_reasons": ["Cyrillic character in Latin text", "Potential homoglyph attack"]
                            }}
                        ],
                        "security_analysis": {{
                            "risk_level": "Medium",
                            "homograph_attacks": [
                                "Cyrillic 'а' (U+0430) detected in Latin context - potential homoglyph substitution"
                            ],
                            "phishing_indicators": [
                                "Mixed script usage detected"
                            ],
                            "steganography_potential": true
                        }},
                        "analysis_metadata": {{
                            "analysis_duration_ms": 1500,
                            "analyzer_version": "2.0.0",
                            "timestamp": "{}",
                            "total_characters_analyzed": {}
                        }}
                    }}"#,
                        text.len(),
                        js_sys::Date::new_0().to_iso_string().as_string().unwrap_or_default(),
                        text.len()
                    );
                    
                    progress_manager.update_progress(3, Some("Finalizing results...".to_string()));
                    gloo_timers::future::sleep(std::time::Duration::from_millis(500)).await;
                    
                    set_analysis_result.set(Some(demo_result.clone()));
                    analysis_context.set_analysis(demo_result, "text".to_string());
                    progress_manager.file_processed();
                    set_toast_message.set(Some(("Analysis completed successfully!".to_string(), ToastType::Success)));
                } else {
                    // Tauri mode - call actual backend
                    let args = AnalyzeRequest { text: text.clone() };
                    match serde_wasm_bindgen::to_value(&args) {
                        Ok(args_js) => {
                            progress_manager.update_progress(2, Some("Calling backend analysis...".to_string()));
                            
                            match invoke("analyze_text", args_js).await {
                                result => {
                                    progress_manager.update_progress(3, Some("Processing results...".to_string()));
                                    
                                    match js_sys::JSON::stringify(&result) {
                                        Ok(json_string) => {
                                            let formatted = json_string.as_string().unwrap_or_else(|| "Failed to format result".to_string());
                                            match serde_json::from_str::<serde_json::Value>(&formatted) {
                                                Ok(json_value) => {
                                                    match serde_json::to_string_pretty(&json_value) {
                                                        Ok(pretty) => {
                                                            set_analysis_result.set(Some(pretty.clone()));
                                                            analysis_context.set_analysis(pretty, "text".to_string());
                                                            progress_manager.file_processed();
                                                            set_toast_message.set(Some(("Analysis completed successfully!".to_string(), ToastType::Success)));
                                                        },
                                                        Err(e) => {
                                                            set_analysis_result.set(Some(formatted));
                                                            error_manager.add_error(
                                                                AppError::analysis_failed(format!("JSON formatting error: {:?}", e))
                                                            );
                                                            progress_manager.file_failed(&format!("JSON formatting error: {:?}", e));
                                                        }
                                                    }
                                                }
                                                Err(e) => {
                                                    set_analysis_result.set(Some(formatted));
                                                    error_manager.add_error(
                                                        AppError::analysis_failed(format!("JSON parsing error: {:?}", e))
                                                    );
                                                    progress_manager.file_failed(&format!("JSON parsing error: {:?}", e));
                                                }
                                            }
                                        }
                                        Err(e) => {
                                            error_manager.add_error(
                                                AppError::analysis_failed(format!("Result formatting error: {:?}", e))
                                            );
                                            progress_manager.file_failed(&format!("Result formatting error: {:?}", e));
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            error_manager.add_error(
                                AppError::analysis_failed(format!("Serialization error: {:?}", e))
                            );
                            progress_manager.file_failed(&format!("Serialization error: {:?}", e));
                        }
                    }
                }
                
                progress_manager.finish_operation();
                set_is_analyzing.set(false);
            });
        }
    };

    let clean_text = {
        let error_manager = error_manager.clone();
        move |_| {
            let text = input_text.get();
            if text.is_empty() {
                error_manager.add_error(AppError::invalid_input("text".to_string()));
                return;
            }

            let error_manager = error_manager.clone();
            spawn_local(async move {
                if !is_tauri_available() {
                    // Web mode - provide proper text cleaning
                    gloo_timers::future::sleep(std::time::Duration::from_millis(500)).await;
                    let cleaned = clean_problematic_unicode(&text);
                    set_cleaned_text.set(Some(format!("Cleaned text: {}", cleaned)));
                    set_toast_message.set(Some(("Text cleaned successfully!".to_string(), ToastType::Success)));
                } else {
                    // Tauri mode - call actual backend
                    let args = AnalyzeRequest { text: text.clone() };
                    match serde_wasm_bindgen::to_value(&args) {
                        Ok(args_js) => {
                            match invoke("clean_text_detailed", args_js).await {
                                result => {
                                    if let Some(cleaned) = result.as_string() {
                                        set_cleaned_text.set(Some(cleaned));
                                        set_toast_message.set(Some(("Text cleaned successfully!".to_string(), ToastType::Success)));
                                    } else {
                                        error_manager.add_error(
                                            AppError::analysis_failed("Failed to get cleaned text result".to_string())
                                        );
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            error_manager.add_error(
                                AppError::analysis_failed(format!("Serialization error: {:?}", e))
                            );
                        }
                    }
                }
            });
        }
    };

    view! {
        <div class="p-8">
            // Professional header section
            <div class="section-header">
                <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <svg class="section-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="section-title">"Bad Character Scanner"</h2>
                    <p class="section-subtitle">"Advanced Unicode Analysis & Security Scanner"</p>
                    <p class="text-xs text-blue-600 mt-1">"with comprehensive text analysis capabilities - By John Shoy"</p>
                </div>
            </div>



            // Main content card
            <div class="card p-6 space-y-6">
                // Text Input Section
                <div class="space-y-4">
                    <div class="flex items-center gap-2 mb-3">
                        <svg class="header-icon text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-800">"Text Input"</h3>
                    </div>

                    <textarea
                        class="input-field h-32 resize-none"
                        placeholder="Enter or paste text to analyze... Supports Unicode, control characters, invisible characters, and more."
                        on:input=move |ev| {
                            set_input_text.set(event_target_value(&ev));
                        }
                        prop:value=input_text
                    />

                    // Character count and bytes display
                    <div class="flex justify-between items-center text-sm text-gray-500">
                        <span>
                            <span class="font-medium">"Characters: "</span>
                            {move || input_text.get().chars().count()}
                        </span>
                        <span>
                            <span class="font-medium">"Bytes: "</span>
                            {move || input_text.get().len()}
                        </span>
                    </div>

                    // Action buttons
                    <div class="flex gap-3">
                        <button
                            class="btn-primary flex items-center gap-2"
                            on:click=analyze_text
                            disabled=move || is_analyzing.get() || input_text.get().is_empty()
                        >
                            <svg class="btn-icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Text" }}
                        </button>

                        <button
                            class="btn-secondary flex items-center gap-2"
                            on:click=clean_text
                            disabled=move || input_text.get().is_empty()
                        >
                            <svg class="btn-icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            "Clean Text"
                        </button>
                    </div>
                </div>

                // Quick Test Samples Section
                <div class="border-t pt-6">
                    <div class="flex items-center gap-2 mb-4">
                        <svg class="btn-icon-sm text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span class="text-sm font-semibold text-gray-700">"Quick Test Samples:"</span>
                    </div>

                    <div class="flex flex-wrap gap-2">
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Regular Text".to_string())
                        >
                            "Regular Text"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Zero-Width Characters: \u{200B}\u{200C}\u{200D}".to_string())
                        >
                            "Zero-Width Characters"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Mixed Scripts: Нello Wοrld".to_string())
                        >
                            "Mixed Scripts"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Control Characters: \u{0001}\u{0002}\u{0003}".to_string())
                        >
                            "Control Characters"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Bidirectional Override: \u{202E}gnirts desrever\u{202C}".to_string())
                        >
                            "Bidirectional Override"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Emoji Text: 🔍📊🛡️".to_string())
                        >
                            "Emoji Text"
                        </button>
                        <button
                            class="test-sample-btn"
                            on:click=move |_| set_input_text.set("Combining Characters: a\u{0300}e\u{0301}i\u{0302}o\u{0303}u\u{0308}".to_string())
                        >
                            "Combining Characters"
                        </button>
                    </div>
                </div>
            </div>

            // Results section
            <Show when=move || analysis_result.get().is_some() || cleaned_text.get().is_some()>
                <div class="mt-6 space-y-4">
                    // Cleaned text result
                    <Show when=move || cleaned_text.get().is_some()>
                        <div class="card p-6 border-l-4 border-l-green-500">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center">
                                    <svg class="card-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">"Text Cleaning Results"</h3>
                                    <p class="text-sm text-gray-600">"Cleaned and normalized text output"</p>
                                </div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4 border">
                                <pre class="whitespace-pre-wrap text-sm text-gray-900 font-mono">
                                    {move || cleaned_text.get().unwrap_or_default()}
                                </pre>
                            </div>
                        </div>
                    </Show>

                    // Analysis results with tabbed interface
                    <Show when=move || analysis_result.get().is_some()>
                        <div class="card p-6 border-l-4 border-l-blue-500">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                                    <svg class="card-icon text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">"Security Analysis Results"</h3>
                                    <p class="text-sm text-gray-600">"Comprehensive character and threat analysis"</p>
                                </div>
                            </div>

                            // Enhanced tabbed interface
                            <SecurityAnalysisTabs analysis_result=analysis_result.get().unwrap_or_default() />

                            // Legacy detailed view (collapsible)
                            <details class="bg-gray-50 rounded-lg border mt-6">
                                <summary class="cursor-pointer p-4 font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors flex items-center justify-between">
                                    <span>"📊 View Raw Analysis Data"</span>
                                    <svg class="btn-icon-md text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </summary>
                                <div class="p-4 border-t bg-white">
                                    <pre class="whitespace-pre-wrap text-xs overflow-auto max-h-96 text-gray-900 font-mono bg-gray-50 p-3 rounded border">
                                        {move || analysis_result.get().unwrap_or_default()}
                                    </pre>
                                </div>
                            </details>
                        </div>
                    </Show>
                </div>
            </Show>
        </div>
        
        // Progress overlay
        <ProgressOverlay progress_state=progress_state on_cancel=None />
        
        // Error display
        <ErrorDisplay errors=errors error_manager=error_manager.clone() max_visible=Some(3) />
        
        // Toast notifications
        <Show when=move || toast_message.get().is_some()>
            {move || {
                if let Some((message, toast_type)) = toast_message.get() {
                    view! {
                        <Toast 
                            message=message 
                            toast_type=toast_type 
                            duration=Some(5000)
                            on_close=Callback::new(move |_| set_toast_message.set(None))
                        />
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}
        </Show>
    }
}
