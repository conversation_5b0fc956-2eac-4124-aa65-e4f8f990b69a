# Lessons Learned: Invisible Character Crisis in Tauri v2 + Leptos Project

**Date:** 2025-06-03  
**Framework:** Tauri v2 + Leptos  
**Issue:** Critical build failure caused by invisible characters in source files  

---

## 🚨 **Crisis Summary**

### **The Problem**
- **Frontend completely broken:** Blank pages, build failures, no WASM output
- **Mysterious errors:** "The system cannot find the path specified" (os error 3)
- **Trunk asset pipeline failures:** Files couldn't be copied or processed
- **Hours of debugging:** Tried script tag fixes, configuration changes, dependency updates

### **The Root Cause** 
**Invisible characters** in 12 critical files, including:
- `src/lib.rs` (main Leptos frontend entrypoint)
- All backup frontend files
- Backend Rust files (`main_module.rs`, `report_generator.rs`)
- Test files and utility scripts

---

## 🔍 **How We Discovered It**

### **Failed Approaches (What Didn't Work)**
1. **Script tag modifications** - Tried various `<script data-trunk src="...">` combinations
2. **Build configuration audits** - Checked `Trunk.toml`, `Cargo.toml`, `justfile`
3. **Dependency updates** - Assumed version compatibility issues
4. **Manual file inspection** - Invisible characters aren't visible in editors!

### **The Breakthrough**
**Automated PowerShell scan** for invisible characters:
```powershell
Get-ChildItem -Path "." -Include "*.rs","*.toml","*.html","*.js","*.json" -Recurse | 
ForEach-Object { 
    $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue; 
    if ($content -and ($content -match "[\u200B-\u200D\uFEFF]" -or $content -match "[\u00A0\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]")) { 
        Write-Host "Invisible characters found in: $($_.FullName)" 
    } 
}
```

**Result:** 12 files infected with invisible characters!

---

## ✅ **The Solution (Step-by-Step for Tauri v2)**

### **1. Detection Script**
Created automated PowerShell script to scan for invisible characters across the entire codebase.

### **2. Backup Strategy**
- Created timestamped backup directory before any changes
- Preserved original files for forensic analysis

### **3. Character Cleanup**
Removed multiple types of invisible characters:
- Zero-width spaces (`\u200B-\u200D`)
- Byte Order Mark (`\uFEFF`)
- Various Unicode whitespace characters (`\u00A0`, `\u2000-\u200A`, etc.)

### **4. Build Verification**
- `cargo clean; trunk clean` - Reset build state
- `trunk serve --port 1420` - Test frontend build
- **Result:** ✅ Successful compilation (1m 36s), WASM/JS assets generated

---

## 🛡️ **Prevention Strategies**

### **1. Regular Scanning**
- Add invisible character detection to CI/CD pipeline
- Run weekly scans during development
- Include in pre-commit hooks

### **2. Editor Configuration**
- Configure editors to show invisible characters
- Use consistent encoding (UTF-8 without BOM)
- Avoid copy-pasting from web sources without cleaning

### **3. Automated Cleanup**
- Created `scripts/clean-invisible-characters.ps1` for future use
- Document the cleanup process for team members
- Include in project recovery playbook

---

## 📊 **Impact Analysis**

### **Time Lost**
- **Multiple development sessions** spent on wrong solutions
- **Frontend progress stalled** for extended period
- **Developer confidence shaken** by mysterious failures

### **Technical Debt Created**
- Multiple backup files with same invisible character issues
- Incomplete understanding of build pipeline led to wrong fixes
- Need to audit all legacy code for similar issues

### **Knowledge Gained**
- **Invisible characters are real threats** in modern development
- **Automated detection is essential** - manual inspection fails
- **Tauri v2 + Leptos build pipeline is sensitive** to file encoding issues
- **PowerShell scripting is powerful** for file content analysis

---

## 🎯 **Recommendations for Future Tauri v2 Projects**

### **Development Workflow**
1. **Include invisible character detection** in project setup
2. **Use consistent file encoding** across all tools
3. **Implement automated scanning** in build pipeline
4. **Document recovery procedures** before they're needed

### **Team Practices**
1. **Train developers** on invisible character risks
2. **Establish code review processes** that include encoding checks
3. **Use standardized development environments** to prevent encoding issues
4. **Create project-specific cleanup scripts** early in development

### **Tauri v2 Specific**
1. **Monitor Trunk build output** for unusual file path errors
2. **Test frontend builds regularly** during development
3. **Keep Tauri v2 dependencies updated** to latest compatible versions
4. **Use browser dev tools** to verify WASM loading and Tauri API availability

---

## 🔧 **Tools and Scripts Created**

### **Detection Script**
- `scripts/clean-invisible-characters.ps1` - PowerShell cleanup automation
- Handles multiple invisible character types
- Creates automatic backups
- Provides clear success/failure reporting

### **Build Verification Process**
1. Clean build artifacts: `cargo clean; trunk clean`
2. Test frontend build: `trunk serve --port 1420`
3. Verify WASM/JS output in `dist/` directory
4. Test Tauri integration: `cargo tauri dev`

---

## 💡 **Key Takeaways**

1. **Invisible problems require automated detection** - Human inspection fails
2. **Build failures can have non-obvious root causes** - Don't assume configuration issues
3. **Backup before cleanup** - Always preserve original state for analysis
4. **Document solutions immediately** - Future you will thank present you
5. **Test the fix thoroughly** - Verify both frontend and backend integration
6. **Create prevention measures** - Don't just fix, prevent recurrence

---

## 🚀 **Next Steps**

1. **Complete frontend-backend integration testing** (P0.1 priority)
2. **Audit remaining legacy files** for similar character issues
3. **Implement automated scanning** in development workflow
4. **Update project documentation** with encoding best practices
5. **Consider creating Bash script interface** for frontend testing (as planned)

---

**Remember:** In Tauri v2 + Leptos development, always suspect invisible characters when facing mysterious build failures that don't match typical configuration or dependency issues.

*This document should be referenced whenever similar mysterious build failures occur in future Tauri v2 projects.*

---

## 🧹 June 2025: Legacy Code Cleanup & Framework Audit

As part of ongoing Tauri v2 + Leptos maintenance, a dedicated PowerShell script (`scripts/audit_and_cleanup_framework.ps1`) was created and used to:
- Audit the codebase for correct Tauri v2 and Leptos usage
- Detect and remove legacy, backup, or confusing frontend/backend files
- Ensure no references to Tauri v1, Yew, Sycamore, or other frameworks remain

**Result:**
- All detected legacy files (e.g., `lib_broken_backup.rs`, `main_backup.rs`, `lib_complete.rs`) were deleted
- Codebase now strictly adheres to Tauri v2 + Leptos standards
- Script can be rerun at any time for future audits or after major refactors

**Best Practice:**
- Integrate this script into the regular development workflow, especially before releases or after large merges
- Keep documentation and tickets updated to reflect codebase hygiene milestones

*This cleanup milestone marks a new standard for ongoing clarity and maintainability in the project.*
