# 🔄 Migration Guide

**Step-by-step migration instructions for upgrading between versions of the Bad Character Scanner**

---

## 🎯 **Quick Migration Reference**

| From Version | To Version | Complexity | Time Required | Breaking Changes |
|--------------|------------|------------|---------------|------------------|
| **v0.3.0** | **v0.3.1** | **Easy** | **5 minutes** | None |
| **v0.2.x** | **v0.3.x** | **Medium** | **30 minutes** | Tauri v2 upgrade |
| **v0.1.x** | **v0.2.x+** | **Major** | **2+ hours** | Complete rewrite |

---

## 🚀 **Latest Migration: v0.3.0 → v0.3.1** ✅

### **Overview**
- **Type**: Documentation update only
- **Breaking Changes**: None
- **Data Migration**: Not required
- **Downtime**: None

### **Migration Steps**
```powershell
# 1. Update dependencies
cargo update
npm update

# 2. Rebuild project (optional)
cargo tauri build

# 3. Update documentation references (if you've customized docs)
# - Check for any hardcoded links to old documentation structure
# - Update any custom documentation to use new format
```

### **What Changed**
- ✅ **Documentation v2.0** - Complete restructure and improvement
- ✅ **Navigation System** - New quick navigation and cross-references
- ✅ **Developer Experience** - 15-minute onboarding process
- ✅ **Archive Organization** - Historical documents properly organized

### **Action Required**
- **None** - This is a documentation-only update
- **Optional**: Review new documentation structure for improved workflow

---

## 🏗️ **Major Migration: v0.2.x → v0.3.0**

### **Overview**
- **Type**: Architecture upgrade
- **Breaking Changes**: Tauri v2.5.x compatibility
- **Data Migration**: Configuration files
- **Downtime**: 30 minutes

### **Prerequisites**
```powershell
# Check current versions
cargo --version        # Rust 1.75+ required
node --version         # Node.js 18+ required
npm --version          # npm 9+ required
```

### **Step-by-Step Migration**

#### **1. Update Rust Toolchain**
```powershell
# Update Rust to latest stable
rustup update stable
rustup default stable

# Verify version
rustc --version  # Should be 1.75+
```

#### **2. Update Dependencies**
```powershell
# Update Tauri CLI
cargo install tauri-cli --version "^2.5" --locked

# Update Trunk
cargo install trunk --locked

# Clean previous builds
cargo clean
Remove-Item -Recurse -Force .\dist -ErrorAction SilentlyContinue
```

#### **3. Configuration Updates**

**Update `src-tauri/Cargo.toml`:**
```toml
[dependencies]
tauri = { version = "2.5", features = ["api-all"] }
# ... other dependencies remain the same
```

**Update `src-tauri/tauri.conf.json`:**
```json
{
  "productName": "Bad Character Scanner",
  "version": "0.3.0",
  "tauri": {
    "allowlist": {
      "all": true
    }
  }
}
```

#### **4. Code Updates**

**No code changes required** - The migration is primarily configuration-based.

#### **5. Build and Test**
```powershell
# Test development build
cargo tauri dev

# Test production build
cargo tauri build
```

### **Verification Checklist**
- [ ] Application starts without errors
- [ ] All analysis features work correctly
- [ ] File operations function properly
- [ ] UI renders correctly
- [ ] Export functionality works
- [ ] Cross-platform compatibility maintained

---

## 🌟 **Complete Migration: v0.1.x → v0.3.x**

### **Overview**
- **Type**: Complete rewrite
- **Breaking Changes**: Everything
- **Data Migration**: Full rebuild required
- **Downtime**: 2+ hours

### **Migration Strategy**
This is essentially a **fresh installation** due to the extensive changes.

#### **Recommended Approach**
1. **Export Data** (if any custom configurations exist)
2. **Fresh Clone** - Start with the latest codebase
3. **Rebuild Environment** - Follow current setup instructions
4. **Import Configurations** - Manually recreate any customizations

#### **Step-by-Step Process**

**1. Backup Current Installation**
```powershell
# Create backup directory
New-Item -ItemType Directory -Path ".\backup-v0.1.x" -Force

# Backup configuration files (if any)
Copy-Item -Recurse ".\src-tauri\tauri.conf.json" ".\backup-v0.1.x\" -ErrorAction SilentlyContinue
Copy-Item -Recurse ".\Cargo.toml" ".\backup-v0.1.x\" -ErrorAction SilentlyContinue
```

**2. Fresh Installation**
```powershell
# Remove old installation
Remove-Item -Recurse -Force .\target -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .\dist -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .\node_modules -ErrorAction SilentlyContinue

# Follow current installation guide
# See ONBOARDING.md for complete setup instructions
```

**3. Configuration Migration**
- **Manual Process**: Review backed-up configurations and manually apply to new version
- **No Automated Migration**: Due to extensive architectural changes

---

## 🛠️ **Common Migration Issues**

### **Tauri v2 Compatibility Issues**

**Problem**: Build errors related to Tauri APIs
```
Error: API `tauri::api::...` not found
```

**Solution**:
```powershell
# Update Tauri CLI to latest
cargo install tauri-cli --version "^2.5" --locked

# Clean and rebuild
cargo clean
cargo tauri build
```

### **Dependency Version Conflicts**

**Problem**: Cargo dependency resolution errors
```
Error: failed to select a version for `tauri`
```

**Solution**:
```powershell
# Update Cargo.lock
Remove-Item .\Cargo.lock -ErrorAction SilentlyContinue
cargo update

# Specific version resolution
cargo tree
```

### **Frontend Build Issues**

**Problem**: Trunk or WASM compilation errors
```
Error: failed to build frontend
```

**Solution**:
```powershell
# Update Trunk
cargo install trunk --locked

# Clear cache
trunk clean
trunk build
```

### **Configuration File Compatibility**

**Problem**: `tauri.conf.json` format changes
```
Error: invalid configuration format
```

**Solution**:
- Compare with reference configuration in current version
- Update to new schema format
- See `src-tauri/tauri.conf.json` in repository

---

## 🔍 **Migration Verification**

### **Post-Migration Testing Checklist**

#### **Functionality Tests**
- [ ] Application launches successfully
- [ ] File selection and loading works
- [ ] Analysis engine processes text correctly
- [ ] Results display properly
- [ ] Export functions work
- [ ] UI is responsive and styled correctly

#### **Performance Tests**
- [ ] Startup time < 2 seconds
- [ ] File processing speed maintained/improved
- [ ] Memory usage within expected bounds
- [ ] No memory leaks during extended use

#### **Cross-Platform Tests**
- [ ] Windows build works
- [ ] macOS build works (if applicable)
- [ ] Linux build works (if applicable)

### **Regression Testing**
```powershell
# Run all tests
cargo test

# Run specific test suites
cargo test --test integration_tests
cargo test --test backend_tests
```

---

## 🆘 **Migration Support**

### **Getting Help**

#### **Self-Service Resources**
1. **[📚 ONBOARDING.md](../ONBOARDING.md)** - Fresh setup guide
2. **[🔧 QUICK_FIX_GUIDE.md](../guides/QUICK_FIX_GUIDE.md)** - Common problems
3. **[⚡ QUICK_NAVIGATION.md](../QUICK_NAVIGATION.md)** - Find any document

#### **Documentation References**
- **[🏗️ ARCHITECTURE.md](ARCHITECTURE.md)** - System architecture
- **[📋 CHANGELOG.md](CHANGELOG.md)** - Detailed version changes
- **[🏗️ DEVELOPER_GUIDE.md](../DEVELOPER_GUIDE.md)** - Development setup

### **Troubleshooting Process**
1. **Check Prerequisites** - Verify all required tools and versions
2. **Review Logs** - Check build output and console errors
3. **Compare Configurations** - Ensure all config files match expected format
4. **Test Incremental Steps** - Verify each migration step individually
5. **Fallback Plan** - Keep backup for potential rollback

### **Common Solutions**

#### **Clean Build Environment**
```powershell
# Complete environment reset
cargo clean
Remove-Item -Recurse -Force .\target -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .\dist -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .\node_modules -ErrorAction SilentlyContinue
Remove-Item .\Cargo.lock -ErrorAction SilentlyContinue

# Reinstall dependencies
cargo update
npm install
```

#### **Version Verification**
```powershell
# Check all tool versions
cargo --version
rustc --version
node --version
npm --version
cargo tauri --version
trunk --version
```

---

## 📋 **Migration History**

### **Completed Migrations**
| Version Pair | Date | Type | Success Rate |
|--------------|------|------|--------------|
| v0.3.0 → v0.3.1 | Jun 2025 | Documentation | 100% |
| v0.2.x → v0.3.0 | Jun 2025 | Architecture | 95% |
| v0.1.x → v0.2.x | Dec 2024 | Feature rewrite | 90% |

### **Lessons Learned**
- **Documentation Updates** - Generally seamless with no user impact
- **Architecture Changes** - Require careful dependency management
- **Major Rewrites** - Fresh installation often more reliable than migration

---

## 🔮 **Future Migration Planning**

### **Upcoming Migrations**
- **v0.3.x → v0.4.0** - Machine learning integration (Medium complexity)
- **v0.4.x → v0.5.0** - Enterprise features (Low complexity)
- **v0.5.x → v1.0.0** - Platform stabilization (Low complexity)

### **Migration Philosophy**
- **Minimize Breaking Changes** - Maintain backward compatibility when possible
- **Clear Communication** - Detailed migration guides for all changes
- **Automated Testing** - Comprehensive validation of migration paths
- **Support Resources** - Multiple channels for migration assistance

---

*For the smoothest migration experience, always review the full changelog and test in a development environment before migrating production systems.*
