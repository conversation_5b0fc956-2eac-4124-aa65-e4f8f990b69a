// AI Detection module - Handles AI-generated content detection
// Extracted from main_module.rs to improve code organization

use crate::modules::data_structures::*;
use crate::modules::asset_manager::AssetManager;

/// Detect AI-generated content in the provided text
pub async fn detect_ai_content(content: String) -> Result<AIDetectionResult, String> {
    let asset_manager = AssetManager::new()?;
    
    if !asset_manager.has_ai_patterns() {
        return Ok(AIDetectionResult {
            overall_confidence: 0.0,
            ai_likelihood: "Unknown".to_string(),
            patterns_detected: 0,
            detected_patterns: Vec::new(),
            analysis_summary: "AI patterns not available for analysis".to_string(),
        });
    }
    
    let mut detected_patterns = Vec::new();
    let mut total_confidence = 0.0;
    let mut pattern_count = 0;
    
    // Analyze different AI pattern categories
    
    // 1. Code injection patterns
    let injection_patterns = asset_manager.get_code_injection_patterns();
    for pattern in injection_patterns {
        if let Ok(regex) = fancy_regex::Regex::new(&pattern.pattern) {
            for mat_result in regex.find_iter(&content) {
                if let Ok(mat) = mat_result {
                        let confidence = pattern.confidence_score.unwrap_or(0.5);
                        total_confidence += confidence;
                        pattern_count += 1;

                        detected_patterns.push(AIDetectionMatch {
                            pattern_name: pattern.name.clone(),
                            description: pattern.description.clone().unwrap_or_else(|| "AI code injection pattern detected".to_string()),
                            severity: pattern.severity.clone().unwrap_or_else(|| "medium".to_string()),
                            confidence,
                            start_position: mat.start(),
                            end_position: mat.end(),
                            matched_text: mat.as_str().to_string(),
                        });
                    }
                }
        }
    }
    
    // 2. Advanced homoglyph patterns
    let homoglyph_patterns = asset_manager.get_advanced_homoglyph_patterns();
    for pattern in homoglyph_patterns {
        if let Ok(regex) = fancy_regex::Regex::new(&pattern.pattern) {
            for mat_result in regex.find_iter(&content) {
                if let Ok(mat) = mat_result {
                    let confidence = pattern.confidence_score.unwrap_or(0.6);
                    total_confidence += confidence;
                    pattern_count += 1;

                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Advanced homoglyph pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "high".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 3. Steganography patterns
    let stego_patterns = asset_manager.get_steganography_patterns();
    for pattern in stego_patterns {
        if let Ok(regex) = fancy_regex::Regex::new(&pattern.pattern) {
            for mat_result in regex.find_iter(&content) {
                if let Ok(mat) = mat_result {
                    let confidence = pattern.confidence_score.unwrap_or(0.7);
                    total_confidence += confidence;
                    pattern_count += 1;

                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Steganography pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "high".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 4. Bidirectional attack patterns
    let bidi_patterns = asset_manager.get_bidirectional_attack_patterns();
    for pattern in bidi_patterns {
        if let Ok(regex) = fancy_regex::Regex::new(&pattern.pattern) {
            for mat_result in regex.find_iter(&content) {
                if let Ok(mat) = mat_result {
                    let confidence = pattern.confidence_score.unwrap_or(0.8);
                    total_confidence += confidence;
                    pattern_count += 1;

                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "Bidirectional attack pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "critical".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // 5. AI code patterns
    let code_patterns = asset_manager.get_ai_code_patterns();
    for pattern in code_patterns {
        if let Ok(regex) = fancy_regex::Regex::new(&pattern.pattern) {
            for mat_result in regex.find_iter(&content) {
                if let Ok(mat) = mat_result {
                    let confidence = pattern.confidence_score.unwrap_or(0.4);
                    total_confidence += confidence;
                    pattern_count += 1;

                    detected_patterns.push(AIDetectionMatch {
                        pattern_name: pattern.name.clone(),
                        description: pattern.description.clone().unwrap_or_else(|| "AI code pattern detected".to_string()),
                        severity: pattern.severity.clone().unwrap_or_else(|| "low".to_string()),
                        confidence,
                        start_position: mat.start(),
                        end_position: mat.end(),
                        matched_text: mat.as_str().to_string(),
                    });
                }
            }
        }
    }
    
    // Calculate overall confidence and likelihood
    let overall_confidence = if pattern_count > 0 {
        total_confidence / pattern_count as f64
    } else {
        0.0
    };
    
    let ai_likelihood = match overall_confidence {
        c if c >= 0.8 => "Very High",
        c if c >= 0.6 => "High",
        c if c >= 0.4 => "Medium", 
        c if c >= 0.2 => "Low",
        _ => "Very Low",
    }.to_string();
    
    let analysis_summary = if pattern_count == 0 {
        "No AI-generated patterns detected in the content.".to_string()
    } else {
        format!(
            "Detected {} AI patterns with an overall confidence of {:.1}%. Analysis suggests {} likelihood of AI-generated content.",
            pattern_count,
            overall_confidence * 100.0,
            ai_likelihood.to_lowercase()
        )
    };
    
    Ok(AIDetectionResult {
        overall_confidence,
        ai_likelihood,
        patterns_detected: detected_patterns.len(),
        detected_patterns,
        analysis_summary,
    })
}
