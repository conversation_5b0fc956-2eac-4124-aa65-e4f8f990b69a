@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark Mode Support and Custom Variables */
@layer base {
  :root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #e9ecef;
    --accent-blue: #0066cc;
    --accent-red: #e03c31;
    --accent-green: #28a745;
    --shadow-color: rgba(0, 0, 0, 0.1);
  }

  [data-theme="dark"] {
    /* Dark theme colors */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --border-color: #333333;
    --accent-blue: #4d9fff;
    --accent-red: #ff5555;
    --accent-green: #50c878;
    --shadow-color: rgba(0, 0, 0, 0.3);
  }

  * {
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced button styles */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2;
    @apply inline-flex items-center justify-center gap-2 cursor-pointer;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }

  .btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-color);
  }

  .btn-primary {
    background-color: var(--accent-blue);
    color: white;
    border-color: var(--accent-blue);
  }

  .btn-ghost {
    background-color: transparent;
    border: 1px solid var(--border-color);
  }

  /* Custom card styles */
  .card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 4px 16px var(--shadow-color);
  }

  /* Input styles */
  .input, textarea {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
  }

  .input:focus, textarea:focus {
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(77, 159, 255, 0.1);
  }

  /* Theme toggle button */
  .theme-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    padding: 0.5rem;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .theme-toggle:hover {
    background-color: var(--accent-blue);
    color: white;
    transform: rotate(180deg);
  }
}
