use leptos::*;
use super::AppSection;

#[component]
pub fn AppHeader(
    current_section: ReadSignal<AppSection>,
    set_current_section: WriteSignal<AppSection>,
) -> impl IntoView {
    view! {
        <header class="app-header">
            <div class="header-content">
                <div class="brand">
                    <h1>"Bad " <span style="color: var(--purple-primary)">"Character Scanner"</span></h1>
                </div>
                
                <nav class="nav-sections">
                    <button
                        class=move || {
                            let base = "nav-btn";
                            if current_section.get() == AppSection::TextAnalysis {
                                format!("{} active", base)
                            } else {
                                base.to_string()
                            }
                        }
                        on:click=move |_| set_current_section.set(AppSection::TextAnalysis)
                    >
                        <span class="nav-icon">"✏️"</span>
                        "Text Input"
                    </button>
                    
                    <button
                        class=move || {
                            let base = "nav-btn";
                            if current_section.get() == AppSection::CodebaseAnalysis {
                                format!("{} active", base)
                            } else {
                                base.to_string()
                            }
                        }
                        on:click=move |_| set_current_section.set(AppSection::CodebaseAnalysis)
                    >
                        <span class="nav-icon">"📁"</span>
                        "Code Base Fixing Tools"
                    </button>
                </nav>
                
                // Header actions removed: SettingsButton not found
            </div>
        </header>
    }
}
