use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::spawn_local;
use chrono::{DateTime, Utc};

// Tauri command bindings
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

// Helper functions for Tauri commands
async fn tauri_invoke_no_args(cmd: &str) -> Result<JsValue, JsValue> {
    let args = JsValue::NULL;
    Ok(invoke(cmd, args).await)
}

async fn tauri_invoke_with_args<T: Serialize>(cmd: &str, args: &T) -> Result<JsValue, JsValue> {
    let args_js = serde_wasm_bindgen::to_value(args)?;
    Ok(invoke(cmd, args_js).await)
}

// Data structures matching the backend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub unicode_name: String,
    pub code_point: String,
    pub category: String,
    pub position: usize,
    pub context: String,
    pub severity: String,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub total_characters: usize,
    pub total_bytes: usize,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub encoding_info: EncodingInfo,
    pub security_analysis: SecurityAnalysis,
    pub recommendations: Vec<String>,
    pub analysis_timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub analyzed_files: usize,
    pub total_issues: usize,
    pub file_details: Vec<FileAnalysisDetail>,
    pub summary: String,
    pub analysis_timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisDetail {
    pub file_path: String,
    pub suspicious_characters: usize,
    pub issues: Vec<String>,
    pub file_size: u64,
    pub encoding: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub risk_level: String,
    pub potential_threats: Vec<String>,
    pub mitigation_suggestions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncodingInfo {
    pub detected_encoding: String,
    pub confidence: f32,
    pub bom_detected: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressPayload {
    pub current: usize,
    pub total: usize,
    pub message: String,
}

#[component]
pub fn App() -> impl IntoView {
    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Leptos Tauri v2 - Bad Character Scanner"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>

        <Router>
            <Routes>
                <Route path="" view=HomePage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    // State management
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_results, set_analysis_results) = create_signal(Option::<AnalysisResults>::None);
    let (codebase_results, set_codebase_results) = create_signal(Option::<CodeBaseAnalysisResult>::None);
    let (selected_folder, set_selected_folder) = create_signal(Option::<String>::None);
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    let (error_message, set_error_message) = create_signal(Option::<String>::None);
    let (current_tab, set_current_tab) = create_signal("text".to_string());
    let (analysis_progress, set_analysis_progress) = create_signal(Option::<ProgressPayload>::None);

    // Analyze text using Tauri backend
    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_error_message.set(Some("Please enter some text to analyze.".to_string()));
            return;
        }

        set_is_analyzing.set(true);
        set_error_message.set(None);

        spawn_local(async move {
            let args = serde_json::json!({ "text": text });
            match tauri_invoke_with_args("analyze_characters", &args).await {
                Ok(result) => {
                    match serde_wasm_bindgen::from_value::<AnalysisResults>(result) {
                        Ok(analysis) => {
                            set_analysis_results.set(Some(analysis));
                        }
                        Err(e) => {
                            set_error_message.set(Some(format!("Failed to parse analysis results: {:?}", e)));
                        }
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Analysis failed: {:?}", e)));
                }
            }
            set_is_analyzing.set(false);
        });
    };

    // Select folder for codebase analysis
    let select_folder = move |_| {
        spawn_local(async move {
            match tauri_invoke_no_args("select_folder").await {
                Ok(result) => {
                    if let Ok(folder_path) = serde_wasm_bindgen::from_value::<Option<String>>(result) {
                        set_selected_folder.set(folder_path);
                    }
                }
                Err(e) => {
                    set_error_message.set(Some(format!("Failed to select folder: {:?}", e)));
                }
            }
        });
    };

    // Analyze codebase
    let analyze_codebase = move |_| {
        if let Some(folder_path) = selected_folder.get() {
            set_is_analyzing.set(true);
            set_error_message.set(None);
            set_analysis_progress.set(None);

            spawn_local(async move {
                let args = serde_json::json!({ "folder_path": folder_path });
                match tauri_invoke_with_args("analyze_codebase", &args).await {
                    Ok(result) => {
                        match serde_wasm_bindgen::from_value::<CodeBaseAnalysisResult>(result) {
                            Ok(analysis) => {
                                set_codebase_results.set(Some(analysis));
                            }
                            Err(e) => {
                                set_error_message.set(Some(format!("Failed to parse codebase analysis: {:?}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Codebase analysis failed: {:?}", e)));
                    }
                }
                set_is_analyzing.set(false);
            });
        }
    };

    // Export codebase report
    let export_report = move |format: &str| {
        if let Some(results) = codebase_results.get() {
            let format = format.to_string();
            spawn_local(async move {
                let args = serde_json::json!({
                    "analysisResult": results,
                    "format": format,
                    "output_path": ""
                });
                match tauri_invoke_with_args("export_codebase_report", &args).await {
                    Ok(_) => {
                        // Report exported successfully
                    }
                    Err(e) => {
                        set_error_message.set(Some(format!("Export failed: {:?}", e)));
                    }
                }
            });
        }
    };

    view! {
        <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="container mx-auto px-4 py-8">
                <div class="max-w-6xl mx-auto">
                    // Header
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-gray-900 mb-4">
                            "🔍 Bad Character Scanner"
                        </h1>
                        <p class="text-xl text-gray-600">
                            "Detect and analyze suspicious characters in text and codebases"
                        </p>
                    </div>

                    // Error message display
                    {move || error_message.get().map(|msg| view! {
                        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                            <span class="font-medium">"Error: "</span>
                            {msg}
                        </div>
                    })}

                    // Main card with tabs
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        // Tab navigation
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8 px-6">
                                <button
                                    class=move || format!("py-4 px-1 border-b-2 font-medium text-sm transition-colors {}",
                                        if current_tab.get() == "text" {
                                            "border-indigo-500 text-indigo-600"
                                        } else {
                                            "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                        }
                                    )
                                    on:click=move |_| set_current_tab.set("text".to_string())
                                >
                                    "📝 Text Analysis"
                                </button>
                                <button
                                    class=move || format!("py-4 px-1 border-b-2 font-medium text-sm transition-colors {}",
                                        if current_tab.get() == "codebase" {
                                            "border-indigo-500 text-indigo-600"
                                        } else {
                                            "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                        }
                                    )
                                    on:click=move |_| set_current_tab.set("codebase".to_string())
                                >
                                    "📁 Codebase Analysis"
                                </button>
                            </nav>
                        </div>

                        // Tab content
                        <div class="p-6">
                            {move || match current_tab.get().as_str() {
                                "text" => view! {
                                    <div>
                                        <label for="text-input" class="block text-sm font-medium text-gray-700 mb-2">
                                            "Enter text to analyze:"
                                        </label>
                                        <textarea
                                            id="text-input"
                                            rows="8"
                                            class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                                            placeholder="Paste your text here... (supports Unicode, control characters, and invisible characters)"
                                            on:input=move |ev| {
                                                set_input_text.set(event_target_value(&ev));
                                            }
                                            prop:value=input_text
                                        />
                                        
                                        <div class="mt-4 flex justify-between items-center">
                                            <div class="text-sm text-gray-500">
                                                "Characters: " {move || input_text.get().chars().count()}
                                                " | Bytes: " {move || input_text.get().len()}
                                            </div>
                                            <button
                                                on:click=analyze_text
                                                disabled=is_analyzing
                                                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50"
                                            >
                                                {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Text" }}
                                            </button>
                                        </div>
                                    </div>
                                }.into_view(),
                                "codebase" => view! {
                                    <div>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    "Select folder to analyze:"
                                                </label>
                                                <div class="flex items-center space-x-4">
                                                    <button
                                                        on:click=select_folder
                                                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                                                    >
                                                        "📁 Select Folder"
                                                    </button>
                                                    {move || selected_folder.get().map(|path| view! {
                                                        <span class="text-sm text-gray-600 font-mono">
                                                            {path}
                                                        </span>
                                                    })}
                                                </div>
                                            </div>
                                            
                                            <button
                                                on:click=analyze_codebase
                                                disabled=move || is_analyzing.get() || selected_folder.get().is_none()
                                                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors disabled:opacity-50"
                                            >
                                                {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze Codebase" }}
                                            </button>
                                            
                                            {move || codebase_results.get().map(|_| view! {
                                                <div class="flex space-x-2">
                                                    <button
                                                        on:click=move |_| export_report("json")
                                                        class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                                                    >
                                                        "📄 Export JSON"
                                                    </button>
                                                    <button
                                                        on:click=move |_| export_report("html")
                                                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                                    >
                                                        "🌐 Export HTML"
                                                    </button>
                                                </div>
                                            })}
                                        </div>
                                        
                                        {move || analysis_progress.get().map(|progress| view! {
                                            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                                                <div class="text-sm text-blue-600 mb-2">{progress.message}</div>
                                                <div class="w-full bg-blue-200 rounded-full h-2">
                                                    <div 
                                                        class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                                        style=format!("width: {}%", (progress.current as f32 / progress.total as f32) * 100.0)
                                                    ></div>
                                                </div>
                                                <div class="text-xs text-blue-500 mt-1">
                                                    {format!("{}/{}", progress.current, progress.total)}
                                                </div>
                                            </div>
                                        })}
                                    </div>
                                }.into_view(),
                                _ => view! { <div>"Unknown tab"</div> }.into_view()
                            }}
                        </div>
                    </div>

                    // Text Analysis Results display
                    {move || analysis_results.get().map(|results| view! {
                        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">"📊 Analysis Results"</h2>
                            
                            // Summary statistics
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{results.total_characters}</div>
                                    <div class="text-sm text-blue-800">"Total Characters"</div>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-red-600">{results.suspicious_characters.len()}</div>
                                    <div class="text-sm text-red-800">"Suspicious Characters"</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{results.total_bytes}</div>
                                    <div class="text-sm text-green-800">"Total Bytes"</div>
                                </div>
                            </div>

                            // Suspicious characters details
                            {(!results.suspicious_characters.is_empty()).then(|| view! {
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"🚨 Suspicious Characters Found"</h3>
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <div class="space-y-3">
                                            {results.suspicious_characters.iter().map(|char_info| view! {
                                                <div class="flex items-start space-x-3 p-3 bg-white rounded border">
                                                    <div class="font-mono text-lg">
                                                        {if char_info.character.is_control() { 
                                                            format!("\\u{{{:04X}}}", char_info.character as u32)
                                                        } else { 
                                                            char_info.character.to_string() 
                                                        }}
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="font-medium text-gray-900">{&char_info.unicode_name}</div>
                                                        <div class="text-sm text-gray-600">{&char_info.description}</div>
                                                        <div class="text-xs text-gray-500">
                                                            "Position: " {char_info.position} " | Code: " {&char_info.code_point}
                                                        </div>
                                                    </div>
                                                    <span class=format!("px-2 py-1 text-xs rounded {}", 
                                                        match char_info.severity.as_str() {
                                                            "high" => "bg-red-100 text-red-800",
                                                            "medium" => "bg-yellow-100 text-yellow-800",
                                                            _ => "bg-blue-100 text-blue-800"
                                                        })>{&char_info.severity}</span>
                                                </div>
                                            }).collect::<Vec<_>>()}
                                        </div>
                                    </div>
                                </div>
                            })}

                            // Recommendations
                            {(!results.recommendations.is_empty()).then(|| view! {
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"💡 Recommendations"</h3>
                                    <ul class="space-y-2">
                                        {results.recommendations.iter().map(|rec| view! {
                                            <li class="flex items-start space-x-2">
                                                <span class="text-indigo-500 mt-1">"•"</span>
                                                <span class="text-gray-700">{rec}</span>
                                            </li>
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            })}
                        </div>
                    })}

                    // Codebase Results display
                    {move || codebase_results.get().map(|results| view! {
                        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">"📁 Codebase Analysis Results"</h2>
                            
                            // Summary statistics
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{results.total_files}</div>
                                    <div class="text-sm text-blue-800">"Total Files"</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{results.analyzed_files}</div>
                                    <div class="text-sm text-green-800">"Analyzed Files"</div>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-red-600">{results.total_issues}</div>
                                    <div class="text-sm text-red-800">"Total Issues"</div>
                                </div>
                            </div>

                            // Files with issues
                            {let files_with_issues: Vec<_> = results.file_details.iter()
                                .filter(|file| file.suspicious_characters > 0)
                                .collect();
                            
                            (!files_with_issues.is_empty()).then(|| view! {
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"⚠️ Files with Issues"</h3>
                                    <div class="space-y-3">
                                        {files_with_issues.into_iter().map(|file| view! {
                                            <div class="border border-gray-200 rounded-lg p-4">
                                                <div class="flex justify-between items-start mb-2">
                                                    <div class="font-mono text-sm text-gray-900">{&file.file_path}</div>
                                                    <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                                                        {format!("{} issues", file.suspicious_characters)}
                                                    </span>
                                                </div>
                                                <div class="text-sm text-gray-600 mb-2">
                                                    "Size: " {file.file_size} " bytes | Encoding: " {&file.encoding}
                                                </div>
                                                {(!file.issues.is_empty()).then(|| view! {
                                                    <div class="space-y-1">
                                                        <div class="text-sm font-medium text-gray-700">"Issues:"</div>
                                                        <ul class="text-sm text-gray-600 space-y-1">
                                                            {file.issues.iter().map(|issue| view! {
                                                                <li class="flex items-start space-x-2">
                                                                    <span class="text-red-500 mt-0.5">"•"</span>
                                                                    <span>{issue}</span>
                                                                </li>
                                                            }).collect::<Vec<_>>()}
                                                        </ul>
                                                    </div>
                                                })}
                                            </div>
                                        }).collect::<Vec<_>>()}
                                    </div>
                                </div>
                            })}
                        </div>
                    })}

                    // Footer with additional information
                    <div class="mt-12 text-center text-gray-500 text-sm">
                        <div class="bg-white rounded-lg p-6 shadow-md">
                            <h3 class="font-semibold text-gray-700 mb-4">"🛡️ What We Detect"</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">"Invisible Characters:"</h4>
                                    <ul class="space-y-1">
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Zero-width spaces and joiners"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Bidirectional text override characters"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Non-breaking spaces and soft hyphens"
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-700 mb-2">"Control Characters:"</h4>
                                    <ul class="space-y-1">
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "ASCII control characters (0x00-0x1F)"
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <span class="mr-2">"•"</span>
                                            "Byte order marks (BOM) and line endings"
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}
