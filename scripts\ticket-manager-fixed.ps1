#!/usr/bin/env powershell
# Comprehensive ticket management and development workflow tools
# Compatible with PowerShell 5.1+

Write-Host "[TICKET MANAGER] ACCESSIBILITY PROJECT TICKET MANAGER" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent
$tickets_dir = Join-Path $project_root "docs\project-management\tickets"

function Show-TicketSummary {
    Write-Host "`n[OVERVIEW] TICKET SYSTEM OVERVIEW" -ForegroundColor Yellow
    Write-Host "=========================" -ForegroundColor Yellow
    
    if (-not (Test-Path $tickets_dir)) {
        Write-Host "[ERROR] Tickets directory not found: $tickets_dir" -ForegroundColor Red
        return
    }
    
    $tickets = Get-ChildItem $tickets_dir -Filter "*.md" -Recurse | Sort-Object Name
    $categories = @{}
    $priorities = @{}
    $statuses = @{}
    
    foreach ($ticket in $tickets) {
        $content = Get-Content $ticket.FullName -Raw -ErrorAction SilentlyContinue
        if (-not $content) { continue }
        
        $category = if ($ticket.BaseName -match '^([A-Z]+)-') { $matches[1] } else { 'OTHER' }
        if ($categories.ContainsKey($category)) { 
            $categories[$category] += 1 
        } else { 
            $categories[$category] = 1 
        }
        
        # Extract priority and status from content
        if ($content -match "Priority.*:\s*(\w+)") { 
            $priority = $matches[1]
            if ($priorities.ContainsKey($priority)) { 
                $priorities[$priority] += 1 
            } else { 
                $priorities[$priority] = 1 
            }
        }
        
        if ($content -match "Status.*:\s*(\w+)") { 
            $status = $matches[1]
            if ($statuses.ContainsKey($status)) { 
                $statuses[$status] += 1 
            } else { 
                $statuses[$status] = 1 
            }
        }
    }
    
    Write-Host "[INFO] Total Tickets: $($tickets.Count)" -ForegroundColor Green
    
    Write-Host "`n[CATEGORIES] By Category:" -ForegroundColor Yellow
    foreach ($cat in $categories.Keys | Sort-Object) {
        Write-Host "   $cat : $($categories[$cat])" -ForegroundColor White
    }
    
    if ($priorities.Count -gt 0) {
        Write-Host "`n[PRIORITY] By Priority:" -ForegroundColor Yellow
        foreach ($pri in $priorities.Keys | Sort-Object) {
            $color = switch ($pri.ToUpper()) {
                "HIGH" { "Red" }
                "MEDIUM" { "Yellow" } 
                "LOW" { "Green" }
                default { "White" }
            }
            Write-Host "   $pri : $($priorities[$pri])" -ForegroundColor $color
        }
    }
    
    if ($statuses.Count -gt 0) {
        Write-Host "`n[STATUS] By Status:" -ForegroundColor Yellow
        foreach ($status in $statuses.Keys | Sort-Object) {
            $color = switch ($status.ToUpper()) {
                "OPEN" { "Red" }
                "IN PROGRESS" { "Yellow" }
                "RESOLVED" { "Green" }
                "CLOSED" { "Green" }
                default { "White" }
            }
            Write-Host "   $status : $($statuses[$status])" -ForegroundColor $color
        }
    }
}

function Show-HighPriorityTickets {
    Write-Host "`n[HIGH PRIORITY] HIGH PRIORITY TICKETS" -ForegroundColor Red
    Write-Host "========================" -ForegroundColor Red
    
    $tickets = Get-ChildItem $tickets_dir -Filter "*.md" -Recurse | Sort-Object Name
    $high_priority_found = $false
    
    foreach ($ticket in $tickets) {
        $content = Get-Content $ticket.FullName -Raw -ErrorAction SilentlyContinue
        if (-not $content) { continue }
        
        if ($content -match "Priority.*:\s*(HIGH|High)" -and $content -notmatch "Status.*:\s*(RESOLVED|CLOSED|Resolved|Closed)") {
            $high_priority_found = $true
            Write-Host "`n[TICKET] $($ticket.BaseName)" -ForegroundColor Red
            
            # Extract title - match markdown headers
            if ($content -match "(?m)^#\s+(.+)") {
                Write-Host "   [TITLE] $($matches[1])" -ForegroundColor White
            }
            
            # Extract issue description
            if ($content -match "(?:Problem Description|Description)(?:\s*\*\*)?[:\s]*\n\n([^\n#]+)") {
                $desc = $matches[1].Trim()
                if ($desc.Length -gt 100) { 
                    $desc = $desc.Substring(0, 97) + "..." 
                }
                Write-Host "   [DESC] $desc" -ForegroundColor Gray
            }
        }
    }
    
    if (-not $high_priority_found) {
        Write-Host "[OK] No high priority tickets found!" -ForegroundColor Green
    }
}

function Show-AccessibilityTickets {
    Write-Host "`n[ACCESSIBILITY] ACCESSIBILITY-FOCUSED TICKETS" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    
    $tickets = Get-ChildItem $tickets_dir -Filter "*.md" -Recurse | Sort-Object Name
    $accessibility_found = $false
    
    $accessibility_keywords = @("accessibility", "dyslexia", "character", "bad char", "unicode", "homoglyph", "invisible")
    
    foreach ($ticket in $tickets) {
        $content = Get-Content $ticket.FullName -Raw -ErrorAction SilentlyContinue
        if (-not $content) { continue }
        
        $is_accessibility = $false
        
        foreach ($keyword in $accessibility_keywords) {
            if ($content -match $keyword) {
                $is_accessibility = $true
                break
            }
        }
        
        if ($is_accessibility) {
            $accessibility_found = $true
            Write-Host "`n[TICKET] $($ticket.BaseName)" -ForegroundColor Cyan
            
            if ($content -match "(?m)^#\s+(.+)") {
                Write-Host "   [TITLE] $($matches[1])" -ForegroundColor White
            }
        }
    }
    
    if (-not $accessibility_found) {
        Write-Host "[INFO] No specific accessibility tickets found in current tickets" -ForegroundColor Yellow
    }
}

function Create-NewTicket {
    param(
        [string]$Category = "FEATURE",
        [string]$Title,
        [string]$Priority = "MEDIUM"
    )
    
    if (-not $Title) {
        Write-Host "[ERROR] Please provide a ticket title" -ForegroundColor Red
        Write-Host "Usage: Create-NewTicket -Title 'Your ticket title' -Category 'FEATURE' -Priority 'HIGH'" -ForegroundColor Yellow
        return
    }
    
    # Find next ticket number for category
    $existing_tickets = Get-ChildItem $tickets_dir -Filter "$Category-*.md" -Recurse | ForEach-Object {
        if ($_.BaseName -match "$Category-(\d+)") {
            [int]$matches[1]
        }
    } | Sort-Object -Descending
    
    $next_number = if ($existing_tickets) { $existing_tickets[0] + 1 } else { 1 }
    $ticket_id = "$Category-$next_number"
    $ticket_file = Join-Path $tickets_dir "$ticket_id.md"
    
    $ticket_template = @"
# $ticket_id`: $Title

## Priority: $Priority
**Category:** $Category  
**Created:** $(Get-Date -Format 'yyyy-MM-dd')  
**Status:** Open  

---

## Description
<!-- Describe the feature/bug/task here -->

## Current State
<!-- What is the current situation? -->

## Expected Outcome
<!-- What should happen after this is resolved? -->

## Implementation Notes
<!-- Technical details, considerations, etc. -->

## Definition of Done
- [ ] <!-- Add specific completion criteria -->
- [ ] Testing completed
- [ ] Documentation updated

## Tags
`$($Category.ToLower())`, `accessibility`, `bad-character-scanner`

---
*This ticket is part of the Bad Character Scanner accessibility project helping people with dyslexia.*
"@
    
    $ticket_template | Out-File -FilePath $ticket_file -Encoding UTF8
    Write-Host "[OK] Created new ticket: $ticket_id" -ForegroundColor Green
    Write-Host "[FILE] $ticket_file" -ForegroundColor White
}

# Command line interface
param(
    [string]$Action = "summary",
    [string]$Title,
    [string]$Category = "FEATURE", 
    [string]$Priority = "MEDIUM"
)

switch ($Action.ToLower()) {
    "summary" { 
        Show-TicketSummary 
    }
    "high" { 
        Show-HighPriorityTickets 
    }
    "accessibility" { 
        Show-AccessibilityTickets 
    }
    "create" { 
        Create-NewTicket -Title $Title -Category $Category -Priority $Priority
    }
    "help" {
        Write-Host "[HELP] TICKET MANAGER COMMANDS" -ForegroundColor Cyan
        Write-Host "=========================" -ForegroundColor Cyan
        Write-Host ".\scripts\ticket-manager-fixed.ps1                    # Show summary" -ForegroundColor White
        Write-Host ".\scripts\ticket-manager-fixed.ps1 -Action high       # Show high priority" -ForegroundColor White
        Write-Host ".\scripts\ticket-manager-fixed.ps1 -Action accessibility # Show accessibility tickets" -ForegroundColor White
        Write-Host ".\scripts\ticket-manager-fixed.ps1 -Action create -Title 'New Feature' -Category 'FEATURE' -Priority 'HIGH'" -ForegroundColor White
    }
    default {
        Write-Host "[ERROR] Unknown action: $Action" -ForegroundColor Red
        Write-Host "Use -Action help for available commands" -ForegroundColor Yellow
    }
}

Write-Host "`nFighting for accessibility, one ticket at a time!" -ForegroundColor Green