#!/usr/bin/env powershell
# Install BCS command alias for easy access

Write-Host "`n📦 INSTALLING BCS COMMAND" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$script_path = Join-Path $PSScriptRoot "bcs.ps1"

# Create the function
$alias_function = @"
function bcs {
    & '$script_path' @args
}
"@

# Check if PowerShell profile exists
$profile_path = $PROFILE.CurrentUserAllHosts
$profile_dir = Split-Path $profile_path -Parent

if (-not (Test-Path $profile_dir)) {
    Write-Host "Creating PowerShell profile directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $profile_dir -Force | Out-Null
}

# Check if already installed
$installed = $false
if (Test-Path $profile_path) {
    $content = Get-Content $profile_path -Raw
    if ($content -match "function bcs") {
        $installed = $true
    }
}

if ($installed) {
    Write-Host "✅ BCS command is already installed!" -ForegroundColor Green
} else {
    Write-Host "Adding BCS command to PowerShell profile..." -ForegroundColor Yellow
    
    # Add to profile
    Add-Content -Path $profile_path -Value "`n# Bad Character Scanner CLI"
    Add-Content -Path $profile_path -Value $alias_function
    
    Write-Host "✅ BCS command installed successfully!" -ForegroundColor Green
}

Write-Host "`n📝 Usage:" -ForegroundColor Yellow
Write-Host "  Restart your PowerShell session or run:" -ForegroundColor Gray
Write-Host "  . `$PROFILE" -ForegroundColor White
Write-Host "`n  Then you can use:" -ForegroundColor Gray
Write-Host "  bcs start" -ForegroundColor White
Write-Host "  bcs doctor" -ForegroundColor White
Write-Host "  bcs help" -ForegroundColor White

Write-Host "`n🦸 Fighting for accessibility!" -ForegroundColor Cyan