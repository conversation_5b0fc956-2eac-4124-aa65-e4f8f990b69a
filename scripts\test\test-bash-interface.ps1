# Test Script for Bash Interface - Bad Character Scanner
# This script thoroughly tests the CLI analyzer and bash script interface
# with verbose output and comprehensive error handling

param(
    [switch]$Verbose = $false,
    [switch]$CleanOnly = $false,
    [string]$TestDir = "test_files"
)

# PowerShell equivalent of bash colors
$RED = "`e[31m"
$GREEN = "`e[32m"
$YELLOW = "`e[33m"
$BLUE = "`e[34m"
$PURPLE = "`e[35m"
$CYAN = "`e[36m"
$NC = "`e[0m"

# Emojis for better UX
$CHECKMARK = "✅"
$CROSS = "❌"
$WARNING = "⚠️"
$INFO = "ℹ️"
$GEAR = "⚙️"
$ROCKET = "🚀"

$SCRIPT_DIR = $PSScriptRoot
$PROJECT_ROOT = $SCRIPT_DIR
$CLI_BINARY = "$PROJECT_ROOT\target\debug\analyzer_cli.exe"
$BASH_SCRIPT = "$PROJECT_ROOT\scripts\codebase_analyzer.sh"
$TEST_DIR_PATH = "$PROJECT_ROOT\$TestDir"
$RESULTS_DIR = "$PROJECT_ROOT\test_results"

# Test counters
$script:TestsPassed = 0
$script:TestsFailed = 0
$script:TestsTotal = 0

function Write-Log {
    param([string]$Level, [string]$Message, [switch]$Force)
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    if ($Verbose -or $Force) {
        switch ($Level) {
            "INFO" { Write-Host "${INFO} ${BLUE}[INFO]${NC} $Message" }
            "SUCCESS" { Write-Host "${CHECKMARK} ${GREEN}[SUCCESS]${NC} $Message" }
            "WARNING" { Write-Host "${WARNING} ${YELLOW}[WARNING]${NC} $Message" }
            "ERROR" { Write-Host "${CROSS} ${RED}[ERROR]${NC} $Message" }
            "DEBUG" { if ($Verbose) { Write-Host "${GEAR} ${PURPLE}[DEBUG]${NC} $Message" } }
            default { Write-Host "$Message" }
        }
    }
}

function Test-Function {
    param(
        [string]$TestName,
        [scriptblock]$TestBlock,
        [string]$Description = ""
    )
    
    $script:TestsTotal++
    Write-Log "INFO" "🧪 Running Test: $TestName" -Force
    if ($Description) {
        Write-Log "INFO" "   Description: $Description"
    }
    
    try {
        $result = & $TestBlock
        if ($result) {
            $script:TestsPassed++
            Write-Log "SUCCESS" "✓ $TestName PASSED" -Force
            return $true
        } else {
            $script:TestsFailed++
            Write-Log "ERROR" "✗ $TestName FAILED" -Force
            return $false
        }
    } catch {
        $script:TestsFailed++
        Write-Log "ERROR" "✗ $TestName FAILED with exception: $($_.Exception.Message)" -Force
        return $false
    }
}

function Setup-TestEnvironment {
    Write-Log "INFO" "${ROCKET} Setting up test environment..." -Force
    
    # Create test directories
    @($TEST_DIR_PATH, $RESULTS_DIR) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
            Write-Log "INFO" "Created directory: $_"
        }
    }
    
    # Create test files with various problematic characters
    $testFiles = @{
        "clean_file.txt" = "This is a clean file with no bad characters.`nJust normal text here.`nNothing suspicious at all!"
        
        "bad_chars_simple.txt" = @"
This file has some bad characters:
Zero-width space: Test​word (U+200B)
Non-breaking space: word word (U+00A0)
Replacement char: � (U+FFFD)
"@
        
        "bad_chars_complex.txt" = @"
This file simulates a document with various problematic Unicode characters.
It includes a Zero Width Space here ->​<- (U+200B) which is invisible.
Sometimes, text might contain a No-Break Space like this: word word (U+00A0) instead of a regular space.
Be careful with directional overrides! This text ->‮sdrawkcab si siht‬<- (U+202E) is an example.

Soft hyphens (U+00AD) are tricky: hy­phen­a­tion. They should not always be visible.
A Zero Width Non-Joiner (U+200C) can affect ligatures, like in fi‌ve (f‌i).
If data gets corrupted, you might see a Replacement Character: � (U+FFFD).

This file also contains a few control characters that might be problematic:
A null character: ->$([char]0)<- (U+0000).
And a Form Feed character here ->$([char]12)<- (U+000C) which might cause a page break.
Finally, an Ideographic Space ->　<- (U+3000) which is wider than a normal space.
End of mixed severity test.
"@
        
        "test_script.js" = @"
// JavaScript file with bad characters
function testFunction() {
    let badString = "test​invisible"; // Contains zero-width space
    console.log("Processing: " + badString);
    return badString.replace(/\s/g, "_");
}
"@
        
        "test_config.json" = @"
{
    "name": "test​config",
    "settings": {
        "enable_feature": true,
        "timeout": 5000
    }
}
"@
    }
    
    foreach ($file in $testFiles.GetEnumerator()) {
        $filePath = Join-Path $TEST_DIR_PATH $file.Key
        $file.Value | Out-File -FilePath $filePath -Encoding UTF8 -Force
        Write-Log "DEBUG" "Created test file: $filePath"
    }
    
    Write-Log "SUCCESS" "Test environment setup complete!" -Force
}

function Test-CLIBinaryExists {
    if (Test-Path $CLI_BINARY) {
        Write-Log "SUCCESS" "CLI binary found at: $CLI_BINARY"
        return $true
    } else {
        Write-Log "ERROR" "CLI binary not found at: $CLI_BINARY"
        Write-Log "INFO" "Attempting to build CLI binary..."
        
        Push-Location "$PROJECT_ROOT\src-tauri"
        try {
            $buildResult = & cargo build --bin analyzer_cli 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Log "SUCCESS" "CLI binary built successfully"
                return Test-Path $CLI_BINARY
            } else {
                Write-Log "ERROR" "Failed to build CLI binary: $buildResult"
                return $false
            }
        } finally {
            Pop-Location
        }
    }
}

function Test-CLIBasicUsage {
    Write-Log "INFO" "Testing CLI basic usage and help..."
    
    try {
        $helpOutput = & $CLI_BINARY 2>&1
        Write-Log "DEBUG" "CLI help output: $helpOutput"
        
        # Should show usage information and exit with code 1
        if ($LASTEXITCODE -eq 1 -and $helpOutput -match "Usage:") {
            return $true
        } else {
            Write-Log "ERROR" "CLI help output unexpected. Exit code: $LASTEXITCODE"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Failed to run CLI binary: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIAnalyzeCleanFile {
    $testFile = Join-Path $TEST_DIR_PATH "clean_file.txt"
    Write-Log "INFO" "Testing CLI analysis of clean file: $testFile"
    
    try {
        $output = & $CLI_BINARY analyze $testFile json 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "DEBUG" "CLI output for clean file: $output"
            
            # Parse JSON to verify structure
            try {
                $jsonResult = $output | ConvertFrom-Json
                if ($jsonResult.total_files -eq 1 -and $jsonResult.files_with_issues -eq 0) {
                    Write-Log "SUCCESS" "Clean file correctly identified as clean"
                    return $true
                } else {
                    Write-Log "ERROR" "Clean file analysis results unexpected"
                    return $false
                }
            } catch {
                Write-Log "ERROR" "Failed to parse JSON output: $($_.Exception.Message)"
                return $false
            }
        } else {
            Write-Log "ERROR" "CLI failed with exit code: $LASTEXITCODE, Output: $output"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Exception during CLI test: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIAnalyzeBadFile {
    $testFile = Join-Path $TEST_DIR_PATH "bad_chars_complex.txt"
    Write-Log "INFO" "Testing CLI analysis of file with bad characters: $testFile"
    
    try {
        $output = & $CLI_BINARY analyze $testFile json 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "DEBUG" "CLI output for bad file: $output"
            
            try {
                $jsonResult = $output | ConvertFrom-Json
                if ($jsonResult.total_files -eq 1 -and $jsonResult.files_with_issues -gt 0 -and $jsonResult.total_suspicious_chars -gt 0) {
                    Write-Log "SUCCESS" "Bad characters correctly detected. Found $($jsonResult.total_suspicious_chars) suspicious characters"
                    Write-Log "INFO" "Issues found: $($jsonResult.file_details[0].issues -join ', ')"
                    return $true
                } else {
                    Write-Log "ERROR" "Bad file analysis results unexpected. Files with issues: $($jsonResult.files_with_issues), Suspicious chars: $($jsonResult.total_suspicious_chars)"
                    return $false
                }
            } catch {
                Write-Log "ERROR" "Failed to parse JSON output: $($_.Exception.Message)"
                return $false
            }
        } else {
            Write-Log "ERROR" "CLI failed with exit code: $LASTEXITCODE, Output: $output"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Exception during CLI test: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIOutputFormats {
    $testFile = Join-Path $TEST_DIR_PATH "bad_chars_simple.txt"
    $formats = @("json", "text", "markdown")
    
    foreach ($format in $formats) {
        Write-Log "INFO" "Testing CLI output format: $format"
        
        try {
            $output = & $CLI_BINARY analyze $testFile $format 2>&1
            if ($LASTEXITCODE -eq 0 -and $output.Length -gt 0) {
                Write-Log "SUCCESS" "Format $format produced output"
                Write-Log "DEBUG" "Sample output ($format): $($output[0..2] -join '; ')..."
            } else {
                Write-Log "ERROR" "Format $format failed or produced no output"
                return $false
            }
        } catch {
            Write-Log "ERROR" "Exception testing format $format : $($_.Exception.Message)"
            return $false
        }
    }
    
    return $true
}

function Test-CLIExportFunctionality {
    Write-Log "INFO" "Testing CLI export functionality..."
    
    # First, create a JSON result file to export
    $testFile = Join-Path $TEST_DIR_PATH "bad_chars_simple.txt"
    $jsonOutputFile = Join-Path $RESULTS_DIR "test_results.json"
    
    try {
        # Generate JSON analysis
        $jsonOutput = & $CLI_BINARY analyze $testFile json 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "ERROR" "Failed to generate JSON for export test"
            return $false
        }
        
        # Save to file
        $jsonOutput | Out-File -FilePath $jsonOutputFile -Encoding UTF8
        
        # Test exporting to different formats
        $exportFormats = @("text", "markdown")
        foreach ($format in $exportFormats) {
            Write-Log "INFO" "Testing export to $format format"
            
            $exportOutput = & $CLI_BINARY export $jsonOutputFile $format 2>&1
            if ($LASTEXITCODE -eq 0 -and $exportOutput.Length -gt 0) {
                Write-Log "SUCCESS" "Export to $format successful"
                
                # Save exported results
                $exportFile = Join-Path $RESULTS_DIR "exported_results.$format"
                $exportOutput | Out-File -FilePath $exportFile -Encoding UTF8
                Write-Log "DEBUG" "Exported results saved to: $exportFile"
            } else {
                Write-Log "ERROR" "Export to $format failed"
                return $false
            }
        }
        
        return $true
    } catch {
        Write-Log "ERROR" "Exception during export test: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIErrorHandling {
    Write-Log "INFO" "Testing CLI error handling..."
    
    # Test non-existent file
    $nonExistentFile = Join-Path $TEST_DIR_PATH "does_not_exist.txt"
    try {
        $output = & $CLI_BINARY analyze $nonExistentFile json 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "SUCCESS" "CLI correctly handled non-existent file with exit code: $LASTEXITCODE"
        } else {
            Write-Log "ERROR" "CLI should have failed for non-existent file"
            return $false
        }
    } catch {
        Write-Log "SUCCESS" "CLI correctly threw exception for non-existent file"
    }
    
    # Test invalid format
    $testFile = Join-Path $TEST_DIR_PATH "clean_file.txt"
    try {
        $output = & $CLI_BINARY analyze $testFile invalid_format 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "SUCCESS" "CLI correctly handled invalid format with exit code: $LASTEXITCODE"
        } else {
            Write-Log "ERROR" "CLI should have failed for invalid format"
            return $false
        }
    } catch {
        Write-Log "SUCCESS" "CLI correctly threw exception for invalid format"
    }
    
    # Test invalid command
    try {
        $output = & $CLI_BINARY invalid_command $testFile 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "SUCCESS" "CLI correctly handled invalid command with exit code: $LASTEXITCODE"
        } else {
            Write-Log "ERROR" "CLI should have failed for invalid command"
            return $false
        }
    } catch {
        Write-Log "SUCCESS" "CLI correctly threw exception for invalid command"
    }
    
    return $true
}

function Test-BashScriptExists {
    if (Test-Path $BASH_SCRIPT) {
        Write-Log "SUCCESS" "Bash script found at: $BASH_SCRIPT"
        return $true
    } else {
        Write-Log "ERROR" "Bash script not found at: $BASH_SCRIPT"
        return $false
    }
}

function Test-BashScriptIntegration {
    Write-Log "INFO" "Testing Bash script integration (if bash available)..."
    
    # Check if bash/WSL is available
    try {
        $bashCheck = & bash --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "SUCCESS" "Bash is available for testing"
            
            # Test the bash script
            $bashScriptTest = & bash $BASH_SCRIPT test 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Log "SUCCESS" "Bash script test command executed successfully"
                return $true
            } else {
                Write-Log "WARNING" "Bash script test failed, but this might be expected"
                Write-Log "DEBUG" "Bash script output: $bashScriptTest"
                return $true  # Don't fail the test suite for bash script issues
            }
        } else {
            Write-Log "WARNING" "Bash not available, skipping bash script integration test"
            return $true  # Don't fail if bash isn't available
        }
    } catch {
        Write-Log "WARNING" "Could not test bash script integration: $($_.Exception.Message)"
        return $true  # Don't fail if bash testing fails
    }
}

function Test-PerformanceAndStress {
    Write-Log "INFO" "Running performance and stress tests..."
    
    # Create a larger test file
    $largeTestFile = Join-Path $TEST_DIR_PATH "large_test_file.txt"
    $largeContent = @()
    for ($i = 1; $i -le 100; $i++) {
        $largeContent += "Line $i with some​hidden​characters and normal text. "
    }
    $largeContent -join "`n" | Out-File -FilePath $largeTestFile -Encoding UTF8
    
    Write-Log "INFO" "Testing CLI performance on larger file..."
    try {
        $startTime = Get-Date
        $output = & $CLI_BINARY analyze $largeTestFile json 2>&1
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "SUCCESS" "Large file analysis completed in ${duration}ms"
            
            # Parse results
            try {
                $jsonResult = $output | ConvertFrom-Json
                Write-Log "INFO" "Performance stats - Files: $($jsonResult.total_files), Suspicious chars: $($jsonResult.total_suspicious_chars), Health score: $($jsonResult.health_score)"
                return $true
            } catch {
                Write-Log "ERROR" "Could not parse performance test results"
                return $false
            }
        } else {
            Write-Log "ERROR" "Large file analysis failed"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Performance test failed: $($_.Exception.Message)"
        return $false
    }
}

function Cleanup-TestEnvironment {
    if ($CleanOnly) {
        Write-Log "INFO" "Cleaning up test environment..." -Force
        
        @($TEST_DIR_PATH, $RESULTS_DIR) | ForEach-Object {
            if (Test-Path $_) {
                Remove-Item -Path $_ -Recurse -Force
                Write-Log "SUCCESS" "Removed: $_" -Force
            }
        }
        
        Write-Log "SUCCESS" "Cleanup complete!" -Force
        return
    }
}

function Show-TestSummary {
    Write-Log "INFO" "${ROCKET} TEST SUMMARY" -Force
    Write-Log "INFO" "=================" -Force
    Write-Log "SUCCESS" "Tests Passed: $script:TestsPassed" -Force
    Write-Log "ERROR" "Tests Failed: $script:TestsFailed" -Force
    Write-Log "INFO" "Total Tests: $script:TestsTotal" -Force
    
    $successRate = if ($script:TestsTotal -gt 0) { ($script:TestsPassed / $script:TestsTotal) * 100 } else { 0 }
    Write-Log "INFO" "Success Rate: $([math]::Round($successRate, 2))%" -Force
    
    if ($script:TestsFailed -eq 0) {
        Write-Log "SUCCESS" "${CHECKMARK} All tests passed! Bash Script Interface is working correctly." -Force
    } else {
        Write-Log "WARNING" "${WARNING} Some tests failed. Review the output above for details." -Force
    }
}

# Main execution
Write-Host "${ROCKET} ${CYAN}Bad Character Scanner - Bash Interface Test Suite${NC}" 
Write-Host "================================================================="

if ($CleanOnly) {
    Cleanup-TestEnvironment
    exit 0
}

Write-Log "INFO" "Starting comprehensive test suite..." -Force
Write-Log "INFO" "Verbose mode: $Verbose" -Force

# Setup
Setup-TestEnvironment

# Run all tests
Test-Function "CLI Binary Exists" { Test-CLIBinaryExists } "Check if the CLI binary exists or can be built"
Test-Function "CLI Basic Usage" { Test-CLIBasicUsage } "Test CLI help and basic command structure"
Test-Function "CLI Analyze Clean File" { Test-CLIAnalyzeCleanFile } "Test analysis of a file without bad characters"
Test-Function "CLI Analyze Bad File" { Test-CLIAnalyzeBadFile } "Test analysis of a file with problematic characters"
Test-Function "CLI Output Formats" { Test-CLIOutputFormats } "Test all supported output formats (JSON, text, markdown)"
Test-Function "CLI Export Functionality" { Test-CLIExportFunctionality } "Test the export command for different formats"
Test-Function "CLI Error Handling" { Test-CLIErrorHandling } "Test error handling for invalid inputs"
Test-Function "Bash Script Exists" { Test-BashScriptExists } "Check if the bash script wrapper exists"
Test-Function "Bash Script Integration" { Test-BashScriptIntegration } "Test bash script integration (if bash available)"
Test-Function "Performance and Stress" { Test-PerformanceAndStress } "Test performance with larger files"

# Show summary
Show-TestSummary

# Exit with appropriate code
exit $script:TestsFailed
