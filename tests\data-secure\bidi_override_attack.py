# This file contains BIDIRECTIONAL OVERRIDE attacks
# These can reverse text direction and hide malicious code

def authenticate_user():
    # The string below contains bidirectional override
    username = "admin‮"
    
    # Hidden bidirectional embedding
    password = "secret‭hidden‬"
    
    # Pop directional isolate attack
    token = "eyJ0eXAiOiJKV1Qi⁦.malicious_payload⁩.signature"
    
    # First strong isolate
    api_endpoint = "https://api.example.com⁨/malicious⁦/path"
    
    return {
        "user": username,
        "pass": password,
        "token": token,
        "endpoint": api_endpoint
    }

# Clean version for reference
def clean_authenticate_user():
    username = "admin"
    password = "secret_hidden"
    token = "eyJ0eXAiOiJKV1Qi.payload.signature"
    api_endpoint = "https://api.example.com/safe/path"
    
    return {
        "user": username,
        "pass": password,
        "token": token,
        "endpoint": api_endpoint
    }
