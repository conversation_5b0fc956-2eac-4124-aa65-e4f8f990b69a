# SETUP-1 - Leptos Project Structure and Core Setup

**Status:** 🟡 In Progress  
**Priority:** High  
**Created:** 2025-05-27  
**Updated:** 2025-05-27  
**Assigned To:** @dev  
**Related Issues:** ARCH-1, CORE-1, PWA-1

## Description

Set up the complete Leptos-based project structure for the Bad Character Scanner, establishing the foundation for both PWA and Tauri desktop development with shared code architecture.

## Acceptance Criteria

- [x] Project structure created with proper Leptos setup
- [x] Tauri v2 configuration completed
- [x] Basic Trunk build configuration
- [ ] Core Leptos components scaffolded
- [ ] Development environment optimized
- [ ] CI/CD pipeline configured
- [ ] Testing framework established

## Current Project Structure

```
laptos-tauri-v2/
├── .github/                  # GitHub workflows and automation
│   └── workflows/
│       ├── ci.yml          # Continuous integration
│       ├── cd.yml          # Continuous deployment
│       └── dependabot.yml  # Dependency updates
├── docs/                     # Project documentation
│   ├── tickets/            # Development tickets
│   ├── ARCHITECTURE.md     # System architecture
│   ├── Framework_issue.md  # Framework analysis
│   └── build_issues.md     # Build troubleshooting
├── src/                      # Leptos frontend source
│   ├── components/         # Reusable UI components
│   │   ├── common/         # Common UI elements
│   │   ├── scanner/        # Scanner-specific components
│   │   ├── layout/         # Layout components
│   │   └── pwa/           # PWA-specific components
│   ├── pages/             # Application pages/routes
│   ├── hooks/             # Custom Leptos hooks
│   ├── utils/             # Utility functions
│   ├── types/             # Type definitions
│   ├── lib.rs            # Main application component
│   └── main.rs           # Application entry point
├── src-tauri/               # Tauri backend (Rust)
│   ├── src/
│   │   ├── main.rs       # Tauri application entry
│   │   ├── scanner/      # Character scanning logic
│   │   ├── api/          # Tauri command APIs
│   │   └── utils/        # Backend utilities
│   ├── Cargo.toml        # Backend dependencies
│   └── tauri.config.json # Tauri configuration
├── public/                  # Static assets for PWA
│   ├── icons/             # PWA icons
│   ├── manifest.json      # PWA manifest
│   └── sw.js             # Service worker (if needed)
├── dist/                    # Build output (generated)
├── target/                  # Rust build cache
├── index.html              # HTML entry point
├── Cargo.toml              # Workspace configuration
├── Trunk.toml              # Trunk build configuration
└── package.json            # Node.js dependencies for tooling
```

## Implementation Tasks

### 1. Core Leptos Setup ✅
- [x] Workspace Cargo.toml configuration
- [x] Leptos dependencies and features
- [x] Basic app structure with lib.rs and main.rs
- [x] Trunk configuration for WASM builds
- [x] HTML entry point with PWA meta tags

### 2. Tauri Integration ✅  
- [x] Tauri v2 configuration
- [x] Backend project structure
- [x] Command API setup
- [x] Development workflow configuration

### 3. Component Architecture 🔄
- [ ] Base component traits and interfaces
- [ ] Layout system with header, footer, sidebar
- [ ] Scanner components (input, results, controls)
- [ ] Common UI components (buttons, modals, etc.)
- [ ] PWA-specific components

### 4. State Management
- [ ] Global app state with Leptos contexts
- [ ] Local storage integration
- [ ] Theme management system
- [ ] User preferences handling

### 5. Build and Development Tools
- [ ] Trunk optimization for development
- [ ] Hot reload configuration
- [ ] Production build optimization
- [ ] Asset handling and bundling

### 6. Testing Infrastructure
- [ ] Unit testing setup with `wasm-bindgen-test`
- [ ] Component testing framework
- [ ] Integration testing for Tauri commands
- [ ] End-to-end testing setup

## Dependencies Status

### Frontend (Leptos) ✅
```toml
[dependencies]
leptos = { version = "0.6", features = ["csr", "nightly"] }
leptos_router = "0.6"
leptos_meta = "0.6"
web-sys = "0.3"
wasm-bindgen = "0.2"
console_error_panic_hook = "0.1"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"
```

### Backend (Tauri) ✅
```toml
[dependencies]
tauri = { version = "2.0", features = ["api-all"] }
tauri-build = { version = "2.0", features = [] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
anyhow = "1.0"
```

### Development Tools
```toml
[build-dependencies]
tauri-build = { version = "2.0", features = [] }
```

## Development Workflow

### Current Commands ✅
- `cargo tauri dev` - Start development with hot reload
- `cargo tauri build` - Build for production
- `trunk serve` - Frontend-only development
- `trunk build` - Frontend-only build

### Planned Commands
- `cargo test` - Run all tests
- `cargo clippy` - Lint checking
- `cargo fmt` - Code formatting
- `just dev` - Alternative development command
- `just build` - Alternative build command
- `just test` - Comprehensive testing

## Next Steps

1. **Immediate (This Sprint)**
   - [ ] Create base component structure
   - [ ] Implement basic layout system
   - [ ] Set up routing with leptos_router
   - [ ] Configure theme system

2. **Short Term (Next Sprint)**
   - [ ] Implement scanner core components
   - [ ] Add PWA manifest and service worker
   - [ ] Set up state management patterns
   - [ ] Create testing framework

3. **Medium Term**
   - [ ] Add comprehensive error handling
   - [ ] Implement offline functionality
   - [ ] Optimize build and bundle size
   - [ ] Add performance monitoring

---
*Last updated: 2025-05-27*
