# Modularization Plan for src/lib.rs

## Current State Analysis
- **File Size**: 383 lines (manageable but can be improved)
- **Main Issues**: Mixed concerns, large HomePage component, inline Tauri bindings
- **Architecture**: Monolithic structure with UI, business logic, and data structures combined

## Proposed Module Structure

### 1. **Core Module Structure**
```
src/
├── lib.rs                    # Main entry point (reduced to ~50 lines)
├── app.rs                    # App component and routing
├── types/                    # Data structures and types
│   ├── mod.rs
│   ├── analysis.rs          # AnalysisResults, CharacterInfo
│   └── codebase.rs          # CodeBaseAnalysisResult
├── components/               # UI Components
│   ├── mod.rs
│   ├── home_page.rs         # HomePage component
│   ├── text_analysis.rs     # Text analysis tab
│   ├── codebase_analysis.rs # Codebase analysis tab
│   └── common.rs            # Shared UI components
├── services/                 # Business logic and API calls
│   ├── mod.rs
│   ├── tauri_commands.rs    # Tauri command wrappers
│   └── analysis_service.rs  # Analysis logic
├── utils/                    # Utility functions
│   ├── mod.rs
│   └── helpers.rs           # Helper functions
└── icons/                    # Icon components (already exists)
    └── mod.rs
```

### 2. **Detailed Breakdown by Module**

#### **A. lib.rs (Main Entry Point)**
**Target Size**: ~50 lines
**Purpose**: Application entry point and module declarations
**Contents**:
- Module declarations
- WASM entry point
- Basic imports

#### **B. app.rs (App Component)**
**Target Size**: ~30 lines
**Purpose**: Main App component and routing setup
**Contents**:
- App component
- Router configuration
- Meta context setup

#### **C. types/ (Data Structures)**
**Target Size**: ~60 lines total
**Purpose**: All data structures and type definitions

**types/analysis.rs** (~30 lines):
- `AnalysisResults` struct
- `CharacterInfo` struct

**types/codebase.rs** (~30 lines):
- `CodeBaseAnalysisResult` struct

#### **D. components/ (UI Components)**
**Target Size**: ~250 lines total
**Purpose**: All UI components separated by concern

**components/home_page.rs** (~80 lines):
- HomePage component (state management only)
- Tab navigation logic
- Error handling

**components/text_analysis.rs** (~85 lines):
- Text analysis tab UI
- Text input handling
- Results display for text analysis

**components/codebase_analysis.rs** (~75 lines):
- Codebase analysis tab UI
- Folder selection UI
- Results display for codebase analysis

**components/common.rs** (~10 lines):
- Shared UI components (if any)
- Common styling classes

#### **E. services/ (Business Logic)**
**Target Size**: ~80 lines total
**Purpose**: API calls and business logic

**services/tauri_commands.rs** (~40 lines):
- `tauri_invoke_no_args` function
- `tauri_invoke_with_args` function
- Tauri bindings

**services/analysis_service.rs** (~40 lines):
- Analysis orchestration logic
- Error handling for services
- State management helpers

#### **F. utils/ (Utilities)**
**Target Size**: ~20 lines
**Purpose**: Utility functions and helpers

**utils/helpers.rs**:
- String formatting helpers
- Common calculations
- Utility functions

## 3. **Migration Strategy**

### **Phase 1: Setup Module Structure (Safe)**
1. Create new module directories
2. Create empty module files with proper exports
3. Test compilation to ensure structure is valid

### **Phase 2: Extract Data Types (Low Risk)**
1. Move data structures to `types/` modules
2. Update imports in main lib.rs
3. Test compilation and functionality

### **Phase 3: Extract Services (Medium Risk)**
1. Move Tauri command wrappers to `services/tauri_commands.rs`
2. Update imports and test Tauri integration
3. Verify all commands still work

### **Phase 4: Extract Components (High Risk - Requires Testing)**
1. Extract text analysis tab to separate component
2. Extract codebase analysis tab to separate component
3. Refactor HomePage to use new components
4. Test all UI functionality thoroughly

### **Phase 5: Final Cleanup (Low Risk)**
1. Move App component to app.rs
2. Clean up lib.rs to minimal entry point
3. Add proper documentation
4. Final testing

## 4. **Benefits of This Structure**

### **Maintainability**
- Each module has a single responsibility
- Easier to locate and modify specific functionality
- Better code organization

### **Testability**
- Individual modules can be unit tested
- Business logic separated from UI logic
- Better error isolation

### **Scalability**
- Easy to add new analysis types
- Simple to extend UI components
- Clear separation for new features

### **Developer Experience**
- Faster compilation of individual modules
- Better IDE support and navigation
- Clearer code structure for new developers

## 5. **Implementation Checklist**

### **Pre-Implementation**
- [ ] Backup current working lib.rs
- [ ] Create test plan for each module
- [ ] Set up rollback strategy

### **Implementation Steps**
- [ ] Phase 1: Create module structure
- [ ] Phase 2: Extract data types
- [ ] Phase 3: Extract services
- [ ] Phase 4: Extract components
- [ ] Phase 5: Final cleanup

### **Post-Implementation**
- [ ] Full application testing
- [ ] Performance verification
- [ ] Documentation updates
- [ ] Code review and optimization

## 6. **Rollback Plan**

If any phase fails:
1. Revert to previous working state
2. Analyze the failure
3. Adjust the plan
4. Retry with modifications

The modular structure is designed to be implemented incrementally, allowing for testing and rollback at each phase.

## 7. **Timeline Estimate**

- **Phase 1**: 30 minutes
- **Phase 2**: 45 minutes
- **Phase 3**: 60 minutes
- **Phase 4**: 90 minutes (most complex)
- **Phase 5**: 30 minutes
- **Testing**: 60 minutes

**Total Estimated Time**: 5 hours

This plan provides a systematic approach to breaking down the monolithic lib.rs into a well-organized, maintainable modular structure while minimizing risk through incremental implementation and thorough testing.
