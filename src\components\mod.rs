// src/components/mod.rs

// Declare the modules that exist as files or subdirectories within src/components/
pub mod analyze;
pub mod clean;
pub mod clean_copy;
pub mod codebase;
pub mod controls;
pub mod display;
pub mod export;
pub mod input;
pub mod shared;

// Modern GUI components
pub mod drag_and_drop;
pub mod help_modal;
pub mod icon_system;
pub mod progress_bar;
pub mod progress_overlay;
pub mod error_handling;
pub mod settings_button;
pub mod theme;
pub mod simple_text_analyzer;
pub mod security_analysis_tabs;
pub mod app_layout;
// pub mod analysis_tabs; // DEPRECATED - moved to app_layout/
pub mod legal;
pub mod quick_test_samples;

// Re-export the specific components needed by src/lib.rs
// These components must be pub in their respective files.
// Removed unused component exports: AnalyzeComponent, CleanCopyComponent, FileMenu
pub use codebase::CodebaseComponent;
pub use export::ExportComponent;

// Modern GUI component re-exports
pub use icon_system::{Icon, IconSize};
pub use progress_overlay::{ProgressOverlay, ProgressManager};
pub use error_handling::{ErrorDisplay, ErrorManager, AppError, Toast, ToastType};
// pub use settings_button::SettingsButton;
pub use theme::ThemeToggle;
pub use simple_text_analyzer::SimpleTextAnalyzer;
// pub use app_layout::AppLayout; // This was causing errors
// pub use legal::disclaimer_modal::LegalDisclaimerModal;
// pub use quick_test_samples::QuickTestSamples;

// Re-export any necessary types from shared if they are directly used by src/lib.rs
// For example, if AnalysisResults is defined in shared/data_types.rs and re-exported by shared/mod.rs
// And if src/lib.rs needs it directly from components module:
// pub use shared::AnalysisResults; // Adjust path as necessary
// However, src/lib.rs seems to define its own AnalysisResults. Check if this is intended.

// If shared::data_types::* is needed by other modules *within* components, 
// those modules should `use crate::components::shared::data_types::*;` or similar.
