// This file contains ZERO-WIDTH SPACE attacks
// These characters are invisible but can break parsing

function getUserData() {
    // Hidden zero-width space after semicolon below ↓
    const apiKey = "sk-1234567890abcdef";
    
    // Zero-width non-joiner attack in variable name
    const userName = "admin";
    
    // Zero-width joiner in string
    const message = "HelloWorld";
    
    // Multiple zero-width characters
    const config = {
        key: "value"
    };
    
    return {
        api: api<PERSON><PERSON>,
        user: userName,
        msg: message,
        config: config
    };
}

// Clean function for reference
function cleanGetUserData() {
    const apiKey = "sk-1234567890abcdef";
    const userName = "admin";
    const message = "HelloWorld";
    
    const config = {
        key: "value"
    };
    
    return {
        api: apiKey,
        user: userName,
        msg: message,
        config: config
    };
}
