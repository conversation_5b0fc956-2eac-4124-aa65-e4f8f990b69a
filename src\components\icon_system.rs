use leptos::*;

/// Apple-inspired icon system with standardized sizing
/// Provides consistent SVG icons with proper responsive behavior
/// By J.Shoy - 2025

#[derive(<PERSON>lone, Co<PERSON>, PartialEq)]
pub enum IconSize {
    SM,    // 16px - Small icons, buttons
    MD,    // 20px - Default size, headers
    LG,    // 24px - Large icons, emphasis
}

impl IconSize {
    pub fn class(&self) -> &'static str {
        match self {
            IconSize::SM => "w-4 h-4",      // 16px
            IconSize::MD => "w-5 h-5",      // 20px
            IconSize::LG => "w-6 h-6",      // 24px
        }
    }
}

#[component]
pub fn Icon(
    /// Icon name/type
    name: &'static str,
    /// Icon size (default: MD)
    #[prop(default = IconSize::MD)]
    size: IconSize,
    /// Additional CSS classes
    #[prop(default = "")]
    class: &'static str,
    /// Accessible label
    #[prop(default = "")]
    aria_label: &'static str,
) -> impl IntoView {
    let size_class = size.class();
    let combined_class = format!("{} {} text-current", size_class, class);
    
    view! {
        <svg
            class=combined_class
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
            aria-label=aria_label
            role="img"
        >
            {render_icon_path(name)}
        </svg>
    }
}

/// Specialized icon for buttons with proper sizing
#[component]
pub fn ButtonIcon(
    name: &'static str,
    #[prop(default = IconSize::SM)]
    size: IconSize,
    #[prop(default = "")]
    class: &'static str,
) -> impl IntoView {
    view! {
        <Icon name=name size=size class=class />
    }
}

/// Specialized icon for headers with consistent sizing
#[component]
pub fn HeaderIcon(
    name: &'static str,
    #[prop(default = "")]
    class: &'static str,
) -> impl IntoView {
    view! {
        <Icon name=name size=IconSize::MD class=class />
    }
}

/// Specialized icon for status indicators
#[component]
pub fn StatusIcon(
    name: &'static str,
    #[prop(default = IconSize::SM)]
    size: IconSize,
    #[prop(default = "")]
    class: &'static str,
) -> impl IntoView {
    let status_class = match name {
        "success" => "text-green-500",
        "warning" => "text-orange-500", 
        "error" => "text-red-500",
        "info" => "text-blue-500",
        _ => "text-current",
    };
    
    let size_class = size.class();
    let combined_class = format!("{} {} {}", size_class, status_class, class);

    view! {
        <svg
            class=combined_class
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
            role="img"
        >
            {render_icon_path(name)}
        </svg>
    }
}

fn render_icon_path(name: &'static str) -> impl IntoView {
    match name {
        "settings" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        }.into_view(),
        "analyze" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        }.into_view(),
        "folder" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25H11.69z" />
        }.into_view(),
        "file" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
        }.into_view(),
        "export" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
        }.into_view(),
        "clean" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
        }.into_view(),
        "security" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
        }.into_view(),
        "warning" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        }.into_view(),
        "success" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        }.into_view(),
        "error" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        }.into_view(),
        "info" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
        }.into_view(),
        "close" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        }.into_view(),
        "chevron-down" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
        }.into_view(),
        "chevron-up" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
        }.into_view(),
        "chevron-right" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
        }.into_view(),
        "chevron-left" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
        }.into_view(),
        "plus" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
        }.into_view(),
        "minus" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 12h-15" />
        }.into_view(),
        "theme-light" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
        }.into_view(),
        "theme-dark" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
        }.into_view(),
        "drag-drop" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
        }.into_view(),
        "progress" => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
        }.into_view(),
        _ => view! {
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
        }.into_view(),
    }
}

// Apple-inspired icon system with standardized sizing and responsive behavior
// Ensures consistent visual hierarchy and proper scaling across all components
// By J.Shoy - 2025
