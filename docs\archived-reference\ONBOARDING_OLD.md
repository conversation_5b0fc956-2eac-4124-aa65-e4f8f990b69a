# 🚀 Developer Onboarding Guide
## Bad Character Scanner - Leptos + Tauri v2 Application

**Welcome to the Bad Character Scanner project!** This guide will get you up and running quickly with our Leptos + Tauri v2 desktop application for advanced Unicode security analysis.

---

## 📋 Table of Contents
1. [Project Overview](#-project-overview)
2. [Prerequisites](#-prerequisites)
3. [Quick Setup](#-quick-setup)
4. [Development Environment](#-development-environment)
5. [Architecture Overview](#-architecture-overview)
6. [Key Components](#-key-components)
7. [Development Workflow](#-development-workflow)
8. [Common Tasks](#-common-tasks)
9. [Troubleshooting](#-troubleshooting)
10. [Resources & Documentation](#-resources--documentation)

---

## 🎯 Project Overview

**Bad Character Scanner (BCS)** is a desktop application built with **Leptos + Tauri v2** that provides comprehensive Unicode security analysis and threat detection capabilities.

### What It Does
- **Unicode Security Analysis**: Detects dangerous characters, homoglyphs, and invisible characters
- **Codebase Analysis**: Scans entire codebases for security vulnerabilities
- **AI Content Detection**: Identifies AI-generated content patterns
- **Export Capabilities**: Multiple output formats (JSON, CSV, PDF reports)
- **Real-time Analysis**: Live analysis with progress tracking

### Technical Stack
- **Frontend**: Leptos (Rust-based reactive UI framework)
- **Backend**: Tauri v2.5.x (Rust desktop app framework)
- **Styling**: Tailwind CSS
- **Build Tools**: Trunk (frontend), Cargo (backend)
- **Platform**: Cross-platform desktop (Windows, macOS, Linux)

---

## 🛠 Prerequisites

### Required Software
1. **Rust** (latest stable)
   ```powershell
   # Install via rustup
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **Node.js** (v16+)
   ```powershell
   # Download from https://nodejs.org/
   # Verify installation
   node --version
   npm --version
   ```

3. **Required Cargo Tools**
   ```powershell
   cargo install tauri-cli --version "^2.5"
   cargo install trunk --locked
   ```

### Platform-Specific Setup
#### Windows (Primary Development Environment)
- **PowerShell**: Use PowerShell for all commands (avoid Command Prompt)
- **Visual Studio Build Tools**: Required for native dependencies
- **Windows SDK**: Latest version recommended

---

## ⚡ Quick Setup

### 1. Clone Repository
```powershell
git clone <repository-url>
cd Leptos_TaurieV2_BCS
```

### 2. Install Dependencies
```powershell
# Install JavaScript dependencies
npm install

# Verify Tauri setup
cargo tauri info
```

### 3. Run Development Environment
```powershell
# Option 1: Use the automated startup script (Recommended)
.\dev_startup.ps1

# Option 2: Manual startup
# Terminal 1: Frontend server
trunk serve --port 1420

# Terminal 2: Tauri backend (new terminal)
cargo tauri dev
```

### 4. Verify Installation
- **Frontend**: http://localhost:1420 (trunk dev server)
- **Desktop App**: Should automatically open Tauri window
- **Processes**: Look for `trunk`, `cargo-tauri`, and `laptos-tauri` processes

---

## 🏗 Architecture Overview

### Project Structure
```
Leptos_TaurieV2_BCS/
├── src/                          # Frontend (Leptos)
│   ├── components/               # UI Components
│   │   ├── analyze_component.rs  # Main analysis interface
│   │   ├── clean_component.rs    # Text cleaning UI
│   │   ├── export_component.rs   # Export functionality
│   │   └── codebase/             # Codebase analysis components
│   ├── lib.rs                    # Frontend entry point
│   └── main.rs                   # WASM entry
├── src-tauri/                    # Backend (Tauri + Rust)
│   ├── src/
│   │   ├── main.rs               # Tauri app entry point
│   │   ├── commands/             # Tauri commands (IPC)
│   │   ├── modules/              # Core analysis modules
│   │   └── report_generator.rs   # Report generation
│   ├── Cargo.toml               # Backend dependencies
│   └── tauri.config.json        # Tauri configuration
├── docs/                        # Documentation
├── dev_startup.ps1              # Development startup script
├── package.json                 # Frontend dependencies
└── Trunk.toml                   # Frontend build config
```

### Communication Flow
```
Frontend (Leptos) ←→ Tauri IPC ←→ Backend (Rust)
     ↓                              ↓
  Web Browser               Desktop Native APIs
```

---

## 🔧 Key Components

### Frontend Components (Leptos)
- **`AnalyzeComponent`**: Main text analysis interface
- **`CleanComponent`**: Text cleaning and normalization
- **`ExportComponent`**: Multi-format export functionality
- **`CodebaseComponent`**: Full codebase analysis UI
- **`ProgressComponent`**: Real-time progress tracking

### Backend Modules (Tauri)
- **`character_analyzer`**: Core Unicode analysis engine
- **`ai_detection`**: AI content pattern detection
- **`pattern_matching`**: Security pattern recognition
- **`enhanced_analysis`**: Advanced threat detection
- **`report_generator`**: Comprehensive report creation

### Tauri Commands (IPC Interface)
- **`analyze_characters`**: Primary text analysis command
- **`clean_text`**: Text cleaning operations
- **`analyze_codebase`**: Full codebase scanning
- **`export_analysis`**: Export functionality

---

## 🔄 Development Workflow

### Daily Development
1. **Start Development Environment**
   ```powershell
   .\dev_startup.ps1
   ```

2. **Make Changes**
   - Frontend: Edit files in `src/` (auto-reloads via trunk)
   - Backend: Edit files in `src-tauri/src/` (auto-recompiles)

3. **Test Changes**
   - Frontend changes: Browser auto-refreshes
   - Backend changes: Tauri app restarts automatically

4. **Commit Changes**
   ```powershell
   git add .
   git commit -m "feat: description of changes"
   git push origin main
   ```

### Code Style & Standards
- **Rust Formatting**: Use `leptos fmt` for all Rust code
- **Tauri v2 Compatibility**: All dependencies must be Tauri v2 compatible
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Performance**: Async operations with progress feedback

---

## 📝 Common Tasks

### Adding a New Analysis Feature
1. **Backend**: Create module in `src-tauri/src/modules/`
2. **Commands**: Add Tauri command in `src-tauri/src/commands/`
3. **Frontend**: Create component in `src/components/`
4. **Integration**: Wire up IPC communication

### Debugging Issues
1. **Frontend Issues**: 
   - Check browser console at http://localhost:1420
   - Look for TypeScript/WASM errors

2. **Backend Issues**:
   - Check `cargo tauri dev` terminal output
   - Look for Rust compilation errors

3. **IPC Issues**:
   - Verify command signatures match between frontend/backend
   - Check Tauri command registration

### Building for Production
```powershell
# Build desktop application
cargo tauri build

# Output location: src-tauri/target/release/
```

---

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. "Failed to load resource: 404" Error
**Cause**: Backend not running (compilation errors)
**Solution**: 
```powershell
# Check for compilation errors
cargo tauri dev

# Look for missing imports or module conflicts
```

#### 2. Module Ambiguity Errors
**Cause**: Duplicate module files (e.g., `file.rs` and `file/mod.rs`)
**Solution**: Remove one of the conflicting files

#### 3. Tauri Commands Not Found
**Cause**: Command not registered or signature mismatch
**Solution**: Verify command registration in `main.rs` and parameter types

#### 4. Frontend Not Loading
**Cause**: Trunk server not running or port conflict
**Solution**: 
```powershell
# Kill processes on port 1420
netstat -ano | findstr :1420
taskkill /PID <PID> /F

# Restart trunk
trunk serve --port 1420
```

### Development Environment Health Check
```powershell
# Verify all required processes are running
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*cargo*" -or $_.ProcessName -like "*laptos*"}

# Expected: trunk, cargo-tauri, laptos-tauri processes
```

---

## 📚 Resources & Documentation

### Essential Documentation (Located in `/docs/`)
- **`README.md`**: Documentation hub and navigation
- **`DEVELOPER_GUIDE.md`**: Extended development guide
- **`QUICK_NAVIGATION.md`**: Fast document finding
- **`COMPLETE_SUCCESS_FULL_STACK_WORKING.md`**: Recent success milestones

### External Resources
- **[Leptos Documentation](https://leptos.dev/)**: Frontend framework guide
- **[Tauri v2 Guide](https://v2.tauri.app/)**: Desktop app framework
- **[Trunk Documentation](https://trunkrs.dev/)**: Frontend build tool

### Getting Help
1. **Check existing documentation** in `/docs/`
2. **Review recent status documents** for similar issues
3. **Check Git history** for recent fixes
4. **Ask team questions** with specific error messages

---

## 🎯 Next Steps

Now that you're set up:

1. **✅ Verify your development environment** is working
2. **🔍 Explore the codebase** - start with `src/lib.rs` and `src-tauri/src/main.rs`
3. **🧪 Run the application** and test core functionality
4. **📖 Read the architecture docs** to understand the system design
5. **🚀 Make your first contribution** - try fixing a small issue or adding a feature

**Welcome to the team! 🎉**

---

*Last Updated: June 16, 2025*  
*Tauri Version: v2.5.x*  
*Leptos Framework: Latest stable*
