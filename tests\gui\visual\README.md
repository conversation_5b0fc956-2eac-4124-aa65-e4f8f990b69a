# Visual Regression Tests

This folder contains Playwright-based visual regression tests for the Leptos/Tauri GUI.

## How to Run
- Install Playwright: `npm install -D @playwright/test`
- Run: `npx playwright test tests/gui/visual/`

## Notes
- Screenshots are compared to baseline images (committed in `visual-baseline/` or similar).
- Add new tests for each major UI state or theme.
- See `test_visual_regression.spec.ts` for an example.
