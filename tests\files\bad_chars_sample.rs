// Test file with suspicious characters for Bad Character Scanner testing

function main() {
    // This line contains a zero-width space after "Hello"
    let message = "Hello​World";
    
    // This line has a bidirectional override character
    let suspicious = "Safe‮txetnetnoC‭Code";
    
    // Non-breaking space characters
    let spacing = "Normal Text";
    
    // Control characters (ASCII 0x01-0x1F)
    let control = "Text with control";
    
    // Unicode homoglyphs that look like normal characters
    let homoglyph = "Аdministrator"; // Contains Cyrillic 'А' instead of Latin 'A'
    
    // Some normal code
    println!("Testing bad character detection");
    
    return 0;
}
