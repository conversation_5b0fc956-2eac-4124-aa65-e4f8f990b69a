[workspace]
members = ["src-tauri"]
resolver = "2"

[package]
name = "leptos-frontend"
version = "0.2.0"
edition = "2021"

[dependencies]
leptos = { version = "0.6", features = ["csr"] }
leptos_meta = { version = "0.6", features = ["csr"] }
leptos_router = { version = "0.6", features = ["csr"] }
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
console_error_panic_hook = "0.1"
console_log = "1"
log = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"
serde_json = "1.0"
js-sys = "0.3"
gloo-timers = { version = "0.3", features = ["futures"] }
chrono = { version = "0.4", features = ["serde", "wasm-bindgen"] }
uuid = { version = "1.0", features = ["v4", "js"] }

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
# tauri-sys = "0.4"  # Not available on crates.io - will use JS API directly
futures = "0.3"

[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Window",
  "Document",
  "Element",
  "HtmlElement",
  "HtmlInputElement",
  "HtmlTextAreaElement",
  "Event",
  "EventTarget",
  "File",
  "FileList",
  "DragEvent",
  "DataTransfer",
  "CustomEvent",
  "Storage",
  "MediaQueryList",
  "HtmlHtmlElement",
]

[lib]
crate-type = ["cdylib"]

# Optimize for WebAssembly and native builds
[profile.release]
opt-level = "s"  # Optimize for size (important for WASM)
lto = true
codegen-units = 1
panic = "abort"
strip = true  # Strip debug symbols for smaller binaries

[profile.dev]
opt-level = 0
debug = true
