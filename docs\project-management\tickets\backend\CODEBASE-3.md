# CODEBASE-3: Enhanced Folder Selection UX for Code Base Analysis

## 🎯 Feature Enhancement

**Priority:** Medium  
**Status:** Open  
**Assignee:** Development Team  
**Created:** 2025-05-28  

## Feature Description

Improve the folder selection experience for codebase analysis by providing multiple intuitive input methods beyond the current "Browse Folder" dialog.

## Current State

- ✅ Browse folder dialog works
- ❌ Limited to GUI folder picker only
- ❌ No direct path input option
- ❌ No recent folders or favorites
- ❌ Difficult for testing and power users

## Proposed Enhancements

### 1. **Direct Path Input Field**
- Text input field for folder paths
- Auto-completion for Windows paths
- Path validation and existence checking
- Support for both forward and backward slashes

### 2. **Drag & Drop Support**
- Drop folder from Windows Explorer
- Visual drop zone indicator
- Support for multiple folder formats

### 3. **Recent Folders History**
- Store last 10 analyzed folders
- Quick selection dropdown
- Persistent across sessions

### 4. **Quick Access Shortcuts**
- Desktop folder shortcut
- Documents folder shortcut
- Current project folder detection
- Git repository root detection

### 5. **Improved Visual Feedback**
- Path validation indicators (✅/❌)
- Folder statistics preview (file count, size)
- Permission check warnings

## Technical Implementation

### Frontend Components
```rust
// Enhanced folder selection component
#[component]
fn FolderSelector() -> impl IntoView {
    // Path input field
    // Drag & drop zone  
    // Recent folders dropdown
    // Quick access buttons
    // Validation feedback
}
```

### Backend Enhancements
```rust
// Path validation utilities
fn validate_folder_path(path: &str) -> Result<FolderInfo, String>
fn get_recent_folders() -> Vec<String>
fn save_recent_folder(path: &str)
```

## User Stories

**As a developer, I want to:**
- Paste a folder path directly so I don't have to navigate through dialogs
- Use recently analyzed folders so I can quickly re-analyze projects
- Drag a folder from Explorer so I can use familiar Windows interactions
- See immediate feedback if a path is invalid

**As a tester, I want to:**
- Quickly input test folder paths for different scenarios
- Use keyboard shortcuts for rapid testing
- Access common test directories easily

## Design Mockup

```
┌─────────────────────────────────────────────────────────────┐
│ 📁 Select Source Code Folder                               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 🗂️ Path Input                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ C:\Users\<USER>\Projects\MyApp                         ✅ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📋 Quick Actions                                           │
│ [Browse...] [Recent ▼] [Desktop] [Documents] [Git Root]   │
│                                                             │
│ 🎯 Drag & Drop Zone                                        │
│ ┌─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─┐ │
│ │   Drag folder here or click Browse to select           │ │
│ └─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─┘ │
│                                                             │
│ 📊 Folder Preview: 1,247 files (23.4 MB)                 │
└─────────────────────────────────────────────────────────────┘
```

## Acceptance Criteria

- [ ] Direct path input field with validation
- [ ] Drag & drop folder support
- [ ] Recent folders dropdown (last 10)
- [ ] Quick access buttons (Desktop, Documents, etc.)
- [ ] Real-time path validation feedback
- [ ] Folder statistics preview
- [ ] Keyboard navigation support
- [ ] Accessible for screen readers
- [ ] Cross-platform compatibility considerations

## Testing Scenarios

1. **Path Input Testing**
   - Valid absolute paths
   - Invalid paths
   - Non-existent directories
   - Permission denied folders
   - Special characters in paths

2. **Drag & Drop Testing**
   - Single folder drop
   - Multiple items (should reject)
   - File drop (should reject)
   - Network paths

3. **Recent Folders Testing**
   - History persistence
   - Maximum limit handling
   - Duplicate prevention

## Dependencies

- Requires CODEBASE-2 to be resolved first
- May need new Tauri commands for path utilities
- Frontend state management for recent folders

## Estimated Effort

**Development:** 2-3 days  
**Testing:** 1 day  
**Documentation:** 0.5 days

## Related Issues

- Builds on CODEBASE-1 (original feature)
- Blocked by CODEBASE-2 (parsing error)
- May inspire similar UX improvements in other areas
