
# Tickets Folder Organization

**See also:** [Documentation Index](../docs/DOCUMENTATION_INDEX.md)

- `open/` - Tickets that are new or not yet started
- `in_progress/` - Tickets currently being worked on
- `completed/` - Tickets that are resolved and closed

## Ticket Naming Convention
- Use `TICKET_<SHORT_DESCRIPTION>.md` for each ticket
- Each ticket should include: Status, Priority, Assigned, Created, Summary, Root Cause, Steps to Reproduce, Acceptance Criteria, and Next Steps


## Current Open Tickets

- TICKET_BUILD_FAILURE.md: Build pipeline and module ambiguity errors
- TICKET_TAURI_ARG_STRUCTURE.md: Tauri command argument structure error (frontend/backend integration)
- TICKET_BACKDOOR_FRONTDOOR_SCANNER.md: Dedicated scanner for detecting and counting back doors and front doors in codebases for enhanced security

---

**See also:** [Documentation Index](../docs/DOCUMENTATION_INDEX.md)
