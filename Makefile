.PHONY: all install fmt lint test dev build clean help

# Variables
CARGO = cargo
RUSTFLAGS = -D warnings

# Default target
all: help

# Install dependencies
install:
	@echo "Installing dependencies..."
	rustup update
	rustup target add wasm32-unknown-unknown
	$(CARGO) install --locked trunk
	$(CARGO) install -f wasm-bindgen-cli
	$(CARGO) install tauri-cli

# Format code
fmt:
	@echo "Checking formatting..."
	$(CARGO) fmt --all -- --check

# Lint code
lint:
	@echo "Linting..."
	$(CARGO) clippy -- -D warnings

# Run tests
test:
	@echo "Running tests..."
	$(CARGO) test --workspace

# Run in development mode
dev:
	@echo "Starting development server..."
	$(CARGO) tauri dev

# Build for production
build:
	@echo "Building for production..."
	$(CARGO) tauri build

# Run formatter and linter
check: fmt lint

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(CARGO) clean
	rm -rf target

# Run all checks (format, lint, test)
ci: check test

# Show help
help:
	@echo "\nLaptos TauriV2 Bad Character Scanner - Makefile"
	@echo "============================================"
	@echo "Available targets:"
	@echo "  install   - Install dependencies"
	@echo "  fmt       - Format code"
	@echo "  lint      - Lint code"
	@echo "  test      - Run tests"
	@echo "  dev       - Run in development mode"
	@echo "  build     - Build for production"
	@echo "  check     - Run formatter and linter"
	@echo "  clean     - Clean build artifacts"
	@echo "  ci        - Run all checks (format, lint, test)"
	@echo "  help      - Show this help"

# Alias for help
tasks: help
