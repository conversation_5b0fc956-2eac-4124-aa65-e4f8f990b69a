# Simple Advanced Testing Script
# Tests our sophisticated attack files with the CLI

Write-Host "🧪 Advanced Bad Character Scanner Testing" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Test files and expected results
$TestFiles = @(
    @{
        name = "Zero-Width Attack"
        file = "test_live_verification\zero_width_attack.js"
        expectedThreats = 3
    },
    @{
        name = "Bidirectional Attack"
        file = "test_live_verification\bidirectional_attack.js" 
        expectedThreats = 3
    },
    @{
        name = "Homograph Attack"
        file = "test_live_verification\homograph_attack.py"
        expectedThreats = 9
    },
    @{
        name = "Control Characters"
        file = "test_live_verification\control_characters.html"
        expectedThreats = 7
    },
    @{
        name = "Clean Reference (should be 0)"
        file = "test_live_verification\clean_reference.js"
        expectedThreats = 0
    }
)

$TotalTests = 0
$PassedTests = 0
$TotalThreats = 0

foreach ($test in $TestFiles) {
    Write-Host ""
    Write-Host "🔍 Testing: $($test.name)" -ForegroundColor Yellow
    
    # Test if file exists
    if (-not (Test-Path $test.file)) {
        Write-Host "  ❌ File not found: $($test.file)" -ForegroundColor Red
        continue
    }
    
    $TotalTests++
    
    # Run analysis
    try {
        $output = & "target\release\analyzer_cli.exe" analyze $test.file json 2>$null
        $result = $output | ConvertFrom-Json
        
        $detectedThreats = $result.total_suspicious_chars
        $healthScore = $result.health_score
        
        $accuracy = if ($test.expectedThreats -gt 0) {
            [math]::Round(($detectedThreats / $test.expectedThreats) * 100, 1)
        } else {
            if ($detectedThreats -eq 0) { 100 } else { 0 }
        }
        
        Write-Host "  📊 Expected: $($test.expectedThreats) | Detected: $detectedThreats | Accuracy: $accuracy%" -ForegroundColor Cyan
        Write-Host "  💚 Health Score: $healthScore%" -ForegroundColor Green
        
        if ($accuracy -eq 100) {
            Write-Host "  ✅ PASS - Perfect detection!" -ForegroundColor Green
            $PassedTests++
        } else {
            Write-Host "  ⚠️  PARTIAL - Detection accuracy: $accuracy%" -ForegroundColor Yellow
        }
        
        $TotalThreats += $detectedThreats
        
        # Test cleaning if threats were found
        if ($detectedThreats -gt 0) {
            $cleanedFile = $test.file -replace '\.([^.]+)$', '_test_cleaned.$1'
            
            Write-Host "  🧹 Testing cleaning..." -ForegroundColor Blue
            
            $cleanOutput = & "target\release\analyzer_cli.exe" clean $test.file $cleanedFile 2>$null
            
            if (Test-Path $cleanedFile) {
                $cleanedResult = & "target\release\analyzer_cli.exe" analyze $cleanedFile json 2>$null
                $cleanedAnalysis = $cleanedResult | ConvertFrom-Json
                
                $remainingThreats = $cleanedAnalysis.total_suspicious_chars
                $cleaningEfficiency = [math]::Round((($detectedThreats - $remainingThreats) / $detectedThreats) * 100, 1)
                
                Write-Host "  🎯 Cleaning Result: $cleaningEfficiency% efficient ($remainingThreats remaining)" -ForegroundColor Magenta
                
                # Clean up test file
                Remove-Item $cleanedFile -Force -ErrorAction SilentlyContinue
            }
        }
        
    } catch {
        Write-Host "  ❌ Analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📋 FINAL RESULTS" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
Write-Host "Total Tests: $TotalTests" -ForegroundColor White
Write-Host "Passed: $PassedTests" -ForegroundColor Green
Write-Host "Failed: $($TotalTests - $PassedTests)" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($PassedTests / $TotalTests) * 100, 1))%" -ForegroundColor Yellow
Write-Host "Total Threats Detected: $TotalThreats" -ForegroundColor Magenta

if ($PassedTests -eq $TotalTests) {
    Write-Host ""
    Write-Host "🎉 ALL TESTS PASSED! Advanced Bad Character Scanner is working perfectly!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️ Some tests need attention. Review results above." -ForegroundColor Yellow
}
