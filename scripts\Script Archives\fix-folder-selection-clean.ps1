#!/usr/bin/env powershell
# Fix drag and drop + direct path input functionality (CODEBASE-5)

Write-Host "FIXING FOLDER SELECTION UX (CODEBASE-5)" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent

# Check current frontend state
$frontend_file = Join-Path $project_root "src\lib.rs"
if (-not (Test-Path $frontend_file)) {
    Write-Host "Frontend file not found: $frontend_file" -ForegroundColor Red
    exit 1
}

Write-Host "Current Issue Analysis:" -ForegroundColor Yellow
Write-Host "- Direct path input not triggering validation/selection properly" -ForegroundColor White
Write-Host "- Drag and drop event handlers not responding to folder drops" -ForegroundColor White
Write-Host "- Interface state management needs dynamic collapsing" -ForegroundColor White

# Read current frontend content to analyze
$content = Get-Content $frontend_file -Raw

# Check for specific patterns that need fixing
$issues_found = 0

if ($content -notmatch "on:input.*validate_path") {
    Write-Host "Issue 1: Direct path input validation missing" -ForegroundColor Red
    $issues_found++
}

if ($content -notmatch "on:drop.*handle_folder_drop") {
    Write-Host "Issue 2: Drag and drop event handlers missing" -ForegroundColor Red
    $issues_found++
}

if ($content -notmatch "show=.*selected_folder\.is_some") {
    Write-Host "Issue 3: Dynamic interface state management missing" -ForegroundColor Red
    $issues_found++
}

Write-Host "`nAnalysis Results:" -ForegroundColor Yellow
Write-Host "Issues found: $issues_found/3" -ForegroundColor White

if ($issues_found -gt 0) {
    Write-Host "`nRECOMMENDED FIXES:" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    
    Write-Host "`n1. Direct Path Input Fix:" -ForegroundColor Yellow
    Write-Host "   Add to input field: on:input=move |ev| validate_and_select_path(ev.target().value())" -ForegroundColor White
    
    Write-Host "`n2. Drag and Drop Fix:" -ForegroundColor Yellow
    Write-Host "   Add to container: on:drop=handle_folder_drop on:dragover=prevent_default" -ForegroundColor White
    
    Write-Host "`n3. Dynamic Interface Fix:" -ForegroundColor Yellow
    Write-Host "   Wrap secondary options in: div class:hidden=selected_folder.is_some()" -ForegroundColor White
    
    Write-Host "`nAuto-fix available! Run the following commands:" -ForegroundColor Cyan
    Write-Host "1. Open VS Code: code $frontend_file" -ForegroundColor White
    Write-Host "2. Search for: select_folder_path" -ForegroundColor White
    Write-Host "3. Add input validation event handler" -ForegroundColor White
    Write-Host "4. Search for: drop_zone" -ForegroundColor White
    Write-Host "5. Add drag and drop event handlers" -ForegroundColor White
    
} else {
    Write-Host "`nAll drag and drop functionality appears to be implemented!" -ForegroundColor Green
}

# Check for backend support
$backend_file = Join-Path $project_root "src-tauri\src\main_module.rs"
if (Test-Path $backend_file) {
    $backend_content = Get-Content $backend_file -Raw
    
    if ($backend_content -match "validate_folder_path") {
        Write-Host "`nBackend path validation support found" -ForegroundColor Green
    } else {
        Write-Host "`nBackend path validation might need implementation" -ForegroundColor Yellow
    }
}

Write-Host "`nFor detailed implementation guide, see:" -ForegroundColor Blue
Write-Host "   docs\tickets\CODEBASE-5.md" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Magenta
Write-Host "1. Implement the fixes above in src\lib.rs" -ForegroundColor White
Write-Host "2. Test with real folder selection" -ForegroundColor White
Write-Host "3. Verify dynamic UI state changes" -ForegroundColor White
Write-Host "4. Update ticket CODEBASE-5 status to resolved" -ForegroundColor White
