use leptos::*;
use crate::AnalysisResults;


#[component]
pub fn PatternsTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
    view! {
        <div class="tab-content-inner">
            <h4>"🎯 Pattern Analysis"</h4>
            {move || results.get().map(|res| {
                let patterns_empty = res.patterns_found.is_empty();
                view! {
                    <div class="patterns-overview">
                        <p>"Detected patterns that may indicate security issues or text manipulation:"</p>
                        {if patterns_empty {
                            view! { <p class="text-muted">"No suspicious patterns detected"</p> }.into_view()
                        } else {
                            view! {
                                <div class="pattern-list">
                                    {res.patterns_found.clone().into_iter().map(|pattern| {
                                        let name = pattern.pattern_name;
                                        let severity = pattern.severity;
                                        let desc = pattern.description;
                                        let start = pattern.start_position;
                                        let end = pattern.end_position;
                                        let severity_for_class = severity.clone();
                                        let severity_for_content = severity;
                                        view! {
                                            <div class="pattern-card">
                                                <div class="pattern-header">
                                                    <span class="pattern-name">{name}</span>
                                                    <span class="pattern-severity" class:high=move || severity_for_class.to_lowercase() == "high" >
                                                        {severity_for_content}
                                                    </span>
                                                </div>
                                                <p class="pattern-desc">{desc}</p>
                                                <div class="pattern-matches">
                                                    {format!("Found at positions {}-{}", start, end)}
                                                </div>
                                            </div>
                                        }
                                    }).collect_view()}
                                </div>
                            }.into_view()
                        }}
                    </div>
                }
            }).unwrap_or_else(|| view! { <div>"No analysis results available"</div> })}
        </div>
    }
}
