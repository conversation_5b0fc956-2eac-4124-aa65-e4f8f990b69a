# test_powershell_wrapper.ps1

# Define Paths relative to the workspace root
$WorkspaceRoot = $PWD # Assumes the script is run from the workspace root
$EnhancedAnalyzerScriptPath = "scripts/enhanced_analyzer.sh" # Relative path
$TestInputPath = "cli_test_sandbox/ps_test_input" # Relative path
$ReportOutputDirRelative = "cli_test_reports" # Relative path for the output directory
$ReportOutputFileName = "ps_wrapper_test_report.json"
$SummaryOutputFileName = "ps_wrapper_test_summary.txt"

# Resolve absolute paths for script and input
$AbsoluteEnhancedAnalyzerScriptPath = (Resolve-Path -Path (Join-Path -Path $WorkspaceRoot -ChildPath $EnhancedAnalyzerScriptPath)).Path -replace '\\', '/'
$AbsoluteTestInputPath = (Resolve-Path -Path (Join-Path -Path $WorkspaceRoot -ChildPath $TestInputPath)).Path -replace '\\', '/'

# Resolve absolute path for the output directory
$AbsoluteReportOutputDir = (Resolve-Path -Path (Join-Path -Path $WorkspaceRoot -ChildPath $ReportOutputDirRelative)).Path

# Ensure output directory exists
if (-not (Test-Path $AbsoluteReportOutputDir)) {
    Write-Host "Creating output directory: $AbsoluteReportOutputDir"
    New-Item -ItemType Directory -Path $AbsoluteReportOutputDir -Force | Out-Null
}

# Construct absolute paths for output files and convert to bash-friendly format
$AbsoluteReportOutputPath = (Join-Path -Path $AbsoluteReportOutputDir -ChildPath $ReportOutputFileName) -replace '\\', '/'
$AbsoluteSummaryOutputPath = (Join-Path -Path $AbsoluteReportOutputDir -ChildPath $SummaryOutputFileName) -replace '\\', '/'

# Construct the command to execute enhanced_analyzer.sh using bash
# Ensure Git Bash or a similar bash environment is in the PATH
# $BashExecutable = "bash" # Or specify full path if not in PATH e.g. "C:\\Program Files\\Git\\bin\\bash.exe"
$GitBashPath = "C:\\Program Files\\Git\\bin\\bash.exe"
$GitBashPathX86 = "C:\\Program Files (x86)\\Git\\bin\\bash.exe"

if (Test-Path $GitBashPath) {
    $BashExecutable = $GitBashPath
} elseif (Test-Path $GitBashPathX86) {
    $BashExecutable = $GitBashPathX86
} else {
    # Fallback to just 'bash' hoping it's in PATH and configured for non-WSL or Git Bash
    Write-Warning "Git Bash not found at common locations. Falling back to 'bash' command. This might use WSL if configured."
    $BashExecutable = "bash"
}

# Output the command for debugging
Write-Host "Workspace Root: $WorkspaceRoot"
Write-Host "Powershell Current Directory (PWD): $PWD"
Write-Host "Bash Executable: $BashExecutable"
Write-Host "Enhanced Analyzer Script (Bash Path): $AbsoluteEnhancedAnalyzerScriptPath"
Write-Host "Test Input Path (Bash Path): $AbsoluteTestInputPath"
Write-Host "Report Output Path (Bash Path): $AbsoluteReportOutputPath"
Write-Host "Summary Output Path (Bash Path): $AbsoluteSummaryOutputPath"

# Construct the argument list for the call operator
$ArgumentListForBash = @(
    $AbsoluteEnhancedAnalyzerScriptPath,
    "analyze",
    $AbsoluteTestInputPath,
    $AbsoluteReportOutputPath,
    $AbsoluteSummaryOutputPath
)
Write-Host "Arguments for Bash: $($ArgumentListForBash -join ' ')"


# Execute the command
$ExitCode = 0
try {
    # Use the call operator & for executing commands, especially with paths containing spaces
    & $BashExecutable $ArgumentListForBash
    $ExitCode = $LASTEXITCODE
} catch {
    Write-Error "An error occurred while trying to invoke the command:"
    Write-Error $_.Exception.Message
    # Attempt to get more details if the command itself wrote to stderr and PS captured it
    if ($_.Exception.ErrorRecord.ErrorDetails) {
        Write-Error "Error Details: $($_.Exception.ErrorRecord.ErrorDetails)"
    }
    $ExitCode = -1 # Indicate failure
}

# Check execution status
if ($ExitCode -ne 0) {
    Write-Error "Error executing enhanced_analyzer.sh. Exit code: $ExitCode"
    # Note: Capturing stderr from bash executed via Invoke-Expression can be complex.
    # The bash script itself should print errors to stderr for them to be visible.
    exit $ExitCode
} else {
    Write-Host "enhanced_analyzer.sh executed successfully."
}

# Verify report generation
Write-Host "Checking for report at: $AbsoluteReportOutputPath"
if (Test-Path $AbsoluteReportOutputPath) {
    Write-Host "SUCCESS: Report generated at $AbsoluteReportOutputPath"
    $ReportContent = Get-Content -Path $AbsoluteReportOutputPath -Raw
    if ($ReportContent.Length -gt 0) {
        Write-Host "Report content is not empty."
        # Basic validation
        if ($ReportContent -match '"total_files_scanned":\s*\d+' -and $ReportContent -match '"total_issues_found":\s*\d+') {
            Write-Host "Report contains expected fields (\'total_files_scanned\', \'total_issues_found\')."
        } else {
            Write-Warning "Report might be missing some expected JSON fields. Preview (first 500 chars):"
            Write-Host ($ReportContent | Select-String -Pattern ".{0,500}")
        }
    } else {
        Write-Warning "Report file is empty."
    }
} else {
    Write-Error "FAILURE: Report not found at $AbsoluteReportOutputPath"
}

# Verify summary generation
Write-Host "Checking for summary at: $AbsoluteSummaryOutputPath"
if (Test-Path $AbsoluteSummaryOutputPath) {
    Write-Host "SUCCESS: Summary generated at $AbsoluteSummaryOutputPath"
    $SummaryContent = Get-Content -Path $AbsoluteSummaryOutputPath
    if ($SummaryContent.Length -gt 0) {
        Write-Host "Summary content:"
        $SummaryContent | ForEach-Object { Write-Host $_ } # Print each line of summary
    } else {
        Write-Warning "Summary file is empty."
    }
} else {
    Write-Warning "Summary report not found at $AbsoluteSummaryOutputPath. This could be normal if no issues were found or if the script logic dictates so."
}

Write-Host "Powershell wrapper test script finished."
