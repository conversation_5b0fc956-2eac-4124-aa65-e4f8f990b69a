#!/usr/bin/env powershell
# Verify that all fixes have been applied correctly

Write-Host "`n✅ VERIFYING FIXES" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$all_good = $true

# Check error_handling.rs
Write-Host "`n📄 Checking error_handling.rs..." -ForegroundColor Yellow

$error_handling = Get-Content "$project_root\src\components\error_handling.rs" -Raw

# Check for proper cloning before view! macro
if ($error_handling -match "let error_manager_clone = error_manager\.clone\(\);\s*\n\s*view!") {
    Write-Host "  ✅ error_manager cloned before view! macro" -ForegroundColor Green
} else {
    Write-Host "  ❌ Missing error_manager clone before view!" -ForegroundColor Red
    $all_good = $false
}

# Check for proper cloning in remove button
if ($error_handling -match "on:click=\{\s*let error_manager = error_manager\.clone\(\)") {
    Write-Host "  ✅ error_manager cloned in remove button" -ForegroundColor Green
} else {
    Write-Host "  ❌ Missing error_manager clone in remove button" -ForegroundColor Red
    $all_good = $false
}

# Check for proper cloning in clear all button
if ($error_handling -match "let em = error_manager_clone\.clone\(\)") {
    Write-Host "  ✅ error_manager_clone used in clear all button" -ForegroundColor Green
} else {
    Write-Host "  ❌ Issue with clear all button" -ForegroundColor Red
    $all_good = $false
}

# Check simple_text_analyzer.rs
Write-Host "`n📄 Checking simple_text_analyzer.rs..." -ForegroundColor Yellow

$text_analyzer = Get-Content "$project_root\src\components\simple_text_analyzer.rs" -Raw

# Check for unsafe blocks
if ($text_analyzer -match "unsafe\s*\{\s*invoke") {
    Write-Host "  ⚠️  Still has unsafe blocks (warnings only)" -ForegroundColor Yellow
} else {
    Write-Host "  ✅ No unsafe blocks around invoke" -ForegroundColor Green
}

# Run compilation check
Write-Host "`n🔨 Running compilation check..." -ForegroundColor Yellow

Push-Location $project_root
$output = cargo check --target wasm32-unknown-unknown 2>&1
$success = $LASTEXITCODE -eq 0
Pop-Location

if ($success) {
    Write-Host "  ✅ Frontend compiles successfully!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Compilation still failing" -ForegroundColor Red
    $all_good = $false
    
    # Extract error details
    $errors = $output | Select-String -Pattern "error\[E"
    if ($errors) {
        Write-Host "`n  Error details:" -ForegroundColor Red
        foreach ($error in $errors | Select-Object -First 3) {
            Write-Host "    $error" -ForegroundColor Red
        }
    }
}

# Summary
Write-Host "`n📊 Verification Summary:" -ForegroundColor Cyan
if ($all_good) {
    Write-Host "  ✅ All fixes verified!" -ForegroundColor Green
    Write-Host "`n  Ready to run: cargo tauri dev" -ForegroundColor White
} else {
    Write-Host "  ❌ Some issues remain" -ForegroundColor Red
    Write-Host "`n  Try running:" -ForegroundColor Yellow
    Write-Host "  1. .\clean-build.ps1" -ForegroundColor White
    Write-Host "  2. .\fix-compiler-errors.ps1" -ForegroundColor White
    Write-Host "  3. .\debug-build-failure.ps1" -ForegroundColor White
}

Write-Host "`n🦸 Verification complete!" -ForegroundColor Cyan