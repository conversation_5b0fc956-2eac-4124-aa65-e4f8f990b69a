// <PERSON>ri commands module for Bad Character Scanner
// Contains all the command handlers for frontend-backend communication

use tauri::App<PERSON><PERSON><PERSON>;
use std::sync::mpsc;
use std::time::Duration;

// <PERSON>ri command to select a file
#[tauri::command]
pub async fn select_file(app: AppHandle) -> Result<String, String> {
    use tauri_plugin_dialog::DialogExt;
    
    let (tx, rx) = mpsc::channel();
      app.dialog().file().pick_file(move |file_path| {
        let result = if let Some(path) = file_path {
            path.to_string()
        } else {
            "".to_string()
        };
        let _ = tx.send(result);
    });
    
    // Wait for the result with a timeout
    match rx.recv_timeout(Duration::from_secs(30)) {
        Ok(path) => Ok(path),
        Err(_) => Ok("".to_string()) // Timeout or user cancelled
    }
}

// <PERSON>ri command to select a folder
#[tauri::command]
pub async fn select_folder(app: AppHandle) -> Result<String, String> {
    use tauri_plugin_dialog::DialogExt;
    
    let (tx, rx) = mpsc::channel();
      app.dialog().file().pick_folder(move |folder_path| {
        let result = if let Some(path) = folder_path {
            path.to_string()
        } else {
            "".to_string()
        };
        let _ = tx.send(result);
    });
    
    // Wait for the result with a timeout
    match rx.recv_timeout(Duration::from_secs(30)) {
        Ok(path) => Ok(path),
        Err(_) => Ok("".to_string()) // Timeout or user cancelled
    }
}

// Health check command
#[tauri::command]
pub async fn health_check() -> Result<String, String> {
    Ok("Backend is healthy".to_string())
}
