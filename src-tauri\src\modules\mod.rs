// Module declarations for the refactored Bad Character Scanner
// Organizes the codebase into logical, maintainable modules

// Data structures used across the application
pub mod data_structures;

// Asset management and loading functionality
pub mod asset_manager;

// Character analysis and detection logic
pub mod character_analyzer;

// AI detection and pattern matching
pub mod ai_detection;

// Text cleaning operations
pub mod cleaning_operations;

// Pattern matching and regex operations
pub mod pattern_matching;

// Tauri command handlers
pub mod commands;

// Enhanced analysis command handlers
pub mod enhanced_commands;

// Re-export commonly used types for convenience
pub use data_structures::*;
pub use asset_manager::AssetManager;
pub use character_analyzer::CharacterAnalyzer;
pub use ai_detection::*;
pub use cleaning_operations::*;
pub use commands::*;
pub use enhanced_commands::*;
