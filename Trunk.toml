[build]
target = "./index.html"
dist = "dist"
public-url = "/"
# Enable optimizations
minify = "always"
integrity = false

[serve]
addresses = ["127.0.0.1"]
port = 1420
open = false
# Improved development performance
hot-reload = true

[watch]
ignore = ["dist", "src-tauri", "target", ".git", "node_modules"]
# Improved watch performance
throttle = 100

[clean]
# Remove deprecated clean.dist in favor of global dist setting
cargo = true
