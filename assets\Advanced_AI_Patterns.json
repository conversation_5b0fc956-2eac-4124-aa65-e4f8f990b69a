{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Advanced AI Text Detection Patterns", "version": "1.0.0", "description": "Comprehensive database for detecting AI-generated text and code with various confidence levels", "lastUpdated": "2025-06-11", "categories": {"aiCodePatterns": {"description": "Patterns that indicate AI-generated code or AI assistance in coding", "highConfidencePatterns": [{"name": "AI Assistance Acknowledgment", "pattern": "(?i)(?:generated|created|written|assisted)\\s+(?:by|with|using)\\s+(?:ai|artificial\\s+intelligence|chatgpt|gpt|claude|copilot|bard|gemini)", "description": "Direct mention of AI assistance", "severity": "informational", "confidence": 0.95}, {"name": "AI Helper Language", "pattern": "(?i)(?:i'?ll?\\s+(?:help|assist|show|guide)|let\\s+me\\s+(?:help|show|explain)|(?:i\\s+)?hope\\s+this\\s+helps)", "description": "Typical AI assistant language patterns", "severity": "medium", "confidence": 0.85}, {"name": "AI Disclaimer Language", "pattern": "(?i)(?:please\\s+(?:adapt|modify|adjust|customize)|you\\s+(?:may\\s+need\\s+to|might\\s+want\\s+to|should)|(?:feel\\s+free\\s+to|don'?t\\s+hesitate\\s+to))", "description": "Common AI disclaimer phrases", "severity": "low", "confidence": 0.75}], "mediumConfidencePatterns": [{"name": "Step-by-Step Instructions", "pattern": "(?i)(?:(?:here\\s+are\\s+the\\s+)?steps?|(?:step\\s+)?\\d+[.:)]|first[,\\s]+(?:you\\s+)?(?:need\\s+to|should)|next[,\\s]+(?:you\\s+)?(?:need\\s+to|should)|finally[,\\s]+(?:you\\s+)?(?:need\\s+to|should))", "description": "Structured step-by-step format common in AI responses", "severity": "low", "confidence": 0.65}, {"name": "Template Placeholders", "pattern": "(?i)(?:\\[(?:your|insert|add|replace)[^\\]]*\\]|<(?:your|insert|add|replace)[^>]*>|{(?:your|insert|add|replace)[^}]*})", "description": "Generic placeholders typical of AI-generated templates", "severity": "medium", "confidence": 0.7}], "lowConfidencePatterns": [{"name": "Uncertainty Language", "pattern": "(?i)(?:might\\s+(?:be|work|help)|could\\s+(?:be|work|try)|perhaps\\s+(?:try|consider)|(?:it\\s+)?(?:may|might)\\s+be\\s+(?:worth|helpful))", "description": "Hedging language common in AI responses", "severity": "low", "confidence": 0.45}, {"name": "Generic Code Comments", "pattern": "(?i)(?://\\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)|#\\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)).*(?:replace|modify|customize|adapt|adjust)", "description": "Generic placeholder comments", "severity": "low", "confidence": 0.5}]}, "advancedHomoglyphPatterns": {"description": "Advanced homoglyph attack patterns beyond simple character substitution", "criticalPatterns": [{"name": "Domain Spoofing Pattern", "pattern": "(?i)(?:https?://)?[a-z]*[а-я]+[a-z]*\\.[a-z]{2,}|(?:https?://)?[а-я]*[a-z]+[а-я]*\\.[a-z]{2,}", "description": "Mixed script domains for phishing attacks", "severity": "critical", "confidence": 0.9}, {"name": "Mathematical <PERSON><PERSON>", "pattern": "[𝐀-𝐳𝑨-𝒛𝒜-𝓩𝔄-𝔷𝕬-𝖟𝖠-𝗓𝗔-𝘇𝘈-𝙕]", "description": "Mathematical alphanumeric symbols used to bypass filters", "severity": "high", "confidence": 0.85}, {"name": "Fullwidth Character Abuse", "pattern": "[Ａ-Ｚａ-ｚ０-９]", "description": "Fullwidth characters used to evade detection", "severity": "high", "confidence": 0.8}]}, "steganographyPatterns": {"description": "Patterns indicating potential hidden data or steganographic techniques", "suspiciousPatterns": [{"name": "Excessive Zero-Width Characters", "pattern": "[\\u200B\\u200C\\u200D\\u2060]{8,}", "description": "Long sequences of invisible characters that could encode data", "severity": "high", "confidence": 0.85}, {"name": "Variation Selector <PERSON>", "pattern": "[\\uFE00-\\uFE0F]{3,}", "description": "Multiple variation selectors that could hide information", "severity": "medium", "confidence": 0.7}, {"name": "Invisible Character Data Pattern", "pattern": "([\\u200B\\u200C\\u200D]){2,}[a-zA-Z0-9]+([\\u200B\\u200C\\u200D]){2,}", "description": "Invisible characters surrounding text in a pattern", "severity": "high", "confidence": 0.8}]}, "codeInjectionPatterns": {"description": "Patterns that could indicate code injection attempts using Unicode", "dangerousPatterns": [{"name": "Zero-Width Code Injection", "pattern": "[\\u200B\\u200C\\u200D](?:function|var|let|const|class|import|export|eval|document|window)", "description": "Invisible characters before code keywords", "severity": "critical", "confidence": 0.95}, {"name": "Bidirectional Override Injection", "pattern": "[\\u202E\\u202D][^\\u202C]*(?:function|eval|script|iframe|onclick)", "description": "Right-to-left override hiding malicious code", "severity": "critical", "confidence": 0.9}]}, "advancedBidirectionalAttacks": {"description": "Sophisticated bidirectional text attacks", "patterns": [{"name": "Nested Bidirectional Controls", "pattern": "[\\u202A\\u202B].*[\\u202A\\u202B]", "description": "Nested bidirectional embedding that can confuse readers", "severity": "high", "confidence": 0.85}, {"name": "Unmatched Directional Formatting", "pattern": "[\\u202A\\u202B\\u202D\\u202E](?!.*\\u202C)", "description": "Bidirectional controls without proper termination", "severity": "high", "confidence": 0.8}, {"name": "Bidirectional Override Masking", "pattern": "\\u202E[a-zA-Z0-9\\s]*(?:password|admin|root|login|token|key|secret)", "description": "Right-to-left override hiding sensitive terms", "severity": "critical", "confidence": 0.9}]}}, "detectionRules": {"scoringSystem": {"description": "How to calculate AI detection confidence scores", "rules": [{"condition": "Multiple high-confidence patterns found", "score": 0.9, "action": "flag_as_likely_ai"}, {"condition": "Single high-confidence + multiple medium-confidence patterns", "score": 0.8, "action": "flag_as_possible_ai"}, {"condition": "Multiple medium-confidence patterns", "score": 0.65, "action": "flag_as_uncertain"}, {"condition": "Only low-confidence patterns", "score": 0.3, "action": "flag_as_unlikely"}]}, "contextualAnalysis": {"description": "Additional context clues for AI detection", "factors": [{"name": "Perfect Grammar with Generic Content", "weight": 0.3, "description": "Grammatically perfect but overly generic text"}, {"name": "Excessive Hedge Words", "weight": 0.2, "description": "Overuse of uncertainty language"}, {"name": "Structured Format", "weight": 0.25, "description": "Highly structured responses with numbered lists"}, {"name": "<PERSON>k of Personal Voice", "weight": 0.25, "description": "Generic tone lacking personal style"}]}}}