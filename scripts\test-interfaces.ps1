<#
.SYNOPSIS
    Test all interfaces of Bad Character Scanner
    
.DESCRIPTION
    Tests Bash CLI, PowerShell wrappers, and basic functionality
#>

Write-Host "=== Testing Bad Character Scanner Interfaces ===" -ForegroundColor Cyan
Write-Host ""

$script:ProjectRoot = Split-Path -Parent $PSScriptRoot
$testResults = @()

# Create test data
Write-Host "[1] Creating test data..." -ForegroundColor Yellow
$testContent = @"
Normal text here.
Zero-width: Hello$([char]0x200C)World$([char]0x200D)Test
RTL override: User $([char]0x202E)Oliver$([char]0x202C) sent
Homoglyphs: <PERSON><PERSON><PERSON> (Greek A) vs <PERSON> (Latin A)
"@

$testFile = "test-bad-chars.txt"
$testContent | Set-Content $testFile -Encoding UTF8
Write-Host "[OK] Test file created" -ForegroundColor Green

# Test 1: Direct CLI test
Write-Host ""
Write-Host "[2] Testing CLI directly..." -ForegroundColor Yellow
$cliPath = Join-Path $script:ProjectRoot "target\release\analyzer_cli.exe"
if (Test-Path $cliPath) {
    $output = & $cliPath analyze $testFile 2>&1
    if ($output -match "zero_width" -or $output -match "bidirectional" -or $output -match "homoglyph") {
        Write-Host "[OK] CLI detected bad characters" -ForegroundColor Green
        $testResults += @{Test="CLI Direct"; Result="PASS"}
    } else {
        Write-Host "[FAIL] CLI did not detect bad characters" -ForegroundColor Red
        $testResults += @{Test="CLI Direct"; Result="FAIL"}
    }
} else {
    Write-Host "[SKIP] CLI not found at $cliPath" -ForegroundColor Yellow
    $testResults += @{Test="CLI Direct"; Result="SKIP"}
}

# Test 2: PowerShell wrapper - analyze
Write-Host ""
Write-Host "[3] Testing PowerShell analyze wrapper..." -ForegroundColor Yellow
try {
    $result = & "$script:ProjectRoot\scripts\analyze-text.ps1" -FilePath $testFile 2>&1
    if ($result) {
        Write-Host "[OK] PowerShell analyze wrapper worked" -ForegroundColor Green
        $testResults += @{Test="PS Analyze"; Result="PASS"}
    } else {
        Write-Host "[FAIL] PowerShell analyze wrapper failed" -ForegroundColor Red
        $testResults += @{Test="PS Analyze"; Result="FAIL"}
    }
} catch {
    Write-Host "[FAIL] PowerShell analyze wrapper error: $_" -ForegroundColor Red
    $testResults += @{Test="PS Analyze"; Result="FAIL"; Error=$_}
}

# Test 3: PowerShell wrapper - clean
Write-Host ""
Write-Host "[4] Testing PowerShell clean wrapper..." -ForegroundColor Yellow
$cleanedFile = "test-cleaned.txt"
try {
    & "$script:ProjectRoot\scripts\clean-file.ps1" -InputPath $testFile -OutputPath $cleanedFile 2>&1
    if (Test-Path $cleanedFile) {
        $cleanedContent = Get-Content $cleanedFile -Raw
        if (-not ($cleanedContent -match '\u200C|\u200D|\u202E|\u202C')) {
            Write-Host "[OK] PowerShell clean wrapper removed bad characters" -ForegroundColor Green
            $testResults += @{Test="PS Clean"; Result="PASS"}
        } else {
            Write-Host "[FAIL] PowerShell clean wrapper did not remove all bad characters" -ForegroundColor Red
            $testResults += @{Test="PS Clean"; Result="FAIL"}
        }
        Remove-Item $cleanedFile -Force
    } else {
        Write-Host "[FAIL] PowerShell clean wrapper did not create output file" -ForegroundColor Red
        $testResults += @{Test="PS Clean"; Result="FAIL"}
    }
} catch {
    Write-Host "[FAIL] PowerShell clean wrapper error: $_" -ForegroundColor Red
    $testResults += @{Test="PS Clean"; Result="FAIL"; Error=$_}
}

# Test 4: Test with pipe input
Write-Host ""
Write-Host "[5] Testing pipe input..." -ForegroundColor Yellow
try {
    $pipeResult = "Hello$([char]0x200C)World" | & "$script:ProjectRoot\scripts\analyze-text.ps1" 2>&1
    if ($pipeResult) {
        Write-Host "[OK] Pipe input analysis worked" -ForegroundColor Green
        $testResults += @{Test="Pipe Input"; Result="PASS"}
    } else {
        Write-Host "[FAIL] Pipe input analysis failed" -ForegroundColor Red
        $testResults += @{Test="Pipe Input"; Result="FAIL"}
    }
} catch {
    Write-Host "[FAIL] Pipe input error: $_" -ForegroundColor Red
    $testResults += @{Test="Pipe Input"; Result="FAIL"; Error=$_}
}

# Test 5: Create test project and scan
Write-Host ""
Write-Host "[6] Testing codebase scan..." -ForegroundColor Yellow
$testProject = "test-project"
New-Item -ItemType Directory -Path $testProject -Force | Out-Null
"clean code" | Set-Content "$testProject\clean.js"
$testContent | Set-Content "$testProject\dirty.txt"

try {
    $scanResult = & "$script:ProjectRoot\scripts\scan-codebase.ps1" -Path $testProject 2>&1
    $scanOutput = $scanResult -join "`n"
    if ($scanOutput -match "Files scanned" -or $scanOutput -match "Issues found") {
        Write-Host "[OK] Codebase scan worked" -ForegroundColor Green
        $testResults += @{Test="Codebase Scan"; Result="PASS"}
    } else {
        Write-Host "[FAIL] Codebase scan did not produce expected output" -ForegroundColor Red
        Write-Host "Output: $scanOutput" -ForegroundColor Gray
        $testResults += @{Test="Codebase Scan"; Result="FAIL"}
    }
} catch {
    Write-Host "[FAIL] Codebase scan error: $_" -ForegroundColor Red
    $testResults += @{Test="Codebase Scan"; Result="FAIL"; Error=$_}
}

# Cleanup
Write-Host ""
Write-Host "Cleaning up..." -ForegroundColor Gray
Remove-Item $testFile -Force -ErrorAction SilentlyContinue
Remove-Item $testProject -Recurse -Force -ErrorAction SilentlyContinue

# Summary
Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
$passed = ($testResults | Where-Object { $_.Result -eq "PASS" }).Count
$failed = ($testResults | Where-Object { $_.Result -eq "FAIL" }).Count
$skipped = ($testResults | Where-Object { $_.Result -eq "SKIP" }).Count
$total = $testResults.Count

Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Skipped: $skipped" -ForegroundColor Yellow

$passRate = if ($total -gt 0) { [Math]::Round(($passed / $total) * 100, 2) } else { 0 }
Write-Host "Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } elseif ($passRate -ge 60) { "Yellow" } else { "Red" })

# Show failures
if ($failed -gt 0) {
    Write-Host ""
    Write-Host "Failed Tests:" -ForegroundColor Red
    $testResults | Where-Object { $_.Result -eq "FAIL" } | ForEach-Object {
        Write-Host "  - $($_.Test)" -ForegroundColor Red
        if ($_.Error) {
            Write-Host "    Error: $($_.Error)" -ForegroundColor DarkRed
        }
    }
}