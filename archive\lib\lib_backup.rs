use leptos::*;
use wasm_bindgen::prelude::*;

#[component]
fn App() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-blue-100 p-8">
            <h1 class="text-4xl font-bold text-blue-900">
                "🔍 Simple Test App"
            </h1>
            <p class="text-xl text-blue-700 mt-4">
                "If you can see this, the app is working!"
            </p>
        </div>
    }
}

#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
    mount_to_body(|| view! { <App/> })
}
