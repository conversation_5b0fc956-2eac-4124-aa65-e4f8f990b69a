<#
.SYNOPSIS
    Audits the codebase for correct framework usage (Tauri v2 + Leptos) and flags/removes legacy or confusing files.
.DESCRIPTION
    - Checks for Tauri v2 and Leptos usage in Cargo.toml and package.json
    - Flags files containing references to old frameworks (Tauri v1, Yew, Sycamore, etc.)
    - Finds legacy/backup/old/broken files in src and src-tauri
    - Optionally deletes flagged files if -Delete is specified
.PARAMETER Delete
    If specified, will delete flagged legacy files. Otherwise, only audits and prints results.
.EXAMPLE
    ./audit_and_cleanup_framework.ps1
    ./audit_and_cleanup_framework.ps1 -Delete
#>

param(
    [switch]$Delete
)

Write-Host "=== Audit: Framework Usage and Legacy File Cleanup ===" -ForegroundColor Cyan

$root = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $root\..  # Go to project root

# 1. Check Cargo.toml for Tauri v2 and Leptos
$cargoFiles = @("Cargo.toml", "src-tauri/Cargo.toml")
foreach ($cargo in $cargoFiles) {
    if (Test-Path $cargo) {
        $content = Get-Content $cargo -Raw
        if ($content -like '*tauri = "2*' -and $content -like '*leptos*') {
            Write-Host "[OK] $cargo contains both Tauri v2 and Leptos" -ForegroundColor Green
        } else {
            Write-Host "[WARN] $cargo missing Tauri v2 or Leptos dependency" -ForegroundColor Yellow
        }
        if ($content -match 'yew' -or $content -match 'sycamore') {
            Write-Host "[ALERT] $cargo references other frontend frameworks!" -ForegroundColor Red
        }
        if ($content -like '*tauri = "1*') {
            Write-Host "[ALERT] $cargo references Tauri v1!" -ForegroundColor Red
        }
    }
}

# 2. Check package.json for Tauri v2 CLI
if (Test-Path "package.json") {
    $pkg = Get-Content "package.json" -Raw | ConvertFrom-Json
    if ($pkg.devDependencies.'@tauri-apps/cli' -or $pkg.dependencies.'@tauri-apps/cli') {
        Write-Host "[OK] package.json includes @tauri-apps/cli" -ForegroundColor Green
    } else {
        Write-Host "[WARN] package.json missing @tauri-apps/cli" -ForegroundColor Yellow
    }
    if ($pkg.dependencies.yew -or $pkg.dependencies.sycamore) {
        Write-Host "[ALERT] package.json references other frameworks!" -ForegroundColor Red
    }
}

# 3. Scan for legacy/confusing files
$patterns = @(
    '*backup*', '*old*', '*legacy*', '*broken*', 'lib_broken*', 'lib_complete*', 'lib_complex*'
)
$legacyFiles = @()
foreach ($pattern in $patterns) {
    $legacyFiles += Get-ChildItem -Path src,src-tauri -Include $pattern -Recurse -ErrorAction SilentlyContinue | Where-Object { -not $_.PSIsContainer }
}

if ($legacyFiles.Count -gt 0) {
    Write-Host "[INFO] Found legacy/confusing files:" -ForegroundColor Yellow
    $legacyFiles | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    if ($Delete) {
        $legacyFiles | ForEach-Object {
            Remove-Item $_.FullName -Force
            Write-Host "[DELETED] $_" -ForegroundColor Red
        }
    } else {
        Write-Host "(Run with -Delete to remove these files)" -ForegroundColor Magenta
    }
} else {
    Write-Host "[OK] No legacy/confusing files found." -ForegroundColor Green
}

# 4. Scan for references to other frameworks in src/
$otherFrameworks = @('yew', 'sycamore', 'tauri::api', 'tauri v1', 'actix', 'rocket')
$srcFiles = Get-ChildItem -Path src,src-tauri -Include *.rs,*.js,*.ts,*.jsx,*.tsx -Recurse -ErrorAction SilentlyContinue | Where-Object { -not $_.PSIsContainer }
foreach ($file in $srcFiles) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    foreach ($fw in $otherFrameworks) {
        if ($content -match $fw) {
            Write-Host "[ALERT] $($file.FullName) references $fw" -ForegroundColor Red
        }
    }
}

Write-Host "=== Audit Complete ===" -ForegroundColor Cyan
