use crate::enhanced_analysis;

#[tauri::command]
pub async fn enhanced_analyze_directory(path: String) -> Result<enhanced_analysis::EnhancedAnalysisResult, String> {
    let config = enhanced_analysis::AnalyzerConfig {
        enable_pattern_detection: true,
        enable_machine_learning: false,
        enable_trend_analysis: false,
        enable_performance_profiling: true,
        batch_size: 100,
        max_file_size_mb: 10.0,
        confidence_threshold: 0.7,
        pattern_learning_enabled: false,
        auto_fix_enabled: false,
    };
    
    let engine = enhanced_analysis::EnhancedAnalysisEngine::new(config)
        .map_err(|e| format!("Failed to create enhanced analysis engine: {}", e))?;
    engine.analyze_comprehensive(path).await
        .map_err(|e| format!("Enhanced analysis failed: {}", e))
}

#[tauri::command]
pub async fn enhanced_analyze_file(path: String) -> Result<enhanced_analysis::EnhancedAnalysisResult, String> {
    let config = enhanced_analysis::AnalyzerConfig {
        enable_pattern_detection: true,
        enable_machine_learning: false,
        enable_trend_analysis: false,
        enable_performance_profiling: true,
        batch_size: 1,
        max_file_size_mb: 10.0,
        confidence_threshold: 0.7,
        pattern_learning_enabled: false,
        auto_fix_enabled: false,
    };
    
    let engine = enhanced_analysis::EnhancedAnalysisEngine::new(config)
        .map_err(|e| format!("Failed to create enhanced analysis engine: {}", e))?;
    engine.analyze_comprehensive(path).await
        .map_err(|e| format!("Enhanced file analysis failed: {}", e))
}

#[tauri::command]
pub async fn export_enhanced_report(
    analysis_result: enhanced_analysis::EnhancedAnalysisResult,
    format: String,
    output_path: String
) -> Result<String, String> {
    match format.as_str() {
        "json" => {
            let json = serde_json::to_string_pretty(&analysis_result)
                .map_err(|e| format!("JSON serialization failed: {}", e))?;
            std::fs::write(&output_path, json)
                .map_err(|e| format!("Failed to write JSON report: {}", e))?;
        }
        "markdown" => {
            let markdown = generate_markdown_report(&analysis_result);
            std::fs::write(&output_path, markdown)
                .map_err(|e| format!("Failed to write Markdown report: {}", e))?;
        }
        "csv" => {
            let csv = generate_csv_report(&analysis_result);
            std::fs::write(&output_path, csv)
                .map_err(|e| format!("Failed to write CSV report: {}", e))?;
        }
        _ => return Err(format!("Unsupported export format: {}", format)),
    }
    
    Ok(format!("Report exported successfully to: {}", output_path))
}

fn generate_markdown_report(result: &enhanced_analysis::EnhancedAnalysisResult) -> String {
    format!(r#"# Enhanced Analysis Report
    
## Executive Summary
- **Overall Risk Score**: {:.1}%
- **Files Analyzed**: {}
- **Files with Issues**: {}
- **Total Suspicious Characters**: {}
- **Health Score**: {:.1}%

## Security Assessment
- **Critical Issues**: {}
- **High Issues**: {}
- **Medium Issues**: {}
- **Low Issues**: {}

## Performance Metrics
- **Analysis Duration**: {}ms
- **Memory Usage**: {:.1}MB

## Recommendations
{}

Generated on: {}
"#,
        result.executive_summary.overall_risk_score,
        result.executive_summary.total_files_analyzed,
        result.executive_summary.files_with_issues,
        result.executive_summary.total_suspicious_chars,
        result.executive_summary.health_score,
        result.executive_summary.critical_issues,
        result.executive_summary.high_issues,
        result.executive_summary.medium_issues,
        result.executive_summary.low_issues,
        result.performance_metrics.analysis_duration_ms,
        result.performance_metrics.memory_usage_mb,
        result.recommendations.iter()
            .map(|r| format!("- **{}**: {}", r.title, r.description))
            .collect::<Vec<_>>()
            .join("\n"),
        result.metadata.timestamp.format("%Y-%m-%d %H:%M:%S UTC")
    )
}

fn generate_csv_report(result: &enhanced_analysis::EnhancedAnalysisResult) -> String {
    let mut csv = String::from("Category,Metric,Value\n");
    csv.push_str(&format!("Summary,Overall Risk Score,{:.1}\n", result.executive_summary.overall_risk_score));
    csv.push_str(&format!("Summary,Files Analyzed,{}\n", result.executive_summary.total_files_analyzed));
    csv.push_str(&format!("Summary,Files with Issues,{}\n", result.executive_summary.files_with_issues));
    csv.push_str(&format!("Summary,Suspicious Characters,{}\n", result.executive_summary.total_suspicious_chars));
    csv.push_str(&format!("Issues,Critical,{}\n", result.executive_summary.critical_issues));
    csv.push_str(&format!("Issues,High,{}\n", result.executive_summary.high_issues));
    csv.push_str(&format!("Issues,Medium,{}\n", result.executive_summary.medium_issues));
    csv.push_str(&format!("Issues,Low,{}\n", result.executive_summary.low_issues));
    csv.push_str(&format!("Performance,Duration (ms),{}\n", result.performance_metrics.analysis_duration_ms));
    csv.push_str(&format!("Performance,Memory (MB),{:.1}\n", result.performance_metrics.memory_usage_mb));
    csv
}
