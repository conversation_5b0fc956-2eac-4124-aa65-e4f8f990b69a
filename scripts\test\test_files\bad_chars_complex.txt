﻿This file simulates a document with various problematic Unicode characters.
It includes a Zero Width Space here -> <- (U+200B) which is invisible.
Sometimes, text might contain a No-Break Space like this: word word (U+00A0) instead of a regular space.
Be careful with directional overrides! This text ->backwards text<- (U+202E) is an example.

Soft hyphens (U+00AD) are tricky: hyphenation. They should not always be visible.
A Zero Width Non-Joiner (U+200C) can affect ligatures, like in five (fi).
If data gets corrupted, you might see a Replacement Character: ï¿½ (U+FFFD).

This file also contains a few control characters that might be problematic:
A null character: -> <- (U+0000).
And a Form Feed character here -> <- (U+000C) which might cause a page break.
Finally, an Ideographic Space -> <- (U+3000) which is wider than a normal space.
End of mixed severity test.
