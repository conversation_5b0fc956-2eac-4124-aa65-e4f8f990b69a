# BUILD-2 - Critical Tauri v2+ Framework Cleanup and Build Fixes

**Priority:** 🚨 CRITICAL  
**Category:** Infrastructure  
**Created:** June 11, 2025  
**Status:** 🔴 OPEN  
**Related:** BCS-101  

## 🚨 Critical Issues Identified

### 1. Tauri v1/v2 Configuration Conflicts
- **Issue:** Both `tauri.conf.json` (v1) and `tauri.config.json` (v2) exist
- **Impact:** Confusion in build system, potential v1 fallback behavior
- **Priority:** CRITICAL

### 2. Build System Errors 
- **Issue:** Multiple Rust compilation errors preventing builds
- **Impact:** Application cannot be built or tested  
- **Priority:** CRITICAL

### 3. Missing Tauri v2+ Features
- **Issue:** No capabilities system, deprecated patterns used
- **Impact:** Not taking advantage of modern Tauri security and features
- **Priority:** HIGH

## 📋 Acceptance Criteria

### Configuration Cleanup
- [ ] Remove legacy `tauri.conf.json` file (v1 style)
- [ ] Consolidate to single `tauri.config.json` with latest v2 schema
- [ ] Implement Tauri v2+ capabilities system
- [ ] Update to latest stable Tauri v2 dependencies (beyond 2.5.x if available)
- [ ] Configure proper asset bundling for v2

### Build System Fixes
- [ ] Resolve all missing module errors (`cleaning_operations`, `pattern_matching`, `commands`)
- [ ] Fix duplicate `CharacterAnalyzer` implementations
- [ ] Resolve type mismatches and private field access errors
- [ ] Fix macro parsing errors
- [ ] `cargo check` passes in `src-tauri`
- [ ] `cargo build` succeeds in `src-tauri`

### Modern Tauri v2+ Features
- [ ] Implement capabilities-based security model
- [ ] Use latest plugin versions (shell, dialog, fs)
- [ ] Remove any remaining v1 API patterns
- [ ] Update error handling to v2 patterns

### Testing & Verification
- [ ] `npm run tauri dev` runs successfully
- [ ] Frontend loads without errors
- [ ] Backend commands work correctly
- [ ] No Tauri v1 warnings or deprecated API usage

## 🔧 Technical Details

### Current Dependency Versions
```toml
tauri = "~2.5.1"
tauri-build = "~2.2.0" 
tauri-plugin-shell = "~2.2.1"
tauri-plugin-dialog = "~2.2.2"
tauri-plugin-fs = "~2.3.0"
```

### Target Versions (Latest Stable)
- **Tauri Core:** 2.6.x (if available)
- **Tauri Build:** 2.3.x (if available)
- **Plugins:** Latest compatible versions

### Build Errors to Fix
1. Missing modules in `src-tauri/src/modules/mod.rs`
2. Duplicate `CharacterAnalyzer` definitions
3. Macro parsing errors with Unicode characters
4. Private field access violations
5. Import resolution failures

## 🎯 Implementation Plan

### Phase 1: Configuration Cleanup
1. Audit both config files for differences
2. Merge into single modern `tauri.config.json`
3. Remove legacy `tauri.conf.json`
4. Add capabilities system configuration

### Phase 2: Dependency Updates
1. Update to latest stable Tauri v2 versions
2. Verify plugin compatibility
3. Test dependency resolution

### Phase 3: Build System Fixes
1. Create missing module files
2. Resolve duplicate implementations
3. Fix access modifiers and imports
4. Test compilation

### Phase 4: Tauri v2+ Modernization
1. Implement capabilities-based security
2. Update API usage patterns
3. Remove deprecated patterns
4. Add v2-specific optimizations

### Phase 5: Verification
1. Full build test
2. Runtime testing
3. Feature verification
4. Performance validation

## 📊 Progress Tracking

- [ ] **Configuration Audit** - Identify all differences between config files
- [ ] **Legacy File Removal** - Clean up v1 artifacts
- [ ] **Dependency Updates** - Latest stable versions
- [ ] **Build Error Resolution** - All compilation errors fixed
- [ ] **v2 Feature Implementation** - Capabilities, modern patterns
- [ ] **Testing & Verification** - Full application functionality

## 🚨 Blockers & Dependencies

**Blocking:**
- BCS-101 testing completion

**Dependencies:**
- Latest Tauri documentation review
- Capabilities system understanding
- Plugin version compatibility matrix

## 📝 Notes

This ticket addresses the fundamental framework issues preventing proper Tauri v2+ utilization. Must be completed before any other framework-related work.

**Next Steps:**
1. Start with configuration cleanup
2. Update dependencies to absolute latest
3. Systematically fix build errors
4. Implement modern v2+ patterns

---

**Created by:** Framework Audit  
**Estimated Time:** 4-6 hours  
**Priority:** Must complete before continuing development
