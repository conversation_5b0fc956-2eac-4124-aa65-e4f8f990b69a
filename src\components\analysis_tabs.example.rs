// ARCHIVED: This file contains the original tab components for reference only.
// It is not used by the application. See the app_layout/ folder for the live components.
//
// #[component]
// pub fn CharactersTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
//     ...existing code...
// }
//
// #[component]
// pub fn SecurityTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
//     ...existing code...
// }
//
// #[component]
// pub fn PatternsTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
//     ...existing code...
// }
//
// #[component]
// pub fn EncodingTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
//     ...existing code...
// }
//
// #[component]
// pub fn ExportTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
//     ...existing code...
// }
