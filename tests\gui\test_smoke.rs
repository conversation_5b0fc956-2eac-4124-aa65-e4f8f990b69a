// tests/gui/test_smoke.rs
//! Basic smoke test: app launches and main UI renders.

#[test]
fn smoke_test_app_launch() {
    // This is a placeholder. For real E2E, use Playwright or similar.
    // Here, we just check that the app binary exists and can be launched.
    let output = std::process::Command::new("cargo")
        .args(["tauri", "build"])
        .output()
        .expect("Failed to run cargo tauri build");
    assert!(output.status.success(), "App should build successfully");
}
