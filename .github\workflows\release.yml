name: Release

on:
  push:
    tags:
      - 'v*' # Push events matching v1.0, v20.15.10, etc.

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Get version from tag
        id: get_version
        run: |
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
      
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ steps.get_version.outputs.version }}
          body: |
            ## What's Changed
            
            ### 🚀 New Features
            - Add your new features here
            
            ### 🐛 Bug Fixes
            - Fix any bugs here
            
            ### 📚 Documentation
            - Update documentation here
            
            ### 🧰 Maintenance
            - Dependency updates
            - CI/CD improvements
            
            ### 🔗 Full Changelog
            [View full changelog](https://github.com/your-username/laptos-tauri-v2/compare/previous-tag...${{ github.ref }})
          draft: false
          prerelease: false

  build:
    name: Build Release
    needs: create-release
    strategy:
      matrix:
        platform: [windows-latest, ubuntu-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
          override: true
          target: wasm32-unknown-unknown
      
      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
          cache: 'npm'
      
      - name: Install Tauri CLI
        run: |
          npm install -g @tauri-apps/cli
      
      - name: Install system dependencies (Linux)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
      
      - name: Install system dependencies (macOS)
        if: matrix.platform == 'macos-latest'
        run: |
          brew install create-dmg
      
      - name: Cache cargo registry
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
      
      - name: Build application
        run: |
          cargo tauri build --verbose
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: release-${{ matrix.platform }}
          path: |
            src-tauri/target/release/
            !src-tauri/target/release/build/
            !src-tauri/target/release/deps/
            !src-tauri/target/release/examples/
            !src-tauri/target/release/incremental/
          if-no-files-found: error
          retention-days: 5
      
      - name: Upload release assets
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: |
            src-tauri/target/release/bundle/**/*.dmg
            src-tauri/target/release/bundle/**/*.app.tar.gz
            src-tauri/target/release/bundle/**/*.app.tar.gz.sig
            src-tauri/target/release/bundle/**/*.AppImage
            src-tauri/target/release/bundle/**/*.deb
            src-tauri/target/release/bundle/**/*.rpm
            src-tauri/target/release/bundle/**/*.msi
