﻿#!/usr/bin/env powershell
# Comprehensive Code Quality Checker for Bad Character Scanner
# Runs multiple quality checks and provides detailed reporting

param(
    [switch]$Fix,
    [switch]$Verbose,
    [switch]$SkipTests,
    [switch]$Help
)

$ErrorActionPreference = "Continue"

# Colors for output
$Colors = @{
    Green = [ConsoleColor]::Green
    Yellow = [ConsoleColor]::Yellow
    Red = [ConsoleColor]::Red
    Cyan = [ConsoleColor]::<PERSON><PERSON> = [ConsoleColor]::Blue
    Magenta = [ConsoleColor]::Magenta
}

function Show-Help {
    Write-Host "🔍 Bad Character Scanner - Quality Checker" -ForegroundColor $Colors.Cyan
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor $Colors.Blue
    Write-Host "    .\check-quality.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor $Colors.Blue
    Write-Host "    -Fix         Automatically fix issues where possible"
    Write-Host "    -Verbose     Enable verbose output"
    Write-Host "    -SkipTests   Skip running tests (faster check)"
    Write-Host "    -Help        Show this help message"
    Write-Host ""
    Write-Host "CHECKS PERFORMED:" -ForegroundColor $Colors.Blue
    Write-Host "    1. Bad character detection (accessibility)"
    Write-Host "    2. Rust code formatting"
    Write-Host "    3. Clippy lints"
    Write-Host "    4. Unit tests"
    Write-Host "    5. Tauri configuration validation"
    Write-Host "    6. Project structure validation"
    exit 0
}

function Write-CheckHeader {
    param([string]$Title, [int]$Number)
    Write-Host ""
    Write-Host "🔍 $Number. $Title" -ForegroundColor $Colors.Cyan
    Write-Host ("=" * 50) -ForegroundColor $Colors.Blue
}

function Write-CheckResult {
    param([string]$Check, [bool]$Passed, [string]$Details = "")
    if ($Passed) {
        Write-Host "✅ $Check" -ForegroundColor $Colors.Green
    } else {
        Write-Host "❌ $Check" -ForegroundColor $Colors.Red
        if ($Details) {
            Write-Host "   $Details" -ForegroundColor $Colors.Yellow
        }
    }
}

function Test-BadCharacters {
    Write-CheckHeader "Bad Character Detection (Accessibility)" 1

    if (-not (Test-Path "scripts\check-bad-characters.js")) {
        Write-CheckResult "Bad character checker" $false "Script not found"
        return $false
    }

    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-CheckResult "Node.js dependency" $false "Node.js not found"
        return $false
    }

    try {
        $output = node "scripts\check-bad-characters.js" 2>&1
        $success = $LASTEXITCODE -eq 0

        if ($Verbose -or -not $success) {
            Write-Host $output
        }

        Write-CheckResult "Bad character scan" $success
        return $success
    } catch {
        Write-CheckResult "Bad character scan" $false $_.Exception.Message
        return $false
    }
}

function Test-RustFormatting {
    Write-CheckHeader "Rust Code Formatting" 2

    try {
        if ($Fix) {
            Write-Host "🔧 Auto-fixing formatting issues..." -ForegroundColor $Colors.Yellow
            cargo fmt
            $success = $LASTEXITCODE -eq 0
        } else {
            $output = cargo fmt --check 2>&1
            $success = $LASTEXITCODE -eq 0

            if ($Verbose -or -not $success) {
                Write-Host $output
            }
        }

        Write-CheckResult "Rust formatting" $success
        return $success
    } catch {
        Write-CheckResult "Rust formatting" $false $_.Exception.Message
        return $false
    }
}

function Test-ClippyLints {
    Write-CheckHeader "Clippy Lints" 3

    try {
        if ($Fix) {
            Write-Host "🔧 Running Clippy with auto-fix..." -ForegroundColor $Colors.Yellow
            $output = cargo clippy --fix --allow-dirty --allow-staged -- -D warnings 2>&1
        } else {
            $output = cargo clippy -- -D warnings 2>&1
        }

        $success = $LASTEXITCODE -eq 0

        if ($Verbose -or -not $success) {
            Write-Host $output
        }

        Write-CheckResult "Clippy lints" $success
        return $success
    } catch {
        Write-CheckResult "Clippy lints" $false $_.Exception.Message
        return $false
    }
}

function Test-UnitTests {
    if ($SkipTests) {
        Write-Host "⏭️  Skipping tests (--SkipTests flag)" -ForegroundColor $Colors.Yellow
        return $true
    }

    Write-CheckHeader "Unit Tests" 4

    try {
        $output = cargo test --quiet 2>&1
        $success = $LASTEXITCODE -eq 0

        if ($Verbose -or -not $success) {
            Write-Host $output
        }

        Write-CheckResult "Unit tests" $success
        return $success
    } catch {
        Write-CheckResult "Unit tests" $false $_.Exception.Message
        return $false
    }
}

function Test-TauriConfig {
    Write-CheckHeader "Tauri Configuration" 5

    $configPath = "src-tauri\tauri.conf.json"
    if (-not (Test-Path $configPath)) {
        Write-CheckResult "Tauri config file" $false "File not found: $configPath"
        return $false
    }

    try {
        $config = Get-Content $configPath | ConvertFrom-Json
        Write-CheckResult "Tauri config syntax" $true

        # Check for required fields
        $requiredFields = @("package", "tauri", "build")
        $allFieldsPresent = $true

        foreach ($field in $requiredFields) {
            if (-not $config.PSObject.Properties.Name.Contains($field)) {
                Write-CheckResult "Required field: $field" $false
                $allFieldsPresent = $false
            }
        }

        if ($allFieldsPresent) {
            Write-CheckResult "Tauri config structure" $true
        }

        return $allFieldsPresent
    } catch {
        Write-CheckResult "Tauri config validation" $false $_.Exception.Message
        return $false
    }
}

function Test-ProjectStructure {
    Write-CheckHeader "Project Structure" 6

    $requiredFiles = @(
        "Cargo.toml",
        "src-tauri\Cargo.toml",
        "src\lib.rs",
        "index.html"
    )

    $allFilesPresent = $true

    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-CheckResult "Required file: $file" $true
        } else {
            Write-CheckResult "Required file: $file" $false
            $allFilesPresent = $false
        }
    }

    return $allFilesPresent
}

function Show-Summary {
    param([hashtable]$Results)

    Write-Host ""
    Write-Host "📊 QUALITY CHECK SUMMARY" -ForegroundColor $Colors.Magenta
    Write-Host ("=" * 50) -ForegroundColor $Colors.Blue

    $totalChecks = $Results.Count
    $passedChecks = ($Results.Values | Where-Object { $_ -eq $true }).Count
    $failedChecks = $totalChecks - $passedChecks

    foreach ($check in $Results.GetEnumerator()) {
        $status = if ($check.Value) { "✅ PASS" } else { "❌ FAIL" }
        $color = if ($check.Value) { $Colors.Green } else { $Colors.Red }
        Write-Host "$($check.Key): $status" -ForegroundColor $color
    }

    Write-Host ""
    Write-Host "Results: $passedChecks/$totalChecks checks passed" -ForegroundColor $(if ($failedChecks -eq 0) { $Colors.Green } else { $Colors.Yellow })

    if ($failedChecks -eq 0) {
        Write-Host "🎉 ALL CHECKS PASSED! Code quality is excellent!" -ForegroundColor $Colors.Green
        return 0
    } else {
        Write-Host "⚠️  $failedChecks check(s) failed. Please review and fix the issues above." -ForegroundColor $Colors.Red
        Write-Host ""
        Write-Host "💡 Quick fixes:" -ForegroundColor $Colors.Blue
        Write-Host "  • Run with -Fix flag to auto-fix formatting and some lints" -ForegroundColor $Colors.Yellow
        Write-Host "  • Use -Verbose flag for detailed output" -ForegroundColor $Colors.Yellow
        return 1
    }
}

# Main execution
if ($Help) {
    Show-Help
}

Write-Host "🔍 Bad Character Scanner - Comprehensive Quality Check" -ForegroundColor $Colors.Cyan
Write-Host "======================================================" -ForegroundColor $Colors.Cyan

$project_root = Split-Path -Parent $PSScriptRoot
Set-Location $project_root

Write-Host "📍 Project: $project_root" -ForegroundColor $Colors.Blue
if ($Fix) {
    Write-Host "🔧 Auto-fix mode enabled" -ForegroundColor $Colors.Yellow
}

# Run all checks
$results = @{
    "Bad Characters" = Test-BadCharacters
    "Rust Formatting" = Test-RustFormatting
    "Clippy Lints" = Test-ClippyLints
    "Unit Tests" = Test-UnitTests
    "Tauri Config" = Test-TauriConfig
    "Project Structure" = Test-ProjectStructure
}

# Show summary and exit with appropriate code
$exitCode = Show-Summary $results
exit $exitCode
