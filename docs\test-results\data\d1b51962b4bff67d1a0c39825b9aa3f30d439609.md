# Page snapshot

```yaml
- banner:
  - img
  - heading "Bad Character Scanner" [level=1]
  - paragraph: Advanced Unicode Analysis & Security Scanner
  - paragraph: with comprehensive text analysis capabilities - By <PERSON>
  - button "Text Analysis & Cleaning":
    - img
    - text: Text Analysis & Cleaning
  - button "Code Base Analysis & Cleaning":
    - img
    - text: Code Base Analysis & Cleaning
  - button "Settings":
    - img
    - text: Settings
- main:
  - img
  - heading "Bad Character Scanner" [level=2]
  - paragraph: Advanced Unicode Analysis & Security Scanner
  - paragraph: with comprehensive text analysis capabilities - By <PERSON>
  - img
  - heading "Text Input" [level=3]
  - textbox "Enter or paste text to analyze... Supports Unicode, control characters, invisible characters, and more."
  - text: "Characters: 0 Bytes: 0"
  - button "Analyze Text" [disabled]:
    - img
    - text: Analyze Text
  - button "Clean Text" [disabled]:
    - img
    - text: Clean Text
  - img
  - text: "Quick Test Samples:"
  - button "Regular Text"
  - button "Zero-Width Characters"
  - button "Mixed Scripts"
  - button "Control Characters"
  - button "Bidirectional Override"
  - button "Emoji Text"
  - button "Combining Characters"
```