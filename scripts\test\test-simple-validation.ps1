# Simple CLI and Bash Script Test
# Tests the core functionality

$ErrorActionPreference = "Continue"
$ProjectRoot = $PWD
$CLIBinary = Join-Path $ProjectRoot "target\release\analyzer_cli.exe"
$BashScript = Join-Path $ProjectRoot "scripts\codebase_analyzer.sh"

Write-Host "🔍 CLI and Bash Script Validation" -ForegroundColor Cyan
Write-Host "CLI Binary: $CLIBinary" -ForegroundColor White
Write-Host "Bash Script: $BashScript" -ForegroundColor White
Write-Host ""

# Test results
$totalTests = 0
$passedTests = 0

function Test-Result {
    param($TestName, $Passed, $Details = "")
    $script:totalTests++
    if ($Passed) {
        $script:passedTests++
        Write-Host "✅ PASS: $TestName" -ForegroundColor Green
    } else {
        Write-Host "❌ FAIL: $TestName" -ForegroundColor Red
        if ($Details) {
            Write-Host "   Details: $Details" -ForegroundColor Yellow
        }
    }
}

# Test 1: Files exist
Test-Result "CLI binary exists" (Test-Path $CLIBinary)
Test-Result "Bash script exists" (Test-Path $BashScript)

# Test 2: Bash script structure
if (Test-Path $BashScript) {
    $content = Get-Content $BashScript -Raw
    Test-Result "Bash script has shebang" ($content -match "^#!/.*bash")
    Test-Result "Bash script has show_usage function" ($content -match "show_usage")
    Test-Result "Bash script has analyze_directory function" ($content -match "analyze_directory")
    Test-Result "Bash script has error handling" ($content -match "log_error")
    Test-Result "Bash script has verbose output" ($content -match "log_debug\|VERBOSE")
    
    # Count comments for documentation
    $lines = $content -split "\n"
    $commentLines = ($lines | Where-Object { $_ -match "^\s*#" }).Count
    $codeLines = ($lines | Where-Object { $_ -match "\S" -and $_ -notmatch "^\s*#" }).Count
    $commentRatio = if ($codeLines -gt 0) { [math]::Round(($commentLines / $codeLines) * 100, 2) } else { 0 }
    
    Test-Result "Bash script has good documentation" ($commentRatio -ge 15) "Comment ratio: $commentRatio%"
}

# Test 3: CLI functionality
if (Test-Path $CLIBinary) {    # Test directory
    $testDir = "test_temp_cli"
    New-Item -ItemType Directory -Path $testDir -Force | Out-Null
    $testFile = Join-Path $testDir "test.js"
    "console.log('test');" | Out-File -FilePath $testFile -Encoding UTF8
    
    try {
        # Test CLI analyze
        $result = & $CLIBinary analyze $testDir json 2>&1
        $exitCode = $LASTEXITCODE
        Test-Result "CLI analyze command works" ($exitCode -eq 0) "Exit code: $exitCode"
        
        # Test if output is valid JSON
        if ($exitCode -eq 0 -and $result) {
            try {
                $jsonResult = $result | ConvertFrom-Json
                Test-Result "CLI produces valid JSON" $true
            } catch {
                Test-Result "CLI produces valid JSON" $false "JSON parsing failed"
            }
        }
        
        # Test error handling
        $result = & $CLIBinary analyze "nonexistent" json 2>&1
        $exitCode = $LASTEXITCODE
        Test-Result "CLI handles missing directory" ($exitCode -ne 0) "Exit code: $exitCode"
        
    } catch {
        Test-Result "CLI testing failed" $false $_.Exception.Message
    }
    
    # Cleanup
    Remove-Item $testDir -Recurse -Force -ErrorAction SilentlyContinue
}

# Test 4: Bash script functions and structure validation
if (Test-Path $BashScript) {
    $content = Get-Content $BashScript -Raw
    
    # Required functions
    $requiredFunctions = @(
        "show_usage",
        "analyze_directory", 
        "export_analysis",
        "scan_file",
        "run_tests",
        "run_demo",
        "check_health",
        "log_info",
        "log_error",
        "log_debug"
    )
    
    foreach ($func in $requiredFunctions) {
        $hasFunction = $content -match "$func\s*\(\)" -or $content -match "function\s+$func"
        Test-Result "Bash script has $func function" $hasFunction
    }
    
    # Check for good practices
    Test-Result "Bash script uses 'set -euo pipefail'" ($content -match "set -euo pipefail")
    Test-Result "Bash script has cleanup function" ($content -match "cleanup\s*\(\)")
    Test-Result "Bash script has proper argument parsing" ($content -match "parse_args\|while.*\[\[\s*\$#")
    Test-Result "Bash script has help/usage documentation" ($content -match "cat.*EOF.*USAGE\|echo.*USAGE")
}

# Summary
Write-Host ""
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
$color = if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" }
Write-Host "Success Rate: $successRate%" -ForegroundColor $color

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed!" -ForegroundColor Green
} else {
    Write-Host "`n❌ Some tests failed. Check the details above." -ForegroundColor Red
}
