#!/usr/bin/env powershell
# Fix "Create Cleaned Copy" functionality (CODEBASE-6)

Write-Host "FIXING CLEANING FUNCTIONALITY (CODEBASE-6)" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent

# Check current frontend and backend state
$frontend_file = Join-Path $project_root "src\lib.rs"
$backend_file = Join-Path $project_root "src-tauri\src\main_module.rs"

if (-not (Test-Path $frontend_file)) {
    Write-Host "Frontend file not found: $frontend_file" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $backend_file)) {
    Write-Host "Backend file not found: $backend_file" -ForegroundColor Red
    exit 1
}

Write-Host "Current Issue Analysis:" -ForegroundColor Yellow
Write-Host "- 'Create Cleaned Copy' button not showing progress indication" -ForegroundColor White
Write-Host "- Character removal logic not actually removing suspicious characters" -ForegroundColor White
Write-Host "- Missing user feedback during cleaning process" -ForegroundColor White

# Read current content to analyze
$frontend_content = Get-Content $frontend_file -Raw
$backend_content = Get-Content $backend_file -Raw

# Check for specific patterns that need fixing
$issues_found = 0

Write-Host "`nChecking Frontend Issues:" -ForegroundColor Yellow

if ($frontend_content -notmatch "cleaning_progress") {
    Write-Host "Issue 1: Progress indication for cleaning missing" -ForegroundColor Red
    $issues_found++
} else {
    Write-Host "Progress indication found" -ForegroundColor Green
}

if ($frontend_content -notmatch "show_cleaning_spinner") {
    Write-Host "Issue 2: Cleaning spinner/feedback missing" -ForegroundColor Red
    $issues_found++
} else {
    Write-Host "Cleaning spinner found" -ForegroundColor Green
}

Write-Host "`nChecking Backend Issues:" -ForegroundColor Yellow

if ($backend_content -notmatch "clean_file_content") {
    Write-Host "Issue 3: File cleaning logic missing" -ForegroundColor Red
    $issues_found++
} else {
    Write-Host "File cleaning logic found" -ForegroundColor Green
}

if ($backend_content -notmatch "remove_.*character") {
    Write-Host "Issue 4: Character removal implementation missing" -ForegroundColor Red
    $issues_found++
} else {
    Write-Host "Character removal implementation found" -ForegroundColor Green
}

Write-Host "`nAnalysis Results:" -ForegroundColor Yellow
Write-Host "Issues found: $issues_found/4" -ForegroundColor White

if ($issues_found -gt 0) {
    Write-Host "`nRECOMMENDED FIXES:" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    
    Write-Host "`n1. Frontend Progress Fix:" -ForegroundColor Yellow
    Write-Host "   Add cleaning_progress signal to track operation status" -ForegroundColor White
    Write-Host "   Show progress bar/spinner during cleaning" -ForegroundColor White
    
    Write-Host "`n2. Backend Character Removal Fix:" -ForegroundColor Yellow
    Write-Host "   Implement actual character removal in clean_file_content" -ForegroundColor White
    Write-Host "   Remove zero-width chars, bidirectional overrides, homoglyphs" -ForegroundColor White
    
    Write-Host "`n3. User Feedback Fix:" -ForegroundColor Yellow
    Write-Host "   Add success/error notifications after cleaning" -ForegroundColor White
    Write-Host "   Display statistics of characters removed" -ForegroundColor White
    
    Write-Host "`nQuick Implementation Steps:" -ForegroundColor Cyan
    Write-Host "1. Add cleaning_progress: RwSignal<bool> to frontend" -ForegroundColor White
    Write-Host "2. Update 'Create Cleaned Copy' button to show progress" -ForegroundColor White
    Write-Host "3. Implement character removal regex patterns in backend" -ForegroundColor White
    Write-Host "4. Add progress updates during file cleaning process" -ForegroundColor White
    
} else {
    Write-Host "`nAll cleaning functionality appears to be implemented!" -ForegroundColor Green
}

# Check for specific cleaning patterns
Write-Host "`nDetailed Pattern Analysis:" -ForegroundColor Blue

$cleaning_patterns = @(
    @{name="Zero-width characters"; pattern="\\u200b|\\u200c|\\u200d|\\ufeff"}
    @{name="Bidirectional overrides"; pattern="\\u202d|\\u202e"}
    @{name="Homoglyph detection"; pattern="[а-я]|[α-ω]"}  # Cyrillic/Greek in Latin text
    @{name="Non-breaking spaces"; pattern="\\u00a0|\\u2007|\\u202f"}
)

foreach ($pattern in $cleaning_patterns) {
    if ($backend_content -match $pattern.pattern) {
        Write-Host "  $($pattern.name): Found" -ForegroundColor Green
    } else {
        Write-Host "  $($pattern.name): Missing" -ForegroundColor Red
    }
}

Write-Host "`nFor detailed implementation guide, see:" -ForegroundColor Blue
Write-Host "   scripts\cleaning-fix-template.md" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Magenta
Write-Host "1. Implement progress indication in frontend" -ForegroundColor White
Write-Host "2. Add character removal logic in backend" -ForegroundColor White
Write-Host "3. Test with files containing suspicious characters" -ForegroundColor White
Write-Host "4. Verify cleaned files have characters actually removed" -ForegroundColor White
Write-Host "5. Update ticket CODEBASE-6 status to resolved" -ForegroundColor White

Write-Host "`nTesting Tip:" -ForegroundColor Blue
Write-Host "Create a test file with suspicious characters and verify they get removed!" -ForegroundColor White
