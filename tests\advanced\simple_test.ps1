# Advanced Live Testing Results Summary
Write-Host "Advanced Bad Character Scanner - Live Testing Results" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan

# Test 1: Zero-Width Attack
Write-Host ""
Write-Host "Test 1: Zero-Width Character Attack" -ForegroundColor Yellow
& "target\release\analyzer_cli.exe" analyze "test_live_verification\zero_width_attack.js" json | ConvertFrom-Json | ConvertTo-Json -Depth 5 | Out-File "results_test1.json"
$result1 = Get-Content "results_test1.json" | ConvertFrom-Json
Write-Host "  Expected: 3 threats | Detected: $($result1.total_suspicious_chars) | Health: $($result1.health_score)%" -ForegroundColor Green

# Test 2: Bidirectional Attack  
Write-Host ""
Write-Host "Test 2: Bidirectional Text Attack" -ForegroundColor Yellow
& "target\release\analyzer_cli.exe" analyze "test_live_verification\bidirectional_attack.js" json | ConvertFrom-Json | ConvertTo-Json -Depth 5 | Out-File "results_test2.json"
$result2 = Get-Content "results_test2.json" | ConvertFrom-Json
Write-Host "  Expected: 3 threats | Detected: $($result2.total_suspicious_chars) | Health: $($result2.health_score)%" -ForegroundColor Green

# Test 3: Homograph Attack
Write-Host ""
Write-Host "Test 3: Homograph Attack" -ForegroundColor Yellow
& "target\release\analyzer_cli.exe" analyze "test_live_verification\homograph_attack.py" json | ConvertFrom-Json | ConvertTo-Json -Depth 5 | Out-File "results_test3.json"
$result3 = Get-Content "results_test3.json" | ConvertFrom-Json
Write-Host "  Expected: 9 threats | Detected: $($result3.total_suspicious_chars) | Health: $($result3.health_score)%" -ForegroundColor Green

# Test 4: Control Characters
Write-Host ""
Write-Host "Test 4: Control Characters Attack" -ForegroundColor Yellow
& "target\release\analyzer_cli.exe" analyze "test_live_verification\control_characters.html" json | ConvertFrom-Json | ConvertTo-Json -Depth 5 | Out-File "results_test4.json"
$result4 = Get-Content "results_test4.json" | ConvertFrom-Json
Write-Host "  Expected: 7 threats | Detected: $($result4.total_suspicious_chars) | Health: $($result4.health_score)%" -ForegroundColor Green

# Test 5: Clean Reference (should be 0)
Write-Host ""
Write-Host "Test 5: Clean Reference File" -ForegroundColor Yellow
& "target\release\analyzer_cli.exe" analyze "test_live_verification\clean_reference.js" json | ConvertFrom-Json | ConvertTo-Json -Depth 5 | Out-File "results_test5.json"
$result5 = Get-Content "results_test5.json" | ConvertFrom-Json
Write-Host "  Expected: 0 threats | Detected: $($result5.total_suspicious_chars) | Health: $($result5.health_score)%" -ForegroundColor Green

# Summary
Write-Host ""
Write-Host "SUMMARY RESULTS:" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
$totalDetected = $result1.total_suspicious_chars + $result2.total_suspicious_chars + $result3.total_suspicious_chars + $result4.total_suspicious_chars + $result5.total_suspicious_chars
$expectedTotal = 3 + 3 + 9 + 7 + 0
$accuracy = [math]::Round(($totalDetected / $expectedTotal) * 100, 1)

Write-Host "Total Expected Threats: $expectedTotal" -ForegroundColor White
Write-Host "Total Detected Threats: $totalDetected" -ForegroundColor White  
Write-Host "Overall Accuracy: $accuracy%" -ForegroundColor $(if($accuracy -eq 100) {'Green'} else {'Yellow'})

if ($accuracy -eq 100) {
    Write-Host ""
    Write-Host "🎉 PERFECT SCORE! All threats detected with 100% accuracy!" -ForegroundColor Green
    Write-Host "✅ The Bad Character Scanner is working flawlessly!" -ForegroundColor Green
}

# Cleanup
Remove-Item "results_test*.json" -Force -ErrorAction SilentlyContinue
