#!/usr/bin/env python3
"""
Icon Generator for CharGuardian

This script generates application icons in various sizes and formats from a high-resolution source image.
It creates a well-organized directory structure for different platforms and use cases.

Usage:
    python generate_icons.py <source_image> [--output-dir OUTPUT_DIR] [--formats FORMATS]

Example:
    python generate_icons.py logo.png --output-dir ./dist --formats png,ico,icns,webp
"""

import os
import sys
import shutil
import argparse
from pathlib import Path
from typing import List, Set, Tuple, Optional
from enum import Enum
from PIL import Image, ImageOps

# Supported output formats
class Format(Enum):
    PNG = "png"
    ICO = "ico"
    ICNS = "icns"
    WEBP = "webp"
    SVG = "svg"  # Note: Requires input SVG to be provided

# Platform-specific icon requirements
PLATFORM_SIZES = {
    "windows": [16, 24, 32, 48, 64, 96, 128, 256],
    "macos": [16, 32, 64, 128, 256, 512, 1024],
    "web": [16, 32, 48, 64, 96, 128, 192, 256, 384, 512],
    "android": [48, 72, 96, 144, 192],
    "ios": [20, 29, 40, 50, 57, 58, 60, 72, 76, 80, 87, 100, 114, 120, 144, 152, 167, 180, 1024],
}

# Common sizes used across platforms
COMMON_SIZES = sorted(list({size for sizes in PLATFORM_SIZES.values() for size in sizes}))

def create_directory_structure(base_dir: Path) -> None:
    """Create the directory structure for the generated icons."""
    dirs = [
        base_dir / "windows",
        base_dir / "macos",
        base_dir / "web",
        base_dir / "android",
        base_dir / "ios",
        base_dir / "common"
    ]
    
    for directory in dirs:
        directory.mkdir(parents=True, exist_ok=True)

def generate_icon(
    source: Image.Image,
    size: int,
    output_path: Path,
    format: Format = Format.PNG,
    **save_kwargs
) -> None:
    """Generate and save an icon with the specified size and format."""
    try:
        # Resize the image using high-quality resampling
        resized = source.resize((size, size), Image.Resampling.LANCZOS)
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save in the appropriate format
        if format == Format.ICO:
            # ICO format requires special handling for multiple sizes
            resized.save(output_path, format='ICO', sizes=[(size, size)])
        elif format == Format.ICNS:
            # ICNS format for macOS
            if hasattr(Image, '_registered_extensions'):
                resized.save(output_path, format='ICNS')
            else:
                print(f"Warning: ICNS format not supported by this PIL installation. Skipping {output_path}")
        else:
            # Other formats (PNG, WebP, etc.)
            resized.save(output_path, format=format.value.upper(), **save_kwargs)
            
        print(f"✅ Generated: {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error generating {output_path}: {str(e)}")
        return False

def generate_icon_set(
    source_path: Path,
    output_dir: Path,
    formats: List[Format],
    sizes: Optional[List[int]] = None,
    platforms: Optional[List[str]] = None
) -> None:
    """Generate a complete set of icons for the specified platforms and formats."""
    if not source_path.exists():
        raise FileNotFoundError(f"Source image not found: {source_path}")
    
    # Default to common sizes if none specified
    if sizes is None:
        sizes = COMMON_SIZES
    
    # Default to all platforms if none specified
    if platforms is None:
        platforms = list(PLATFORM_SIZES.keys())
    
    # Load the source image
    try:
        source = Image.open(source_path)
        source = source.convert('RGBA')  # Ensure we have alpha channel
    except Exception as e:
        print(f"❌ Error loading source image: {e}")
        return
    
    # Generate icons for each platform, format, and size
    for platform in platforms:
        platform_sizes = PLATFORM_SIZES.get(platform, sizes)
        
        for size in platform_sizes:
            if size not in sizes:
                continue
                
            for format in formats:
                # Skip unsupported combinations
                if platform == 'ios' and format == Format.ICO:
                    continue  # iOS doesn't use ICO
                    
                # Determine output path
                if platform == 'common':
                    rel_path = f"{platform}/icon_{size}x{size}.{format.value}"
                else:
                    rel_path = f"{platform}/icon_{size}.{format.value}"
                
                output_path = output_dir / rel_path
                
                # Generate the icon
                generate_icon(
                    source=source,
                    size=size,
                    output_path=output_path,
                    format=format,
                    quality=95,
                    optimize=True
                )
    
    # Generate favicon.ico for web
    if Format.ICO in formats:
        favicon_path = output_dir / "web" / "favicon.ico"
        generate_icon(
            source=source,
            size=32,
            output_path=favicon_path,
            format=Format.ICO
        )
    
    # Generate apple-touch-icon.png for iOS
    if Format.PNG in formats:
        apple_touch_icon_path = output_dir / "ios" / "apple-touch-icon.png"
        generate_icon(
            source=source,
            size=180,  # Recommended size for iOS
            output_path=apple_touch_icon_path,
            format=Format.PNG
        )
    
    print("\n🎉 Icon generation complete!")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate application icons in various formats and sizes.')
    parser.add_argument('source', type=str, help='Path to the source image file')
    parser.add_argument('--output-dir', '-o', type=str, default='./dist',
                        help='Output directory for generated icons (default: ./dist)')
    parser.add_argument('--formats', '-f', type=str, default='png,ico,webp',
                        help='Comma-separated list of formats to generate (png, ico, icns, webp)')
    parser.add_argument('--platforms', '-p', type=str, default='all',
                        help='Comma-separated list of platforms (windows,macos,web,android,ios,all)')
    return parser.parse_args()

def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Parse formats
    try:
        format_names = [f.strip().lower() for f in args.formats.split(',')]
        formats = [Format(f) for f in format_names if f in [f.value for f in Format]]
        if not formats:
            raise ValueError("No valid formats specified")
    except ValueError as e:
        print(f"Error: {e}")
        print(f"Available formats: {', '.join(f.value for f in Format)}")
        sys.exit(1)
    
    # Parse platforms
    if args.platforms.lower() == 'all':
        platforms = list(PLATFORM_SIZES.keys()) + ['common']
    else:
        platforms = [p.strip().lower() for p in args.platforms.split(',')]
        platforms = [p for p in platforms if p in PLATFORM_SIZES or p == 'common']
        if not platforms:
            print(f"Warning: No valid platforms specified. Using 'common'.")
            platforms = ['common']
    
    # Set up output directory
    output_dir = Path(args.output_dir).resolve()
    create_directory_structure(output_dir)
    
    # Generate icons
    print(f"🚀 Generating icons from: {args.source}")
    print(f"📁 Output directory: {output_dir}")
    print(f"🖼️  Formats: {', '.join(f.value for f in formats)}")
    print(f"🖥️  Platforms: {', '.join(platforms)}\n")
    
    try:
        generate_icon_set(
            source_path=Path(args.source).resolve(),
            output_dir=output_dir,
            formats=formats,
            platforms=platforms
        )
    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
