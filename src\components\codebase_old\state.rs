use leptos::*;
use crate::components::codebase::types::BasicProgress;

#[derive(Clone)]
pub struct CodebaseState {
    pub is_analyzing: (ReadSignal<bool>, WriteSignal<bool>),
    pub analysis_result: (ReadSignal<Option<String>>, WriteSignal<Option<String>>),
    pub selected_path: (ReadSignal<String>, WriteSignal<String>),
    pub progress: (ReadSignal<Option<BasicProgress>>, WriteSignal<Option<BasicProgress>>),
}

impl CodebaseState {
    pub fn new() -> Self {
        Self {
            is_analyzing: create_signal(false),
            analysis_result: create_signal(None::<String>),
            selected_path: create_signal(String::new()),
            progress: create_signal(None::<BasicProgress>),
        }
    }
}