#  Quick Fix Guide - Common Solutions

**Fast solutions for common problems with Bad Character Scanner.**

---

## 🔥 **Emergency Fixes**

### **� App Won't Start**
```powershell
# Solution 1: Use startup script
.\dev_startup.ps1

# Solution 2: Manual start
trunk serve --port 1420
# In new terminal:
cargo tauri dev
```

### **⚠️ Build Errors**
```powershell
# Missing WASM target
rustup target add wasm32-unknown-unknown

# Clean and rebuild
cargo clean && trunk clean
cargo tauri dev
```

### **🔌 Port Already in Use**
```powershell
# Find processes using ports 1420/1421
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"}

# Kill stuck processes
Get-Process | Where-Object {$_.ProcessName -like "*trunk*"} | Stop-Process
```

---

## 🐛 **Common Problems & Solutions**

### **Problem: No Analysis Results**
**Symptoms**: App runs but doesn't show analysis results
```
✅ Quick Fixes:
1. Check if text actually contains Unicode characters
2. Try with known suspicious text (copy/paste from examples)
3. Clear browser cache (Ctrl+F5)
4. Restart the application
```

### **Problem: Folder Selection Not Working**
**Symptoms**: Can't select folders or drag-drop fails
```
✅ Quick Fixes:
1. Try clicking "Browse" instead of drag-drop
2. Check folder permissions
3. Try a smaller folder first
4. Restart app and try again
```

### **Problem: Export Not Working**
**Symptoms**: Export button doesn't create files
```
✅ Quick Fixes:
1. Check Downloads folder permissions
2. Try different export format (JSON vs CSV)
3. Ensure analysis was completed first
4. Check disk space availability
```

### **Problem: Slow Performance**
**Symptoms**: App is sluggish or unresponsive
```
✅ Quick Fixes:
1. Close other applications
2. Try smaller files/folders
3. Check available RAM
4. Restart the application
```

---

## 🔧 **Development Issues**

### **Frontend Issues**
```powershell
# Check browser console
# Open http://localhost:1420
# Press F12 → Console tab
# Look for JavaScript/WASM errors

# Common solutions:
trunk clean
trunk serve --port 1420
```

### **Backend Issues**
```powershell
# Check terminal output from cargo tauri dev
# Look for Rust compilation errors

# Common solutions:
cargo clean
cargo check
cargo tauri dev
```

### **IPC Communication Issues**
```powershell
# Symptoms: Frontend can't talk to backend
# Check both frontend console AND backend terminal

# Solution:
1. Ensure Tauri commands are properly registered
2. Check parameter names match frontend/backend
3. Restart both frontend and backend
```

---

## 🏥 **Diagnostic Commands**

### **Check Application Health**
```powershell
# Verify Tauri setup
cargo tauri info

# Check Node.js and npm
node --version
npm --version

# Check Rust toolchain
rustc --version
cargo --version
```

### **Check Running Processes**
```powershell
# See what's running
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*" -or $_.ProcessName -like "*leptos*"}

# Check port usage
netstat -ano | findstr :1420
netstat -ano | findstr :1421
```

### **Test Basic Functionality**
```powershell
# Run automated tests
.\test-application.ps1

# Manual test
cargo test
```

---

## 📝 **Recently Fixed Issues**

### **✅ FIXED: Invalid Args for analyze_codebase**
**Was**: `"invalid args `folderPath` for command `analyze_codebase`"`
**Fix**: Updated frontend parameter names to match backend expectations
**Status**: ✅ Resolved in current version

### **✅ FIXED: Missing WASM Target**
**Was**: Build errors related to `wasm32-unknown-unknown`
**Fix**: Added to installation instructions
**Status**: ✅ Resolved in ONBOARDING.md

### **✅ FIXED: Port Conflicts**
**Was**: Development server conflicts
**Fix**: Added port cleanup to startup script
**Status**: ✅ Resolved in dev_startup.ps1

---

## 🆘 **When All Else Fails**

### **Nuclear Option (Complete Reset)**
```powershell
# 1. Stop everything
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"} | Stop-Process

# 2. Clean everything
cargo clean
trunk clean
Remove-Item -Recurse -Force node_modules
Remove-Item -Recurse -Force target

# 3. Reinstall
npm install
cargo install trunk --locked
cargo install tauri-cli --version "^2.5"

# 4. Start fresh
.\dev_startup.ps1
```

### **Check System Requirements**
```
Minimum Requirements:
✅ Windows 10 or later
✅ PowerShell 5.1 or later  
✅ 4GB RAM minimum (8GB recommended)
✅ 2GB free disk space
✅ Admin rights for installation
```

---

## 🎯 **Prevention Tips**

### **Before Starting Development**
- Run `cargo tauri info` to verify setup
- Check that all required tools are installed
- Use the provided startup script when possible
- Keep terminals open to see error messages

### **During Development**
- Save work frequently
- Monitor both frontend and backend logs
- Test with small examples before large codebases
- Use version control to track working states

---

## 📞 **Getting More Help**

### **Documentation**
- **[🚀 ONBOARDING.md](../ONBOARDING.md)** - Setup guide
- **[⚡ QUICK_NAVIGATION.md](../QUICK_NAVIGATION.md)** - Find documents
- **[📊 Latest Status](../project-management/COMPLETE_SUCCESS_FULL_STACK_WORKING.md)** - Project status

### **Recent Fixes**
- **[📋 Status Reports](../project-management/status-reports/)** - Recent bug fixes
- **[📚 Working Code](../reference/working-versions/)** - Known good implementations

---

**Last Updated**: June 17, 2025 | **Status**: Current and tested
**Before** (causing the error):
```rust
let args = serde_json::json!({ "path": folder_path });
```

**After** (fixed):
```rust
let args = serde_json::json!({ "folderPath": folder_path });
```

### Backend Command Signature (unchanged):
```rust
#[tauri::command]
pub async fn analyze_codebase(folder_path: String, app_handle: tauri::AppHandle) -> Result<CodeBaseAnalysisResult, String>
```

## 🎯 Why This Happened

1. **Parameter Binding**: Tauri automatically maps JSON parameters to Rust function parameters by name
2. **Case Sensitivity**: Parameter names must match exactly (including camelCase)
3. **Frontend-Backend Contract**: The parameter names form a contract between frontend and backend
4. **Serde Serialization**: The JSON keys must match the Rust parameter names for proper deserialization

## 🔍 How to Prevent Similar Issues

### 1. **Use Type-Safe Parameter Structures**
Instead of:
```rust
let args = serde_json::json!({ "folderPath": folder_path });
```

Consider:
```rust
#[derive(Serialize)]
struct AnalyzeCodebaseArgs {
    folder_path: String,  // Note: Rust naming convention
}

let args = AnalyzeCodebaseArgs { folder_path };
```

### 2. **Consistent Naming Conventions**
- **Rust backend**: Use `snake_case` (folder_path)
- **JavaScript frontend**: Use `camelCase` (folderPath)  
- **Serde attributes**: Bridge the gap with `#[serde(rename = "folderPath")]`

### 3. **Testing Commands**
Always test Tauri commands with:
- Valid parameters
- Invalid parameters 
- Missing parameters
- Wrong parameter types

## 🐛 Other Common Issues & Quick Fixes

### Issue: "Failed to select folder"
**Fix**: Check file system permissions for the target directory

### Issue: Progress bar not updating
**Fix**: This is usually normal for small projects that analyze quickly

### Issue: "Folder does not exist" 
**Fix**: Verify the selected path is valid and accessible

### Issue: Analysis takes very long
**Fix**: Avoid large dependency directories like `node_modules` or `target`

## ✅ Verification Steps

To confirm everything is working:

1. **Text Analysis Test**:
   - Enter some text with unusual characters (copy/paste this: "Hello​World" - contains zero-width space)
   - Click "Analyze Text"
   - Should detect suspicious characters

2. **Codebase Analysis Test**:
   - Select a small code directory (not the entire project root)
   - Click "Analyze Codebase"  
   - Should complete without errors

3. **Error Handling Test**:
   - Try selecting a non-existent directory
   - Should show appropriate error message (not crash)

## 📋 Development Notes

- **Hot Reload**: Changes to frontend code automatically rebuild
- **Backend Changes**: Require full restart of `cargo tauri dev`
- **Error Console**: Check browser developer tools for detailed error messages
- **Logs**: Backend logs appear in the terminal running `cargo tauri dev`

---

*This fix ensures the Bad Character Scanner works reliably for codebase analysis!* 🎯
