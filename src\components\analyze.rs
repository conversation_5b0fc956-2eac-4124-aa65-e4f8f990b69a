use leptos::*;
use serde::Serialize;
use wasm_bindgen::prelude::*;
use js_sys;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>)]
struct AnalyzeRequest {
    text: String,
}

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[component]
pub fn AnalyzeComponent() -> impl IntoView {
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_result, set_analysis_result) = create_signal(None::<String>);
    let (is_analyzing, set_is_analyzing) = create_signal(false);

    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            return;
        }

        set_is_analyzing.set(true);
        spawn_local(async move {
            let args = AnalyzeRequest { text: text.clone() };
            match serde_wasm_bindgen::to_value(&args) {
                Ok(args_js) => {
                    let result = invoke("analyze_text", args_js).await;
                    // The result is already a complex object, so we need to format it
                    match js_sys::JSON::stringify(&result) {
                        Ok(json_string) => {
                            let formatted = json_string.as_string().unwrap_or_else(|| "Failed to format result".to_string());
                            // Pretty print the JSON
                            match serde_json::from_str::<serde_json::Value>(&formatted) {
                                Ok(json_value) => {
                                    match serde_json::to_string_pretty(&json_value) {
                                        Ok(pretty) => set_analysis_result.set(Some(pretty)),
                                        Err(_) => set_analysis_result.set(Some(formatted)),
                                    }
                                }
                                Err(_) => set_analysis_result.set(Some(formatted)),
                            }
                        }
                        Err(e) => set_analysis_result.set(Some(format!("Error formatting result: {:?}", e))),
                    }
                }
                Err(e) => set_analysis_result.set(Some(format!("Serialization error: {:?}", e))),
            }
            set_is_analyzing.set(false);
        });
    };

    view! {
        <div class="space-y-4">
            <div>
                <label for="analyze-input" class="block text-sm font-medium text-gray-700 mb-2">
                    "Text to Analyze"
                </label>
                <textarea
                    id="analyze-input"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="6"
                    placeholder="Enter text to analyze for suspicious characters..."
                    on:input=move |ev| {
                        set_input_text.set(event_target_value(&ev));
                    }
                    prop:value=input_text
                />
            </div>

            <button
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                on:click=analyze_text
                disabled=move || is_analyzing.get() || input_text.get().is_empty()
            >
                {move || if is_analyzing.get() { "Analyzing..." } else { "Analyze" }}
            </button>

            {move || {
                analysis_result
                    .get()
                    .map(|result| {
                        match serde_json::from_str::<serde_json::Value>(&result) {
                            Ok(json) => {
                                let risk_level = json
                                    .get("security_analysis")
                                    .and_then(|sa| sa.get("risk_level"))
                                    .and_then(|rl| rl.as_str())
                                    .unwrap_or("Unknown")
                                    .to_string();
                                let suspicious_count = json
                                    .get("suspicious_characters")
                                    .and_then(|sc| sc.as_array())
                                    .map(|arr| arr.len())
                                    .unwrap_or(0);
                                let total_chars = json
                                    .get("total_characters")
                                    .and_then(|tc| tc.as_u64())
                                    .unwrap_or(0);
                                let risk_color = match risk_level.as_str() {
                                    "Critical" => "text-red-600",
                                    "High" => "text-orange-600",
                                    "Medium" => "text-yellow-600",
                                    _ => "text-green-600",
                                };
                                // Try to parse the result as JSON for better display

                                view! {
                                    <div class="mt-4 space-y-4">
                                        <div class="p-4 bg-gray-50 rounded-md">
                                            <h3 class="text-lg font-semibold mb-2">
                                                "Analysis Summary"
                                            </h3>
                                            <div class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <span class="text-sm text-gray-600">"Risk Level: "</span>
                                                    <span class=format!(
                                                        "font-semibold {}",
                                                        risk_color,
                                                    )>{risk_level}</span>
                                                </div>
                                                <div>
                                                    <span class="text-sm text-gray-600">
                                                        "Total Characters: "
                                                    </span>
                                                    <span class="font-semibold">{total_chars}</span>
                                                </div>
                                                <div>
                                                    <span class="text-sm text-gray-600">
                                                        "Suspicious Characters: "
                                                    </span>
                                                    <span class="font-semibold text-red-600">
                                                        {suspicious_count}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <details class="p-4 bg-gray-50 rounded-md">
                                            <summary class="cursor-pointer font-semibold">
                                                "Full Analysis Details"
                                            </summary>
                                            <pre class="mt-2 whitespace-pre-wrap text-xs overflow-auto max-h-96">
                                                {result}
                                            </pre>
                                        </details>
                                    </div>
                                }
                            }
                            Err(_) => {
                                view! {
                                    <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                        <h3 class="text-lg font-semibold mb-2">
                                            "Analysis Results"
                                        </h3>
                                        <pre class="whitespace-pre-wrap text-sm">{result}</pre>
                                    </div>
                                }
                            }
                        }
                    })
            }}
        </div>
    }
}