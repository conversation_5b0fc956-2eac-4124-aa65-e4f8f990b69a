use std::fs;

// Import the main.rs module (for testing the clean_text function)
#[path = "../src-tauri/src/main.rs"]
mod main_module;

#[test]
fn test_enhanced_character_cleaning() {
    // Test string containing all 18 problematic characters
    let test_content = "Final test of the 18 remaining characters:

1. U+FFFF: \u{FFFF}
2. U+110BD: \u{110BD}
3. U+1BCA0: \u{1BCA0}
4. U+1BCA1: \u{1BCA1}
5. U+1BCA2: \u{1BCA2}
6. U+1BCA3: \u{1BCA3}
7. U+1D173: \u{1D173}
8. U+1D174: \u{1D174}
9. U+1D175: \u{1D175}
10. U+1D176: \u{1D176}
11. U+1D177: \u{1D177}
12. U+1D178: \u{1D178}
13. U+1D179: \u{1D179}
14. U+1D17A: \u{1D17A}
15. U+E0000: \u{E0000}
16. U+E0001: \u{E0001}
17. U+E0020: \u{E0020}
18. U+E007F: \u{E007F}

These should all be removed by the enhanced cleaning function.";

    // Count suspicious characters before cleaning
    let suspicious_before: Vec<char> = test_content.chars()
        .filter(|&ch| {
            let code = ch as u32;
            matches!(ch, '\u{FFFF}' | '\u{110BD}' | '\u{1BCA0}' | '\u{1BCA1}' | '\u{1BCA2}' | '\u{1BCA3}' |
                     '\u{1D173}' | '\u{1D174}' | '\u{1D175}' | '\u{1D176}' | '\u{1D177}' | '\u{1D178}' |
                     '\u{1D179}' | '\u{1D17A}' | '\u{E0000}' | '\u{E0001}' | '\u{E0020}' | '\u{E007F}')
        })
        .collect();

    println!("Suspicious characters before cleaning: {}", suspicious_before.len());
    
    // Apply the cleaning function from main_module
    let cleaner = main_module::TextCleaner::new();
    let cleaned_content = cleaner.clean_text(test_content, "comprehensive_clean");
    
    // Count suspicious characters after cleaning
    let suspicious_after: Vec<char> = cleaned_content.chars()
        .filter(|&ch| {
            let code = ch as u32;
            matches!(ch, '\u{FFFF}' | '\u{110BD}' | '\u{1BCA0}' | '\u{1BCA1}' | '\u{1BCA2}' | '\u{1BCA3}' |
                     '\u{1D173}' | '\u{1D174}' | '\u{1D175}' | '\u{1D176}' | '\u{1D177}' | '\u{1D178}' |
                     '\u{1D179}' | '\u{1D17A}' | '\u{E0000}' | '\u{E0001}' | '\u{E0020}' | '\u{E007F}')
        })
        .collect();
    
    println!("Suspicious characters after cleaning: {}", suspicious_after.len());
    
    // Verify all suspicious characters were removed
    assert_eq!(suspicious_after.len(), 0, "All 18 problematic characters should be removed");
    
    println!("✅ Enhanced character cleaning test passed!");
}
