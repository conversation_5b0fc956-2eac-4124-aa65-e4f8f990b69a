// Playwright E2E test: Full codebase analysis flow
// E2E: Full codebase analysis, summary readability, and export
import { test, expect } from '@playwright/test';

test('Full codebase analysis flow', async ({ page }) => {
  page.on('console', msg => console.log(msg.text()));
  // Launch the Tauri app (assumes Playwright config is set up for Tauri)
  await page.goto('http://localhost:1421');

  // Wait for main UI with a longer timeout
  await expect(page.locator('text=Codebase Analysis & Character Scanner')).toBeVisible({ timeout: 15000 });

  // Simulate entering a path and running analysis
  await page.fill('input[placeholder="Enter codebase path..."]', 'test-results/test-data/valid_codebase');
  await page.click('button:has-text("Analyze")');

  // Wait for results
  await expect(page.locator('.result-msg')).toBeVisible({ timeout: 20000 });
  await expect(page.locator('.result-msg')).toContainText('Files analyzed');

  // --- New: Check summary readability ---
  const summaryText = await page.locator('.result-msg').innerText();
  expect(summaryText).toMatch(/Summary|Files analyzed|Characters|Threats|Recommendations/i);
  expect(summaryText.length).toBeGreaterThan(20);

  // --- New: Export as JSON and check download ---
  const [ download ] = await Promise.all([
    page.waitForEvent('download'),
    page.click('button:has-text("Export as JSON")'),
  ]);
  const path = await download.path();
  expect(path).toBeTruthy();
  // Optionally, read and parse the file for content checks
  await expect(page.locator('text=C:\\Users\\<USER>\\Downloads')).toBeVisible();
});

test('Export tab: disables unavailable formats and shows preview', async ({ page }) => {
  await page.goto('http://localhost:1421');
  await expect(page.locator('text=Codebase Analysis & Character Scanner')).toBeVisible({ timeout: 15000 });
  await page.click('text=Export');
  await expect(page.locator('button:has-text("Export as CSV")')).toBeDisabled();
  await expect(page.locator('button:has-text("Export as PDF Report")')).toBeDisabled();
  await expect(page.locator('button:has-text("Export as SARIF")')).toBeDisabled();
  await expect(page.locator('.export-preview')).toBeVisible();
});
