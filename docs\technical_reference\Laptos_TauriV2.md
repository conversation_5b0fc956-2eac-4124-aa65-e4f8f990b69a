# Laptos Tauri V2 Version Documentation

## Core Components & Versions

### Tauri Ecosystem

- **tauri**: v2.5.1
- **@tauri-apps/api**: v2.5.0
- **tauri-cli (Rust)**: v2.5.0
- **@tauri-apps/cli (JavaScript)**: v2.5.0
- **tauri-bundler**: v2.4.0
- **wry**: v0.51.2
- **tao**: v0.33.0

### Compatibility Matrix

| Component          | Required Version | Compatible With         |
|--------------------|------------------|-------------------------|
| tauri-build        | 2.5.0            | tauri-cli 2.5.x         |
| tauri-plugin-fs    | 2.0.0            | tauri 2.5.x             |
| tauri-plugin-shell | 2.0.0            | Rust 1.77+              |

## Critical Version Locking

```toml
# src-tauri/Cargo.toml
[dependencies]
tauri = { version = "2.5.1", features = ["api-all"] }
tauri-build = "2.5.0"  # Must match tauri-cli version

# Frontend package.json
{
  "dependencies": {
    "@tauri-apps/api": "2.5.0",
    "@tauri-apps/cli": "2.5.0"
  }
}
```

## Verification Script

See: `scripts/check-tauri-version.js` for version validation checks
