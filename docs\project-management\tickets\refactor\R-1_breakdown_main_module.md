# R-1: Refactor main_module.rs into Sub-Modules

**Priority:** Medium
**Category:** Refactoring
**Status:** OPEN

## Goal
Improve code maintainability and readability by breaking down the large `src-tauri/src/main_module.rs` file into smaller, more focused sub-modules within a dedicated directory structure (e.g., `src-tauri/src/main_module_components/`).

## Problem Statement
The `src-tauri/src/main_module.rs` file has become very large, encompassing a wide range of functionalities including Tauri command handlers, helper functions, and potentially distinct logical units. This makes the file difficult to navigate, understand, and maintain, increasing cognitive load and the risk of errors.

## Proposed Solution
1.  **Analyze `main_module.rs`:** Identify distinct logical sections within the file. This could include:
    *   Tauri command handlers related to character analysis.
    *   Tauri command handlers related to file operations.
    *   Tauri command handlers related to text cleaning.
    *   Tauri command handlers related to reporting.
    *   Helper functions for specific tasks.
    *   Struct/enum definitions specific to these commands (if not already in global modules).
2.  **Create a New Directory Structure:** Create a new directory, for example, `src-tauri/src/main_module_components/`.
3.  **Create Sub-Module Files:** Within this new directory, create separate `.rs` files for each identified logical section (e.g., `analysis_commands.rs`, `cleaning_commands.rs`, `file_commands.rs`, `helpers.rs`, etc.).
4.  **Create `mod.rs`:** Add a `mod.rs` file inside `src-tauri/src/main_module_components/` to declare these new sub-modules.
5.  **Refactor Code:** Carefully move the relevant code from `main_module.rs` into the newly created sub-module files.
6.  **Update `main_module.rs`:** Modify `main_module.rs` to declare the new `main_module_components` module (e.g., `mod main_module_components;`) and `pub use` the necessary items from its sub-modules to maintain the public API.
7.  **Update Imports:** Adjust `use` statements throughout the codebase where necessary to reflect the new module paths.

## Acceptance Criteria
- `src-tauri/src/main_module.rs` is significantly reduced in size and primarily acts as an orchestrator or re-exporter for its sub-modules.
- The new sub-modules in `src-tauri/src/main_module_components/` are well-organized and contain focused logic.
- The codebase compiles successfully (`cargo check` and `cargo build` pass in `src-tauri`).
- All existing application functionality remains unchanged and all tests pass.
- Code readability and organization of the affected areas are demonstrably improved.

## Affected Files
- `src-tauri/src/main_module.rs`
- New files within `src-tauri/src/main_module_components/`
- `src-tauri/src/lib.rs` (if `main_module.rs` structure changes how it's declared)
- Any files that directly import items that are moved.

## Notes
- This refactoring should be done carefully, one logical section at a time, with frequent checks to ensure the build remains stable.
- Consider how this might affect the `ScriptDetector` struct and its implementation if it's closely tied to the logic being moved.
