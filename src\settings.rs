use leptos::*;
use serde::{Deserialize, Serialize};
use crate::icons::*;

// Settings data structures
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScannerSettings {
    pub enabled_character_types: CharacterTypes,
    pub exclusion_settings: ExclusionSettings,
    pub scanner_sensitivity: ScannerSensitivity,
    pub cli_mode_enabled: bool,
    pub auto_fix_enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CharacterTypes {
    pub forbidden_windows_chars: bool,
    pub c0_control_chars: bool,
    pub c1_control_chars: bool,
    pub zero_width_chars: bool,
    pub space_variants: bool,
    pub bidirectional_chars: bool,
    pub line_separators: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExclusionSettings {
    pub excluded_file_types: Vec<String>,
    pub excluded_patterns: Vec<String>,
    pub excluded_characters: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScannerSensitivity {
    pub level: String, // "low", "medium", "high", "custom"
    pub include_comments: bool,
    pub include_strings: bool,
    pub include_whitespace: bool,
}

impl Default for ScannerSettings {
    fn default() -> Self {
        Self {
            enabled_character_types: CharacterTypes {
                forbidden_windows_chars: true,
                c0_control_chars: true,
                c1_control_chars: true,
                zero_width_chars: true,
                space_variants: true,
                bidirectional_chars: true,
                line_separators: true,
            },
            exclusion_settings: ExclusionSettings {
                excluded_file_types: vec!["jpg".to_string(), "png".to_string(), "pdf".to_string()],
                excluded_patterns: vec!["node_modules".to_string(), ".git".to_string()],
                excluded_characters: vec![],
            },
            scanner_sensitivity: ScannerSensitivity {
                level: "medium".to_string(),
                include_comments: true,
                include_strings: true,
                include_whitespace: false,
            },
            cli_mode_enabled: false,
            auto_fix_enabled: false,
        }
    }
}

#[component]
pub fn SettingsModal(
    show_settings: ReadSignal<bool>,
    set_show_settings: WriteSignal<bool>,
    settings: ReadSignal<ScannerSettings>,
    set_settings: WriteSignal<ScannerSettings>,
) -> impl IntoView {
    let (active_settings_tab, set_active_settings_tab) = create_signal("scanner".to_string());
    let (show_about, set_show_about) = create_signal(false);

    view! {
        {move || {
            if show_settings.get() {
                view! {
                    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                            // Header
                            <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
                                <div class="flex items-center space-x-3">
                                    <CogIcon class="header-icon text-gray-600" />
                                    <h2 class="text-xl font-semibold text-gray-900">
                                        "Settings & Information"
                                    </h2>
                                </div>
                                <button
                                    on:click=move |_| set_show_settings.set(false)
                                    class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                                >
                                    <CloseIcon class="btn-icon-md" />
                                </button>
                            </div>

                            // Content
                            <div class="flex max-h-[calc(90vh-80px)]">
                                // Sidebar navigation
                                <div class="w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
                                    <nav class="p-4 space-y-2">
                                        <button
                                            on:click=move |_| {
                                                set_active_settings_tab.set("scanner".to_string());
                                                set_show_about.set(false);
                                            }
                                            class=move || {
                                                format!(
                                                    "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors {}",
                                                    if active_settings_tab.get() == "scanner"
                                                        && !show_about.get()
                                                    {
                                                        "bg-blue-100 text-blue-700"
                                                    } else {
                                                        "text-gray-600 hover:bg-gray-100"
                                                    },
                                                )
                                            }
                                        >
                                            <AdjustmentsIcon class="w-5 h-5 mr-3" />
                                            "Scanner Settings"
                                        </button>
                                        <button
                                            on:click=move |_| {
                                                set_active_settings_tab.set("exclusions".to_string());
                                                set_show_about.set(false);
                                            }
                                            class=move || {
                                                format!(
                                                    "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors {}",
                                                    if active_settings_tab.get() == "exclusions"
                                                        && !show_about.get()
                                                    {
                                                        "bg-blue-100 text-blue-700"
                                                    } else {
                                                        "text-gray-600 hover:bg-gray-100"
                                                    },
                                                )
                                            }
                                        >
                                            <FilterIcon class="w-5 h-5 mr-3" />
                                            "Exclusions"
                                        </button>
                                        <button
                                            on:click=move |_| {
                                                set_active_settings_tab.set("cli".to_string());
                                                set_show_about.set(false);
                                            }
                                            class=move || {
                                                format!(
                                                    "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors {}",
                                                    if active_settings_tab.get() == "cli" && !show_about.get() {
                                                        "bg-blue-100 text-blue-700"
                                                    } else {
                                                        "text-gray-600 hover:bg-gray-100"
                                                    },
                                                )
                                            }
                                        >
                                            <TerminalIcon class="w-5 h-5 mr-3" />
                                            "CLI Mode"
                                        </button>
                                        <div class="border-t border-gray-200 mt-4 pt-4">
                                            <button
                                                on:click=move |_| set_show_about.set(true)
                                                class=move || {
                                                    format!(
                                                        "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors {}",
                                                        if show_about.get() {
                                                            "bg-blue-100 text-blue-700"
                                                        } else {
                                                            "text-gray-600 hover:bg-gray-100"
                                                        },
                                                    )
                                                }
                                            >
                                                <InfoIcon class="w-5 h-5 mr-3" />
                                                "About & Documentation"
                                            </button>
                                        </div>
                                    </nav>
                                </div>

                                // Main content area
                                <div class="flex-1 overflow-y-auto">
                                    {move || {
                                        if show_about.get() {
                                            view! { <AboutContent /> }.into_view()
                                        } else {
                                            match active_settings_tab.get().as_str() {
                                                "scanner" => {
                                                    view! {
                                                        <ScannerSettingsContent
                                                            settings=settings
                                                            set_settings=set_settings
                                                        />
                                                    }
                                                        .into_view()
                                                }
                                                "exclusions" => {
                                                    view! {
                                                        <ExclusionSettingsContent
                                                            settings=settings
                                                            set_settings=set_settings
                                                        />
                                                    }
                                                        .into_view()
                                                }
                                                "cli" => {
                                                    view! {
                                                        <CliSettingsContent
                                                            settings=settings
                                                            set_settings=set_settings
                                                        />
                                                    }
                                                        .into_view()
                                                }
                                                _ => {
                                                    view! { <div class="p-6">"Unknown section"</div> }
                                                        .into_view()
                                                }
                                            }
                                        }
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>
                }
                    .into_view()
            } else {
                view! { <div></div> }.into_view()
            }
        }}
    }
}

#[component]
fn ScannerSettingsContent(
    settings: ReadSignal<ScannerSettings>,
    set_settings: WriteSignal<ScannerSettings>,
) -> impl IntoView {
    view! {
        <div class="p-6">
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        "Character Types to Scan"
                    </h3>
                    <div class="space-y-3">
                        <SettingsCheckbox
                            label="Forbidden Windows Characters"
                            description="Characters like <, >, :, \", /, \\, |, ?, * that are forbidden in Windows filenames"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.forbidden_windows_chars
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.forbidden_windows_chars = checked;
                                    });
                            }
                        />
                        <SettingsCheckbox
                            label="C0 Control Characters"
                            description="Non-printable control characters (U+0000-U+001F and U+007F)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.c0_control_chars
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.c0_control_chars = checked;
                                    });
                            }
                        />
                        <SettingsCheckbox
                            label="C1 Control Characters"
                            description="Extended control characters (U+0080-U+009F)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.c1_control_chars
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.c1_control_chars = checked;
                                    });
                            }
                        />
                        <SettingsCheckbox
                            label="Zero-Width Characters"
                            description="Invisible characters like Zero Width Space (U+200B), BOM (U+FEFF)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.zero_width_chars
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.zero_width_chars = checked;
                                    });
                            }
                        />
                        <SettingsCheckbox
                            label="Space Variants"
                            description="Alternative space characters (En Space, Em Space, etc.)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.space_variants
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| s.enabled_character_types.space_variants = checked);
                            }
                        />
                        <SettingsCheckbox
                            label="Bidirectional Characters"
                            description="Text direction control characters (LRM, RLM, LRE, RLE, etc.)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.bidirectional_chars
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.bidirectional_chars = checked;
                                    });
                            }
                        />
                        <SettingsCheckbox
                            label="Line Separators"
                            description="Unicode line and paragraph separators (U+2028, U+2029)"
                            checked=Signal::derive(move || {
                                settings.get().enabled_character_types.line_separators
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| {
                                        s.enabled_character_types.line_separators = checked;
                                    });
                            }
                        />
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">"Scanner Sensitivity"</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                "Sensitivity Level"
                            </label>
                            <select
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                on:change=move |ev| {
                                    let value = event_target_value(&ev);
                                    set_settings.update(|s| s.scanner_sensitivity.level = value);
                                }
                                prop:value=move || settings.get().scanner_sensitivity.level
                            >
                                <option value="low">"Low - Basic patterns only"</option>
                                <option value="medium">"Medium - Balanced detection"</option>
                                <option value="high">"High - Maximum sensitivity"</option>
                                <option value="custom">"Custom - User defined"</option>
                            </select>
                        </div>
                        <SettingsCheckbox
                            label="Include Comments"
                            description="Scan inside code comments"
                            checked=Signal::derive(move || {
                                settings.get().scanner_sensitivity.include_comments
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| s.scanner_sensitivity.include_comments = checked);
                            }
                        />
                        <SettingsCheckbox
                            label="Include String Literals"
                            description="Scan inside quoted strings"
                            checked=Signal::derive(move || {
                                settings.get().scanner_sensitivity.include_strings
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| s.scanner_sensitivity.include_strings = checked);
                            }
                        />
                        <SettingsCheckbox
                            label="Include Whitespace Analysis"
                            description="Perform detailed whitespace character analysis"
                            checked=Signal::derive(move || {
                                settings.get().scanner_sensitivity.include_whitespace
                            })
                            on_change=move |checked| {
                                set_settings
                                    .update(|s| s.scanner_sensitivity.include_whitespace = checked);
                            }
                        />
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn ExclusionSettingsContent(
    settings: ReadSignal<ScannerSettings>,
    set_settings: WriteSignal<ScannerSettings>,
) -> impl IntoView {
    let (new_file_type, set_new_file_type) = create_signal(String::new());
    let (new_pattern, set_new_pattern) = create_signal(String::new());
    let (new_character, set_new_character) = create_signal(String::new());

    view! {
        <div class="p-6">
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">"Excluded File Types"</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        "File extensions to skip during scanning (e.g., jpg, png, pdf)"
                    </p>
                    <div class="space-y-3">
                        <div class="flex space-x-2">
                            <input
                                type="text"
                                placeholder="File extension (e.g., jpg)"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                on:input=move |ev| set_new_file_type.set(event_target_value(&ev))
                                prop:value=new_file_type
                            />
                            <button
                                on:click=move |_| {
                                    let file_type = new_file_type.get().trim().to_string();
                                    if !file_type.is_empty() {
                                        set_settings
                                            .update(|s| {
                                                if !s
                                                    .exclusion_settings
                                                    .excluded_file_types
                                                    .contains(&file_type)
                                                {
                                                    s.exclusion_settings.excluded_file_types.push(file_type);
                                                }
                                            });
                                        set_new_file_type.set(String::new());
                                    }
                                }
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                "Add"
                            </button>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            {move || {
                                settings
                                    .get()
                                    .exclusion_settings
                                    .excluded_file_types
                                    .iter()
                                    .map(|file_type| {
                                        let file_type_clone = file_type.clone();
                                        let file_type_for_remove = file_type.clone();
                                        view! {
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                                                {file_type_clone}
                                                <button
                                                    on:click=move |_| {
                                                        let file_type_to_remove = file_type_for_remove.clone();
                                                        set_settings
                                                            .update(|s| {
                                                                s.exclusion_settings
                                                                    .excluded_file_types
                                                                    .retain(|x| x != &file_type_to_remove);
                                                            });
                                                    }
                                                    class="ml-2 text-gray-500 hover:text-gray-700"
                                                >
                                                    "×"
                                                </button>
                                            </span>
                                        }
                                    })
                                    .collect_view()
                            }}
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">"Excluded Patterns"</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        "Directory or file patterns to skip (e.g., node_modules, .git)"
                    </p>
                    <div class="space-y-3">
                        <div class="flex space-x-2">
                            <input
                                type="text"
                                placeholder="Pattern (e.g., node_modules)"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                on:input=move |ev| set_new_pattern.set(event_target_value(&ev))
                                prop:value=new_pattern
                            />
                            <button
                                on:click=move |_| {
                                    let pattern = new_pattern.get().trim().to_string();
                                    if !pattern.is_empty() {
                                        set_settings
                                            .update(|s| {
                                                if !s
                                                    .exclusion_settings
                                                    .excluded_patterns
                                                    .contains(&pattern)
                                                {
                                                    s.exclusion_settings.excluded_patterns.push(pattern);
                                                }
                                            });
                                        set_new_pattern.set(String::new());
                                    }
                                }
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                "Add"
                            </button>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            {move || {
                                settings
                                    .get()
                                    .exclusion_settings
                                    .excluded_patterns
                                    .iter()
                                    .map(|pattern| {
                                        let pattern_clone = pattern.clone();
                                        let pattern_for_remove = pattern.clone();
                                        view! {
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                                                {pattern_clone}
                                                <button
                                                    on:click=move |_| {
                                                        let pattern_to_remove = pattern_for_remove.clone();
                                                        set_settings
                                                            .update(|s| {
                                                                s.exclusion_settings
                                                                    .excluded_patterns
                                                                    .retain(|x| x != &pattern_to_remove);
                                                            });
                                                    }
                                                    class="ml-2 text-gray-500 hover:text-gray-700"
                                                >
                                                    "×"
                                                </button>
                                            </span>
                                        }
                                    })
                                    .collect_view()
                            }}
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">"Excluded Characters"</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        "Specific characters to exclude from scanning (useful for false positives)"
                    </p>
                    <div class="space-y-3">
                        <div class="flex space-x-2">
                            <input
                                type="text"
                                placeholder="Character or Unicode (e.g., U+00A0)"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                on:input=move |ev| set_new_character.set(event_target_value(&ev))
                                prop:value=new_character
                            />
                            <button
                                on:click=move |_| {
                                    let character = new_character.get().trim().to_string();
                                    if !character.is_empty() {
                                        set_settings
                                            .update(|s| {
                                                if !s
                                                    .exclusion_settings
                                                    .excluded_characters
                                                    .contains(&character)
                                                {
                                                    s.exclusion_settings.excluded_characters.push(character);
                                                }
                                            });
                                        set_new_character.set(String::new());
                                    }
                                }
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                "Add"
                            </button>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            {move || {
                                settings
                                    .get()
                                    .exclusion_settings
                                    .excluded_characters
                                    .iter()
                                    .map(|character| {
                                        let character_clone = character.clone();
                                        let character_for_remove = character.clone();
                                        view! {
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                                                {character_clone}
                                                <button
                                                    on:click=move |_| {
                                                        let character_to_remove = character_for_remove.clone();
                                                        set_settings
                                                            .update(|s| {
                                                                s.exclusion_settings
                                                                    .excluded_characters
                                                                    .retain(|x| x != &character_to_remove);
                                                            });
                                                    }
                                                    class="ml-2 text-gray-500 hover:text-gray-700"
                                                >
                                                    "×"
                                                </button>
                                            </span>
                                        }
                                    })
                                    .collect_view()
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn CliSettingsContent(
    settings: ReadSignal<ScannerSettings>,
    set_settings: WriteSignal<ScannerSettings>,
) -> impl IntoView {
    view! {
        <div class="p-6">
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">"CLI Mode Settings"</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        "Enable CLI mode to use the Bad Character Scanner from the command line with bash scripts."
                    </p>

                    <SettingsCheckbox
                        label="Enable CLI Mode"
                        description="Allow command-line interface access to the scanner"
                        checked=Signal::derive(move || settings.get().cli_mode_enabled)
                        on_change=move |checked| {
                            set_settings.update(|s| s.cli_mode_enabled = checked);
                        }
                    />

                    <SettingsCheckbox
                        label="Enable Auto-Fix"
                        description="Automatically fix detected issues when possible"
                        checked=Signal::derive(move || settings.get().auto_fix_enabled)
                        on_change=move |checked| {
                            set_settings.update(|s| s.auto_fix_enabled = checked);
                        }
                    />
                </div>

                {move || {
                    if settings.get().cli_mode_enabled {
                        view! {
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-md font-medium text-gray-900 mb-3">
                                    "CLI Usage Examples"
                                </h4>
                                <div class="space-y-2 text-sm font-mono bg-gray-900 text-green-400 p-4 rounded">
                                    <div>"# Scan a single file"</div>
                                    <div>"./bcs-scanner scan --file path/to/file.txt"</div>
                                    <div></div>
                                    <div>"# Scan entire directory"</div>
                                    <div>"./bcs-scanner scan --dir path/to/directory"</div>
                                    <div></div>
                                    <div>"# Scan with auto-fix"</div>
                                    <div>"./bcs-scanner scan --dir path/to/directory --fix"</div>
                                    <div></div>
                                    <div>"# Export results to JSON"</div>
                                    <div>
                                        "./bcs-scanner scan --dir path/to/directory --output results.json"
                                    </div>
                                </div>
                            </div>
                        }
                            .into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn AboutContent() -> impl IntoView {
    view! {
        <div class="p-6 overflow-y-auto">
            <div class="space-y-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <ShieldIcon class="icon-lg text-blue-600" />
                        <h3 class="text-2xl font-semibold text-gray-900">
                            "Bad Character Scanner"
                        </h3>
                    </div>
                    <p class="text-gray-600 text-lg mb-4">
                        "A comprehensive tool for detecting and eliminating problematic Unicode characters in text and code files."
                    </p>
                    <div class="text-sm text-gray-500">
                        "Version 2.0 • Built with Leptos & Tauri • By John Shoy © 2025"
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">"🎯 What We Detect"</h4>
                    <div class="space-y-4">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h5 class="font-medium text-red-800 mb-2">
                                "Extremely Problematic Characters"
                            </h5>
                            <ul class="text-sm text-red-700 space-y-1">
                                <li>"• Zero Width Space (U+200B) - Invisible, breaks tokens"</li>
                                <li>
                                    "• Byte Order Mark (U+FEFF) - Can break scripts and parsing"
                                </li>
                                <li>
                                    "• Word Joiner (U+2060) - Invisible line-break prevention"
                                </li>
                            </ul>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h5 class="font-medium text-yellow-800 mb-2">"High Priority Issues"</h5>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                <li>
                                    "• Alternative space characters (En Space, Em Space, etc.)"
                                </li>
                                <li>"• Bidirectional text controls (LRM, RLM, LRE, RLE)"</li>
                                <li>"• Line and paragraph separators (U+2028, U+2029)"</li>
                                <li>"• No-Break Space (U+00A0) - Looks like space but isn't"</li>
                            </ul>
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h5 class="font-medium text-blue-800 mb-2">"Filename Issues"</h5>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>"• Forbidden Windows characters: < > : \" / \\ | ? *"</li>
                                <li>"• C0 Control characters (U+0000-U+001F, U+007F)"</li>
                                <li>"• C1 Control characters (U+0080-U+009F)"</li>
                                <li>"• Leading/trailing spaces in filenames"</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">
                        "📚 Character Reference"
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-medium text-gray-800 mb-3">
                            "Forbidden Windows Characters"
                        </h5>
                        <div class="overflow-x-auto">
                            <table class="min-w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-2 px-3">"Character"</th>
                                        <th class="text-left py-2 px-3">"Hex Code"</th>
                                        <th class="text-left py-2 px-3">"Description"</th>
                                    </tr>
                                </thead>
                                <tbody class="text-gray-600">
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"<"</td>
                                        <td class="py-2 px-3 font-mono">"U+003C"</td>
                                        <td class="py-2 px-3">
                                            "Less-than sign (I/O redirection)"
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">">"</td>
                                        <td class="py-2 px-3 font-mono">"U+003E"</td>
                                        <td class="py-2 px-3">
                                            "Greater-than sign (I/O redirection)"
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">":"</td>
                                        <td class="py-2 px-3 font-mono">"U+003A"</td>
                                        <td class="py-2 px-3">"Colon (drive letter separator)"</td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"\""</td>
                                        <td class="py-2 px-3 font-mono">"U+0022"</td>
                                        <td class="py-2 px-3">
                                            "Double quote (filename enclosing)"
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"/"</td>
                                        <td class="py-2 px-3 font-mono">"U+002F"</td>
                                        <td class="py-2 px-3">"Forward slash (path separator)"</td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"\\"</td>
                                        <td class="py-2 px-3 font-mono">"U+005C"</td>
                                        <td class="py-2 px-3">
                                            "Backslash (Windows path separator)"
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"|"</td>
                                        <td class="py-2 px-3 font-mono">"U+007C"</td>
                                        <td class="py-2 px-3">"Vertical bar (pipe operator)"</td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3 font-mono">"?"</td>
                                        <td class="py-2 px-3 font-mono">"U+003F"</td>
                                        <td class="py-2 px-3">"Question mark (wildcard)"</td>
                                    </tr>
                                    <tr>
                                        <td class="py-2 px-3 font-mono">"*"</td>
                                        <td class="py-2 px-3 font-mono">"U+002A"</td>
                                        <td class="py-2 px-3">"Asterisk (wildcard)"</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">
                        "⚠️ Invisible Character Threats"
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="space-y-3 text-sm text-gray-700">
                            <div class="border-l-4 border-red-500 pl-4">
                                <strong class="text-red-700">"Zero Width Space (U+200B):"</strong>
                                " Completely invisible character that can break identifiers, keywords, or numbers by splitting them into separate tokens. Extremely difficult to spot visually and can cause syntax errors."
                            </div>
                            <div class="border-l-4 border-yellow-500 pl-4">
                                <strong class="text-yellow-700">"No-Break Space (U+00A0):"</strong>
                                " Visually identical to a regular space but prevents line breaks. Can cause parsing errors when used instead of regular spaces in code syntax."
                            </div>
                            <div class="border-l-4 border-blue-500 pl-4">
                                <strong class="text-blue-700">"Bidirectional Controls:"</strong>
                                " Characters like LRE (U+202A) and RLE (U+202B) can drastically alter the visual appearance and logical order of code, potentially hiding malicious content."
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">"🛠️ How It Works"</h4>
                    <div class="space-y-3 text-gray-600">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                1
                            </div>
                            <div>
                                <strong>"Text Analysis:"</strong>
                                " Input text is scanned character by character, analyzing Unicode properties and detecting problematic characters."
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                2
                            </div>
                            <div>
                                <strong>"Codebase Scanning:"</strong>
                                " Recursively analyzes entire directories, respecting exclusion patterns and file type filters."
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                3
                            </div>
                            <div>
                                <strong>"Categorization:"</strong>
                                " Characters are classified by type and severity level, providing detailed information about each detection."
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                4
                            </div>
                            <div>
                                <strong>"Reporting:"</strong>
                                " Comprehensive reports show locations, character details, and recommendations for fixes."
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">
                        "💡 Tips for Developers"
                    </h4>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <ul class="space-y-2 text-sm text-green-800">
                            <li>
                                "• Always scan text copied from websites or documents before using in code"
                            </li>
                            <li>"• Use UTF-8 encoding consistently across your project"</li>
                            <li>"• Configure your editor to show invisible characters"</li>
                            <li>
                                "• Set up pre-commit hooks to automatically scan for bad characters"
                            </li>
                            <li>
                                "• Be extra careful with internationalization and multilingual content"
                            </li>
                            <li>
                                "• Regular scans help maintain code quality and prevent deployment issues"
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-6">
                    <div class="text-center text-gray-500 text-sm">
                        <div class="mb-2">"Bad Character Scanner v2.0"</div>
                        <div>
                            "Built with ❤️ using Leptos & Tauri • Copyright © 2025 John Shoy"
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn SettingsCheckbox<F>(
    label: &'static str,
    description: &'static str,
    checked: Signal<bool>,
    on_change: F,
) -> impl IntoView
where
    F: Fn(bool) + 'static,
{
    view! {
        <div class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <input
                type="checkbox"
                class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                prop:checked=move || checked.get()
                on:change=move |ev| {
                    let checked = event_target_checked(&ev);
                    on_change(checked);
                }
            />
            <div class="flex-1">
                <label class="text-sm font-medium text-gray-900 cursor-pointer">{label}</label>
                <p class="text-xs text-gray-600 mt-1">{description}</p>
            </div>
        </div>
    }
}
