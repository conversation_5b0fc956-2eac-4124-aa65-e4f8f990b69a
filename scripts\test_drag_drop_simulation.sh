#!/bin/bash
# 🎯 Drag & Drop Simulation Script
# Tests multi-file handling, validation, and error scenarios
# This script simulates what the GUI drag & drop should do

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAX_FILES=100
MAX_FILE_SIZE_MB=50
SUPPORTED_EXTENSIONS=("txt" "js" "ts" "py" "rs" "md" "json" "xml" "html" "css")
TEMP_DIR="/tmp/drag_drop_test"
OUTPUT_DIR="./reports/drag_drop_results"

# Logging
LOG_FILE="$OUTPUT_DIR/drag_drop_test.log"

# Initialize directories
init_directories() {
    mkdir -p "$TEMP_DIR"
    mkdir -p "$OUTPUT_DIR"
    touch "$LOG_FILE"
    echo "$(date): Initialized drag & drop simulation" >> "$LOG_FILE"
}

# Logging function
log_message() {
    local level="$1"
    local message="$2"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" >> "$LOG_FILE"
    
    case "$level" in
        "ERROR")   echo -e "${RED}❌ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "INFO")    echo -e "${BLUE}ℹ️  $message${NC}" ;;
        *)         echo "$message" ;;
    esac
}

# Validate individual file
validate_file() {
    local file_path="$1"
    local errors=()
    
    # Check if file exists
    if [[ ! -f "$file_path" ]]; then
        errors+=("File does not exist")
        return 1
    fi
    
    # Check file size
    local file_size_mb=$(du -m "$file_path" | cut -f1)
    if [[ $file_size_mb -gt $MAX_FILE_SIZE_MB ]]; then
        errors+=("File too large: ${file_size_mb}MB (max: ${MAX_FILE_SIZE_MB}MB)")
    fi
    
    # Check file extension
    local extension="${file_path##*.}"
    local extension_valid=false
    for supported_ext in "${SUPPORTED_EXTENSIONS[@]}"; do
        if [[ "$extension" == "$supported_ext" ]]; then
            extension_valid=true
            break
        fi
    done
    
    if [[ "$extension_valid" == false ]]; then
        errors+=("Unsupported file extension: .$extension")
    fi
    
    # Check file readability
    if [[ ! -r "$file_path" ]]; then
        errors+=("File is not readable")
    fi
    
    # Return validation result
    if [[ ${#errors[@]} -eq 0 ]]; then
        return 0
    else
        printf '%s\n' "${errors[@]}"
        return 1
    fi
}

# Validate directory
validate_directory() {
    local dir_path="$1"
    
    if [[ ! -d "$dir_path" ]]; then
        echo "Directory does not exist"
        return 1
    fi
    
    if [[ ! -r "$dir_path" ]]; then
        echo "Directory is not readable"
        return 1
    fi
    
    # Count files in directory
    local file_count=$(find "$dir_path" -type f | wc -l)
    if [[ $file_count -gt $MAX_FILES ]]; then
        echo "Too many files in directory: $file_count (max: $MAX_FILES)"
        return 1
    fi
    
    return 0
}

# Process dropped files (main simulation function)
process_dropped_files() {
    local dropped_items=("$@")
    local valid_files=()
    local invalid_files=()
    local total_files=0
    local processed_files=0
    
    log_message "INFO" "Processing ${#dropped_items[@]} dropped items"
    
    # First pass: validate all items and collect files
    for item in "${dropped_items[@]}"; do
        if [[ -f "$item" ]]; then
            # Single file
            if validate_file "$item" >/dev/null 2>&1; then
                valid_files+=("$item")
                log_message "SUCCESS" "Valid file: $(basename "$item")"
            else
                local validation_errors=$(validate_file "$item" 2>&1)
                invalid_files+=("$item")
                log_message "ERROR" "Invalid file $(basename "$item"): $validation_errors"
            fi
        elif [[ -d "$item" ]]; then
            # Directory - recursively find files
            if validate_directory "$item" >/dev/null 2>&1; then
                log_message "INFO" "Processing directory: $item"
                while IFS= read -r -d '' file; do
                    if validate_file "$file" >/dev/null 2>&1; then
                        valid_files+=("$file")
                        log_message "SUCCESS" "Valid file from directory: $(basename "$file")"
                    else
                        invalid_files+=("$file")
                        log_message "WARNING" "Skipping invalid file: $(basename "$file")"
                    fi
                done < <(find "$item" -type f -print0)
            else
                local validation_errors=$(validate_directory "$item" 2>&1)
                log_message "ERROR" "Invalid directory $item: $validation_errors"
            fi
        else
            log_message "ERROR" "Item is neither file nor directory: $item"
            invalid_files+=("$item")
        fi
    done
    
    total_files=${#valid_files[@]}
    
    # Display validation summary
    echo
    log_message "INFO" "=== VALIDATION SUMMARY ==="
    log_message "INFO" "Total items dropped: ${#dropped_items[@]}"
    log_message "INFO" "Valid files found: $total_files"
    log_message "INFO" "Invalid items: ${#invalid_files[@]}"
    
    if [[ $total_files -eq 0 ]]; then
        log_message "ERROR" "No valid files to process!"
        return 1
    fi
    
    # Second pass: process valid files
    echo
    log_message "INFO" "=== PROCESSING FILES ==="
    
    for file in "${valid_files[@]}"; do
        process_single_file "$file"
        ((processed_files++))
        
        # Progress indicator
        local progress=$((processed_files * 100 / total_files))
        log_message "INFO" "Progress: $processed_files/$total_files ($progress%)"
    done
    
    # Generate summary report
    generate_summary_report "$total_files" "$processed_files" "${invalid_files[@]}"
    
    log_message "SUCCESS" "Drag & drop simulation completed!"
    return 0
}

# Process a single file (simulate analysis)
process_single_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    local output_file="$OUTPUT_DIR/${file_name}.analysis.json"
    
    log_message "INFO" "Analyzing: $file_name"
    
    # Simulate analysis by calling the existing enhanced analyzer
    if [[ -f "./scripts/enhanced_analyzer.sh" ]]; then
        # Call the real analyzer
        if ./scripts/enhanced_analyzer.sh -i "$file_path" -o "$output_file" -f json >/dev/null 2>&1; then
            log_message "SUCCESS" "Analysis completed: $file_name"
        else
            log_message "ERROR" "Analysis failed: $file_name"
            # Create a placeholder result for failed analysis
            create_placeholder_result "$file_path" "$output_file"
        fi
    else
        # Create a simulated analysis result
        create_simulated_result "$file_path" "$output_file"
    fi
}

# Create simulated analysis result (when real analyzer isn't available)
create_simulated_result() {
    local file_path="$1"
    local output_file="$2"
    local file_size=$(stat -c%s "$file_path" 2>/dev/null || echo "0")
    
    cat > "$output_file" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "file_path": "$file_path",
    "file_name": "$(basename "$file_path")",
    "file_size_bytes": $file_size,
    "analysis_type": "drag_drop_simulation",
    "status": "simulated",
    "suspicious_characters": [],
    "risk_level": "low",
    "processing_time_ms": $((RANDOM % 1000 + 100)),
    "notes": "Simulated analysis for drag & drop testing"
}
EOF
    
    log_message "INFO" "Created simulated result: $(basename "$output_file")"
}

# Create placeholder result for failed analysis
create_placeholder_result() {
    local file_path="$1"
    local output_file="$2"
    
    cat > "$output_file" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "file_path": "$file_path",
    "file_name": "$(basename "$file_path")",
    "analysis_type": "drag_drop_simulation",
    "status": "failed",
    "error": "Analysis failed during processing",
    "notes": "Placeholder result for failed analysis"
}
EOF
    
    log_message "WARNING" "Created placeholder result for failed analysis"
}

# Generate summary report
generate_summary_report() {
    local total_files="$1"
    local processed_files="$2"
    shift 2
    local invalid_files=("$@")
    
    local summary_file="$OUTPUT_DIR/drag_drop_summary.json"
    local failed_files=$((total_files - processed_files))
    
    cat > "$summary_file" << EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "test_type": "drag_drop_simulation",
    "statistics": {
        "total_files": $total_files,
        "processed_files": $processed_files,
        "failed_files": $failed_files,
        "invalid_files": ${#invalid_files[@]},
        "success_rate": $(echo "scale=2; $processed_files * 100 / $total_files" | bc -l)
    },
    "invalid_files": [
        $(printf '"%s",' "${invalid_files[@]}" | sed 's/,$//')
    ],
    "output_directory": "$OUTPUT_DIR",
    "log_file": "$LOG_FILE"
}
EOF
    
    log_message "SUCCESS" "Summary report generated: $summary_file"
    
    # Display summary
    echo
    log_message "INFO" "=== FINAL SUMMARY ==="
    log_message "INFO" "Total files processed: $processed_files/$total_files"
    log_message "INFO" "Success rate: $(echo "scale=1; $processed_files * 100 / $total_files" | bc -l)%"
    log_message "INFO" "Results saved to: $OUTPUT_DIR"
    log_message "INFO" "Log file: $LOG_FILE"
}

# Test function - run automated tests
run_tests() {
    log_message "INFO" "=== RUNNING AUTOMATED TESTS ==="
    
    # Test 1: Valid single file
    test_single_file
    
    # Test 2: Multiple files
    test_multiple_files
    
    # Test 3: Directory processing
    test_directory_processing
    
    # Test 4: Invalid inputs
    test_invalid_inputs
    
    # Test 5: Edge cases
    test_edge_cases
    
    log_message "SUCCESS" "All automated tests completed!"
}

# Individual test functions
test_single_file() {
    log_message "INFO" "Testing single file processing..."
    
    # Create a test file
    local test_file="$TEMP_DIR/test_single.txt"
    echo "This is a test file for drag & drop simulation" > "$test_file"
    
    if validate_file "$test_file" >/dev/null 2>&1; then
        log_message "SUCCESS" "Single file validation passed"
    else
        log_message "ERROR" "Single file validation failed"
    fi
}

test_multiple_files() {
    log_message "INFO" "Testing multiple files processing..."
    
    # Create multiple test files
    local test_files=()
    for i in {1..5}; do
        local test_file="$TEMP_DIR/test_multi_$i.txt"
        echo "Test file $i content" > "$test_file"
        test_files+=("$test_file")
    done
    
    # Validate all files
    local valid_count=0
    for file in "${test_files[@]}"; do
        if validate_file "$file" >/dev/null 2>&1; then
            ((valid_count++))
        fi
    done
    
    if [[ $valid_count -eq ${#test_files[@]} ]]; then
        log_message "SUCCESS" "Multiple files validation passed ($valid_count/${#test_files[@]})"
    else
        log_message "ERROR" "Multiple files validation failed ($valid_count/${#test_files[@]})"
    fi
}

test_directory_processing() {
    log_message "INFO" "Testing directory processing..."
    
    # Create test directory structure
    local test_dir="$TEMP_DIR/test_directory"
    mkdir -p "$test_dir"
    
    for i in {1..3}; do
        echo "Directory test file $i" > "$test_dir/file_$i.txt"
    done
    
    if validate_directory "$test_dir" >/dev/null 2>&1; then
        log_message "SUCCESS" "Directory validation passed"
    else
        log_message "ERROR" "Directory validation failed"
    fi
}

test_invalid_inputs() {
    log_message "INFO" "Testing invalid inputs..."
    
    # Test non-existent file
    if ! validate_file "/nonexistent/file.txt" >/dev/null 2>&1; then
        log_message "SUCCESS" "Non-existent file properly rejected"
    else
        log_message "ERROR" "Non-existent file validation should have failed"
    fi
    
    # Test non-existent directory
    if ! validate_directory "/nonexistent/directory" >/dev/null 2>&1; then
        log_message "SUCCESS" "Non-existent directory properly rejected"
    else
        log_message "ERROR" "Non-existent directory validation should have failed"
    fi
}

test_edge_cases() {
    log_message "INFO" "Testing edge cases..."
    
    # Test empty file
    local empty_file="$TEMP_DIR/empty.txt"
    touch "$empty_file"
    
    if validate_file "$empty_file" >/dev/null 2>&1; then
        log_message "SUCCESS" "Empty file validation passed"
    else
        log_message "WARNING" "Empty file validation failed (this might be expected)"
    fi
}

# Help function
show_help() {
    echo "🎯 Drag & Drop Simulation Script"
    echo
    echo "Usage: $0 [OPTIONS] [FILES/DIRECTORIES...]"
    echo
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -t, --test     Run automated tests"
    echo "  -v, --verbose  Enable verbose output"
    echo "  -c, --clean    Clean temporary files"
    echo
    echo "Examples:"
    echo "  $0 file1.txt file2.js               # Process specific files"
    echo "  $0 /path/to/directory               # Process entire directory"
    echo "  $0 file1.txt /path/to/dir file2.py  # Mix files and directories"
    echo "  $0 --test                           # Run automated tests"
    echo
    echo "This script simulates the drag & drop functionality that will be"
    echo "implemented in the GUI. It validates files, processes them, and"
    echo "generates analysis reports."
}

# Clean up function
cleanup() {
    log_message "INFO" "Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
    log_message "SUCCESS" "Cleanup completed"
}

# Main execution
main() {
    local verbose=false
    local run_tests_flag=false
    local clean_flag=false
    local files_to_process=()
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                run_tests_flag=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -*)
                log_message "ERROR" "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                files_to_process+=("$1")
                shift
                ;;
        esac
    done
    
    # Initialize
    init_directories
    
    # Handle cleanup
    if [[ "$clean_flag" == true ]]; then
        cleanup
        exit 0
    fi
    
    # Handle tests
    if [[ "$run_tests_flag" == true ]]; then
        run_tests
        exit 0
    fi
    
    # Handle file processing
    if [[ ${#files_to_process[@]} -eq 0 ]]; then
        log_message "ERROR" "No files or directories specified!"
        show_help
        exit 1
    fi
    
    # Process the dropped files
    process_dropped_files "${files_to_process[@]}"
    
    # Show final results
    echo
    log_message "INFO" "Drag & drop simulation complete!"
    log_message "INFO" "Check results in: $OUTPUT_DIR"
    log_message "INFO" "Check logs in: $LOG_FILE"
}

# Run main function with all arguments
main "$@"
