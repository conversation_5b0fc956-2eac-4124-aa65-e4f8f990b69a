﻿#!/usr/bin/env powershell
# Enhanced Development Server Starter for Bad Character Scanner
# Provides pre-flight checks and better error handling

param(
    [switch]$SkipChecks,
    [switch]$Verbose,
    [switch]$Help
)

$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Green = [ConsoleColor]::Green
    Yellow = [ConsoleColor]::Yellow
    Red = [ConsoleColor]::Red
    Cyan = [ConsoleColor]::Cyan
    <PERSON> = [ConsoleColor]::Blue
}

function Show-Help {
    Write-Host "🚀 Bad Character Scanner - Development Server" -ForegroundColor $Colors.Cyan
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor $Colors.Blue
    Write-Host "    .\start-dev.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor $Colors.Blue
    Write-Host "    -SkipChecks    Skip pre-flight dependency checks"
    Write-Host "    -Verbose       Enable verbose output"
    Write-Host "    -Help          Show this help message"
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor $Colors.Blue
    Write-Host "    .\start-dev.ps1                    # Normal startup with checks"
    Write-Host "    .\start-dev.ps1 -SkipChecks        # Quick startup"
    Write-Host "    .\start-dev.ps1 -Verbose           # Detailed output"
    exit 0
}

function Write-Status {
    param([string]$Message, [ConsoleColor]$Color = $Colors.Green)
    Write-Host "✅ $Message" -ForegroundColor $Color
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $Colors.Red
}

function Test-Dependencies {
    Write-Host "🔍 Checking dependencies..." -ForegroundColor $Colors.Blue

    $issues = @()

    # Check Rust/Cargo
    if (-not (Get-Command cargo -ErrorAction SilentlyContinue)) {
        $issues += "Cargo (Rust) not found. Install from https://rustup.rs"
    } else {
        $cargoVersion = cargo --version
        if ($Verbose) { Write-Status "Cargo: $cargoVersion" }
    }

    # Check Tauri CLI
    try {
        $tauriVersion = cargo tauri --version 2>$null
        if ($Verbose) { Write-Status "Tauri CLI: $tauriVersion" }
    } catch {
        $issues += "Tauri CLI not found. Run: cargo install tauri-cli"
    }

    # Check Node.js (for scripts)
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-Warning "Node.js not found. Some scripts may not work."
    } else {
        $nodeVersion = node --version
        if ($Verbose) { Write-Status "Node.js: $nodeVersion" }
    }

    if ($issues.Count -gt 0) {
        Write-Error "Missing dependencies:"
        foreach ($issue in $issues) {
            Write-Host "  • $issue" -ForegroundColor $Colors.Red
        }
        exit 1
    }

    Write-Status "All dependencies found"
}

function Test-ProjectStructure {
    Write-Host "📁 Checking project structure..." -ForegroundColor $Colors.Blue

    $projectRoot = Split-Path -Parent $PSScriptRoot
    $requiredFiles = @(
        "Cargo.toml",
        "src-tauri\Cargo.toml",
        "src\lib.rs"
    )

    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $projectRoot $file
        if (-not (Test-Path $filePath)) {
            Write-Error "Required file not found: $file"
            Write-Host "Make sure you're running this from the project root directory." -ForegroundColor $Colors.Yellow
            exit 1
        }
    }

    Write-Status "Project structure validated"
}

function Start-DevServer {
    $projectRoot = Split-Path -Parent $PSScriptRoot

    Write-Host ""
    Write-Host "🚀 Starting Bad Character Scanner Development Server..." -ForegroundColor $Colors.Cyan
    Write-Host "📍 Project: $projectRoot" -ForegroundColor $Colors.Blue
    Write-Host "🌐 Frontend will be available at: http://localhost:1420" -ForegroundColor $Colors.Green
    Write-Host "🔄 Hot reload enabled for both frontend and backend" -ForegroundColor $Colors.Green
    Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor $Colors.Yellow
    Write-Host ""

    # Change to project root
    Set-Location $projectRoot

    # Start the development server
    try {
        cargo tauri dev
    } catch {
        Write-Error "Failed to start development server: $_"
        Write-Host ""
        Write-Host "💡 Troubleshooting tips:" -ForegroundColor $Colors.Blue
        Write-Host "  • Run 'cargo clean' to clear build cache" -ForegroundColor $Colors.Yellow
        Write-Host "  • Check that all dependencies are installed" -ForegroundColor $Colors.Yellow
        Write-Host "  • Verify Tauri configuration in src-tauri/tauri.conf.json" -ForegroundColor $Colors.Yellow
        exit 1
    }
}

# Main execution
if ($Help) {
    Show-Help
}

Write-Host "🦸 Bad Character Scanner - Development Mode" -ForegroundColor $Colors.Cyan
Write-Host "============================================" -ForegroundColor $Colors.Cyan

if (-not $SkipChecks) {
    Test-Dependencies
    Test-ProjectStructure
} else {
    Write-Warning "Skipping pre-flight checks"
}

Start-DevServer
