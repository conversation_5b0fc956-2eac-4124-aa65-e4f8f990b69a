// Pattern matching module for Bad Character Scanner
// Handles regex patterns and advanced text analysis

use fancy_regex::Regex;
use std::collections::HashMap;
use anyhow::Result;

pub struct PatternMatcher {
    patterns: Vec<CompiledPattern>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CompiledPattern {
    pub name: String,
    pub regex: Regex,
    pub description: String,
    pub severity: PatternSeverity,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum PatternSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl PatternMatcher {
    pub fn new() -> Result<Self> {
        let patterns = Self::build_default_patterns()?;
        Ok(Self { patterns })
    }

    fn build_default_patterns() -> Result<Vec<CompiledPattern>> {
        let mut patterns = Vec::new();

        // Zero-width characters
        patterns.push(CompiledPattern {
            name: "zero_width_chars".to_string(),
            regex: Regex::new(r"[\u200B\u200C\u200D\u2060\uFEFF]")?,
            description: "Zero-width characters that can be used for steganography".to_string(),
            severity: PatternSeverity::High,
        });

        // Bidirectional text controls
        patterns.push(CompiledPattern {
            name: "bidi_controls".to_string(),
            regex: Regex::new(r"[\u202A-\u202E\u2066-\u2069]")?,
            description: "Bidirectional text control characters".to_string(),
            severity: PatternSeverity::Critical,
        });

        // Homoglyph patterns (basic examples)
        patterns.push(CompiledPattern {
            name: "homoglyphs".to_string(),
            regex: Regex::new(r"[а-я].*[a-z]|[a-z].*[а-я]")?, // Cyrillic mixed with Latin
            description: "Potential homoglyph mixing between scripts".to_string(),
            severity: PatternSeverity::Medium,
        });

        Ok(patterns)
    }

    /// Find all pattern matches in text
    pub fn find_matches(&self, text: &str) -> Vec<PatternMatch> {
        let mut matches = Vec::new();

        for pattern in &self.patterns {
            match pattern.regex.find_iter(text) {
                Ok(iter) => {
                    for mat in iter {
                        matches.push(PatternMatch {
                            pattern_name: pattern.name.clone(),
                            start: mat.start(),
                            end: mat.end(),
                            matched_text: mat.as_str().to_string(),
                            severity: pattern.severity.clone(),
                            description: pattern.description.clone(),
                        });
                    }
                }
                Err(_e) => {
                    // Optionally log or handle regex error
                }
            }
        }

        matches
    }

    /// Get pattern statistics
    pub fn get_pattern_stats(&self, text: &str) -> HashMap<String, usize> {
        let mut stats = HashMap::new();

        for pattern in &self.patterns {
            let count = match pattern.regex.find_iter(text) {
                Ok(iter) => iter.count(),
                Err(_) => 0,
            };
            stats.insert(pattern.name.clone(), count);
        }

        stats
    }
}

#[derive(Debug, Clone)]
pub struct PatternMatch {
    pub pattern_name: String,
    pub start: usize,
    pub end: usize,
    pub matched_text: String,
    pub severity: PatternSeverity,
    pub description: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    use fancy_regex::Regex;

    #[test]
    fn detects_zero_width_characters() {
        let matcher = PatternMatcher::new().unwrap();
        let text = "Hello\u{200B}World";
        let matches = matcher.find_matches(text);
        assert!(matches.iter().any(|m| m.pattern_name == "zero_width_chars"), "Should detect zero-width character");
    }

    #[test]
    fn detects_bidi_controls() {
        let matcher = PatternMatcher::new().unwrap();
        let text = "abc\u{202E}def";
        let matches = matcher.find_matches(text);
        assert!(matches.iter().any(|m| m.pattern_name == "bidi_controls"), "Should detect bidi control character");
    }

    #[test]
    fn detects_homoglyphs() {
        let matcher = PatternMatcher::new().unwrap();
        let text = "aа"; // Latin 'a' + Cyrillic 'а'
        let matches = matcher.find_matches(text);
        assert!(matches.iter().any(|m| m.pattern_name == "homoglyphs"), "Should detect homoglyph pattern");
    }

    #[test]
    fn detects_lookbehind_pattern() {
        // Example: match 'X' only if preceded by 'A'
        let regex = Regex::new(r"(?<=A)X").unwrap();
        assert!(!regex.is_match("ABX").unwrap());
        assert!(regex.is_match("AX").unwrap());
    }

    #[test]
    fn detects_backreference_pattern() {
        // Example: match repeated word
        let regex = Regex::new(r"(word)\\s+\\1").unwrap();
        assert!(regex.is_match("word word").unwrap());
        assert!(!regex.is_match("word test").unwrap());
    }
}
