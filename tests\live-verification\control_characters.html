<!DOCTYPE html>
<html>
<head>
    <title>Control Characters Attack Test</title>
</head>
<body>
    <!-- This file contains various control characters that can cause issues -->
    
    <h1>Clean​Header</h1>  <!-- <PERSON><PERSON>O WIDTH SPACE in header -->
    
    <script>
        // Null byte injection attempt
        const userInput = "normal text\x00<script>alert('xss')</script>";
        
        // Backspace character attack
        const filename = "important.txt\x08\x08\x08\x08evil.exe";
        
        // Form feed character
        const data = "legitimate\x0Cmalicious";
        
        // Vertical tab
        const path = "/safe/path\x0B../../etc/passwd";
        
        // Various other control characters
        const mixed = "text\x01\x02\x03\x04\x05more text";
        
        console.log("Control character attacks loaded");
    </script>
    
    <!-- HTML with suspicious characters -->
    <p>Normal paragraph​with hidden space</p>
    <div>Content‌with‍zero​width​characters</div>
    
    <!-- Comment with bidirectional override -->
    <!-- Safe comment ‮ --> <script>malicious()</script> <!-- ⁦ end -->
    
</body>
</html>
