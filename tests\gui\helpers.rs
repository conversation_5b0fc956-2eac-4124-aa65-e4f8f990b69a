// tests/gui/helpers.rs
//! Shared helpers for GUI/E2E tests.

use std::process::{Child, Command};

/// Launch the Tauri app for E2E testing. Returns the process handle.
pub fn launch_tauri_app() -> Child {
    Command::new("cargo")
        .args(["tauri", "dev"])
        .spawn()
        .expect("Failed to launch Tauri app")
}

/// Utility to wait for the app to be ready (customize as needed).
pub fn wait_for_app_ready() {
    std::thread::sleep(std::time::Duration::from_secs(3));
}

// Add more helpers as needed (e.g., for Playwright setup, test data loading, etc.)
