<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bad Character Scanner - Investor Pitch Deck</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .deck-container {
            max-width: 210mm;
            width: 210mm;
            margin: 0 auto;
            padding: 10mm;
        }

        .slide {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 20px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 600px;
            max-height: 750px;
            width: 210mm;
            max-width: 210mm;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            page-break-after: always;
            margin-left: auto;
            margin-right: auto;
        }

        .slide-number {
            position: absolute;
            top: 20px;
            right: 30px;
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }

        .slide h1 {
            font-size: 2.2em;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide h2 {
            font-size: 1.6em;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .slide h3 {
            font-size: 1.3em;
            margin-bottom: 8px;
            color: #34495e;
        }

        .slide h4 {
            font-size: 1.1em;
            margin-bottom: 6px;
            color: #667eea;
        }

        .subtitle {
            font-size: 1.1em;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-style: italic;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin: 10px 0;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(144px, 1fr));
            gap: 10px;
            margin: 12px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 10px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        }

        .stat-number {
            font-size: 1.6em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            margin-top: 4px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
            margin: 15px 0;
        }

        .feature-card {
            background: rgba(102, 126, 234, 0.1);
            padding: 12px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .revenue-timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .year-milestone {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px;
            border-radius: 15px;
            text-align: center;
            min-width: 140px;
            margin: 6px;
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
        }

        .year-milestone h4 {
            color: white;
            margin-bottom: 10px;
        }

        .competitive-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            font-size: 0.9em;
        }

        .competitive-table th {
            background: #667eea;
            color: white;
            padding: 10px 8px;
            font-weight: bold;
            font-size: 0.85em;
        }

        .competitive-table td {
            padding: 8px;
            border-bottom: 1px solid #eee;
            font-size: 0.85em;
        }

        .check-mark {
            color: #27ae60;
            font-size: 1.3em;
            font-weight: bold;
        }

        .x-mark {
            color: #e74c3c;
            font-size: 1.3em;
            font-weight: bold;
        }        .funding-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(144px, 1fr));
            gap: 10px;
            margin: 12px 0;
        }

        .funding-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 10px;
            border-radius: 12px;
            text-align: center;
            color: #2c3e50;
        }

        .funding-percentage {
            font-size: 1.4em;
            font-weight: bold;
            color: #667eea;
        }

        .contact-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 25px 0;
        }

        .cta-button {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }        /* Professional Stock-Style Graph */
        .revenue-graph {
            background: #1a1a1a;
            border-radius: 15px;
            padding: 35px;
            margin: 20px 0;
            position: relative;
            height: 375px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border: 2px solid #333;
        }

        .graph-line {
            position: absolute;
            bottom: 75px;
            left: 60px;
            right: 60px;
            height: 225px;
            background: transparent;
        }

        .graph-line::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: #444;
        }

        .graph-line::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 1px;
            height: 100%;
            background: #444;
        }

        /* Grid lines */
        .revenue-graph::before {
            content: '';
            position: absolute;
            bottom: 75px;
            left: 60px;
            right: 60px;
            height: 225px;
            background-image: 
                repeating-linear-gradient(0deg, transparent, transparent 44px, rgba(68, 68, 68, 0.3) 45px),
                repeating-linear-gradient(90deg, transparent, transparent 119px, rgba(68, 68, 68, 0.3) 120px);
        }

        /* SVG-style line path */
        .graph-path {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 225px;
            background: linear-gradient(to right,
                transparent 0%,
                transparent 20%,
                #00ff88 20.5%,
                #00ff88 21%,
                transparent 21.5%,
                transparent 49.5%,
                #ff4444 50%,
                #ff4444 50.5%,
                transparent 51%,
                transparent 79.5%,
                #00ff88 80%,
                #00ff88 80.5%,
                transparent 81%);
        }

        .graph-points {
            position: absolute;
            bottom: 75px;
            left: 60px;
            right: 60px;
            height: 225px;
        }

        .graph-point {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #fff;
            border: 3px solid;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
            z-index: 10;
        }

        .graph-point::after {
            content: attr(data-value);
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
        }

        .point-boom1 {
            left: 20%;
            bottom: 150px;
            border-color: #00ff88;
        }

        .point-bust {
            left: 50%;
            bottom: 90px;
            border-color: #ff4444;
        }

        .point-boom2 {
            left: 80%;
            bottom: 195px;
            border-color: #00ff88;
        }

        .graph-labels {
            position: absolute;
            bottom: 20px;
            left: 60px;
            right: 60px;
            display: flex;
            justify-content: space-between;
            font-size: 1em;
            color: #888;
            font-weight: bold;
        }

        .graph-y-axis {
            position: absolute;
            left: 10px;
            bottom: 75px;
            height: 225px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 0.8em;
            color: #888;
        }

        .graph-title {
            position: absolute;
            top: 15px;
            left: 35px;
            font-weight: bold;
            color: #fff;
            font-size: 1.3em;
        }

        .compact-timeline {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }

        .compact-milestone {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px;
            border-radius: 10px;
            text-align: center;
            font-size: 0.7em;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }

        .compact-milestone h5 {
            color: white;
            margin-bottom: 5px;
            font-size: 0.8em;
        }

        .compact-milestone .revenue {
            font-size: 1.2em;
            font-weight: bold;
            margin: 3px 0;
        }

        .phase-summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin: 10px 0;
        }

        .phase-card-compact {
            background: rgba(102, 126, 234, 0.1);
            padding: 8px;
            border-radius: 10px;
            border-left: 3px solid #667eea;
            font-size: 0.65em;
        }

        .phase-card-compact h5 {
            color: #667eea;
            margin-bottom: 4px;
            font-size: 1em;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 20px;
                margin: 15px 0;
            }

            .slide h1 {
                font-size: 2.2em;
            }

            .slide h2 {
                font-size: 1.8em;
            }

            .revenue-timeline {
                flex-direction: column;
            }

            .risk-mitigation {
                grid-template-columns: 1fr;
            }
        }        /* Print Styles for A4 Portrait Format - One Slide Per Page */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                background: white !important;
                color: #333 !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 12pt !important;
                line-height: 1.2 !important;
            }

            .deck-container {
                max-width: none !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .slide {
                background: white !important;
                box-shadow: none !important;
                border: none !important;
                border-radius: 0 !important;
                margin: 0 !important;
                padding: 20mm 15mm 15mm 15mm !important;
                width: 210mm !important;
                height: 297mm !important;
                min-height: 297mm !important;
                max-height: 297mm !important;
                page-break-after: always !important;
                page-break-inside: avoid !important;
                overflow: hidden !important;
                position: relative !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: flex-start !important;
            }

            .slide:last-child {
                page-break-after: auto !important;
            }

            .slide-number {
                position: absolute !important;
                top: 10mm !important;
                right: 15mm !important;
                font-size: 10pt !important;
                color: #666 !important;
            }

            /* Title and heading adjustments */
            .slide h1 {
                font-size: 24pt !important;
                margin: 0 0 10pt 0 !important;
                color: #333 !important;
                line-height: 1.2 !important;
            }

            .slide h2 {
                font-size: 18pt !important;
                margin: 0 0 8pt 0 !important;
                color: #333 !important;
                line-height: 1.2 !important;
            }

            .slide h3 {
                font-size: 14pt !important;
                margin: 0 0 6pt 0 !important;
                color: #666 !important;
                line-height: 1.2 !important;
            }

            .slide h4 {
                font-size: 12pt !important;
                margin: 0 0 4pt 0 !important;
                color: #333 !important;
                line-height: 1.2 !important;
            }

            .slide h5 {
                font-size: 11pt !important;
                margin: 0 0 3pt 0 !important;
                color: #333 !important;
                line-height: 1.2 !important;
            }

            .subtitle {
                font-size: 14pt !important;
                margin: 0 0 15pt 0 !important;
                color: #666 !important;
            }

            /* Content spacing for print */
            .highlight-box {
                margin: 8pt 0 !important;
                padding: 10pt !important;
                border-radius: 8pt !important;
            }

            .stats-grid, .feature-grid, .funding-breakdown {
                margin: 8pt 0 !important;
                gap: 8pt !important;
            }

            .revenue-timeline, .compact-timeline, .phase-summary {
                margin: 8pt 0 !important;
                gap: 6pt !important;
            }            /* Revenue graph adjustments for print */
            .revenue-graph {
                background: #f8f9fa !important;
                border: 2px solid #333 !important;
                border-radius: 8pt !important;
                padding: 20pt !important;
                margin: 10pt 0 !important;
                height: 180pt !important;
                position: relative !important;
                box-shadow: none !important;
            }

            .revenue-graph::before {
                background-image: 
                    repeating-linear-gradient(0deg, transparent, transparent 32pt, rgba(68, 68, 68, 0.3) 33pt),
                    repeating-linear-gradient(90deg, transparent, transparent 85pt, rgba(68, 68, 68, 0.3) 86pt) !important;
            }

            .graph-line {
                bottom: 50pt !important;
                left: 40pt !important;
                right: 40pt !important;
                height: 100pt !important;
            }

            .graph-points {
                bottom: 50pt !important;
                left: 40pt !important;
                right: 40pt !important;
                height: 100pt !important;
            }

            .graph-y-axis {
                left: 8pt !important;
                bottom: 50pt !important;
                height: 100pt !important;
                font-size: 8pt !important;
            }

            .graph-labels {
                bottom: 15pt !important;
                left: 40pt !important;
                right: 40pt !important;
                font-size: 9pt !important;
            }

            .graph-title {
                color: #333 !important;
                font-size: 11pt !important;
                font-weight: bold !important;
                top: 10pt !important;
                left: 20pt !important;
            }

            .graph-point {
                background: white !important;
                border: 2pt solid !important;
                width: 8pt !important;
                height: 8pt !important;
                box-shadow: 0 0 4pt rgba(0, 0, 0, 0.3) !important;
            }

            .point-boom1 {
                left: 20% !important;
                bottom: 60pt !important;
                border-color: #28a745 !important;
            }

            .point-bust {
                left: 50% !important;
                bottom: 30pt !important;
                border-color: #dc3545 !important;
            }

            .point-boom2 {
                left: 80% !important;
                bottom: 85pt !important;
                border-color: #28a745 !important;
            }

            .graph-point::after {
                background: rgba(0, 0, 0, 0.9) !important;
                color: white !important;
                font-size: 8pt !important;
                font-weight: bold !important;
                padding: 2pt 4pt !important;
                border-radius: 3pt !important;
                top: -25pt !important;
            }            /* Print-specific line visualization - Remove problematic green lines */
            .graph-path {
                height: 100pt !important;
                background: none !important;
            }

            .graph-path::after {
                display: none !important;
            }

            .graph-path::before {
                display: none !important;
            }/* Colored elements with proper print colors */
            .highlight-box {
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                color: white !important;
                border: 1pt solid #667eea !important;
            }

            .stat-card {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
                color: white !important;
                padding: 8pt !important;
                border-radius: 6pt !important;
                text-align: center !important;
            }

            .stat-number {
                font-size: 16pt !important;
                font-weight: bold !important;
                display: block !important;
            }

            .stat-label {
                font-size: 10pt !important;
                opacity: 0.9 !important;
            }

            .compact-milestone, .year-milestone {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
                color: white !important;
                padding: 8pt !important;
                border-radius: 6pt !important;
                text-align: center !important;
                font-size: 9pt !important;
            }

            .funding-item {
                background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
                border: 1pt solid #a8edea !important;
                padding: 8pt !important;
                border-radius: 6pt !important;
                color: #333 !important;
            }

            .contact-info {
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                color: white !important;
                padding: 10pt !important;
                border-radius: 6pt !important;
                text-align: center !important;
            }

            .feature-card {
                background: rgba(102, 126, 234, 0.1) !important;
                border-left: 3pt solid #667eea !important;
                padding: 8pt !important;
                border-radius: 4pt !important;
            }

            .phase-card-compact {
                background: rgba(102, 126, 234, 0.1) !important;
                border-left: 3pt solid #667eea !important;
                padding: 8pt !important;
                border-radius: 4pt !important;
                font-size: 9pt !important;
            }

            /* Table styles for print */
            .competitive-table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin: 8pt 0 !important;
                font-size: 10pt !important;
            }

            .competitive-table th {
                background: #667eea !important;
                color: white !important;
                padding: 6pt !important;
                border: 1pt solid #333 !important;
            }

            .competitive-table td {
                padding: 4pt 6pt !important;
                border: 1pt solid #ddd !important;
                text-align: center !important;
            }

            .check-mark {
                color: #28a745 !important;
                font-weight: bold !important;
            }

            .x-mark {
                color: #dc3545 !important;
                font-weight: bold !important;
            }

            /* List adjustments */
            ul, ol {
                margin: 4pt 0 !important;
                padding-left: 12pt !important;
            }

            li {
                margin: 2pt 0 !important;
                line-height: 1.3 !important;
            }

            /* Paragraph spacing */
            p {
                margin: 4pt 0 !important;
                line-height: 1.3 !important;
            }

            /* Strong text */
            strong {
                font-weight: bold !important;
                color: #333 !important;
            }

            /* Grid layouts for print */
            .stats-grid {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(80pt, 1fr)) !important;
                gap: 8pt !important;
            }

            .feature-grid {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(120pt, 1fr)) !important;
                gap: 8pt !important;
            }

            .funding-breakdown {
                display: grid !important;
                grid-template-columns: repeat(auto-fit, minmax(100pt, 1fr)) !important;
                gap: 8pt !important;
            }

            .compact-timeline, .phase-summary {
                display: grid !important;
                grid-template-columns: 1fr 1fr 1fr !important;
                gap: 6pt !important;
            }            /* Page setup */
            @page {
                size: A4 portrait !important;
                margin: 0 !important;
            }

            /* Slide 8 specific optimizations for A4 fit */
            .slide:nth-child(8) {
                padding: 15mm 12mm 8mm 12mm !important;
            }

            .slide:nth-child(8) h2 {
                font-size: 16pt !important;
                margin-bottom: 4pt !important;
            }

            .slide:nth-child(8) h3 {
                font-size: 12pt !important;
                margin-bottom: 6pt !important;
            }

            .slide:nth-child(8) .revenue-graph {
                height: 130pt !important;
                margin: 6pt 0 !important;
                padding: 15pt !important;
            }

            .slide:nth-child(8) .graph-line {
                bottom: 35pt !important;
                left: 30pt !important;
                right: 30pt !important;
                height: 70pt !important;
            }

            .slide:nth-child(8) .graph-points {
                bottom: 35pt !important;
                left: 30pt !important;
                right: 30pt !important;
                height: 70pt !important;
            }

            .slide:nth-child(8) .graph-y-axis {
                left: 6pt !important;
                bottom: 35pt !important;
                height: 70pt !important;
                font-size: 7pt !important;
            }

            .slide:nth-child(8) .graph-labels {
                bottom: 10pt !important;
                left: 30pt !important;
                right: 30pt !important;
                font-size: 8pt !important;
            }

            .slide:nth-child(8) .graph-title {
                font-size: 10pt !important;
                top: 8pt !important;
                left: 15pt !important;
            }

            .slide:nth-child(8) .point-boom1 {
                bottom: 42pt !important;
            }

            .slide:nth-child(8) .point-bust {
                bottom: 21pt !important;
            }

            .slide:nth-child(8) .point-boom2 {
                bottom: 60pt !important;
            }

            .slide:nth-child(8) .graph-point::after {
                font-size: 7pt !important;
                top: -20pt !important;
            }

            .slide:nth-child(8) .compact-timeline {
                margin: 4pt 0 !important;
                gap: 4pt !important;
            }

            .slide:nth-child(8) .compact-milestone {
                padding: 6pt !important;
                font-size: 8pt !important;
            }

            .slide:nth-child(8) .compact-milestone h5 {
                font-size: 8pt !important;
                margin-bottom: 2pt !important;
            }

            .slide:nth-child(8) .compact-milestone .revenue {
                font-size: 10pt !important;
                margin: 2pt 0 !important;
            }

            .slide:nth-child(8) .stats-grid {
                margin: 6pt 0 !important;
                gap: 6pt !important;
            }

            .slide:nth-child(8) .stat-card {
                padding: 6pt !important;
            }            .slide:nth-child(8) .stat-label {
                font-size: 8pt !important;
            }

            /* Slide 8 revenue graph grid lines */
            .slide:nth-child(8) .revenue-graph::before {
                bottom: 35pt !important;
                left: 30pt !important;
                right: 30pt !important;
                height: 70pt !important;
                background-image: 
                    repeating-linear-gradient(0deg, transparent, transparent 22pt, rgba(68, 68, 68, 0.3) 23pt),
                    repeating-linear-gradient(90deg, transparent, transparent 60pt, rgba(68, 68, 68, 0.3) 61pt) !important;
            }

            /* Remove problematic line paths from slide 8 graph */
            .slide:nth-child(8) .graph-path::after {
                display: none !important;
            }

            .slide:nth-child(8) .graph-path::before {
                display: none !important;
            }

            /* Slide 11 specific optimizations for A4 fit */
            .slide:nth-child(11) {
                padding: 15mm 12mm 10mm 12mm !important;
            }

            .slide:nth-child(11) h2 {
                font-size: 16pt !important;
                margin-bottom: 6pt !important;
            }

            .slide:nth-child(11) h3 {
                font-size: 12pt !important;
                margin-bottom: 8pt !important;
            }

            .slide:nth-child(11) h4 {
                font-size: 11pt !important;
                margin-bottom: 3pt !important;
            }

            .slide:nth-child(11) .highlight-box {
                padding: 8pt !important;
                margin: 6pt 0 !important;
            }

            .slide:nth-child(11) .feature-grid {
                gap: 6pt !important;
                margin: 6pt 0 !important;
            }

            .slide:nth-child(11) .feature-card {
                padding: 6pt !important;
                font-size: 9pt !important;
            }

            .slide:nth-child(11) .stats-grid {
                margin: 8pt 0 !important;
                gap: 6pt !important;
            }            .slide:nth-child(11) .stat-card {
                padding: 6pt !important;
                font-size: 9pt !important;
            }

            .slide:nth-child(11) .stat-number {
                font-size: 12pt !important;
            }

            .slide:nth-child(11) .stat-label {
                font-size: 7pt !important;
            }

            .slide:nth-child(11) .contact-info {
                padding: 12pt !important;
                margin: 10pt 0 !important;
            }

            .slide:nth-child(11) ul {
                margin: 2pt 0 !important;
                padding-left: 10pt !important;
            }

            .slide:nth-child(11) li {
                margin: 1pt 0 !important;
                line-height: 1.2 !important;
            }

            /* Hide any unnecessary elements for print */
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="deck-container">        <!-- Slide 1: Title -->
        <div class="slide">
            <div class="slide-number">1/11</div>
            <h1>Bad Character Scanner</h1>
            <p class="subtitle">Unicode Security Platform for AI-Generated Content</p>
            
            <div class="highlight-box">
                <h3>Seeking: $2M Series Seed</h3>
                <h3>Bootstrapped to Date</h3>
                <p><strong>Date:</strong> June 2025</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">$197B+</span>
                    <span class="stat-label">Total Addressable Market</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">78%</span>
                    <span class="stat-label">Developers Using AI</span>
                </div>
            </div>

            <div class="contact-info">
                <p><strong>Contact:</strong> <EMAIL></p>
                <p><strong>Demo:</strong> Live prototype available</p>
            </div>
        </div>        <!-- Slide 2: The Problem -->
        <div class="slide">
            <div class="slide-number">2/11</div>
            <h2>The Invisible AI Security Crisis</h2>
            <h3>LLMs Are Creating Invisible Vulnerabilities</h3>

            <div class="highlight-box">
                <h4 style="color: white;">The Hidden Threat:</h4>
                <ul>
                    <li><strong>78% of developers</strong> now use AI coding assistants</li>
                    <li><strong>LLMs are Unicode-blind</strong> and cannot distinguish 'а' (Cyrillic) from 'a' (Latin)</li>
                    <li><strong>Zero existing tools</strong> detect Unicode vulnerabilities in AI workflows</li>
                </ul>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Homograph Attacks</h4>
                    <p>аpple.com vs apple.com (Cyrillic 'а' vs Latin 'a')</p>
                </div>
                <div class="feature-card">
                    <h4>Steganography</h4>
                    <p>Hidden data in invisible Unicode characters</p>
                </div>
                <div class="feature-card">
                    <h4>Code Injection</h4>
                    <p>Malicious Unicode in AI-generated functions</p>
                </div>
                <div class="feature-card">
                    <h4>CV Manipulation</h4>
                    <p>AI-generated resumes with hidden tracking</p>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">$23B</span>
                    <span class="stat-label">AI Coding Market Exposed</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">92%</span>
                    <span class="stat-label">Fortune 500 Using AI Content</span>
                </div>
            </div>
        </div>        <!-- Slide 3: Market Opportunity -->
        <div class="slide">
            <div class="slide-number">3/11</div>
            <h2>Market Opportunity</h2>
            <h3>$197B+ Total Addressable Market</h3>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Individual Developers & Small Teams</h4>
                    <h3 style="color: #667eea;">$2B Market</h3>
                    <ul>
                        <li>30M+ developers worldwide using AI tools</li>
                        <li>Basic scanner: $20 one-time purchase</li>
                        <li>Pro upgrades: $120/year subscription</li>
                        <li>Low-friction adoption model</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>IDE & Development Platform Integration</h4>
                    <h3 style="color: #667eea;">$15B Market</h3>
                    <ul>
                        <li>Enterprise licensing to Windsurf, Copilot, Cursor</li>
                        <li>White-label Unicode security modules</li>
                        <li>Custom integration development</li>
                        <li>Revenue sharing partnerships</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>LLM Provider Partnerships</h4>
                    <h3 style="color: #667eea;">$180B Market</h3>
                    <ul>
                        <li>OpenAI, Anthropic, Google, Meta integrations</li>
                        <li>Real-time Unicode validation APIs</li>
                        <li>Enterprise safety compliance solutions</li>
                        <li>Custom security consulting services</li>
                    </ul>
                </div>
            </div>

            <div class="highlight-box">
                <h4 style="color: white;">Market Entry Strategy:</h4>
                <ul>
                    <li><strong>Bottom-up adoption:</strong> $20 entry point builds massive user base</li>
                    <li><strong>Enterprise upsell:</strong> Large user bases attract platform partnerships</li>
                    <li><strong>B2B integration:</strong> Custom solutions for major AI platforms</li>
                </ul>
            </div>
        </div>        <!-- Slide 4: Solution -->
        <div class="slide">
            <div class="slide-number">4/11</div>
            <h2>Bad Character Scanner Platform</h2>
            <h3>The World's First Unicode Security Platform</h3>

            <div class="highlight-box">
                <h4 style="color: white;">Unique Value Proposition:</h4>
                <p><em>"The only security tool that can see the threats AI creates but traditional scanners miss"</em></p>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Bad Character Scanner Basic ($20)</h4>
                    <ul>
                        <li>Fully offline desktop application</li>
                        <li>Perfect for secure government/defense environments</li>
                        <li>Essential Unicode analysis with 6-tab interface</li>
                        <li>14-day free trial, then one-time $20 purchase</li>
                        <li>1 year of security updates included</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Bad Character Scanner Pro ($120/year)</h4>
                    <ul>
                        <li>Everything in Basic PLUS:</li>
                        <li>Live threat database with real-time updates</li>
                        <li>Advanced pattern recognition</li>
                        <li>Team collaboration features</li>
                        <li>Priority support and training</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Bad Character Scanner Enterprise (Custom)</h4>
                    <ul>
                        <li>White-label integration for IDEs (Windsurf, Copilot)</li>
                        <li>API licensing for LLM providers (OpenAI, Anthropic)</li>
                        <li>Custom Unicode security modules</li>
                        <li>Dedicated engineering & consulting services</li>
                        <li>Revenue sharing partnerships</li>
                    </ul>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">30M+</span>
                    <span class="stat-label">Target Developers Worldwide</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">99.7%</span>
                    <span class="stat-label">Attack Pattern Detection Rate</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">12</span>
                    <span class="stat-label">Major AI Platforms to Target</span>
                </div>
            </div>

            
        </div>        <!-- Slide 5: Business Model -->
        <div class="slide">
            <div class="slide-number">5/11</div>
            <h2>Business Model</h2>
            <h3>SaaS + Enterprise + Platform Strategy</h3>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Bad Character Scanner Basic</h4>
                    <h3 style="color: #667eea;">$20 One-Time Purchase</h3>
                    <ul>
                        <li><strong>14-day free trial</strong></li>
                        <li><strong>Fully offline</strong> for secure environments</li>
                        <li><strong>Basic codebase scanning</strong></li>
                        <li><strong>1 year of updates included</strong></li>
                        <li>Perfect for individual developers</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Bad Character Scanner Pro</h4>
                    <h3 style="color: #667eea;">$120/year</h3>
                    <ul>
                        <li><strong>No free trial</strong> (upgrade from Basic)</li>
                        <li><strong>Live threat database</strong></li>
                        <li><strong>Real-time pattern updates</strong></li>
                        <li><strong>Advanced analytics</strong></li>
                        <li>Team collaboration features</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Bad Character Scanner Enterprise</h4>
                    <h3 style="color: #667eea;">Custom Quote</h3>
                    <ul>
                        <li><strong>Custom integrations</strong> for B2B partners</li>
                        <li><strong>White-label solutions</strong></li>
                        <li><strong>API licensing</strong> for platforms</li>
                        <li><strong>Dedicated support & consulting</strong></li>
                        <li>Custom feature development</li>
                    </ul>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">$15</span>
                    <span class="stat-label">Customer Acquisition Cost</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">$500K+</span>
                    <span class="stat-label">Enterprise Deal Size</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">95%</span>
                    <span class="stat-label">Gross Margin</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">1.2</span>
                    <span class="stat-label">Months Payback Period</span>
                </div>
            </div>

            <div class="highlight-box">
                <h4 style="color: white;">Strategic B2B Targets:</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; margin-top: 5px;">
                    <div>
                        <h5 style="color: white; margin-bottom: 8px;">IDE & Dev Tools</h5>
                        <ul style="font-size: 0.9em;">
                            <li>Windsurf AI IDE</li>
                            </ul>
                    </div>
                    <div>
                        <h5 style="color: white; margin-bottom: 8px;">LLM Providers</h5>
                        <ul style="font-size: 0.9em;">
                            <li>OpenAI (ChatGPT)</li>                                                       
                        </ul>
                    </div>
                </div>
            </div>
        </div>        <!-- Slide 6: Traction & Validation -->
        <div class="slide">
            <div class="slide-number">6/11</div>
            <h2>Traction & Validation</h2>
            <h3>Strong Early Signals & Technical Validation</h3>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Product Traction</h4>
                    <ul>
                        <li>Working Prototype: v0.2.1 with native drag & drop</li>
                        <li>Cross-Platform Ready: Windows deployed, Tauri v2 enables macOS/Linux</li>
                        <li>Performance Tested: Handles large codebases efficiently</li>
                        <li>Technical Validation: 50+ Unicode attack patterns detected</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Market Validation</h4>
                    <ul>
                        <li>Developer Pain Point: 15,000+ Stack Overflow questions</li>
                        <li>Security Incidents: High-profile homograph attacks in 2024</li>
                        <li>AI Adoption Surge: 300% growth in 18 months</li>
                        <li>Regulatory Interest: New compliance requirements emerging</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Early Interest Signals</h4>
                    <ul>
                        <li>Interest Waitlist: 15 developers signed up for early access</li>
                        <li>Security Awareness: Growing discussion at developer conferences</li>
                        <li>Developer Communities: Positive reception in security forums</li>
                        <li>Partnership Interest: Initial conversations with security vendors</li>
                    </ul>
                </div>
            </div>

            <div class="highlight-box">
                <h4 style="color: #f8f9fa;">Key Milestones Achieved:</h4>
                <ul style="color: #f8f9fa;">
                    <li>Microsoft Store app name reserved for "Bad Character Scanner"</li>
                    <li>Pro version architecture designed with live database capability</li>
                    <li>Tauri v2 framework ensures cross-platform deployment readiness</li>
                    <li>Early access waitlist launched with positive developer interest</li>
                </ul>
            </div>
        </div>        <!-- Slide 7: Competitive Landscape -->
        <div class="slide">
            <div class="slide-number">7/11</div>
            <h2>Competitive Landscape</h2>
            <h3>Blue Ocean Opportunity</h3>

            <table class="competitive-table">
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Bad Character Scanner</th>
                        <th>Invisible Character Viewer</th>
                        <th>expdevBadChars</th>
                        <th>Remove Special Characters</th>
                        <th>InterlaceIQ</th>
                        <th>textclean</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Codebase scanning</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                    </tr>
                    <tr>
                        <td>User-friendly interface</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                    </tr>
                    <tr>
                        <td>Up-to-date & maintained</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                    </tr>
                    <tr>
                        <td>Security & privacy focused</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="check-mark">✓</td>
                        <td class="check-mark">✓</td>
                    </tr>
                    <tr>
                        <td>AI-generated content focus</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                    </tr>
                    <tr>
                        <td>Native desktop app</td>
                        <td class="check-mark">✓</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                        <td class="x-mark">✗</td>
                    </tr>
                </tbody>
            </table>            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Defensive Moats</h4>
                    <ul>
                        <li><strong>Technical Moat:</strong> Sophisticated detection algorithms (patent pending)</li>
                        <li><strong>Data Moat:</strong> Largest Unicode attack pattern database</li>
                        <li><strong>Network Moat:</strong> First-mover advantage in emerging market</li>
                        <li><strong>Brand Moat:</strong> Category leadership in Unicode security</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Market Position</h4>
                    <p><em>"We're not competing in an existing market we're creating a new category"</em></p>
                    <ul>
                        <li>12-18 months to establish category leadership</li>
                        <li>Major security vendors haven't recognized opportunity</li>
                        <li>First major AI Unicode breach will create overnight demand</li>
                    </ul>
                </div>
            </div>
        </div>        <!-- Slide 8: Financial Projections -->
        <div class="slide">
            <div class="slide-number">8/11</div>
            <h2>Financial Projections</h2>
            <h3>Boom-Bust-Boom: Riding the AI Security Wave</h3>

            <!-- Professional Stock-Style Revenue Growth Chart -->
            <div class="revenue-graph">
                <div class="graph-title">ARR Growth Trajectory ($M)</div>
                <div class="graph-y-axis">
                    <span>$50M</span>
                    <span>$40M</span>
                    <span>$30M</span>
                    <span>$20M</span>
                    <span>$10M</span>
                    <span>$0M</span>
                </div>
                <div class="graph-line">
                    <div class="graph-path"></div>
                </div>
                <div class="graph-points">
                    <div class="graph-point point-boom1" data-value="$4M"></div>
                    <div class="graph-point point-bust" data-value="$8M"></div>
                    <div class="graph-point point-boom2" data-value="$45M"></div>
                </div>
                <div class="graph-labels">
                    <span>2025-26</span>
                    <span>2027-28</span>
                    <span>2029+</span>
                </div>
            </div>

            <!-- Compact Timeline -->
            <div class="compact-timeline">
                <div class="compact-milestone">
                    <h5>Year 1-2 (2025-26)</h5>
                    <div style="color: #00ff88; font-weight: bold;">BOOM 1</div>
                    <div class="revenue">$4M ARR</div>
                    <div>Unicode Crisis</div>
                    <div style="font-size: 0.6em;">AI breach triggers adoption</div>
                </div>
                <div class="compact-milestone">
                    <h5>Year 3-4 (2027-28)</h5>
                    <div style="color: #ff4444; font-weight: bold;">BUST</div>
                    <div class="revenue">$8M ARR</div>
                    <div>Market Saturation</div>
                    <div style="font-size: 0.6em;">Steady enterprise base</div>
                </div>
                <div class="compact-milestone">
                    <h5>Year 5+ (2029+)</h5>
                    <div style="color: #00ff88; font-weight: bold;">BOOM 2</div>
                    <div class="revenue">$45M ARR</div>
                    <div>AI Security Platform</div>
                    <div style="font-size: 0.6em;">Industry standard</div>
                </div>
            </div>            <div class="stats-grid" style="grid-template-columns: repeat(2, 1fr); gap: 8px; margin: 10px 0;">
                <div class="stat-card" style="padding: 8px;">
                    <span class="stat-number" style="font-size: 1.4em;">11x</span>
                    <span class="stat-label" style="font-size: 0.8em;">Growth</span>
                </div>
                <div class="stat-card" style="padding: 8px;">
                    <span class="stat-number" style="font-size: 1.4em;">$500M+</span>
                    <span class="stat-label" style="font-size: 0.8em;">Exit Target</span>
                </div>
            </div>
        </div>        <!-- Slide 9: Strategic Evolution & Market Phases -->
        <div class="slide">
            <div class="slide-number">9/11</div>
            <h2>Strategic Evolution</h2>
            <h3>Unicode Security to Comprehensive AI Security Platform</h3>

            <!-- Detailed Phase Descriptions -->
            <div class="phase-summary">
                <div class="phase-card-compact">
                    <h5>Phase 1: Unicode Crisis (2025-26)</h5>
                    <ul style="margin: 4px 0; padding-left: 12px;">
                        <li>Major AI-generated code breach creates market urgency</li>
                        <li>Unicode attacks become mainstream security concern</li>
                        <li>$20 price point enables rapid mass adoption</li>
                        <li>Enterprise rush for immediate solutions</li>
                        <li>200K+ users, 2,000+ enterprise customers</li>
                        <li>First-mover advantage in emerging market</li>
                    </ul>
                </div>
                <div class="phase-card-compact">
                    <h5>Phase 2: Market Maturation (2027-28)</h5>
                    <ul style="margin: 4px 0; padding-left: 12px;">
                        <li>Larger security players enter Unicode market</li>
                        <li>Unicode detection becomes commoditized feature</li>
                        <li>Maintain reliable enterprise revenue base</li>
                        <li>Heavy R&D investment in next-gen AI security</li>
                        <li>Strategic partnerships with major vendors</li>
                        <li>Platform foundation for broader AI threats</li>
                    </ul>
                </div>
                <div class="phase-card-compact">
                    <h5>Phase 3: AI Security Platform (2029+)</h5>
                    <ul style="margin: 4px 0; padding-left: 12px;">
                        <li>Expand beyond Unicode to general LLM security</li>
                        <li>Prompt injection & model poisoning detection</li>
                        <li>First-mover advantage in comprehensive AI security</li>
                        <li>Category leadership in AI threat landscape</li>
                        <li>Strategic acquisition by major security vendors</li>
                        <li>$500M+ exit opportunity with industry leaders</li>
                    </ul>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Revenue Model Evolution</h4>
                    <ul>
                        <li><strong>Phase 1:</strong> Individual + SMB licenses ($20-200)</li>
                        <li><strong>Phase 2:</strong> Enterprise contracts ($5K-50K)</li>
                        <li><strong>Phase 3:</strong> Platform subscriptions ($10K-500K)</li>
                        <li><strong>Exit:</strong> Strategic acquisition ($500M+)</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Technical Evolution</h4>
                    <ul>
                        <li><strong>V1:</strong> Unicode character detection & analysis</li>
                        <li><strong>V2:</strong> Real-time scanning + enterprise features</li>
                        <li><strong>V3:</strong> AI-powered threat intelligence</li>
                        <li><strong>V4:</strong> Comprehensive AI security platform</li>
                    </ul>
                </div>
            </div>

            <div class="highlight-box">
                <h4 style="color: white;">Strategic Roadmap: Unicode to General AI Security Leadership</h4>
                <div style="font-size: 0.9em;">
                    <strong>Years 1-2:</strong> Dominate Unicode security market with rapid user acquisition
                    <strong>Years 3-4:</strong> Build sustainable enterprise revenue while developing broader AI threat capabilities
                    <strong>Year 5+:</strong> Launch comprehensive "AI Code Security Platform" creating new category
                    <strong>Exit Strategy:</strong> $500M+ acquisition by CrowdStrike, Palo Alto Networks, or major cloud provider
                </div>
            </div>
        </div>        <!-- Slide 10: Use of Funds -->
        <div class="slide">
            <div class="slide-number">10/11</div>
            <h2>Use of Funds</h2>
            <h3>$2M Strategic Investment Allocation</h3>            <div class="funding-breakdown">
                <div class="funding-item">
                    <div class="funding-percentage">40%</div>
                    <h4>Product Development</h4>
                    <p><strong>$800K</strong></p>
                    <ul>
                        <li>Platform features & APIs</li>
                        <li>Infrastructure development</li>
                        <li>Security enhancements</li>
                    </ul>
                </div>
                <div class="funding-item">
                    <div class="funding-percentage">30%</div>
                    <h5>Go-to-Market</h5>
                    <p><strong>$600K</strong></p>
                    <ul>
                        <li>Sales team expansion</li>
                        <li>Marketing initiatives</li>
                        <li>Customer success</li>
                    </ul>
                </div>
                <div class="funding-item">
                    <div class="funding-percentage">20%</div>
                    <h5>Engineering Team</h5>
                    <p><strong>$400K</strong></p>
                    <ul>
                        <li>Senior engineers</li>
                        <li>Infrastructure specialists</li>
                        <li>Quality assurance</li>
                    </ul>
                </div>
                <div class="funding-item">
                    <div class="funding-percentage">10%</div>
                    <h5>Operations & Legal</h5>
                    <p><strong>$200K</strong></p>
                    <ul>
                        <li>Compliance & certifications</li>
                        <li>Legal protections</li>
                        <li>Corporate operations</li>
                    </ul>
                </div>
            </div><div class="highlight-box">
                <h5>18-Month Milestones:</h5>
                <ul>
                    <li>$5M ARR run rate</li>
                    <li>Series A readiness with strong metrics</li>
                    <li>Strategic partnerships with major security vendors</li>
                </ul>
            </div>
        </div>        <!-- Slide 11: Conclusion & Call to Action -->
        <div class="slide">
            <div class="slide-number">11/11</div>
            <h2>Partner with Us</h2>
            <h3>First-Mover Advantage in $197B+ AI Security Market</h3>

            <div class="highlight-box">
                <h4 style="color: white;">The Investment Opportunity:</h4>
                <ul style="font-size: 1.1em; margin: 8px 0;">
                    <li><strong>Proven Problem:</strong> 78% of developers vulnerable to Unicode attacks</li>
                    <li><strong>Zero Competition:</strong> Only solution for AI-generated content security</li>
                    <li><strong>Clear Exit Path:</strong> $500M+ acquisition by security leaders</li>
                </ul>
            </div>

            <div class="feature-grid" style="grid-template-columns: 1fr 1fr; gap: 12px;">
                <div class="feature-card">
                    <h4>Growth Strategy</h4>
                    <ul style="font-size: 0.95em;">
                        <li><strong>Year 1-2:</strong> Unicode security boom → $4M ARR</li>
                        <li><strong>Year 3-4:</strong> Enterprise platform → $8M ARR</li>
                        <li><strong>Year 5+:</strong> AI Security leader → $45M ARR</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>$2M Series Seed Usage</h4>
                    <ul style="font-size: 0.95em;">
                        <li><strong>40%</strong> Product & AI security R&D</li>
                        <li><strong>30%</strong> Go-to-market & sales</li>
                        <li><strong>20%</strong> Engineering team</li>
                        <li><strong>10%</strong> Operations & legal</li>
                    </ul>
                </div>
            </div>

            <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); gap: 10px; margin: 15px 0;">
                <div class="stat-card" style="padding: 12px;">
                    <span class="stat-number" style="font-size: 1.8em;">$500M+</span>
                    <span class="stat-label">Exit Target</span>
                </div>
                <div class="stat-card" style="padding: 12px;">
                    <span class="stat-number" style="font-size: 1.8em;">12-18</span>
                    <span class="stat-label">Months to Leadership</span>
                </div>
                <div class="stat-card" style="padding: 12px;">
                    <span class="stat-number" style="font-size: 1.8em;">25x</span>
                    <span class="stat-label">ROI Potential</span>
                </div>
            </div>

            <div class="feature-grid" style="grid-template-columns: 1fr 1fr; gap: 12px; margin: 15px 0;">
                <div class="feature-card">
                    <h4>Why Now?</h4>
                    <ul style="font-size: 0.9em;">
                        <li>AI adoption surge creates immediate demand</li>
                        <li>Working prototype ready for market</li>
                        <li>12-18 month window for category leadership</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Competitive Advantage</h4>
                    <ul style="font-size: 0.9em;">
                        <li>First & only Unicode security platform</li>
                        <li>Patent-pending detection algorithms</li>
                        <li>Cross-platform deployment ready</li>
                    </ul>
                </div>
            </div>

            <div class="contact-info" style="margin: 20px 0; padding: 20px;">
                <h3 style="color: white; margin-bottom: 10px;">Ready to Lead the AI Security Revolution?</h3>
                <p style="font-size: 1.1em; margin: 8px 0;"><strong>Contact:</strong> <EMAIL></p>
                <p style="font-size: 1.0em; margin: 8px 0;"><strong>Demo:</strong> Live prototype available immediately</p>
            </div>
        </div>
    </div>
</body>
</html>