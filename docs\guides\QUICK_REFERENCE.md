# ⚡ Quick Reference - Using Bad Character Scanner

**Fast reference for using the Bad Character Scanner application.**

---

## 🚀 **Getting Started**

### **Run the Application**
```powershell
# Development mode
.\dev_startup.ps1
# OR
cargo tauri dev
```

### **First Use**
1. **Open the app** - Desktop window should appear
2. **Try text analysis** - Paste some text in the Analyze tab
3. **Test file scanning** - Drag a folder to the Codebase tab
4. **Check results** - View analysis results and export if needed

---

## � **Using the Interface**

### **🔍 Analyze Tab**
```
Purpose: Analyze text for Unicode threats
Steps:
1. Paste text in the input area
2. Results appear automatically 
3. Click items to see details
4. Use "Clean Text" if threats found
```

### **📂 Codebase Tab**
```
Purpose: Scan entire projects/folders
Steps:
1. Drag & drop folder OR click "Browse"
2. Wait for analysis (progress shown)
3. Review file-by-file results
4. Export report if needed
```

### **🧹 Clean Tab**
```
Purpose: Clean suspicious text
Steps:
1. Input text with threats
2. Click "Clean Text"
3. Review before/after comparison
4. Copy cleaned text
```

### **Export Tab**
```
Purpose: Generate analysis reports
Steps:
1. Choose format (JSON/CSV/HTML/XML)
2. Click "Export Analysis"
3. Find file in Downloads
```

---

## 🔍 **What the App Detects**

### **🔴 Critical Threats**
- **Bidirectional attacks** (RLO/LRO characters)
- **Zero-width characters** (invisible text)
- **Homograph attacks** (lookalike characters)

### **🟡 Medium Threats**
- **Control characters** (non-printable)
- **Suspicious patterns** (AI-generated content)
- **Unicode variants** (unusual encodings)

### **📊 Example Results**
```
🔍 Analysis Summary:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📂 Files Analyzed: 45
🔴 Critical Issues: 2 (Bidirectional attacks)
🟡 Medium Risk: 8 (Control characters)
🟢 Health Score: 95/100 ✅
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 🛠️ **Common Commands**

### **Development**
```powershell
# Start dev environment
.\dev_startup.ps1

# Clean build
cargo clean && trunk clean
cargo tauri dev

# Build production
cargo tauri build
```

### **Testing**
```powershell
# Test application
.\test-application.ps1

# Manual testing
cargo test
```

### **Troubleshooting**
```powershell
# Check running processes
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"}

# Kill stuck processes
Get-Process | Where-Object {$_.ProcessName -like "*trunk*"} | Stop-Process
```

---

## 🎯 **Use Cases**

### **👨‍💻 For Developers**
- **Before commits**: Scan your code for Unicode threats
- **Code reviews**: Verify files don't contain malicious characters
- **Security audits**: Check dependencies and third-party code

### **🔒 For Security Teams**
- **Incident response**: Analyze suspicious files quickly
- **Threat hunting**: Find hidden Unicode attacks
- **Compliance**: Ensure code meets security standards

### **📝 For Content Teams**
- **User input**: Validate uploaded files and text
- **Content moderation**: Check for hidden malicious content
- **API security**: Scan incoming data for threats

---

## ⚡ **Quick Tips**

### **🔥 Power User Tips**
- **Batch scanning**: Drop multiple folders for parallel analysis
- **Export formats**: Use JSON for developers, CSV for managers, HTML for reports
- **Keyboard shortcuts**: Tab between sections, Enter to analyze
- **Progress tracking**: Watch the progress bar for large codebases

### **🐛 Common Issues**
| Problem | Solution |
|---------|----------|
| App won't start | Run `.\dev_startup.ps1` |
| No analysis results | Check if text contains Unicode characters |
| Export not working | Check Downloads folder permissions |
| Slow analysis | Reduce folder size or close other apps |

---

## � **Need More Help?**

### **📖 Documentation**
- **[🚀 ONBOARDING.md](../ONBOARDING.md)** - Complete setup guide
- **[✨ FEATURES.md](../FEATURES.md)** - Detailed feature overview
- **[🏗️ DEVELOPER_GUIDE.md](../DEVELOPER_GUIDE.md)** - Development guide

### **🔧 Quick Solutions**
- **[🚨 QUICK_FIX_GUIDE.md](QUICK_FIX_GUIDE.md)** - Common problems
- **[⚡ QUICK_NAVIGATION.md](../QUICK_NAVIGATION.md)** - Find any document
- **[📊 Latest Status](../project-management/COMPLETE_SUCCESS_FULL_STACK_WORKING.md)** - Current project state

---

**Status**: ✅ **Production Ready** | **Last Updated**: June 17, 2025
- Click "Select Folder" → Native picker opens → Select folder
- **Expected**: Folder appears in recent folders list

### **✅ Codebase Analysis**
- Select folder → Click "Analyze Codebase" → Watch progress bar
- **Expected**: Real-time progress updates, analysis completes

### **✅ Progress Tracking**
- Any long operation shows live progress
- **Expected**: Smooth percentage updates, current file display

---

## 📖 DOCUMENTATION GUIDE

### **Primary References**
- `README.md` - Project overview and features
- `docs/PROJECT_STATUS_FINAL.md` - Complete technical status
- `docs/TICKET_COMPLETION_SUMMARY.md` - All tickets completed
- `FINAL_DOCUMENTATION_STATUS.md` - Documentation verification

### **Implementation Details**
- `COMMAND_REGISTRATION_COMPLETE.md` - All 19 commands documented
- `PROGRESS_BAR_IMPLEMENTATION_COMPLETE.md` - Progress system details
- `test-application.ps1` - Testing automation script

### **Historical Records**
- `docs/tickets/TICKET_ProgressBarEnhancement_TauriV2.md` - Progress implementation
- `docs/tickets/CODEBASE-7-STATUS.md` - Core implementation history

---

## 🔧 TECHNICAL SPECIFICATIONS

### **Tauri Commands (19 Total)**
```rust
main_module::analyze_characters,
main_module::analyze_codebase,
main_module::export_analysis,
main_module::export_codebase_report,
main_module::clean_codebase,
main_module::batch_analyze,
main_module::get_character_details,
main_module::detect_encoding,
main_module::check_homographs,
main_module::normalize_text,
main_module::get_script_info,
main_module::clean_text,
main_module::clean_text_detailed,
main_module::generate_report,
main_module::select_folder,
main_module::validate_folder_path,
main_module::get_recent_folders,
main_module::save_recent_folder,
main_module::get_quick_access_folders
```

### **Progress Events**
- `"analysis-progress"` - Codebase analysis progress
- `"cleaning-progress"` - Text cleaning progress

### **Build Targets**
- Backend: Rust with Tauri v2
- Frontend: Leptos with WASM via Trunk
- Development: `cargo tauri dev`
- Production: `cargo tauri build`

---

## 🎉 SUCCESS METRICS

### **✅ All Objectives Achieved**
- ✅ 19/19 commands implemented and registered
- ✅ Real-time progress tracking operational
- ✅ Error-free compilation and runtime
- ✅ Complete folder management system
- ✅ Advanced text cleaning capabilities
- ✅ Professional reporting features
- ✅ Production-ready build system
- ✅ Comprehensive documentation

### **✅ Quality Standards Met**
- ✅ No critical errors or warnings
- ✅ All "Command not found" issues resolved
- ✅ Smooth user experience with progress feedback
- ✅ Cross-platform compatibility maintained
- ✅ Professional-grade error handling

---

## 🏁 FINAL STATUS

**PROJECT: COMPLETE ✅**  
**STATUS: PRODUCTION READY ✅**  
**DOCUMENTATION: CURRENT ✅**  
**TESTING: READY ✅**  

The Bad Character Scanner application is now fully implemented with all planned features operational and documented. Ready for deployment and use.

---

*Quick Reference Guide - Bad Character Scanner v0.2.0*
