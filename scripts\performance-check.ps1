#!/usr/bin/env powershell
# Performance analysis for Bad Character Scanner

param(
    [switch]$Detailed,
    [switch]$BenchmarkOnly,
    [string]$TestFile
)

Write-Host "`n⚡ PERFORMANCE ANALYSIS" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent

function Measure-BuildTime {
    Write-Host "`n🔨 Build Performance:" -ForegroundColor Yellow
    
    # Frontend build
    Write-Host "  Frontend build time: " -NoNewline
    $frontend_time = Measure-Command {
        trunk build 2>&1 | Out-Null
    }
    Write-Host "$([int]$frontend_time.TotalSeconds)s" -ForegroundColor Green
    
    # Backend build
    Write-Host "  Backend build time: " -NoNewline
    Push-Location "$project_root\src-tauri"
    $backend_time = Measure-Command {
        cargo build 2>&1 | Out-Null
    }
    Pop-Location
    Write-Host "$([int]$backend_time.TotalSeconds)s" -ForegroundColor Green
    
    # Total
    $total = $frontend_time.TotalSeconds + $backend_time.TotalSeconds
    Write-Host "  Total build time: $([int]$total)s" -ForegroundColor White
}

function Measure-BundleSize {
    Write-Host "`n📦 Bundle Sizes:" -ForegroundColor Yellow
    
    # Frontend bundle
    $dist_dir = Join-Path $project_root "dist"
    if (Test-Path $dist_dir) {
        $frontend_size = (Get-ChildItem $dist_dir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        Write-Host "  Frontend bundle: $([math]::Round($frontend_size, 2)) MB" -ForegroundColor $(if ($frontend_size -lt 5) { "Green" } else { "Yellow" })
        
        # Breakdown by file type
        if ($Detailed) {
            Write-Host "  Breakdown:" -ForegroundColor Gray
            $types = @("*.js", "*.wasm", "*.css", "*.html")
            foreach ($type in $types) {
                $size = (Get-ChildItem $dist_dir -Filter $type -Recurse | Measure-Object -Property Length -Sum).Sum / 1KB
                if ($size -gt 0) {
                    Write-Host "    $type : $([math]::Round($size, 2)) KB" -ForegroundColor Gray
                }
            }
        }
    }
    
    # Backend binary
    $exe_path = Join-Path $project_root "src-tauri\target\debug\laptos-tauri.exe"
    if (Test-Path $exe_path) {
        $exe_size = (Get-Item $exe_path).Length / 1MB
        Write-Host "  Backend binary: $([math]::Round($exe_size, 2)) MB" -ForegroundColor $(if ($exe_size -lt 50) { "Green" } else { "Yellow" })
    }
}

function Measure-CodeMetrics {
    Write-Host "`n📊 Code Metrics:" -ForegroundColor Yellow
    
    # Count lines of code
    $rust_loc = (Get-ChildItem -Path $project_root -Filter "*.rs" -Recurse | 
        Where-Object { $_.FullName -notmatch "target|tests" } | 
        Get-Content | Measure-Object -Line).Lines
    
    $ts_loc = (Get-ChildItem -Path $project_root -Filter "*.ts" -Recurse | 
        Where-Object { $_.FullName -notmatch "node_modules|dist" } | 
        Get-Content | Measure-Object -Line).Lines
    
    Write-Host "  Rust LOC: $rust_loc" -ForegroundColor Gray
    Write-Host "  TypeScript LOC: $ts_loc" -ForegroundColor Gray
    Write-Host "  Total LOC: $($rust_loc + $ts_loc)" -ForegroundColor White
    
    # Function count
    $rust_functions = (Get-ChildItem -Path $project_root -Filter "*.rs" -Recurse | 
        Where-Object { $_.FullName -notmatch "target" } | 
        Get-Content -Raw | Select-String -Pattern "fn\s+\w+" -AllMatches).Matches.Count
    
    Write-Host "  Rust functions: $rust_functions" -ForegroundColor Gray
    
    # Module count
    $modules = (Get-ChildItem -Path "$project_root\src-tauri\src" -Filter "*.rs").Count
    Write-Host "  Backend modules: $modules" -ForegroundColor Gray
}

function Run-Benchmark {
    param([string]$TestFilePath)
    
    Write-Host "`n🏃 Running Performance Benchmark:" -ForegroundColor Yellow
    
    if (-not $TestFilePath) {
        # Create a test file with bad characters
        $test_dir = Join-Path $project_root "temp_perf_test"
        New-Item -ItemType Directory -Path $test_dir -Force | Out-Null
        
        $TestFilePath = Join-Path $test_dir "test_file.txt"
        
        # Generate test content with various bad characters
        $content = @"
Normal text here.
Text with zero-width characters: A​B​C
Text with homoglyphs: Аррlе (Cyrillic A)
Text with RTL override: ‏Hello
Text with non-breaking spaces: Hello World
"@ * 1000  # Repeat for larger file
        
        Set-Content -Path $TestFilePath -Value $content -Encoding UTF8
        Write-Host "  Created test file: $([math]::Round((Get-Item $TestFilePath).Length / 1KB, 2)) KB" -ForegroundColor Gray
    }
    
    # Test CLI analyzer if available
    $cli_path = Join-Path $project_root "src-tauri\target\debug\analyzer_cli.exe"
    if (Test-Path $cli_path) {
        Write-Host "  Testing CLI analyzer..." -ForegroundColor Gray
        
        $cli_time = Measure-Command {
            & $cli_path $TestFilePath --output-format json 2>&1 | Out-Null
        }
        
        Write-Host "  CLI analysis time: $([int]$cli_time.TotalMilliseconds)ms" -ForegroundColor Green
    }
    
    # Test Node.js analyzer
    $node_analyzer = Join-Path $project_root "scripts\check-bad-characters.js"
    if ((Test-Path $node_analyzer) -and (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-Host "  Testing Node.js analyzer..." -ForegroundColor Gray
        
        $node_time = Measure-Command {
            node $node_analyzer $test_dir 2>&1 | Out-Null
        }
        
        Write-Host "  Node.js analysis time: $([int]$node_time.TotalMilliseconds)ms" -ForegroundColor Green
    }
    
    # Cleanup
    if ($test_dir -and (Test-Path $test_dir)) {
        Remove-Item -Path $test_dir -Recurse -Force
    }
}

function Check-MemoryUsage {
    Write-Host "`n💾 Memory Analysis:" -ForegroundColor Yellow
    
    # Check if dev server is running
    $tauri_process = Get-Process -Name "laptos-tauri" -ErrorAction SilentlyContinue
    if ($tauri_process) {
        $memory_mb = [math]::Round($tauri_process.WorkingSet64 / 1MB, 2)
        Write-Host "  Current memory usage: $memory_mb MB" -ForegroundColor $(if ($memory_mb -lt 500) { "Green" } else { "Yellow" })
        Write-Host "  Process uptime: $([int]((Get-Date) - $tauri_process.StartTime).TotalMinutes) minutes" -ForegroundColor Gray
    } else {
        Write-Host "  Application not running" -ForegroundColor Gray
    }
}

# Run analyses
if (-not $BenchmarkOnly) {
    Measure-BuildTime
    Measure-BundleSize
    Measure-CodeMetrics
    Check-MemoryUsage
}

if ($TestFile -or -not $BenchmarkOnly) {
    Run-Benchmark -TestFilePath $TestFile
}

# Performance recommendations
Write-Host "`n💡 Performance Recommendations:" -ForegroundColor Cyan

$recommendations = @()

# Check bundle size
$dist_dir = Join-Path $project_root "dist"
if (Test-Path $dist_dir) {
    $size = (Get-ChildItem $dist_dir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
    if ($size -gt 5) {
        $recommendations += "Consider optimizing frontend bundle size (currently $([math]::Round($size, 2)) MB)"
    }
}

# Check for large modules
$large_modules = Get-ChildItem -Path "$project_root\src-tauri\src" -Filter "*.rs" | 
    Where-Object { (Get-Content $_.FullName | Measure-Object -Line).Lines -gt 1000 }

if ($large_modules) {
    $recommendations += "Consider splitting large modules: $($large_modules.Name -join ', ')"
}

# Display recommendations
if ($recommendations) {
    foreach ($rec in $recommendations) {
        Write-Host "  - $rec" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ✅ Performance looks good!" -ForegroundColor Green
}

Write-Host "`n⚡ Keep it fast and accessible!" -ForegroundColor Cyan