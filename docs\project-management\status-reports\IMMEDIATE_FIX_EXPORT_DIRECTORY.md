# IMMEDIATE FIX: Export Directory Issue

**Issue**: Export button causes application restart due to file watcher monitoring `reports` directory in source tree.

**Quick Fix**: Change export directory from project source tree to user Documents folder.

## Code Change Required

**File**: `src-tauri/src/main_module.rs`  
**Line**: ~343  

### Current Code (Problematic):
```rust
let reports_dir = project_root.join("reports");
```

### Fixed Code:
```rust
// Use user's Documents directory to avoid file watcher conflicts
let reports_dir = dirs::document_dir()
    .unwrap_or_else(|| std::env::temp_dir())
    .join("BadCharacterScanner")
    .join("Reports");
```

## Why This Fixes the Issue

1. **Moves exports outside source tree**: Files won't trigger <PERSON><PERSON>'s development file watcher
2. **User-accessible location**: Documents folder is easily accessible to users
3. **Cross-platform**: `dirs::document_dir()` works on Windows, macOS, and Linux
4. **Fallback safety**: Uses temp directory if Documents folder unavailable
5. **Organized**: Creates dedicated subfolder for the application

## Implementation Steps

1. Replace the single line of code in `main_module.rs`
2. Test export functionality
3. Verify no application restart occurs
4. Update documentation with new export location

## Expected Export Locations

- **Windows**: `C:\Users\<USER>\Documents\BadCharacterScanner\Reports\`
- **macOS**: `/Users/<USER>/Documents/BadCharacterScanner/Reports/`
- **Linux**: `/home/<USER>/Documents/BadCharacterScanner/Reports/`

## Testing

1. Click Export button
2. Verify file is created in Documents folder
3. Confirm application does not restart
4. Check success message displays correctly

This is a minimal, one-line fix that resolves the core issue immediately.
