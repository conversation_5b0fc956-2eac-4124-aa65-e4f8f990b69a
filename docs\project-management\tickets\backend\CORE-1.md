# CORE-1 - Character Analysis Engine

**Status:** 🟢 Open  
**Priority:** High  
**Created:** 2025-05-27  
**Updated:** 2025-05-27  
**Assigned To:** @dev  
**Related Issues:** ARCH-1, SETUP-1

## Description

Implement the core character analysis engine that identifies potentially problematic Unicode characters, analyzes text encoding, and provides detailed character information. This engine will be shared between PWA and Tauri implementations.

## Acceptance Criteria

- [ ] Unicode character classification system
- [ ] Bad character detection algorithms
- [ ] Encoding analysis and validation
- [ ] Performance-optimized for real-time analysis
- [ ] Comprehensive character information database
- [ ] Configurable detection rules
- [ ] Export functionality for results

---

## Sub-tasks
- CORE-1.1 — Unicode Character Analysis: Ensure backend engine detects all target invisible/suspicious characters.
- CORE-1.2 — Export/Reporting: Implement export of results (CSV, JSON, etc.) via frontend.
- CORE-1.3 — Performance and Scalability: Test and optimize for large files and codebases.

## Technical Implementation

### Core Analysis Types

```rust
// Core data structures
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: Option<String>,
    pub unicode_block: String,
    pub category: UnicodeCategory,
    pub codepoint: u32,
    pub utf8_bytes: Vec<u8>,
    pub utf16_units: Vec<u16>,
    pub is_suspicious: bool,
    pub suspicion_reasons: Vec<SuspicionReason>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SuspicionReason {
    InvisibleCharacter,
    LookalikeCharacter { resembles: char },
    ControlCharacter,
    PrivateUseArea,
    NonPrintable,
    DirectionalOverride,
    ZeroWidthCharacter,
    UnusualCombining,
    DeprecatedCharacter,
    SecuritySensitive,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub input_text: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub encoding_info: EncodingInfo,
    pub suspicious_characters: Vec<CharacterInfo>,
    pub character_breakdown: HashMap<UnicodeCategory, usize>,
    pub analysis_duration: Duration,
    pub confidence_score: f32,
}
```

### Analysis Engine Implementation

```rust
pub struct CharacterAnalyzer {
    rules: Vec<Box<dyn AnalysisRule>>,
    unicode_db: UnicodeDatabase,
    lookalike_db: LookalikeDatabase,
}

impl CharacterAnalyzer {
    pub fn new() -> Self {
        Self {
            rules: Self::default_rules(),
            unicode_db: UnicodeDatabase::new(),
            lookalike_db: LookalikeDatabase::new(),
        }
    }
    
    pub async fn analyze_text(&self, text: &str) -> AnalysisResults {
        let start_time = Instant::now();
        let mut suspicious_chars = Vec::new();
        let mut char_breakdown = HashMap::new();
        
        for (pos, ch) in text.char_indices() {
            let char_info = self.analyze_character(ch, pos);
            
            // Update statistics
            *char_breakdown.entry(char_info.category).or_insert(0) += 1;
            
            // Check if suspicious
            if char_info.is_suspicious {
                suspicious_chars.push(char_info);
            }
        }
        
        AnalysisResults {
            input_text: text.to_string(),
            total_characters: text.chars().count(),
            total_bytes: text.len(),
            encoding_info: self.analyze_encoding(text),
            suspicious_characters: suspicious_chars,
            character_breakdown: char_breakdown,
            analysis_duration: start_time.elapsed(),
            confidence_score: self.calculate_confidence(&suspicious_chars),
        }
    }
    
    fn analyze_character(&self, ch: char, position: usize) -> CharacterInfo {
        let mut suspicion_reasons = Vec::new();
        
        // Apply all analysis rules
        for rule in &self.rules {
            if let Some(reason) = rule.check_character(ch) {
                suspicion_reasons.push(reason);
            }
        }
        
        CharacterInfo {
            character: ch,
            position,
            unicode_name: self.unicode_db.get_name(ch),
            unicode_block: self.unicode_db.get_block(ch),
            category: self.unicode_db.get_category(ch),
            codepoint: ch as u32,
            utf8_bytes: ch.to_string().as_bytes().to_vec(),
            utf16_units: ch.encode_utf16(&mut [0; 2]).to_vec(),
            is_suspicious: !suspicion_reasons.is_empty(),
            suspicion_reasons,
            recommendations: self.generate_recommendations(ch, &suspicion_reasons),
        }
    }
}
```

### Analysis Rules System

```rust
pub trait AnalysisRule: Send + Sync {
    fn check_character(&self, ch: char) -> Option<SuspicionReason>;
    fn name(&self) -> &str;
    fn description(&self) -> &str;
}

// Invisible character detection
pub struct InvisibleCharacterRule;
impl AnalysisRule for InvisibleCharacterRule {
    fn check_character(&self, ch: char) -> Option<SuspicionReason> {
        match ch {
            '\u{200B}' | '\u{200C}' | '\u{200D}' | '\u{FEFF}' => {
                Some(SuspicionReason::ZeroWidthCharacter)
            },
            '\u{2060}' | '\u{2061}' | '\u{2062}' | '\u{2063}' => {
                Some(SuspicionReason::InvisibleCharacter)
            },
            _ => None,
        }
    }
    
    fn name(&self) -> &str { "Invisible Characters" }
    fn description(&self) -> &str { "Detects invisible or zero-width characters" }
}

// Lookalike character detection
pub struct LookalikeCharacterRule {
    lookalike_db: Arc<LookalikeDatabase>,
}

impl AnalysisRule for LookalikeCharacterRule {
    fn check_character(&self, ch: char) -> Option<SuspicionReason> {
        if let Some(resembles) = self.lookalike_db.find_lookalike(ch) {
            Some(SuspicionReason::LookalikeCharacter { resembles })
        } else {
            None
        }
    }
    
    fn name(&self) -> &str { "Lookalike Characters" }
    fn description(&self) -> &str { "Detects characters that look similar to common ASCII" }
}
```

### Real-time Analysis for Leptos

```rust
// Hook for real-time character analysis in Leptos
pub fn use_character_analyzer() -> (
    ReadSignal<String>,
    WriteSignal<String>,
    ReadSignal<Option<AnalysisResults>>,
    ReadSignal<bool>, // is_analyzing
) {
    let (input_text, set_input_text) = create_signal(String::new());
    let (results, set_results) = create_signal(None);
    let (is_analyzing, set_is_analyzing) = create_signal(false);
    
    let analyzer = CharacterAnalyzer::new();
    
    // Debounced analysis effect
    create_effect(move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_results.set(None);
            return;
        }
        
        set_is_analyzing.set(true);
        
        spawn_local(async move {
            // Add small delay for debouncing
            TimeoutFuture::new(300).await;
            
            let analysis_results = analyzer.analyze_text(&text).await;
            set_results.set(Some(analysis_results));
            set_is_analyzing.set(false);
        });
    });
    
    (input_text, set_input_text, results, is_analyzing)
}
```

## Implementation Tasks

### Phase 1: Core Engine
- [ ] Unicode database integration
- [ ] Basic character classification
- [ ] Suspicious character rule system
- [ ] Performance optimization for large texts

### Phase 2: Advanced Detection
- [ ] Lookalike character database
- [ ] Context-aware analysis
- [ ] Machine learning-based detection
- [ ] Custom rule configuration

### Phase 3: Integration & Export
- [ ] Leptos hooks and components
- [ ] Tauri command integration
- [ ] Multiple export formats (JSON, CSV, PDF)
- [ ] Batch processing capabilities

## Dependencies

```toml
[dependencies]
# Unicode handling
unicode-normalization = "0.1"
unicode-segmentation = "1.10"
unicode-width = "0.1"

# Data structures and serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Performance and async
tokio = { version = "1.0", features = ["time"] }
rayon = "1.7" # For parallel processing

# Optional: ML-based detection
candle = { version = "0.3", optional = true }
```

## Performance Requirements

- [ ] Real-time analysis for texts up to 100KB
- [ ] Analysis latency < 100ms for typical inputs
- [ ] Memory usage < 50MB for analysis engine
- [ ] Batch processing for files up to 10MB

## Testing Strategy

### Unit Tests
- [ ] Individual rule testing
- [ ] Unicode database accuracy
- [ ] Performance benchmarks
- [ ] Edge case handling

### Integration Tests
- [ ] Complete analysis workflow
- [ ] Large text processing
- [ ] Real-world suspicious text samples
- [ ] False positive rate testing

### Validation Tests
- [ ] Known bad character datasets
- [ ] Security research samples
- [ ] Internationalization edge cases
- [ ] Performance regression tests

---
*Last updated: 2025-05-27*
