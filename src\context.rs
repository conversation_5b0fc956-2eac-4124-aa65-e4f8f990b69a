// Global application context for sharing state between components
use leptos::*;
use serde::{Serialize, Deserialize};
use js_sys;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalysisContext {
    pub current_analysis: Option<String>, // JSON string of the analysis result
    pub analysis_type: Option<String>,    // "text", "file", or "codebase"
    pub analysis_timestamp: Option<String>,
}

impl Default for AnalysisContext {
    fn default() -> Self {
        Self {
            current_analysis: None,
            analysis_type: None,
            analysis_timestamp: None,
        }
    }
}

impl AnalysisContext {
    pub fn set_analysis(&mut self, analysis_json: String, analysis_type: String) {
        self.current_analysis = Some(analysis_json);
        self.analysis_type = Some(analysis_type);
        self.analysis_timestamp = Some(
            js_sys::Date::new_0()
                .to_iso_string()
                .as_string()
                .unwrap_or_default()
        );
    }
    
    #[allow(dead_code)]
    pub fn clear_analysis(&mut self) {
        self.current_analysis = None;
        self.analysis_type = None;
        self.analysis_timestamp = None;
    }

    #[allow(dead_code)]
    pub fn has_analysis(&self) -> bool {
        self.current_analysis.is_some()
    }
    
    pub fn get_analysis_data(&self) -> Option<&String> {
        self.current_analysis.as_ref()
    }
}

// Context provider for the analysis state
#[derive(Clone)]
pub struct AnalysisContextValue {
    pub analysis: RwSignal<AnalysisContext>,
}

impl AnalysisContextValue {
    pub fn new() -> Self {
        Self {
            analysis: create_rw_signal(AnalysisContext::default()),
        }
    }
    
    pub fn set_analysis(&self, analysis_json: String, analysis_type: String) {
        self.analysis.update(|ctx| {
            ctx.set_analysis(analysis_json, analysis_type);
        });
    }
    
    #[allow(dead_code)]
    pub fn clear_analysis(&self) {
        self.analysis.update(|ctx| {
            ctx.clear_analysis();
        });
    }

    pub fn get_analysis_data(&self) -> Option<String> {
        self.analysis.with(|ctx| ctx.get_analysis_data().cloned())
    }

    #[allow(dead_code)]
    pub fn has_analysis(&self) -> bool {
        self.analysis.with(|ctx| ctx.has_analysis())
    }
    
    pub fn get_analysis_type(&self) -> Option<String> {
        self.analysis.with(|ctx| ctx.analysis_type.clone())
    }
}

// Provide the context at the app level
pub fn provide_analysis_context() -> AnalysisContextValue {
    let context = AnalysisContextValue::new();
    provide_context(context.clone());
    context
}

// Use the context in components
pub fn use_analysis_context() -> AnalysisContextValue {
    expect_context::<AnalysisContextValue>()
}
