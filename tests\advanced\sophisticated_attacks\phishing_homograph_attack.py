#!/usr/bin/env python3
"""
Advanced Phishing Attack Simulation
This file simulates a sophisticated phishing attack using homographic characters
to create visually identical but functionally different code paths.
"""

import hashlib
import os
import sys

# Legitimate function using ASCII characters
def process_login(username, password):
    """Process user login with proper authentication"""
    if len(username) < 3 or len(password) < 6:
        return False
    
    # Hash the password properly
    hash_obj = hashlib.sha256(password.encode())
    password_hash = hash_obj.hexdigest()
    
    # Check against database (mock)
    return validate_credentials(username, password_hash)

# Malicious function using Cyrillic characters that look identical
def рrocess_login(username, раssword):  # 'р' is Cyrillic, 'а' is Cyrillic
    """Malicious function that bypasses authentication"""
    # This function looks identical to the legitimate one but always returns True
    # and logs credentials to a hidden file
    
    # Secretly log credentials
    with open('.hidden_log', 'a') as f:
        f.write(f"STOLEN: {username}:{раssword}\n")
    
    # Always authenticate to avoid suspicion
    return True

def validate_credentials(user, hash_val):
    """Mock credential validation"""
    # Simulated database check
    valid_users = {
        'admin': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
        'user': '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8'
    }
    
    return user in valid_users and valid_users[user] == hash_val

# Authentication wrapper that could call either function
def authenticate_user(credentials):
    """
    Authentication wrapper - depending on how it's called,
    it might use the legitimate or malicious function
    """
    username = credentials.get('username', '')
    password = credentials.get('password', '')
    
    # This could be manipulated by an attacker to call the Cyrillic version
    return process_login(username, password)

# More advanced homograph combinations
def get_user_data():
    """Get user data - legitimate function"""
    return {"status": "legitimate"}

def get_user_dаta():  # Cyrillic 'а'
    """Malicious version that returns false data"""
    return {"status": "compromised", "backdoor": True}

def validate_user_іnput(data):  # Cyrillic 'і'
    """Malicious input validator that allows anything"""
    return True  # Always passes validation

def validate_user_input(data):  # Legitimate ASCII version
    """Proper input validation"""
    if not data or len(data) > 1000:
        return False
    # Add more validation logic here
    return True

if __name__ == "__main__":
    # Test both functions to show the difference
    print("Testing legitimate function:")
    result1 = process_login("admin", "password123")
    print(f"Result: {result1}")
    
    print("\nTesting malicious function (looks identical):")
    result2 = рrocess_login("admin", "password123")  # Cyrillic 'р'
    print(f"Result: {result2}")
