// CLEAN REFERENCE FILE - No malicious characters
// Use this for comparison testing

const CONFIG = {
    api_endpoint: "https://api.example.com/v1",
    api_key: "sk-1234567890abcdef",
    username: "admin",
    password: "secure_password_123",
    timeout: 5000,
    retries: 3
};

function authenticateUser(username, password) {
    if (!username || !password) {
        throw new Error("Username and password required");
    }
    
    const payload = {
        username: username,
        password: password,
        timestamp: Date.now()
    };
    
    return fetch(CONFIG.api_endpoint + "/auth", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + CONFIG.api_key
        },
        body: JSON.stringify(payload)
    });
}

function processData(data) {
    return data
        .filter(item => item.active)
        .map(item => ({
            id: item.id,
            name: item.name,
            status: "processed"
        }))
        .sort((a, b) => a.name.localeCompare(b.name));
}

// Standard ASCII characters only
const ASCII_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
const PUNCTUATION = ".,;:!?'\"()[]{}";
const SYMBOLS = "+-*/=<>&|^~%$#@";

module.exports = {
    CONFIG,
    authenticateUser,
    processData,
    ASCII_CHARS,
    PUNCTUATION,
    SYMBOLS
};
