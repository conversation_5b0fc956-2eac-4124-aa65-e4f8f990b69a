# Simple CLI Testing Script for Bash Interface

# Configuration
$ProjectRoot = $PWD
$CLIBinary = Join-Path $ProjectRoot "target\release\analyzer_cli.exe"
$SecureTestData = Join-Path $ProjectRoot "test_data_secure"

# Test tracking
$TestsPassed = 0
$TestsFailed = 0

function Write-TestResult {
    param([string]$TestName, [bool]$Passed, [string]$Details = "")
    
    if ($Passed) {
        $script:TestsPassed++
        Write-Host "PASS: $TestName" -ForegroundColor Green
    } else {
        $script:TestsFailed++
        Write-Host "FAIL: $TestName" -ForegroundColor Red
        if ($Details) {
            Write-Host "   Details: $Details" -ForegroundColor Yellow
        }
    }
}

# Main tests
Write-Host "CLI Testing Framework" -ForegroundColor Cyan

# Test 1: CLI exists
$cliExists = Test-Path $CLIBinary
Write-TestResult "CLI binary exists" $cliExists

if (-not $cliExists) {
    Write-Host "Cannot continue without CLI binary" -ForegroundColor Red
    exit 1
}

# Test 2: CLI shows usage
try {
    $result = & $CLIBinary 2>&1
    $showsUsage = ($result | Where-Object { $_ -like "*Usage:*" }).Count -gt 0
    Write-TestResult "CLI shows usage" $showsUsage
} catch {
    Write-TestResult "CLI shows usage" $false $_.Exception.Message
}

# Test 3: Test malicious file detection
$testFiles = @(
    @{ File = "zero_width_attack.js"; Expected = 7 },
    @{ File = "bidi_override_attack.py"; Expected = 3 },
    @{ File = "clean_reference.js"; Expected = 0 }
)

foreach ($test in $testFiles) {
    $filePath = Join-Path $SecureTestData $test.File
    
    if (Test-Path $filePath) {
        try {
            $output = & $CLIBinary analyze $filePath json 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                # Look for suspicious character count in output
                $issueCount = 0
                foreach ($line in $output) {
                    if ($line -match '"total_suspicious_chars":\s*(\d+)') {
                        $issueCount = [int]$matches[1]
                        break
                    }
                }
                
                $correct = $issueCount -eq $test.Expected
                Write-TestResult "$($test.File) detection" $correct "Expected: $($test.Expected), Found: $issueCount"
            } else {
                Write-TestResult "$($test.File) detection" $false "CLI failed with exit code: $LASTEXITCODE"
            }
        } catch {
            Write-TestResult "$($test.File) detection" $false $_.Exception.Message
        }
    } else {
        Write-TestResult "$($test.File) detection" $false "Test file not found"
    }
}

# Test 4: Error handling
try {
    $result = & $CLIBinary analyze "nonexistent.txt" json 2>&1
    $handlesError = $LASTEXITCODE -ne 0
    Write-TestResult "Error handling (missing file)" $handlesError "Exit code: $LASTEXITCODE"
} catch {
    Write-TestResult "Error handling (missing file)" $false $_.Exception.Message
}

# Test 5: Output formats
$formats = @("json", "text")
$testFile = Join-Path $SecureTestData "clean_reference.js"

foreach ($format in $formats) {
    try {
        $output = & $CLIBinary analyze $testFile $format 2>&1
        $formatWorks = $LASTEXITCODE -eq 0 -and $output.Length -gt 0
        Write-TestResult "$format format output" $formatWorks "Exit code: $LASTEXITCODE"
    } catch {
        Write-TestResult "$format format output" $false $_.Exception.Message
    }
}

# Summary
Write-Host ""
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
$total = $TestsPassed + $TestsFailed
Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $TestsPassed" -ForegroundColor Green
Write-Host "Failed: $TestsFailed" -ForegroundColor Red

$successRate = if ($total -gt 0) { [math]::Round(($TestsPassed / $total) * 100, 2) } else { 0 }
$color = if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" }
Write-Host "Success Rate: $successRate%" -ForegroundColor $color

if ($TestsFailed -eq 0) {
    Write-Host ""
    Write-Host "All tests passed! CLI is working correctly." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "Some tests failed. Please review the results." -ForegroundColor Yellow
}
