# Justfile for Laptos TauriV2 Bad Character Scanner
# Install just: cargo install just

# Default target (run with just or just --list)
default:
    @just --list

# Install dependencies
install:
    # Install Rust toolchain if not installed
    rustup update
    rustup target add wasm32-unknown-unknown
    
    # Install trunk for frontend
    cargo install --locked trunk
    
    # Install wasm-bindgen
    cargo install -f wasm-bindgen-cli
    
    # Install Tauri CLI
    cargo install tauri-cli

# Format code
fmt:
    cargo fmt --all -- --check

# Lint code
lint:
    cargo clippy -- -D warnings

# Run tests
test:
    cargo test --workspace

# Run in development mode
dev:
    cargo tauri dev

# Build for production
build:
    cargo tauri build

# Run formatter and linter
check: fmt lint

# Clean build artifacts
clean:
    cargo clean
    rm -rf target

# Run all checks (format, lint, test)
ci: check test

# Show help
help:
    @echo "Available commands:"
    @just --list

# Alias for help
tasks: help
