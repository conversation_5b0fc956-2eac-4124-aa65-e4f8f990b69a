use wasm_bindgen::prelude::*;
use js_sys;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    pub async fn invoke(cmd: &str, args: JsValue) -> JsValue;
    
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "event"])]
    pub async fn listen(event: &str, handler: &js_sys::Function) -> JsValue;
    
    #[wasm_bindgen(js_namespace = ["window"], catch)]
    pub fn check_tauri() -> Result<JsValue, JsValue>;
}