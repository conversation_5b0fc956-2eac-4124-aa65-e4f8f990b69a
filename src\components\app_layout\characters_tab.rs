use leptos::*;
use crate::{AnalysisResults, CharacterInfo};


#[component]
pub fn CharactersTab(results: ReadSignal<Option<AnalysisResults>>) -> impl IntoView {
    // Create a memoized signal for suspicious characters.
    // This avoids re-cloning the data on every view render and is more idiomatic.
    // Use create_memo to efficiently track suspicious characters from results.
    let suspicious_characters = create_memo(move |_| {
        results.with(|res| {
            res.as_ref()
                .map(|r| r.suspicious_characters.as_slice().to_vec())
                .unwrap_or_else(Vec::new)
        })
    });

    // Derived signals for count and emptiness check.
    let suspicious_count = create_memo(move |_| {
        suspicious_characters.with(|chars| chars.len())
    });
    let has_suspicious_chars = create_memo(move |_| {
        suspicious_count.get() > 0
    });

    view! {
        <div class="tab-content-inner">
            <img src="assets/images/ui_images_40x40/uiimg_003.png" class="icon-40" style="width:40px;height:40px;display:inline-block;" alt="Character Analysis Icon" />
            {move || results.with(|res_opt| {
                if let Some(res) = res_opt {
                    view! {
                        <div class="stats-grid">
                            <div class="stat-card icon-40" style="width:40px;height:40px;display:inline-block;">
                                <div class="stat-value">{res.total_characters}</div>
                                <div class="stat-label">"Total Characters"</div>
                            </div>
                            <div class="stat-card">
                            <div class="stat-value">{suspicious_count.get()}</div>
                                <div class="stat-label">"Suspicious Characters"</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">{res.character_breakdown.get("Invisible").copied().unwrap_or(0)}</div>
                                <div class="stat-label">"Invisible Characters"</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">{res.security_analysis.homograph_attacks.len()}</div>
                                <div class="stat-label">"Homoglyphs"</div>
                            </div>
                        </div>

                        <div class="suspicious-chars-container">
                            <img src="assets/images/ui_images_40x40/uiimg_004.png" class="table-header icon-40" style="width:40px;height:40px;display:inline-block;" alt="Suspicious Characters List Icon" />
                            <Show
                                when=move || has_suspicious_chars.get()
                                fallback=|| view! { <p>"No suspicious characters found."</p> }
                            >
                                <div class="table-container">
                                    <table class="char-table">
                                        <thead>
                                            <tr>
                                                <th>"Character"</th>
                                                <th>"Code"</th>
                                                <th>"Description"</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <For
                                                each=move || suspicious_characters.get()
                                                key=|char_info| (char_info.character, char_info.position)
                                                children=move |char_info: CharacterInfo| {
                                                    view! {
                                                        <tr>
                                                            <td>{char_info.character}</td>
                                                            <td>{format!("U+{:04X}", char_info.codepoint)}</td>
                                                            <td>{char_info.unicode_name}</td>
                                                        </tr>
                                                    }
                                                }
                                            />
                                        </tbody>
                                    </table>
                                </div>
                        </Show>
                        </div>
                    }.into_view()
                } else {
                    view! { <div>"No analysis results available"</div> }.into_view()
                }
            })}
        </div>
    }
}
