use leptos::*;
use wasm_bindgen::prelude::*;
use wasm_bindgen::closure::Closure;
use wasm_bindgen::JsCast;
// Removed unused DragEvent import
use js_sys;
use serde_json;

use crate::components::codebase::{
    types::BasicProgress,
    tauri_bindings::{invoke, listen},
    utils::{is_tauri_available, get_demo_result},
};
use crate::utils::logging::{Logger, callback_manager};
// Removed unused use_analysis_context import

async fn simulate_web_analysis_progress(
    set_progress: WriteSignal<Option<BasicProgress>>,
    set_analysis_result: WriteSignal<Option<String>>,
    analysis_context: Option<crate::context::AnalysisContextValue>,
    _path: String,
) {
    // Simulate realistic file analysis progress
    let demo_files = vec![
        "src/main.rs",
        "src/lib.rs",
        "src/components/mod.rs",
        "src/components/codebase/handlers.rs",
        "src/components/codebase/types.rs",
        "src/components/text/analyzer.rs",
        "src/utils/unicode.rs",
        "Cargo.toml",
        "README.md",
        "package.json",
        "tsconfig.json",
        "index.html",
        "style.css",
    ];

    let total_files = demo_files.len() as u32;
    let _start_time = std::time::Instant::now();

    // Initial progress
    set_progress.set(Some(BasicProgress {
        current: 0,
        total: total_files,
        percentage: 0.0,
        current_file: "Initializing analysis...".to_string(),
        status: "Starting".to_string(),
    }));

    // Small initial delay
    gloo_timers::future::sleep(std::time::Duration::from_millis(300)).await;

    // Analyze each file with realistic timing
    for (index, file) in demo_files.iter().enumerate() {
        let current = (index + 1) as u32;
        let percentage = (current as f32 / total_files as f32) * 100.0;

        set_progress.set(Some(BasicProgress {
            current,
            total: total_files,
            percentage,
            current_file: file.to_string(),
            status: "Analyzing".to_string(),
        }));

        // Variable delay based on file type to simulate realistic analysis
        let delay = match file {
            f if f.contains("handlers.rs") => 400,  // Larger source file
            f if f.contains("main.rs") => 300,
            f if f.contains("lib.rs") => 350,
            f if f.contains(".json") => 150,        // Config files
            f if f.contains(".toml") => 100,
            f if f.contains(".css") => 200,
            f if f.contains(".html") => 180,
            _ => 250,
        };

        gloo_timers::future::sleep(std::time::Duration::from_millis(delay)).await;
    }

    // Final completion
    set_progress.set(Some(BasicProgress {
        current: total_files,
        total: total_files,
        percentage: 100.0,
        current_file: "Analysis complete!".to_string(),
        status: "Complete".to_string(),
    }));

    // Brief pause to show completion
    gloo_timers::future::sleep(std::time::Duration::from_millis(500)).await;

    // Set the demo result
    let demo_result = get_demo_result();
    let result_string = demo_result.to_string();
    set_analysis_result.set(Some(result_string.clone()));

    // Store in global context if available
    if let Some(context) = analysis_context {
        context.set_analysis(result_string, "codebase".to_string());
    }

    // Clear progress
    set_progress.set(None);
}

pub fn setup_progress_listener(set_progress: WriteSignal<Option<BasicProgress>>) {
    spawn_local(async move {
        if !is_tauri_available() {
            // Web mode - no progress listener needed
            return;
        }

        let progress_handler = Closure::wrap(Box::new(move |event: JsValue| {
            if let Ok(progress_data) = serde_wasm_bindgen::from_value::<BasicProgress>(event) {
                set_progress.set(Some(progress_data));
            }
        }) as Box<dyn FnMut(JsValue)>);

        // SAFETY: listen() is safe to call with valid callback reference
        // The callback is properly constructed and the reference is valid for the duration of the call
        let _result = listen("analysis-progress", progress_handler.as_ref().unchecked_ref()).await;

        Logger::debug("Analysis progress listener set up");
        // Register the callback for proper memory management
        let callback_id = "analysis-progress-listener".to_string();
        match callback_manager::register_callback(callback_id, progress_handler) {
            Ok(_) => {
                Logger::debug("Progress listener callback registered for memory management");
            }
            Err(e) => {
                Logger::warn(&format!("Failed to register progress callback: {}", e));
                // Note: progress_handler was moved into register_callback, so we can't forget it here
            }
        }
    });
}

// For now, let's use a simpler approach and set up file drop through JavaScript
// We'll add the file drop setup in the main component using a script tag
pub fn setup_file_drop_listener(
    set_is_analyzing: WriteSignal<bool>,
    set_progress: WriteSignal<Option<BasicProgress>>,
    set_selected_path: WriteSignal<String>,
    set_analysis_result: WriteSignal<Option<String>>,
    analysis_context: Option<crate::context::AnalysisContextValue>,
) {
    // This will be handled by JavaScript in the main component
    // The JavaScript will call a global function that we'll expose

    // Create a global callback function that JavaScript can call
    let callback = Closure::wrap(Box::new(move |path: String| {
        Logger::info(&format!("File drop callback called with path: {}", path));

        analyze_codebase(
            path,
            set_is_analyzing,
            set_progress,
            set_selected_path,
            set_analysis_result,
            analysis_context.clone(),
        );
    }) as Box<dyn FnMut(String)>);

    // Expose the callback to the global window object
    match web_sys::window() {
        Some(window) => {
            // SAFETY: Reflect::set is safe to call with valid window object and callback reference
            // The window object exists and the callback reference is valid
            match js_sys::Reflect::set(
                    &window,
                    &"handleFileDrop".into(),
                    callback.as_ref().unchecked_ref(),
                )
            {
                Ok(_) => {
                    Logger::info("Successfully exposed handleFileDrop callback to global window");
                    // Register the callback for proper memory management
                    let callback_id = "file-drop-handler".to_string();
                    match callback_manager::register_callback(callback_id, callback) {
                        Ok(_) => {
                            Logger::debug("File drop callback registered for memory management");
                        }
                        Err(e) => {
                            Logger::warn(&format!("Failed to register file drop callback: {}", e));
                            // Fallback: callback was moved into register_callback, so we can't forget it here
                        }
                    }
                }
                Err(e) => {
                    Logger::error(&format!("Failed to expose handleFileDrop callback: {:?}", e));
                    // Don't register the callback on error to allow proper cleanup
                }
            }
        }
        None => {
            Logger::error("Failed to get window object - file drop functionality will not work");
            // Don't forget the closure since we couldn't set it up
        }
    }
}

pub fn analyze_codebase(
    path: String,
    set_is_analyzing: WriteSignal<bool>,
    set_progress: WriteSignal<Option<BasicProgress>>,
    set_selected_path: WriteSignal<String>,
    set_analysis_result: WriteSignal<Option<String>>,
    analysis_context: Option<crate::context::AnalysisContextValue>,
) {
    set_is_analyzing.set(true);
    set_progress.set(None); // Reset progress
    set_selected_path.set(path.clone());
    
    spawn_local(async move {
        if !is_tauri_available() {
            // Web context - simulate realistic progress for demo
            simulate_web_analysis_progress(
                set_progress,
                set_analysis_result,
                analysis_context,
                path.clone(),
            ).await;
            set_is_analyzing.set(false);
            return;
        }
        
        // Tauri context - use real analysis
        // The Tauri command expects a "request" parameter
        let tauri_args = serde_json::json!({
            "request": {
                "path": path
            }
        });
        
        match serde_wasm_bindgen::to_value(&tauri_args) {
            Ok(args_js) => {
                Logger::debug("Invoking Tauri analyze_codebase_advanced command");
                // SAFETY: invoke() is safe to call with valid arguments
                // The args_js is properly serialized and the command name is valid
                match invoke("analyze_codebase_advanced", args_js).await {
                    result if !result.is_undefined() => {
                        Logger::debug("Received result from Tauri command, processing...");
                        // SAFETY: JSON::stringify is safe to call with valid JsValue
                        match js_sys::JSON::stringify(&result) {
                            Ok(json_string) => {
                                let formatted = json_string.as_string().unwrap_or_else(|| {
                                    Logger::warn("Failed to convert JSON string to Rust string");
                                    "Failed to format result".to_string()
                                });
                                match serde_json::from_str::<serde_json::Value>(&formatted) {
                                    Ok(json_value) => {
                                        match serde_json::to_string_pretty(&json_value) {
                                            Ok(pretty) => {
                                                Logger::info("Analysis completed successfully");
                                                set_analysis_result.set(Some(pretty.clone()));
                                                // Store in global context if available
                                                if let Some(context) = &analysis_context {
                                                    context.set_analysis(pretty, "codebase".to_string());
                                                }
                                            },
                                            Err(e) => {
                                                Logger::warn(&format!("Failed to pretty-print JSON, using raw format: {}", e));
                                                set_analysis_result.set(Some(formatted.clone()));
                                                // Store in global context if available
                                                if let Some(context) = &analysis_context {
                                                    context.set_analysis(formatted, "codebase".to_string());
                                                }
                                            },
                                        }
                                    }
                                    Err(e) => {
                                        Logger::error(&format!("Failed to parse JSON result: {}", e));
                                        set_analysis_result.set(Some(format!("JSON Parse Error: {}\n\nRaw result: {}", e, formatted)));
                                    }
                                }
                            }
                            Err(e) => {
                                Logger::error(&format!("Failed to stringify result: {:?}", e));
                                set_analysis_result.set(Some(format!("Stringify Error: {:?}", e)));
                            }
                        }
                    }
                    _ => {
                        Logger::error("Tauri command returned undefined or null result");
                        set_analysis_result.set(Some("No result returned from analysis command".to_string()));
                    }
                }
            }
            Err(e) => {
                Logger::error(&format!("Failed to serialize arguments for Tauri command: {:?}", e));
                set_analysis_result.set(Some(format!("Serialization error: {:?}", e)));
            }
        }
        set_is_analyzing.set(false);
        set_progress.set(None); // Clear progress when done
    });
}

pub fn select_folder(
    set_is_analyzing: WriteSignal<bool>,
    set_progress: WriteSignal<Option<BasicProgress>>,
    set_selected_path: WriteSignal<String>,
    set_analysis_result: WriteSignal<Option<String>>,
    analysis_context: Option<crate::context::AnalysisContextValue>,
) -> impl Fn(web_sys::MouseEvent) {
    move |_| {
        let set_is_analyzing = set_is_analyzing.clone();
        let set_progress = set_progress.clone();
        let set_selected_path = set_selected_path.clone();
        let set_analysis_result = set_analysis_result.clone();
        let analysis_context = analysis_context.clone();

        spawn_local(async move {
            if !is_tauri_available() {
                // Web context - use demo path
                analyze_codebase(
                    "Demo Project (Web Mode)".to_string(),
                    set_is_analyzing,
                    set_progress,
                    set_selected_path,
                    set_analysis_result,
                    analysis_context.clone(),
                );
                return;
            }

            // Tauri context - use real folder selection
            Logger::debug("Invoking folder selection dialog");
            // SAFETY: invoke() is safe to call with NULL argument for select_folder command
            let result = invoke("select_folder", JsValue::NULL).await;
            match result.as_string() {
                Some(path) if !path.is_empty() => {
                    Logger::info(&format!("User selected folder: {}", path));
                    analyze_codebase(
                        path,
                        set_is_analyzing,
                        set_progress,
                        set_selected_path,
                        set_analysis_result,
                        analysis_context,
                    );
                }
                Some(_) => {
                    Logger::info("User cancelled folder selection or selected empty path");
                }
                None => {
                    Logger::warn("Folder selection returned non-string result");
                }
            }
        });
    }
}

// Removed unused handle_drop function - replaced with enhanced implementation in drop_zone.rs