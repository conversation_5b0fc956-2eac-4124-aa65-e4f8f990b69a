#!/bin/bash

# Test Script for Bash Interface - Bad Character Scanner
# This script thoroughly tests the CLI analyzer and bash script interface
# with verbose output and comprehensive error handling

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
CLI_BINARY="$PROJECT_ROOT/target/debug/analyzer_cli"
BASH_SCRIPT="$PROJECT_ROOT/scripts/codebase_analyzer.sh"
TEST_DIR="$PROJECT_ROOT/test_files"
RESULTS_DIR="$PROJECT_ROOT/test_results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emoji for better UX
CHECKMARK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
ROCKET="🚀"

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Verbose mode
VERBOSE=false
CLEAN_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clean)
            CLEAN_ONLY=true
            shift
            ;;
        -h|--help)
            cat << EOF
Usage: $0 [OPTIONS]

Test the Bash Script Interface for the Bad Character Scanner

OPTIONS:
    -v, --verbose    Enable verbose output
    -c, --clean      Clean up test files and exit
    -h, --help       Show this help message

EOF
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging functions
log_info() {
    if [[ "$VERBOSE" == "true" ]] || [[ "${2:-}" == "--force" ]]; then
        echo -e "${INFO} ${BLUE}[INFO]${NC} $1" >&2
    fi
}

log_success() {
    echo -e "${CHECKMARK} ${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
    echo -e "${WARNING} ${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
    echo -e "${CROSS} ${RED}[ERROR]${NC} $1" >&2
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${GEAR} ${PURPLE}[DEBUG]${NC} $1" >&2
    fi
}

# Test function wrapper
test_function() {
    local test_name="$1"
    local test_function="$2"
    local description="${3:-}"
    
    ((TESTS_TOTAL++))
    log_info "🧪 Running Test: $test_name" --force
    [[ -n "$description" ]] && log_info "   Description: $description"
    
    if $test_function; then
        ((TESTS_PASSED++))
        log_success "✓ $test_name PASSED"
        return 0
    else
        ((TESTS_FAILED++))
        log_error "✗ $test_name FAILED"
        return 1
    fi
}

# Setup test environment
setup_test_environment() {
    log_info "${ROCKET} Setting up test environment..." --force
    
    # Create test directories
    mkdir -p "$TEST_DIR" "$RESULTS_DIR"
    
    # Create test files with various problematic characters
    cat > "$TEST_DIR/clean_file.txt" << 'EOF'
This is a clean file with no bad characters.
Just normal text here.
Nothing suspicious at all!
EOF

    cat > "$TEST_DIR/bad_chars_simple.txt" << 'EOF'
This file has some bad characters:
Zero-width space: Test​word (U+200B)
Non-breaking space: word word (U+00A0)
Replacement char: � (U+FFFD)
EOF

    cat > "$TEST_DIR/bad_chars_complex.txt" << 'EOF'
This file simulates a document with various problematic Unicode characters.
It includes a Zero Width Space here ->​<- (U+200B) which is invisible.
Sometimes, text might contain a No-Break Space like this: word word (U+00A0) instead of a regular space.
Be careful with directional overrides! This text ->‮sdrawkcab si siht‬<- (U+202E) is an example.

Soft hyphens (U+00AD) are tricky: hy­phen­a­tion. They should not always be visible.
A Zero Width Non-Joiner (U+200C) can affect ligatures, like in fi‌ve (f‌i).
If data gets corrupted, you might see a Replacement Character: � (U+FFFD).

This file also contains a few control characters that might be problematic:
A null character: -> <- (U+0000).
And a Form Feed character here ->← (U+000C) which might cause a page break.
Finally, an Ideographic Space ->　<- (U+3000) which is wider than a normal space.
End of mixed severity test.
EOF

    cat > "$TEST_DIR/test_script.js" << 'EOF'
// JavaScript file with bad characters
function testFunction() {
    let badString = "test​invisible"; // Contains zero-width space
    console.log("Processing: " + badString);
    return badString.replace(/\s/g, "_");
}
EOF

    cat > "$TEST_DIR/test_config.json" << 'EOF'
{
    "name": "test​config",
    "settings": {
        "enable_feature": true,
        "timeout": 5000
    }
}
EOF

    log_success "Test environment setup complete!"
}

# Test functions
test_cli_binary_exists() {
    if [[ -f "$CLI_BINARY" ]]; then
        log_success "CLI binary found at: $CLI_BINARY"
        return 0
    else
        log_error "CLI binary not found at: $CLI_BINARY"
        log_info "Attempting to build CLI binary..."
        
        cd "$PROJECT_ROOT/src-tauri"
        if cargo build --bin analyzer_cli; then
            log_success "CLI binary built successfully"
            cd "$PROJECT_ROOT"
            [[ -f "$CLI_BINARY" ]] && return 0
        else
            log_error "Failed to build CLI binary"
            cd "$PROJECT_ROOT"
            return 1
        fi
    fi
}

test_cli_basic_usage() {
    log_info "Testing CLI basic usage and help..."
    
    local output
    if output=$("$CLI_BINARY" 2>&1); then
        log_error "CLI should have failed without arguments"
        return 1
    else
        if echo "$output" | grep -q "Usage:"; then
            log_debug "CLI help output: $output"
            return 0
        else
            log_error "CLI help output unexpected: $output"
            return 1
        fi
    fi
}

test_cli_analyze_clean_file() {
    local test_file="$TEST_DIR/clean_file.txt"
    log_info "Testing CLI analysis of clean file: $test_file"
    
    local output
    if output=$("$CLI_BINARY" analyze "$test_file" json 2>&1); then
        log_debug "CLI output for clean file: $output"
        
        # Check if JSON is valid and contains expected structure
        if echo "$output" | jq -e '.total_files == 1 and .files_with_issues == 0' > /dev/null 2>&1; then
            log_success "Clean file correctly identified as clean"
            return 0
        else
            log_error "Clean file analysis results unexpected"
            return 1
        fi
    else
        log_error "CLI failed for clean file: $output"
        return 1
    fi
}

test_cli_analyze_bad_file() {
    local test_file="$TEST_DIR/bad_chars_complex.txt"
    log_info "Testing CLI analysis of file with bad characters: $test_file"
    
    local output
    if output=$("$CLI_BINARY" analyze "$test_file" json 2>&1); then
        log_debug "CLI output for bad file: $output"
        
        # Check if bad characters were detected
        if echo "$output" | jq -e '.total_files == 1 and .files_with_issues > 0 and .total_suspicious_chars > 0' > /dev/null 2>&1; then
            local suspicious_count=$(echo "$output" | jq -r '.total_suspicious_chars')
            log_success "Bad characters correctly detected. Found $suspicious_count suspicious characters"
            return 0
        else
            log_error "Bad file analysis results unexpected"
            return 1
        fi
    else
        log_error "CLI failed for bad file: $output"
        return 1
    fi
}

test_cli_output_formats() {
    local test_file="$TEST_DIR/bad_chars_simple.txt"
    local formats=("json" "text" "markdown")
    
    for format in "${formats[@]}"; do
        log_info "Testing CLI output format: $format"
        
        local output
        if output=$("$CLI_BINARY" analyze "$test_file" "$format" 2>&1) && [[ -n "$output" ]]; then
            log_success "Format $format produced output"
            log_debug "Sample output ($format): ${output:0:100}..."
        else
            log_error "Format $format failed or produced no output"
            return 1
        fi
    done
    
    return 0
}

test_cli_export_functionality() {
    log_info "Testing CLI export functionality..."
    
    local test_file="$TEST_DIR/bad_chars_simple.txt"
    local json_output_file="$RESULTS_DIR/test_results.json"
    
    # Generate JSON analysis
    local json_output
    if json_output=$("$CLI_BINARY" analyze "$test_file" json 2>&1); then
        echo "$json_output" > "$json_output_file"
        
        # Test exporting to different formats
        local export_formats=("text" "markdown")
        for format in "${export_formats[@]}"; do
            log_info "Testing export to $format format"
            
            local export_output
            if export_output=$("$CLI_BINARY" export "$json_output_file" "$format" 2>&1) && [[ -n "$export_output" ]]; then
                log_success "Export to $format successful"
                
                # Save exported results
                local export_file="$RESULTS_DIR/exported_results.$format"
                echo "$export_output" > "$export_file"
                log_debug "Exported results saved to: $export_file"
            else
                log_error "Export to $format failed"
                return 1
            fi
        done
        
        return 0
    else
        log_error "Failed to generate JSON for export test"
        return 1
    fi
}

test_cli_error_handling() {
    log_info "Testing CLI error handling..."
    
    # Test non-existent file
    local non_existent_file="$TEST_DIR/does_not_exist.txt"
    local output
    if output=$("$CLI_BINARY" analyze "$non_existent_file" json 2>&1); then
        log_error "CLI should have failed for non-existent file"
        return 1
    else
        log_success "CLI correctly handled non-existent file"
    fi
    
    # Test invalid format
    local test_file="$TEST_DIR/clean_file.txt"
    if output=$("$CLI_BINARY" analyze "$test_file" invalid_format 2>&1); then
        log_error "CLI should have failed for invalid format"
        return 1
    else
        log_success "CLI correctly handled invalid format"
    fi
    
    # Test invalid command
    if output=$("$CLI_BINARY" invalid_command "$test_file" 2>&1); then
        log_error "CLI should have failed for invalid command"
        return 1
    else
        log_success "CLI correctly handled invalid command"
    fi
    
    return 0
}

test_bash_script_exists() {
    if [[ -f "$BASH_SCRIPT" ]]; then
        log_success "Bash script found at: $BASH_SCRIPT"
        return 0
    else
        log_error "Bash script not found at: $BASH_SCRIPT"
        return 1
    fi
}

test_bash_script_integration() {
    log_info "Testing Bash script integration..."
    
    if [[ -x "$BASH_SCRIPT" ]] || chmod +x "$BASH_SCRIPT" 2>/dev/null; then
        local output
        if output=$("$BASH_SCRIPT" test 2>&1); then
            log_success "Bash script test command executed successfully"
            return 0
        else
            log_warning "Bash script test failed, but this might be expected"
            log_debug "Bash script output: $output"
            return 0  # Don't fail the test suite for bash script issues
        fi
    else
        log_warning "Could not make bash script executable or run it"
        return 0  # Don't fail if bash script can't be executed
    fi
}

test_performance_and_stress() {
    log_info "Running performance and stress tests..."
    
    # Create a larger test file
    local large_test_file="$TEST_DIR/large_test_file.txt"
    for i in {1..100}; do
        echo "Line $i with some​hidden​characters and normal text."
    done > "$large_test_file"
    
    log_info "Testing CLI performance on larger file..."
    local start_time=$(date +%s%3N)
    local output
    if output=$("$CLI_BINARY" analyze "$large_test_file" json 2>&1); then
        local end_time=$(date +%s%3N)
        local duration=$((end_time - start_time))
        
        log_success "Large file analysis completed in ${duration}ms"
        
        # Parse results
        if command -v jq >/dev/null 2>&1; then
            local total_files=$(echo "$output" | jq -r '.total_files')
            local suspicious_chars=$(echo "$output" | jq -r '.total_suspicious_chars')
            local health_score=$(echo "$output" | jq -r '.health_score')
            log_info "Performance stats - Files: $total_files, Suspicious chars: $suspicious_chars, Health score: $health_score"
        fi
        
        return 0
    else
        log_error "Large file analysis failed"
        return 1
    fi
}

cleanup_test_environment() {
    if [[ "$CLEAN_ONLY" == "true" ]]; then
        log_info "Cleaning up test environment..." --force
        
        rm -rf "$TEST_DIR" "$RESULTS_DIR"
        
        log_success "Cleanup complete!" --force
        return
    fi
}

show_test_summary() {
    log_info "${ROCKET} TEST SUMMARY" --force
    log_info "=================" --force
    log_success "Tests Passed: $TESTS_PASSED" --force
    log_error "Tests Failed: $TESTS_FAILED" --force
    log_info "Total Tests: $TESTS_TOTAL" --force
    
    local success_rate=0
    if [[ $TESTS_TOTAL -gt 0 ]]; then
        success_rate=$(( TESTS_PASSED * 100 / TESTS_TOTAL ))
    fi
    log_info "Success Rate: ${success_rate}%" --force
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "${CHECKMARK} All tests passed! Bash Script Interface is working correctly." --force
    else
        log_warning "${WARNING} Some tests failed. Review the output above for details." --force
    fi
}

# Main execution
echo -e "${ROCKET} ${CYAN}Bad Character Scanner - Bash Interface Test Suite${NC}"
echo "================================================================="

if [[ "$CLEAN_ONLY" == "true" ]]; then
    cleanup_test_environment
    exit 0
fi

log_info "Starting comprehensive test suite..." --force
log_info "Verbose mode: $VERBOSE" --force

# Setup
setup_test_environment

# Run all tests
test_function "CLI Binary Exists" test_cli_binary_exists "Check if the CLI binary exists or can be built"
test_function "CLI Basic Usage" test_cli_basic_usage "Test CLI help and basic command structure"
test_function "CLI Analyze Clean File" test_cli_analyze_clean_file "Test analysis of a file without bad characters"
test_function "CLI Analyze Bad File" test_cli_analyze_bad_file "Test analysis of a file with problematic characters"
test_function "CLI Output Formats" test_cli_output_formats "Test all supported output formats (JSON, text, markdown)"
test_function "CLI Export Functionality" test_cli_export_functionality "Test the export command for different formats"
test_function "CLI Error Handling" test_cli_error_handling "Test error handling for invalid inputs"
test_function "Bash Script Exists" test_bash_script_exists "Check if the bash script wrapper exists"
test_function "Bash Script Integration" test_bash_script_integration "Test bash script integration"
test_function "Performance and Stress" test_performance_and_stress "Test performance with larger files"

# Show summary
show_test_summary

# Exit with appropriate code
exit $TESTS_FAILED
