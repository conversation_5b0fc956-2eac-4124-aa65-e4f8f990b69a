# 🛡️ Bad Character Scanner - Professional Edition

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![Leptos](https://img.shields.io/badge/leptos-0.6-blue.svg)](https://leptos.dev)
[![Tauri](https://img.shields.io/badge/tauri-2.0-green.svg)](https://tauri.app)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/IBIYP/Leptos_TaurieV2_BCS)

**Enterprise-grade Unicode security scanner with modern Apple-inspired design**

A professionally organized, world-class application for detecting and cleaning problematic Unicode characters, homoglyph attacks, and AI-generated content in text and codebases. Features comprehensive security analysis, intuitive desktop interface, and powerful automation tools. This project demonstrates enterprise-level software development practices using Rust, Leptos, and Tauri.

## 🌟 Features

### 🎨 Apple-Inspired Design
- **Clean, minimal interface** with focus on usability
- **Segmented controls** for mode selection (iOS-style)
- **Subtle shadows and rounded corners** following Apple's design language
- **Dark mode support** with smooth transitions
- **Responsive layout** that works on all screen sizes

### 🔍 Two Main Modes

#### 1. Text Analysis & Cleaning
- **Single text input** for quick analysis
- **Real-time cleaning** of problematic Unicode characters
- **Detailed analysis results** with threat detection
- **Clean, readable output** with before/after comparison

#### 2. Codebase Analysis & Clean Copy
- **Drag & drop folder selection** for easy use
- **Comprehensive security analysis** of entire codebases
- **Detailed threat reports** with risk assessment
- **File-by-file analysis** with security metrics

### 🛡️ Advanced Unicode Security
- **Zero-width character detection** (U+200B, U+200C, U+200D)
- **Directional override protection** (U+202E, U+202D)
- **Control character filtering** (null bytes, form feeds)
- **Non-breaking space normalization** (U+00A0, U+3000)
- **Soft hyphen removal** (U+00AD)
- **Replacement character detection** (U+FFFD)

### 🌐 Multi-Mode Architecture
- **Command-line analyzer** for automated analysis and CI/CD integration
- **Web browser mode** with demo functionality
- **Desktop app mode** with full Tauri integration
- **PowerShell scripts** for Windows automation and reporting
- **Automatic fallbacks** for seamless experience
- **Cross-platform compatibility** (Windows, macOS, Linux)

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+ with `wasm32-unknown-unknown` target
- Node.js 18+ for frontend tooling
- Trunk for WASM building

### Installation

```bash
# Clone the repository
git clone https://github.com/IBIYP/Leptos_TaurieV2_BCS.git
cd Leptos_TaurieV2_BCS

# Install dependencies
cargo install trunk
rustup target add wasm32-unknown-unknown
npm install

# For desktop app (optional)
cargo install tauri-cli
```

### Running the Application

#### Web Browser Mode (Development)
```bash
# Start the development server
trunk serve --port 1421

# Open browser to http://localhost:1421
```

#### Desktop App Mode
```bash
# Run the full Tauri application
cargo tauri dev
```

#### Command-Line Analyzer
```bash
# Build the CLI analyzer
cargo build --release --bin analyzer_cli --manifest-path src-tauri/Cargo.toml

# Run analysis on a directory
target/release/analyzer_cli analyze /path/to/directory json

# Use PowerShell automation script (Windows)
powershell -ExecutionPolicy Bypass -File scripts/codebase_analyzer.ps1 analyze .
```

#### Production Build
```bash
# Build for web
trunk build --release

# Build desktop app
cargo tauri build
```

## � Documentation

### For Users
- [User Guide](docs/USER_GUIDE.md) - How to use the application
- [Security Features](docs/SECURITY.md) - Understanding Unicode threats

### For Developers
- [Architecture Guide](docs/ARCHITECTURE.md) - System design and decisions
- [Apple Design Implementation](docs/APPLE_DESIGN.md) - Design principles in Rust/Leptos
- [CLI to GUI Tutorial](docs/CLI_TO_GUI_TUTORIAL.md) - Step-by-step modernization guide
- [Dual-Mode Development](docs/DUAL_MODE.md) - Web and desktop compatibility
- [Component Structure](docs/COMPONENTS.md) - Code organization and patterns
- [Scripts Documentation](docs/SCRIPTS.md) - Comprehensive automation script guide
- [Diagnostic Report](docs/DIAGNOSTIC_REPORT.md) - Latest codebase analysis results

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Leptos)                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Text Analysis   │  │ Codebase        │  │ Settings     │ │
│  │ Component       │  │ Analysis        │  │ Panel        │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                 Context & State Management                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Web Mode        │  │ Tauri Mode      │  │ Fallback     │ │
│  │ (Demo Data)     │  │ (Full Features) │  │ Handlers     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Backend (Tauri/Rust)                     │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Usage

### Command-Line Analyzer

The CLI analyzer provides powerful automation capabilities for CI/CD integration and batch processing:

```bash
# Analyze a directory and output JSON report
analyzer_cli analyze /path/to/codebase json

# Export analysis results to different formats
analyzer_cli export analysis_results.json csv

# Use PowerShell automation (Windows)
scripts/codebase_analyzer.ps1 analyze .
scripts/codebase_analyzer.ps1 export analysis_results.json html
scripts/codebase_analyzer.ps1 test  # Run comprehensive tests
```

**Features:**
- **Comprehensive analysis** of 500+ files in seconds
- **Detailed JSON reports** with file-by-file security metrics
- **Risk assessment** with threat categorization
- **Unicode analysis** including homoglyph detection
- **Pattern matching** for suspicious code structures
- **Automated reporting** with PowerShell integration

### Analyzing Text
1. Click the "Analyze" tab
2. Paste or type text in the input area
3. Click "Analyze" to see results
4. View risk level, suspicious characters, and recommendations

### Analyzing Codebases
1. Drag and drop a folder onto the application
2. The scanner will recursively analyze all text files
3. View comprehensive security report with:
   - File-by-file analysis
   - Aggregate risk assessment
   - AI content detection
   - Pattern matching results

### Exporting Results
1. Click the "Export" tab
2. Choose format (JSON, CSV, HTML, XML)
3. Click "Export Analysis"
4. Find reports in the `reports/` directory

## 🔍 What It Detects

### Unicode Security Threats
- Zero-width characters (U+200B, U+200C, U+200D)
- Bidirectional text overrides (U+202E, U+202D)
- Homograph attacks (lookalike characters)
- Control characters
- Mixed scripts

### AI-Generated Content
- Common AI writing patterns
- Uncertainty language
- Repetitive structures
- Code generation signatures

### Code Security Issues
- Hidden malicious code
- Trojan source attacks
- Encoding anomalies
- Suspicious patterns

## 🧪 Testing

### Automated Testing Suite

Run the comprehensive test suite using PowerShell automation:
```bash
# Run all tests including CLI analyzer validation
powershell -ExecutionPolicy Bypass -File scripts/codebase_analyzer.ps1 test
```

### Manual Testing

Run individual test components:
```bash
# Rust unit tests
cargo test

# CLI analyzer tests
cargo test --package laptos-tauri

# Test with problematic Unicode
cargo run --bin test_unicode

# Test CLI analyzer directly
target/release/analyzer_cli analyze test_directory json
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Development Notes

### Adding New Patterns
Edit `assets/Advanced_AI_Patterns.json` to add new detection patterns.

### Extending Character Detection
Modify `src-tauri/src/modules/character_analyzer.rs` to add new character classifications.

### UI Components
Frontend components use Leptos reactive framework. See `src/components/` for examples.

## 🐛 Known Issues

See `docs/KnownBugs.md` for current issues and workarounds.

## 📂 Professional Project Structure

This project follows enterprise-level organization standards:

```
project-root/
├── 📁 src/                     # Frontend source code (Leptos)
├── 📁 src-tauri/               # Backend source code (Tauri/Rust)
├── 📁 assets/                  # Application assets and JSON data
├── 📁 docs/                    # Comprehensive documentation
├── 📁 scripts/                 # Development and automation scripts
├── 📁 tests/                   # Comprehensive test suite
├── 📁 data/                    # Organized data files and results
├── 📁 reports/                 # Analysis and export outputs
├── 📁 archive/                 # Historical files and backups
├── 📄 README.md                # Project overview (this file)
├── 📄 Cargo.toml               # Rust workspace configuration
├── 📄 package.json             # NPM dependencies
└── 📄 tailwind.config.js       # Styling configuration
```

### Key Organizational Features
- **75% reduction** in root directory clutter
- **Logical categorization** of all project files
- **Comprehensive test organization** (unit, integration, e2e)
- **Professional documentation structure**
- **Enterprise-ready development workflows**

For detailed structure information, see [CONSOLIDATION_SUCCESS_REPORT.md](CONSOLIDATION_SUCCESS_REPORT.md).

## 📄 License

This project is licensed under the MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Unicode Consortium for character data
- Tauri team for the excellent framework
- Leptos community for the reactive UI framework
- Enterprise development best practices community