// Test export functionality
use std::path::Path;

fn main() {
    // Simulate the export path logic
    let current_dir = std::env::current_dir().unwrap();
    println!("Current directory: {}", current_dir.display());
    
    // Try to find the project root (where Cargo.toml is located)
    let mut project_root = current_dir.clone();
    while !project_root.join("Cargo.toml").exists() && project_root.parent().is_some() {
        project_root = project_root.parent().unwrap().to_path_buf();
    }
    
    // If we didn't find Cargo.toml, use current directory
    if !project_root.join("Cargo.toml").exists() {
        project_root = current_dir;
    }
    
    let reports_dir = project_root.join("reports");
    println!("Reports directory path: {}", reports_dir.display());
    println!("Reports directory exists: {}", reports_dir.exists());
    
    // Create a test export file
    let test_file = reports_dir.join("test_export.json");
    let test_content = r#"{"test": "export working", "timestamp": "now"}"#;
    
    match std::fs::write(&test_file, test_content) {
        Ok(_) => println!("✅ Test export file created: {}", test_file.display()),
        Err(e) => println!("❌ Failed to create test export: {}", e),
    }
}
