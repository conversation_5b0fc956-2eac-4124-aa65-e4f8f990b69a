This is a test file containing various suspicious Unicode characters.

Zero-width space example: Hello​<PERSON><PERSON><PERSON> (invisible character between Hello and World)

Homograph attack: раураӏ.соm (uses Cyrillic characters)

RTL override: user@evil‮txe.com (dangerous email format)

Mixed scripts: аpple.com (Cyrillic 'а' mixed with Latin)

Control characters: file.txt (contains control characters)

Combining characters: e̵̴̵̶̷̸̡̢̨̧̡̢̧̨̛̛̖̗̘̙̜̝̞̟̠̣̤̥̦̩̪̫̬̭̮̯̰̱̲̳̖̗̘̙̜̝̞̟̠̣̤̥̦̩̪̫̬̭̮̯̰̱̲̳̹̺̻̼̽̾̿̀́̂̃̄̅̆̇̈̉̊̋̌̍̎̏̐̑̒̓̔̕̚

Invisible separators: word1 word2 (various invisible characters)

Steganography potential: ​‌‍⁠ (zero-width characters for data hiding)

This file should trigger multiple security alerts and pattern matches.
