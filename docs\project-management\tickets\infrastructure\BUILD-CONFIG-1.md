# BUILD-CONFIG-1 - Tauri v2 Configuration Modernization

**Status:** 🟢 Open  
**Priority:** Medium  
**Created:** 2025-06-20  
**Updated:** 2025-06-20  
**Assigned To:** Development Team  
**Estimated Effort:** 2-3 hours  
**Story Points:** 3

## Description

Resolve the dual Tauri configuration files issue and modernize to a single, properly configured `tauri.config.json` file. The project currently has both `tauri.conf.json` and `tauri.config.json` which can cause configuration conflicts and build issues.

## Current Issues

### Configuration File Conflicts
- **Two config files**: `tauri.conf.json` (legacy) and `tauri.config.json` (modern)
- **Inconsistent settings** between the two files
- **Build system confusion** about which config to use
- **Tauri v2 compatibility** issues with legacy configuration

### Missing Modern Features
- **Capabilities-based security model** not fully implemented
- **Latest plugin versions** not configured
- **V1 API patterns** still present in configuration
- **Error handling** not updated to v2 patterns

## Acceptance Criteria

- [ ] Single `tauri.config.json` file with modern Tauri v2 configuration
- [ ] Remove legacy `tauri.conf.json` file
- [ ] Capabilities-based security model implemented
- [ ] Latest plugin versions configured (shell, dialog, fs)
- [ ] All v1 API patterns removed from configuration
- [ ] Build system works correctly with new configuration
- [ ] All existing functionality preserved

## Technical Details

### Current Configuration Analysis Needed
1. **Compare both config files** to identify differences
2. **Identify critical settings** that must be preserved
3. **Map v1 patterns to v2 equivalents**
4. **Verify plugin compatibility** with latest versions

### Modern Tauri v2 Features to Implement
- **Capabilities system** for fine-grained permissions
- **Updated plugin configuration** for shell, dialog, fs
- **Modern security settings** and CSP configuration
- **Optimized bundle configuration**

### Files to Modify
- `src-tauri/tauri.config.json` (update and modernize)
- `src-tauri/tauri.conf.json` (remove after migration)
- `src-tauri/Cargo.toml` (verify plugin versions)
- Build scripts (if any reference old config)

## Implementation Plan

### Phase 1: Configuration Audit (45 minutes)
1. **Compare configuration files**
   - Document differences between tauri.conf.json and tauri.config.json
   - Identify critical settings that must be preserved
   - Note any custom configurations

2. **Analyze current functionality**
   - Test current build process
   - Document working features
   - Identify any configuration-dependent behaviors

### Phase 2: Modern Configuration Creation (60 minutes)
1. **Create unified tauri.config.json**
   - Merge critical settings from both files
   - Apply Tauri v2 best practices
   - Implement capabilities-based security

2. **Update plugin configurations**
   - Configure latest versions of shell, dialog, fs plugins
   - Remove deprecated plugin patterns
   - Add any missing plugin configurations

### Phase 3: Security and Capabilities (30 minutes)
1. **Implement capabilities system**
   - Define required capabilities for the application
   - Configure fine-grained permissions
   - Update CSP settings for modern security

2. **Remove v1 patterns**
   - Eliminate any remaining v1 API configurations
   - Update error handling patterns
   - Modernize bundle configuration

### Phase 4: Testing and Cleanup (15 minutes)
1. **Remove legacy configuration**
   - Delete tauri.conf.json file
   - Update any references in documentation
   - Clean up build scripts if needed

2. **Verify functionality**
   - Test build process
   - Verify all features work correctly
   - Check for any configuration-related errors

## Testing Strategy

### Build Testing
- `cargo tauri build` succeeds without errors
- Development mode `cargo tauri dev` works correctly
- All Tauri commands function properly

### Feature Testing
- File system operations work (dialog, fs plugins)
- Shell commands execute correctly
- All existing application features preserved

### Security Testing
- Capabilities system properly restricts permissions
- CSP settings prevent security issues
- No unauthorized access to system resources

## Dependencies

### Prerequisites
- Understanding of current application functionality
- Knowledge of Tauri v2 configuration format
- Access to Tauri v2 documentation

### Related Tickets
- May relate to BUILD-1 (production build optimization)
- Could affect UPGRADE-1 (framework upgrades)

## Risk Assessment

### Low Risk
- Configuration changes are reversible
- Can maintain backup of working configuration
- Changes are isolated to configuration files

### Mitigation Strategies
- **Backup current working configuration** before changes
- **Test incrementally** rather than making all changes at once
- **Document all changes** for easy rollback if needed

## Success Metrics

- **Single source of truth**: One configuration file instead of two
- **Modern compliance**: Full Tauri v2 compatibility
- **Security improvement**: Capabilities-based permissions implemented
- **Build reliability**: Consistent build behavior across environments

## Notes

- This modernization will improve long-term maintainability
- May unlock access to newer Tauri v2 features
- Should be done before major feature additions
- Will simplify the build system and reduce confusion

---
*Last updated: 2025-06-20*
