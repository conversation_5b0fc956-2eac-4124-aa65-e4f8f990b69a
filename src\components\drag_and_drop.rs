use leptos::*;
use web_sys::DragEvent;

// By <PERSON> - 2025
// Standardized drag-and-drop interface with consistent button sizing and improved file handling

#[component]
pub fn DragAndDropLandingBox() -> impl IntoView {
    let (dropped_files, set_dropped_files) = create_signal(Vec::<FileInfo>::new());
    let (is_dragging, set_is_dragging) = create_signal(false);
    let (drag_count, set_drag_count) = create_signal(0);
    let (is_processing, set_is_processing) = create_signal(false);

    #[derive(<PERSON><PERSON>, Debug)]
    struct FileInfo {
        name: String,
        size: u64,
        file_type: String,
        content: Option<String>, // Store file content for analysis
    }

    let handle_drag_enter = move |ev: DragEvent| {
        ev.prevent_default();
        set_drag_count.update(|count| *count += 1);
        set_is_dragging.set(true);
    };

    let handle_drag_leave = move |_ev: DragEvent| {
        set_drag_count.update(|count| *count -= 1);
        if drag_count.get() == 0 {
            set_is_dragging.set(false);
        }
    };

    let handle_drop = move |ev: DragEvent| {
        ev.prevent_default();
        set_is_dragging.set(false);
        set_drag_count.set(0);
        set_is_processing.set(true);

        let mut files = Vec::new();
        if let Some(data_transfer) = ev.data_transfer() {
            if let Some(file_list) = data_transfer.files() {
                for i in 0..file_list.length() {
                    if let Some(file) = file_list.item(i) {
                        files.push(FileInfo {
                            name: file.name(),
                            size: file.size() as u64,
                            file_type: file.type_(),
                            content: None, // Will be loaded when needed
                        });
                    }
                }
            }
        }
        set_dropped_files.set(files);
        set_is_processing.set(false);
    };

    let clear_files = move |_| {
        set_dropped_files.set(Vec::new());
    };

    // Standardized button functions with consistent emoji sizing (50x50px = text-5xl)
    let analyze_files = move |_| {
        // TODO: Implement file analysis functionality
        logging::log!("Analyzing files...");
    };

    let export_files = move |_| {
        // TODO: Implement export functionality  
        logging::log!("Exporting files...");
    };

    let clean_files = move |_| {
        // TODO: Implement cleaning functionality
        logging::log!("Cleaning files...");
    };

    let analyze_codebase = move |_| {
        // TODO: Implement codebase analysis
        logging::log!("Analyzing codebase...");
    };

    view! {
        <div class="w-full max-w-4xl mx-auto p-6 space-y-6">
            // Enhanced drop zone with better visual feedback
            <div
                class="relative w-full min-h-80 border-2 border-dashed rounded-2xl transition-all duration-300 ease-in-out flex flex-col items-center justify-center cursor-pointer group overflow-hidden"
                class:border-blue-500=move || is_dragging.get()
                class:bg-blue-50=move || is_dragging.get()
                class:scale-102=move || is_dragging.get()
                class:border-gray-300=move || !is_dragging.get()
                class:hover:border-blue-400=move || !is_dragging.get()
                class:hover:bg-gray-50=move || !is_dragging.get()
                class:hover:scale-101=move || !is_dragging.get()
                on:dragenter=handle_drag_enter
                on:dragover=|ev| ev.prevent_default()
                on:dragleave=handle_drag_leave
                on:drop=handle_drop
            >
                // Icon and text content
                <div class="text-center z-10 p-8">
                    <svg
                        class="mx-auto h-20 w-20 text-gray-400 group-hover:text-blue-500 transition-colors mb-6"
                        class:text-blue-500=move || is_dragging.get()
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                    </svg>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3">
                        {move || {
                            if is_dragging.get() {
                                "Drop your files here"
                            } else {
                                "Drag and drop files or folders"
                            }
                        }}
                    </h3>
                    <p class="text-lg text-gray-600 mb-6">"Upload files for security analysis"</p>
                    <div class="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
                        <span class="px-3 py-1 bg-gray-100 rounded-full">.txt</span>
                        <span class="px-3 py-1 bg-gray-100 rounded-full">.js</span>
                        <span class="px-3 py-1 bg-gray-100 rounded-full">.py</span>
                        <span class="px-3 py-1 bg-gray-100 rounded-full">.rs</span>
                        <span class="px-3 py-1 bg-gray-100 rounded-full">.html</span>
                        <span class="px-3 py-1 bg-gray-100 rounded-full">+ more</span>
                    </div>
                </div>

                // Animated overlay when dragging
                <Show when=move || is_dragging.get()>
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded-2xl flex items-center justify-center">
                        <div class="text-blue-700 font-bold text-xl animate-pulse">
                            "Release to analyze files"
                        </div>
                    </div>
                </Show>

                // Processing indicator
                <Show when=move || is_processing.get()>
                    <div class="absolute inset-0 bg-white/95 rounded-2xl flex items-center justify-center">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 border-3 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                            <span class="text-blue-600 font-semibold text-lg">"Processing files..."</span>
                        </div>
                    </div>
                </Show>
            </div>

            // Standardized Action Buttons with consistent 50x50px emoji sizing
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button
                    on:click=analyze_files
                    class="action-button bg-white border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 group p-6"
                >
                    <div class="emoji-size group-hover:scale-110 transition-transform">"📊"</div>
                    <span class="font-medium text-gray-700 group-hover:text-blue-700">"Analyze"</span>
                </button>
                
                <button
                    on:click=export_files
                    class="action-button bg-white border-2 border-gray-200 rounded-xl hover:border-green-500 hover:bg-green-50 transition-all duration-200 group p-6"
                >
                    <div class="emoji-size group-hover:scale-110 transition-transform">"📤"</div>
                    <span class="font-medium text-gray-700 group-hover:text-green-700">"Export"</span>
                </button>
                
                <button
                    on:click=clean_files
                    class="action-button bg-white border-2 border-gray-200 rounded-xl hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 group p-6"
                >
                    <div class="emoji-size group-hover:scale-110 transition-transform">"🧹"</div>
                    <span class="font-medium text-gray-700 group-hover:text-purple-700">"Clean"</span>
                </button>
                
                <button
                    on:click=analyze_codebase
                    class="action-button bg-white border-2 border-gray-200 rounded-xl hover:border-orange-500 hover:bg-orange-50 transition-all duration-200 group p-6"
                >
                    <div class="emoji-size group-hover:scale-110 transition-transform">"💻"</div>
                    <span class="font-medium text-gray-700 group-hover:text-orange-700">"Codebase Analysis"</span>
                </button>
            </div>

            // File preview section with enhanced styling
            <Show when=move || !dropped_files.get().is_empty()>
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                        <h4 class="text-lg font-semibold text-gray-900">
                            {move || {
                                format!("Files ready for analysis ({})", dropped_files.get().len())
                            }}
                        </h4>
                        <button
                            on:click=clear_files
                            class="text-sm text-gray-500 hover:text-red-500 transition-colors px-3 py-1 rounded hover:bg-red-50"
                        >
                            "Clear all"
                        </button>
                    </div>
                    <div class="max-h-80 overflow-y-auto">
                        <For
                            each=move || dropped_files.get()
                            key=|file| file.name.clone()
                            children=move |file| {
                                view! {
                                    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50">
                                        <div class="flex items-center space-x-4">
                                            <svg
                                                class="h-8 w-8 text-green-500 flex-shrink-0"
                                                fill="currentColor"
                                                viewBox="0 0 20 20"
                                            >
                                                <path
                                                    fill-rule="evenodd"
                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                    clip-rule="evenodd"
                                                />
                                            </svg>
                                            <div class="min-w-0 flex-1">
                                                <p class="text-base font-medium text-gray-900 truncate">{file.name}</p>
                                                <p class="text-sm text-gray-500">
                                                    {format!("{} KB • {}", (file.size / 1024).max(1), 
                                                        if file.file_type.is_empty() { "Unknown" } else { &file.file_type }
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                "Ready"
                                            </span>
                                        </div>
                                    </div>
                                }
                            }
                        />
                    </div>
                </div>
            </Show>

            // Text Analysis Section
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">"Text to Analyze"</h4>
                <textarea
                    class="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter text to analyze for suspicious characters..."
                ></textarea>
                <button class="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    "Analyze Text"
                </button>
            </div>
        </div>
    }
}
