# in this docuemtiong we talk about the limtis of the application

## spaciflcy for Bad Character Scanner Basic

## <PERSON> Chaercter types we scan for (Next to each put a probablabilyt fo being able tos can it and how we scan each type. )

### Filename-Specific Issues

#### Forbidden Windows Characters

These characters are disallowed by the Windows operating system for use in filenames and directory names. Our scanner will flag these with **High** probability.

| Character | Hex Code | Description       | Notes                                                            |
| :-------: | :------: | ----------------- | ---------------------------------------------------------------- | -------------------------------------------------- |
|    `<`    | `U+003C` | Less-than sign    | Reserved for I/O redirection in the command prompt.              |
|    `>`    | `U+003E` | Greater-than sign | Reserved for I/O redirection in the command prompt.              |
|    `:`    | `U+003A` | Colon             | Used to separate the drive letter from a file path (e.g., `C:`). |
|    `"`    | `U+0022` | Double quote      | Used to enclose filenames that contain spaces.                   |
|    `/`    | `U+002F` | Forward slash     | Used as a path separator in URLs and on other OSs.               |
|    `\`    | `U+005C` | Backslash         | The primary path separator on Windows.                           |
|    `|`    | `U+007C` | Vertical bar or Pipe | Used to pipe the output of one command to another.               |
|    `?`    | `U+003F` | Question mark     | Used as a wildcard character for a single character.             |
|    `*`    | `U+002A` | Asterisk          | Used as a wildcard character for multiple characters.            |

#### General Control Characters (C0)

These non-printable characters (U+0000 to U+001F and U+007F) are legacy control codes that are forbidden in filenames across most operating systems. They provide no visual representation and can cause significant issues.

| Name                       | Hex Code | Description                                        |
| :------------------------- | :------: | -------------------------------------------------- |
| Null (NUL)                 | `U+0000` | Often terminates strings; will truncate filenames. |
| Start of Heading (SOH)     | `U+0001` | Legacy control code.                               |
| Start of Text (STX)        | `U+0002` | Legacy control code.                               |
| End of Text (ETX)          | `U+0003` | Legacy control code.                               |
| End of Transmission (EOT)  | `U+0004` | Legacy control code.                               |
| Enquiry (ENQ)              | `U+0005` | Legacy control code.                               |
| Acknowledge (ACK)          | `U+0006` | Legacy control code.                               |
| Bell (BEL)                 | `U+0007` | Can cause an audible system beep.                  |
| Backspace (BS)             | `U+0008` | Can cause characters to be overwritten.            |
| Horizontal Tab (HT)        | `U+0009` | Not typically allowed or rendered in filenames.    |
| Line Feed (LF)             | `U+000A` | Interpreted as a newline, breaking the filename.   |
| Vertical Tab (VT)          | `U+000B` | Legacy control code.                               |
| Form Feed (FF)             | `U+000C` | Legacy control code.                               |
| Carriage Return (CR)       | `U+000D` | Interpreted as a newline, breaking the filename.   |
| Shift Out (SO)             | `U+000E` | Legacy control code for character set switching.   |
| Shift In (SI)              | `U+000F` | Legacy control code for character set switching.   |
| Data Link Escape (DLE)     | `U+0010` | Legacy control code.                               |
| Device Control 1 (DC1)     | `U+0011` | Legacy control code.                               |
| Device Control 2 (DC2)     | `U+0012` | Legacy control code.                               |
| Device Control 3 (DC3)     | `U+0013` | Legacy control code.                               |
| Device Control 4 (DC4)     | `U+0014` | Legacy control code.                               |
| Negative Acknowledge (NAK) | `U+0015` | Legacy control code.                               |
| Synchronous Idle (SYN)     | `U+0016` | Legacy control code.                               |
| End of Transmission Block  | `U+0017` | Legacy control code.                               |
| Cancel (CAN)               | `U+0018` | Legacy control code.                               |
| End of Medium (EM)         | `U+0019` | Legacy control code.                               |
| Substitute (SUB)           | `U+001A` | Legacy control code.                               |
| Escape (ESC)               | `U+001B` | Used to start escape sequences (e.g., for color).  |
| File Separator (FS)        | `U+001C` | Legacy control code.                               |
| Group Separator (GS)       | `U+001D` | Legacy control code.                               |
| Record Separator (RS)      | `U+001E` | Legacy control code.                               |
| Unit Separator (US)        | `U+001F` | Legacy control code.                               |
| Delete (DEL)               | `U+007F` | Legacy control code.                               |

#### C1 Control Characters

These characters (U+0080 to U+009F) are also forbidden in filenames. They are rarely used but can cause similar issues to C0 control characters.

| Name                  | Range           | Description                                 |
| :-------------------- | :-------------- | :------------------------------------------ |
| C1 Control Characters | `U+0080-U+009F` | Forbidden in filenames across most systems. |

#### Other Problematic Filename Patterns

These are not single characters but patterns that can cause issues, especially on Windows.

| Pattern                              | Description   | Notes                                                   |
| :----------------------------------- | :------------ | :------------------------------------------------------ |
| Filename with leading/trailing space | `example.txt` | Can cause access issues and is trimmed by some systems. |
| Filename ending with a period        | `example.`    | Not allowed on Windows filesystems.                     |

### Content-Specific Issues

#### High Problems

Characters that are visually subtle (e.g., may look like standard spaces but have different properties) or have significant disruptive effects (e.g., altering text direction, acting as unexpected newlines). They frequently cause errors or logical issues in code.

| Name                             | Hex Code | Description                                                                                                                                                                                                                                                                                                                       |
| :------------------------------- | :------: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| No-Break Space                   | `U+00A0` | Visually similar to a regular space, but prevents line breaks. If used instead of a regular space (U+0020) within code syntax (e.g., separating keywords, in identifiers if the language allows spaces, or in string literals where a specific space type is expected), it can cause parsing errors or failed string comparisons. |
| En Quad                          | `U+2000` | A space character, typically wider than a standard space. Problematic if used instead of U+0020 in code syntax, causing parsing errors or unexpected string behavior.                                                                                                                                                             |
| Em Quad                          | `U+2001` | A space character, typically the width of the point size (wider than En Quad). Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                              |
| En Space                         | `U+2002` | A space character, typically half an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                    |
| Em Space                         | `U+2003` | A space character, typically equal to the point size. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                       |
| Three-Per-Em Space               | `U+2004` | A space character, one-third of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                      |
| Four-Per-Em Space                | `U+2005` | A space character, one-fourth of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                     |
| Six-Per-Em Space                 | `U+2006` | A space character, one-sixth of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                      |
| Figure Space                     | `U+2007` | A space character with the width of a digit. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                |
| Punctuation Space                | `U+2008` | A space character with the width of a narrow punctuation mark. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                              |
| Thin Space                       | `U+2009` | A narrow space character. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                                   |
| Hair Space                       | `U+200A` | A very narrow space character. Problematic if used instead of U+0020 in code syntax, can be almost invisible.                                                                                                                                                                                                                     |
| Narrow No-Break Space            | `U+202F` | A narrow version of the No-Break Space. Problematic for the same reasons as U+00A0 if used in code syntax.                                                                                                                                                                                                                        |
| Medium Mathematical Space        | `U+205F` | A space character used in mathematical formulae, typically 4/18 em. Problematic if used instead of U+0020 in general code syntax.                                                                                                                                                                                                 |
| Ideographic Space                | `U+3000` | A wide space character used with East Asian scripts, typically the width of one CJK character. Visually distinct but will cause parsing errors if used as a standard space in code.                                                                                                                                               |
| Line Separator                   | `U+2028` | Intended to unambiguously separate lines. Some languages (like JavaScript) treat it as a newline, but many others or tools might not, or might treat it as invalid whitespace. Can cause inconsistent line ending behavior or syntax errors if invisible in an editor.                                                            |
| Paragraph Separator              | `U+2029` | Intended to unambiguously separate paragraphs. Similar issues to Line Separator (U+2028); may be treated as a newline or invalid character depending on the context.                                                                                                                                                              |
| Left-to-Right Mark (LRM)         | `U+200E` | Invisible character affecting bidirectional text rendering. Can cause confusion or errors if copied into string literals or comments, or if it affects the interpretation of surrounding tokens, although less likely to break syntax directly compared to other bidi controls.                                                   |
| Right-to-Left Mark (RLM)         | `U+200F` | Invisible character affecting bidirectional text rendering. Similar issues to LRM.                                                                                                                                                                                                                                                |
| Left-to-Right Embedding (LRE)    | `U+202A` | Forces subsequent text to be treated as left-to-right. Can drastically alter the visual appearance and logical order of code or string literals if copied, leading to confusion and errors. Must be paired with PDF (U+202C).                                                                                                     |
| Right-to-Left Embedding (RLE)    | `U+202B` | Forces subsequent text to be treated as right-to-left. Can drastically alter code or string literals. Must be paired with PDF (U+202C).                                                                                                                                                                                           |
| Pop Directional Formatting (PDF) | `U+202C` | Terminates explicit directional embeddings (LRE, RLE) or overrides (LRO, RLO). If mismatched or unexpected, can lead to incorrect text rendering.                                                                                                                                                                                 |
| Left-to-Right Override (LRO)     | `U+202D` | Forces all subsequent characters to be treated as strong left-to-right, ignoring their inherent properties. Can make code unreadable or malicious. Must be paired with PDF (U+202C).                                                                                                                                              |

#### Medium Probability Problematic Characters (Context-Dependent / Risky for Filenames/URLs/Scripts)

These characters might be allowed by some operating systems in filenames but can cause issues in specific contexts like web URLs, command-line scripts, programming languages, or when files are moved across different systems. They are generally best avoided for maximum compatibility and to prevent unexpected behavior.

| Character Name / Rule | Symbol(s)             | Hex Code(s) (Typical) | Description / Common Issues                                                                                                | Notes                                                                                                     |
| :-------------------- | :-------------------- | :-------------------- | :------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------- |
| Pound / Hash          | `#`                   | `U+0023`              | Used for comments in scripts (e.g., Bash) and fragment identifiers in URLs. Can truncate filenames or cause misinterpretation. | Avoid in filenames intended for web use or scripting.                                                     |
| Percent               | `%`                   | `U+0025`              | Used for URL encoding (e.g., `%20` for space). Literal `%` in filenames can be misinterptered by web servers or scripts.     | Can break URL links or script processing if not handled carefully.                                        |
| Ampersand             | `&`                   | `U+0026`              | Special meaning in command shells (background process) and URLs (parameter separator).                                     | Requires quoting in shells; can break URLs.                                                               |
| Curly Brackets        | `{ }`                 | `U+007B`, `U+007D`    | Used for variable expansion or grouping in shells (e.g., Bash brace expansion) and as syntax in many programming languages.  | Can lead to unintended expansions or syntax errors if filenames are used directly in scripts.             |
| Dollar Sign           | `$`                   | `U+0024`              | Used for variable expansion in shells (e.g., `$VAR`) and as a special character in some programming languages (e.g., jQuery). | Filenames with `$` can be misinterpreted by shells or build tools.                                        |
| Exclamation Point     | `!`                   | `U+0021`              | Used for history expansion in some shells (e.g., Bash `!!`). Can also be problematic in some older filesystems or URLs.    | May need escaping or cause unexpected behavior in scripts.                                                |
| Single Quote          | `'`                   | `U+0027`              | String delimiter in shells and programming languages. Can prematurely terminate strings if filenames are unquoted.           | Always quote filenames containing single quotes when used in scripts.                                     |
| At Sign               | `@`                   | `U+0040`              | Sometimes used for special purposes (e.g., email addresses, user mentions, specific file system attributes like on macOS). | Generally less problematic but can have contextual meaning.                                               |
| Plus Sign             | `+`                   | `U+002B`              | Can be misinterpreted as a space in URL query strings (older CGI standard) or have special meaning in regular expressions.  | Safer to avoid or URL-encode if filenames are part of web paths.                                          |
| Backtick              | `` ` ``               | `U+0060`              | Used for command substitution in shells (e.g., `` `date` ``). Filenames with backticks can execute unintended commands.      | Extremely dangerous if filenames are interpolated into shell commands without proper sanitization.        |
| Equal Sign            | `=`                   | `U+003D`              | Used for assignments in scripts or as part of URL query parameters.                                                        | Can cause parsing issues if filenames are part of command-line arguments or configuration files.        |
| Emojis                | (various)             | (various Unicode)     | Multi-byte characters with varying support across OS, filesystems, and applications. Can cause display or processing issues. | Best avoided for critical filenames requiring broad compatibility.                                        |
| Leading/Trailing Chars| (see notes)           |                       | Filenames starting or ending with space, period (`.`), hyphen (`-`), or underline (`_`).                                   | Can be invisible, ignored by some OS (e.g., trailing periods on Windows), or cause issues with some tools. |
| Spaces in Filenames   | ` `                   | `U+0020`              | While allowed by most modern OS, spaces require filenames to be quoted in command-line interfaces and can cause script errors. | Already in "High Problems" (Windows specific), but generally a "medium" nuisance. Use hyphens or underscores instead. |

#### Low Probability Problematic Characters (Format/Whitespace and Special)

| Name                                  | Hex Code          | Description                                                                                                                                                                                                                                                           | Notes |
| :------------------------------------ | :---------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---- |
| Function Application                  | `U+2061`          | Invisible character used in mathematical notation. Unlikely in code, but could cause issues if accidentally present.                                                                                                                                                |       |
| Invisible Times                       | `U+2062`          | Invisible character used in mathematical notation for multiplication. Unlikely in code.                                                                                                                                                                              |       |
| Invisible Separator / Invisible Comma | `U+2063`          | Invisible character used in mathematical notation as a separator. Unlikely in code.                                                                                                                                                                                    |       |
| Invisible Plus                        | `U+2064`          | Invisible character used in mathematical notation. Unlikely in code.                                                                                                                                                                                                   |       |
| Variation Selector 1 through 16       | `U+FE00 - U+FE0F` | Invisible characters that select a specific glyph variant for the preceding character. If isolated or attached to an ASCII character in code, behavior is undefined or may cause subtle rendering differences. Unlikely to break parsing but can be confusing.         |       |
| Interlinear Annotation Anchor         | `U+FFF9`          | Marks the start of annotated text. Invisible and highly specialized; problematic if found in code.                                                                                                                                                                   |       |
| Interlinear Annotation Separator      | `U+FFFA`          | Separates annotated text from the annotation. Invisible and specialized; problematic.                                                                                                                                                                                  |       |
| Interlinear Annotation Terminator     | `U+FFFB`          | Marks the end of annotated text. Invisible and specialized; problematic.                                                                                                                                                                                               |       |
| Replacement Character                 | `U+FFFD`          | Usually visible (often as a question mark in a diamond ), indicating a character that could not be decoded. While visible, its presence in copied code signifies data corruption or encoding issues and needs to be addressed, otherwise it might be treated as a literal character causing unexpected behavior. |       |

## 3. Scannable File Types

The application is designed to scan a wide variety of file types. This section outlines the major categories and common examples of file extensions that are processed. For a complete and detailed list of all supported file types, extensions, and their specific classifications, please refer to the `FileTypesSummary.json` asset located in the `assets/` directory of the project.

The file types are categorized based on their common usage and potential risks.

### Executable and System Files

Files that can directly execute code or are critical system components.
*Examples: .exe, .dll, .sys, .so, .elf, .bat, .sh, .ps1, .jar, .com, .app, .cpl, .scr*
(See `FileTypesSummary.json` under "Executable and System Files" for a full list.)

### Scripting Files

Files containing code interpreted by a scripting engine.
*Examples: .js, .py, .rb, .pl, .php, .vbs, .wsf, .lua, .tcl*
(See `FileTypesSummary.json` under "Scripting Files" for a full list.)

### Document and Office Formats

Files created by office suites and document processing software. These can contain macros or embedded objects.
*Examples: .doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf, .rtf, .odt, .ods, .odp, .wpd*
(See `FileTypesSummary.json` under "Document Formats" for a full list.)

### Archive and Compressed Files

Files that bundle or compress other files. Archives can obscure malicious content.
*Examples: .zip, .rar, .7z, .tar, .gz, .bz2, .xz, .cab, .iso, .img, .arj, .lzh*
(See `FileTypesSummary.json` under "Archive and Compressed Files" for a full list.)

### Image and Multimedia Files

Image, audio, and video files. Some formats can have vulnerabilities or carry metadata.
*Examples: .jpg, .png, .gif, .bmp, .tiff, .svg, .webp, .mp3, .wav, .mp4, .avi, .mkv, .mov*
(See `FileTypesSummary.json` under "Image Formats" and "Multimedia Formats" for full lists.)

### Web-related Files

Files commonly used in web development and browsing.
*Examples: .html, .htm, .css, .json, .xml, .asp, .aspx, .jsp, .xhtml, .rss, .atom*
(See `FileTypesSummary.json` under "Web Development Files" for a full list.)

### Source Code and Development Files

Files containing source code for various programming languages, project files, and development artifacts.
This is a broad category. Key sub-groups include:

- **Programming Languages:** .c, .cpp, .java, .cs, .go, .swift, .kt, .rs, .scala, .m, .pas, .f, .f90, .ada, .cob
- **Build Systems & Package Management:** Makefile, CMakeLists.txt, .gradle, .pom.xml, package.json, Gemfile, requirements.txt, .sln, .csproj, .vcproj
- **Version Control & Patches:** .patch, .diff, .gitattributes, .gitignore, .svn
- **IDE/Editor Specific:** .vscode/, .idea/, .project, .classpath, .sublime-project
- **Other Development Files:** .h, .hpp, .lib, .a, .def, .idl, .proto, .sql
(For an exhaustive list, please consult `FileTypesSummary.json` under "Source Code and Development Files" and its subcategories.)

### Configuration and Data Files

Files storing application settings, structured data, or logs.
*Examples: .ini, .conf, .cfg, .yaml, .yml, .toml, .log, .dat, .bak, .tmp, .env, .properties, .reg, .plist*
(See `FileTypesSummary.json` under "Configuration and Data Files" for a full list.)

### Database Files

Files used by database systems to store data.
*Examples: .db, .sqlite, .sqlite3, .mdb, .accdb, .dbf, .sqlitedb, .myd, .frm*
(See `FileTypesSummary.json` under "Database Files" for a full list.)

### Virtualization and Container Files

Files related to virtual machines, disk images, and container technologies.
*Examples: .vmdk, .vdi, .vhd, .vhdx, .ova, .ovf, .qcow2, .dockerfile, .ova, .box*
(See `FileTypesSummary.json` under "Virtualization and Container Files" for a full list.)

### Font Files

Files defining typefaces.
*Examples: .ttf, .otf, .woff, .woff2, .eot*
(See `FileTypesSummary.json` under "Font Files" for a full list.)

### Email Files

Files storing email messages or mailboxes.
*Examples: .eml, .msg, .pst, .mbox, .emlx*
(See `FileTypesSummary.json` under "Email Files" for a full list.)

### CAD and 3D Modeling Files

Files used in Computer-Aided Design and 3D modeling.
*Examples: .dwg, .dxf, .stl, .obj, .fbx, .3ds, .blend, .skp*
(See `FileTypesSummary.json` under "CAD and 3D Modeling Files" for a full list.)

### GIS (Geographic Information System) Files

Files used for storing and managing geospatial data.
*Examples: .shp, .shx, .gpx, .kml, .kmz, .geojson, .tab*
(See `FileTypesSummary.json` under "GIS Files" for a full list.)

### Financial Data Files

Files containing financial data, often from accounting or spreadsheet software.
*Examples: .qfx, .ofx, .qif, .tax2023, .money*
(See `FileTypesSummary.json` under "Financial Data Files" for a full list.)

### Scientific Data Formats

Files used in scientific research and data analysis.
*Examples: .fits, .cdf, .nc, .h5, .fasta, .pdb*
(See `FileTypesSummary.json` under "Scientific Data Formats" for a full list.)

### Ebook Formats

Files for storing electronic books.
*Examples: .epub, .mobi, .azw, .azw3, .iba*
(See `FileTypesSummary.json` under "Ebook Formats" for a full list.)

### Security Related Files

Files related to security certificates, keys, and cryptographic operations.
*Examples: .pem, .crt, .cer, .key, .pfx, .p12, .asc, .gpg, .kdbx*
(See `FileTypesSummary.json` under "Security Related Files" for a full list.)

### Miscellaneous Files

Other file types that may be relevant for scanning.
*Examples: .torrent, .url, .pif, .lnk, .diagcab, .ics, .msi, .msp, .mst*
(See `FileTypesSummary.json` under "Miscellaneous Files" and "Named Files" for a full list.)

---
*Note: The presence of a file extension in this list does not automatically imply it is malicious, only that it is a type of file the application can analyze. The actual risk depends on the file's content and context.*
