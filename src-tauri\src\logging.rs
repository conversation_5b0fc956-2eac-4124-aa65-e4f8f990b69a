use std::path::PathBuf;
use tracing::{Level, Subscriber};
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::{
    filter::EnvFilter,
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    registry::LookupSpan,
    Layer,
};

/// Initialize the logging system with structured JSON output
pub fn init_logging(app_name: &str, log_dir: Option<PathBuf>) -> anyhow::Result<()> {
    // Determine log directory
    let log_directory = log_dir.unwrap_or_else(|| {
        dirs::data_local_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join(app_name)
            .join("logs")
    });

    // Create log directory if it doesn't exist
    std::fs::create_dir_all(&log_directory)?;

    // Set up file appender with daily rotation
    let file_appender = RollingFileAppender::new(
        Rotation::DAILY,
        log_directory.clone(),
        "bad-character-scanner.log",
    );

    // Create the subscriber with both file and console output
    let subscriber = tracing_subscriber::registry()
        .with(create_console_layer())
        .with(create_file_layer(file_appender))
        .with(create_env_filter());

    // Set as global default
    tracing::subscriber::set_global_default(subscriber)
        .map_err(|e| anyhow::anyhow!("Failed to set global subscriber: {}", e))?;

    // Log initialization
    tracing::info!(
        app_name = app_name,
        log_dir = ?log_directory,
        "Logging system initialized"
    );

    // Also set up panic hook to log panics
    setup_panic_hook();

    Ok(())
}

/// Create console logging layer with pretty formatting for development
fn create_console_layer<S>() -> impl Layer<S>
where
    S: Subscriber + for<'a> LookupSpan<'a>,
{
    fmt::layer()
        .with_target(true)
        .with_thread_ids(false)
        .with_thread_names(false)
        .with_span_events(FmtSpan::CLOSE)
        .with_ansi(true)
        .pretty()
}

/// Create file logging layer with JSON formatting for production
fn create_file_layer<S>(appender: RollingFileAppender) -> impl Layer<S>
where
    S: Subscriber + for<'a> LookupSpan<'a>,
{
    fmt::layer()
        .with_writer(appender)
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_span_events(FmtSpan::NEW | FmtSpan::CLOSE)
        .with_ansi(false)
        .json()
}

/// Create environment filter for log levels
fn create_env_filter() -> EnvFilter {
    EnvFilter::try_from_default_env()
        .or_else(|_| EnvFilter::try_new("info"))
        .unwrap_or_else(|_| {
            // Default filter: info for our app, warn for dependencies
            EnvFilter::new("warn,laptos_tauri=info,bad_character_scanner=info")
        })
}

/// Set up panic hook to log panics before termination
fn setup_panic_hook() {
    let default_panic = std::panic::take_hook();
    
    std::panic::set_hook(Box::new(move |panic_info| {
        let location = panic_info
            .location()
            .map(|loc| format!("{}:{}:{}", loc.file(), loc.line(), loc.column()))
            .unwrap_or_else(|| "unknown location".to_string());

        let message = if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
            s.to_string()
        } else if let Some(s) = panic_info.payload().downcast_ref::<String>() {
            s.clone()
        } else {
            "Unknown panic payload".to_string()
        };

        tracing::error!(
            location = location,
            message = message,
            "Application panicked"
        );

        // Call the default panic handler
        default_panic(panic_info);
    }));
}

/// Log levels that can be configured
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

impl From<LogLevel> for Level {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Trace => Level::TRACE,
            LogLevel::Debug => Level::DEBUG,
            LogLevel::Info => Level::INFO,
            LogLevel::Warn => Level::WARN,
            LogLevel::Error => Level::ERROR,
        }
    }
}

/// Security event logger for audit trail
pub mod security {
    use serde::Serialize;
    use tracing::{event, Level};

    #[derive(Debug, Serialize)]
    pub struct SecurityEvent {
        pub event_type: SecurityEventType,
        pub user_id: Option<String>,
        pub resource: Option<String>,
        pub action: String,
        pub result: SecurityResult,
        pub details: Option<String>,
    }

    #[derive(Debug, Serialize)]
    pub enum SecurityEventType {
        Authentication,
        Authorization,
        DataAccess,
        Configuration,
        SystemAccess,
    }

    #[derive(Debug, Serialize)]
    pub enum SecurityResult {
        Success,
        Failure,
        Blocked,
    }

    /// Log a security event
    pub fn log_security_event(event: SecurityEvent) {
        event!(
            target: "security",
            Level::INFO,
            event_type = ?event.event_type,
            user_id = event.user_id,
            resource = event.resource,
            action = %event.action,
            result = ?event.result,
            details = event.details,
            "Security event"
        );
    }

    /// Log a failed authentication attempt
    pub fn log_auth_failure(user_id: Option<&str>, reason: &str) {
        log_security_event(SecurityEvent {
            event_type: SecurityEventType::Authentication,
            user_id: user_id.map(String::from),
            resource: None,
            action: "login".to_string(),
            result: SecurityResult::Failure,
            details: Some(reason.to_string()),
        });
    }

    /// Log a successful file access
    pub fn log_file_access(file_path: &str, action: &str) {
        log_security_event(SecurityEvent {
            event_type: SecurityEventType::DataAccess,
            user_id: None,
            resource: Some(file_path.to_string()),
            action: action.to_string(),
            result: SecurityResult::Success,
            details: None,
        });
    }

    /// Log a blocked operation
    pub fn log_blocked_operation(operation: &str, reason: &str) {
        log_security_event(SecurityEvent {
            event_type: SecurityEventType::SystemAccess,
            user_id: None,
            resource: None,
            action: operation.to_string(),
            result: SecurityResult::Blocked,
            details: Some(reason.to_string()),
        });
    }
}

/// Performance monitoring helpers
pub mod performance {
    use std::time::Instant;
    use tracing::{info_span, Instrument};

    /// Create a span for timing operations
    pub fn timed_operation<F, R>(operation_name: &str, f: F) -> R
    where
        F: FnOnce() -> R,
    {
        let span = info_span!("timed_operation", name = operation_name);
        let start = Instant::now();
        
        let result = f().instrument(span.clone()).into_inner();
        
        let duration = start.elapsed();
        tracing::info!(
            target: "performance",
            operation = operation_name,
            duration_ms = duration.as_millis(),
            "Operation completed"
        );
        
        result
    }

    /// Log a performance metric
    pub fn log_metric(metric_name: &str, value: f64, unit: &str) {
        tracing::info!(
            target: "metrics",
            metric = metric_name,
            value = value,
            unit = unit,
            "Performance metric"
        );
    }
}

#[cfg(test)]
mod tests {
    //! Unit tests for logging functionality
    
    // Explicitly import only what's needed from parent module
    use super::{init_logging, security, performance};
    // TempDir is used in test_logging_initialization
    use tempfile::TempDir;
    #[test]
    fn test_logging_initialization() {
        // Create temp directory with error context
        let temp_dir = TempDir::new().expect("Failed to create temp directory");
        
        // Initialize logging
        let result = init_logging("test_app", Some(temp_dir.path().to_path_buf()));
        assert!(result.is_ok());
        
        // Test logging at different levels
        tracing::info!("Test info message");
        tracing::warn!("Test warning message");
        tracing::error!("Test error message");
    }

    #[test]
    fn test_security_logging() {
        use security::*;
        
        log_auth_failure(Some("test_user"), "Invalid password");
        log_file_access("/test/file.txt", "read");
        log_blocked_operation("delete_all", "Insufficient permissions");
    }

    #[test]
    fn test_performance_logging() {
        use performance::*;
        
        let result = timed_operation("test_operation", || {
            std::thread::sleep(std::time::Duration::from_millis(10));
            42
        });
        assert_eq!(result, 42);
        
        log_metric("test_metric", 123.45, "ms");
    }
}