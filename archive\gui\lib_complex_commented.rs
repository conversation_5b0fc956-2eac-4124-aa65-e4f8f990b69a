use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::spawn_local;

// Console error panic hook for better debugging
use console_error_panic_hook::set_once as set_panic_hook;

// Import icons module
mod icons;
use icons::*;

// Import settings module
mod settings;
use settings::*;

// Tauri command bindings
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

// Helper functions for Tauri commands
async fn tauri_invoke_no_args(cmd: &str) -> Result<JsValue, JsValue> {
    let args = JsValue::NULL;
    Ok(invoke(cmd, args).await)
}

async fn tauri_invoke_with_args<T: Serialize>(cmd: &str, args: &T) -> Result<JsValue, JsValue> {
    let args_js = serde_wasm_bindgen::to_value(args)?;
    Ok(invoke(cmd, args_js).await)
}

// Simplified data structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResults {
    pub id: String,
    pub input_text: String,
    pub total_characters: usize,
    pub total_bytes: usize,
    pub suspicious_characters: Vec<CharacterInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterInfo {
    pub character: char,
    pub position: usize,
    pub unicode_name: String,
    pub category: String,
    pub codepoint: u32,
    pub is_suspicious: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeBaseAnalysisResult {
    pub total_files: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub health_score: f64,
}

#[component]
pub fn App() -> impl IntoView {
    // Initialize console error panic hook
    set_panic_hook();

    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Bad Character Scanner - Simplified"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>

        <Router>
            <Routes>
                <Route path="" view=HomePage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    // State for the active tab
    let (active_tab, set_active_tab) = create_signal("text_analysis".to_string());

    // State for Text Analysis Tab
    let (text_input, set_text_input) = create_signal(String::new());
    let (text_analysis_result, set_text_analysis_result) = create_signal(Option::<String>::None);

    // State for Codebase Analysis Tab
    let (folder_path, set_folder_path) = create_signal(Option::<String>::None);
    let (codebase_analysis_result, set_codebase_analysis_result) = create_signal(Option::<String>::None);    // State for Settings Modal
    let (show_settings, set_show_settings) = create_signal(false);
    let (current_settings, set_current_settings) = create_signal(ScannerSettings::default());

    // Theme state
    let (is_dark_mode, set_is_dark_mode) = create_signal(false);

    // Theme toggle action
    let toggle_theme = move |_| {
        let new_theme = !is_dark_mode.get();
        set_is_dark_mode.set(new_theme);
        
        // Update HTML data-theme attribute
        if let Some(window) = web_sys::window() {
            if let Some(document) = window.document() {
                if let Some(html) = document.document_element() {
                    let theme_value = if new_theme { "dark" } else { "light" };
                    let _ = html.set_attribute("data-theme", theme_value);
                    // Save to localStorage
                    if let Ok(Some(storage)) = window.local_storage() {
                        let _ = storage.set_item("theme", theme_value);
                    }
                }
            }
        }
    };

    // Action for analyzing text
    let analyze_text_action = move |_| {
        set_text_analysis_result.set(Some("Processing... please wait.".to_string()));
        let current_text = text_input.get();
        spawn_local(async move {
            let args = serde_json::json!({ "text": current_text });
            logging::log!("Leptos Frontend: Invoking 'analyze_characters' with args: {:?}", args);
            match tauri_invoke_with_args("analyze_characters", &args).await {
                Ok(result) => {
                    logging::log!("Leptos Frontend: Received Ok from 'analyze_characters': {:?}", result);
                    match serde_wasm_bindgen::from_value::<AnalysisResults>(result) {
                        Ok(parsed_result) => {
                            // Format the result for display
                            let display_result = format!(
                                "Total Characters: {}\nSuspicious Characters: {}\nDetails: {:#?}",
                                parsed_result.total_characters,
                                parsed_result.suspicious_characters.len(),
                                parsed_result.suspicious_characters
                            );
                            set_text_analysis_result.set(Some(display_result));
                        }
                        Err(e) => {
                            set_text_analysis_result.set(Some("An error occurred while processing the analysis results. Please try again.".to_string()));
                            logging::error!("Error parsing analysis results: {}", e);
                        }
                    }
                }
                Err(e) => {
                    logging::error!("Leptos Frontend: Received Err from 'analyze_characters': {:?}", e);
                    set_text_analysis_result.set(Some("The analysis request could not be completed due to a system error. Please try again.".to_string()));
                }
            }
        });
    };

    // Action for selecting a folder
    let select_folder_action = move |_| {
        spawn_local(async move {
            match tauri_invoke_no_args("select_folder").await {
                Ok(result) => {
                    if let Ok(folder) = serde_wasm_bindgen::from_value::<String>(result) {
                        if !folder.is_empty() {
                            set_folder_path.set(Some(folder));
                        } else {
                            set_folder_path.set(None); // Clear if empty path is returned
                            set_codebase_analysis_result.set(Some("Folder selection cancelled or failed.".to_string()));
                        }
                    } else {
                         set_codebase_analysis_result.set(Some("Failed to parse folder selection result.".to_string()));
                    }
                }
                Err(e) => {
                    set_codebase_analysis_result.set(Some(format!("Failed to select folder: {:?}", e)));
                }
            }
        });
    };

    // Action for analyzing codebase
    let analyze_codebase_action = move |_| {
        if let Some(path) = folder_path.get() {
            spawn_local(async move {
                let args = serde_json::json!({ "path": path });
                match tauri_invoke_with_args("analyze_codebase", &args).await {
                    Ok(result) => {
                         match serde_wasm_bindgen::from_value::<CodeBaseAnalysisResult>(result) {
                            Ok(parsed_result) => {
                                // Format the result for display
                                let display_result = format!(
                                    "Total Files: {}\nFiles with Issues: {}\nTotal Suspicious Chars: {}\nHealth Score: {:.1}%",
                                    parsed_result.total_files,
                                    parsed_result.files_with_issues,
                                    parsed_result.total_suspicious_chars,
                                    parsed_result.health_score
                                );
                                set_codebase_analysis_result.set(Some(display_result));
                            }
                            Err(e) => {
                                set_codebase_analysis_result.set(Some(format!("Error parsing codebase analysis results: {}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        set_codebase_analysis_result.set(Some(format!("Tauri invocation failed for codebase analysis: {:?}", e)));
                    }
                }            });
        }
    };

    view! {
        <div class="container mx-auto p-4">
            <div class="flex justify-between items-center mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md card">
                <div class="flex items-center gap-3">
                    <BCSLogo class="w-10 h-10 text-blue-600" />
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">"Bad Character Scanner"</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-300">"Advanced Unicode Security Analysis"</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button 
                        class="theme-toggle btn btn-ghost p-2"
                        title="Toggle dark mode"
                        on:click=toggle_theme
                    >
                        <ThemeToggleIcon class="w-5 h-5" />
                    </button>
                    <button 
                        class="btn btn-ghost flex items-center gap-2" 
                        on:click=move |_| set_show_settings.set(true)
                    >
                        <CogIcon class="w-5 h-5" />
                        "Settings"
                    </button>
                </div>
            </div>

            // Tab Navigation
            <div class="tabs mb-4">
                <button
                    class=move || format!("tab tab-lifted flex items-center gap-2 {} px-4 py-2 rounded-t-lg font-medium transition-all", 
                        if active_tab.get() == "text_analysis" { "tab-active bg-blue-100 text-blue-700 border-blue-300" } else { "text-gray-600 hover:text-gray-800 hover:bg-gray-50" })
                    on:click=move |_| set_active_tab.set("text_analysis".to_string())
                >
                    <SearchIcon class="w-5 h-5" />
                    "Text Analysis"
                </button>
                <button
                    class=move || format!("tab tab-lifted flex items-center gap-2 {} px-4 py-2 rounded-t-lg font-medium transition-all", 
                        if active_tab.get() == "codebase_analysis" { "tab-active bg-blue-100 text-blue-700 border-blue-300" } else { "text-gray-600 hover:text-gray-800 hover:bg-gray-50" })
                    on:click=move |_| set_active_tab.set("codebase_analysis".to_string())
                >
                    <ChartIcon class="w-5 h-5" />
                    "Codebase Analysis"
                </button>
            </div>

            // Tab Content
            {move || match active_tab.get().as_str() {
                "text_analysis" => view! {
                    <div class="card p-6 border rounded-lg shadow-md bg-white dark:bg-gray-800">
                        <div class="flex items-center gap-3 mb-4">
                            <SearchIcon class="w-6 h-6 text-blue-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">"Text Analysis"</h2>
                        </div>
                        <textarea
                            class="input textarea w-full h-32 mb-4 p-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 resize-none"
                            placeholder="Enter text here to analyze for suspicious Unicode characters..."
                            on:input=move |ev| set_text_input.set(event_target_value(&ev))
                            prop:value=text_input
                        ></textarea>
                        <button class="btn btn-primary flex items-center gap-2 mb-4" on:click=analyze_text_action>
                            <ShieldIcon class="w-5 h-5" />
                            "Analyze Text"
                        </button>
                        {move || text_analysis_result.get().map(|res| view! { 
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border">
                                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">"Analysis Results:"</h3>
                                <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{res}</pre>
                            </div>
                        })}
                    </div>
                }.into_view(),
                "codebase_analysis" => view! {
                    <div class="card p-6 border rounded-lg shadow-md bg-white dark:bg-gray-800">
                        <div class="flex items-center gap-3 mb-4">
                            <ChartIcon class="w-6 h-6 text-blue-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">"Codebase Analysis"</h2>
                        </div>
                        <button class="btn btn-secondary flex items-center gap-2 mb-4" on:click=select_folder_action>
                            <DocumentIcon class="w-5 h-5" />
                            "Select Folder"
                        </button>
                        {move || folder_path.get().map(|path| view! { 
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800 mb-4">
                                <p class="text-sm text-blue-800 dark:text-blue-200">
                                    <strong>"Selected Path: "</strong> {path}
                                </p>
                            </div>
                        })}
                        <button class="btn btn-primary flex items-center gap-2 mb-4" on:click=analyze_codebase_action disabled=move || folder_path.get().is_none()>
                            <ShieldIcon class="w-5 h-5" />
                            "Analyze Codebase"
                        </button>
                        {move || codebase_analysis_result.get().map(|res| view! { 
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border">
                                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">"Analysis Results:"</h3>
                                <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{res}</pre>
                            </div>
                        })}
                    </div>
                }.into_view(),
                _ => view! { <p class="text-center text-gray-500 py-8">"Select a tab to begin analysis"</p> }.into_view()
            }}

            // Settings Modal
            <SettingsModal
                show_settings=show_settings
                set_show_settings=set_show_settings
                settings=current_settings
                set_settings=set_current_settings
            />
        </div>
    }
}

// Main entry point for WASM
#[wasm_bindgen(start)]
pub fn main() {
    console_error_panic_hook::set_once();
    mount_to_body(|| view! { <App/> })
}
