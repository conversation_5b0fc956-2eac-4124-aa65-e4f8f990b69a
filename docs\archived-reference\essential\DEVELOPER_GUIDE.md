# Developer Guide

## Getting Started

### Prerequisites
1. Install Rust: https://rustup.rs/
2. Install Node.js 16+: https://nodejs.org/
3. Install required tools:
   ```bash
   cargo install tauri-cli
   cargo install trunk
   ```

### Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd bad-character-scanner

# Install dependencies
npm install

# Run in development mode
cargo tauri dev
```

## Architecture Overview

### Frontend (Leptos)
- **Framework**: Leptos (Rust-based reactive UI)
- **Styling**: Tailwind CSS
- **Build Tool**: Trunk
- **Key Components**:
  - `AnalyzeComponent`: Text analysis UI
  - `CleanComponent`: Text cleaning functionality
  - `ExportComponent`: Export to various formats
  - `FileMenu`: Application menu

### Backend (Tauri + Rust)
- **Framework**: Tauri v2
- **Core Modules**:
  - `character_analyzer`: Unicode security analysis
  - `ai_detection`: AI content detection
  - `pattern_matching`: Pattern recognition
  - `enhanced_analysis`: Advanced codebase scanning

## Key Features Implementation

### 1. Unicode Character Analysis
Located in `src-tauri/src/modules/character_analyzer.rs`

```rust
// Example: Analyzing text for suspicious characters
let analyzer = CharacterAnalyzer::new()?;
let results = analyzer.analyze_text(&input_text);
```

### 2. AI Content Detection
Uses patterns from `assets/Advanced_AI_Patterns.json`

### 3. Codebase Analysis
The `enhanced_analysis.rs` module provides comprehensive codebase scanning:
- Recursive file traversal
- Pattern detection
- Risk assessment
- Report generation

## Adding New Features

### Adding a New Detection Pattern
1. Edit `assets/Advanced_AI_Patterns.json`
2. Add your pattern:
```json
{
  "name": "Your Pattern Name",
  "regex": "your-regex-pattern",
  "severity": "Low|Medium|High|Critical",
  "description": "What this pattern detects"
}
```

### Adding a New Tauri Command
1. Create the command in `src-tauri/src/main_module.rs`:
```rust
#[tauri::command]
pub async fn your_command(param: String) -> Result<String, String> {
    // Implementation
    Ok("Result".to_string())
}
```

2. Register it in `src-tauri/src/lib.rs`:
```rust
.invoke_handler(tauri::generate_handler![
    // ... existing commands
    main_module::your_command,
])
```

3. Call from frontend:
```rust
let result = invoke("your_command", args).await;
```

### Adding a New Component
1. Create component file in `src/components/`
2. Export from `src/components/mod.rs`
3. Use in main app

## Testing

### Unit Tests
```bash
cargo test
```

### Integration Tests
```bash
cargo test --test integration
```

### Manual Testing Checklist
- [ ] Text analysis with Unicode characters
- [ ] Codebase scanning (drag & drop)
- [ ] Export functionality (all formats)
- [ ] File menu operations
- [ ] Clean text functionality

## Common Issues & Solutions

### Build Errors
1. **Missing wasm target**: 
   ```bash
   rustup target add wasm32-unknown-unknown
   ```

2. **Port already in use**:
   ```bash
   # Find and kill process on port 1421
   netstat -ano | findstr :1421
   taskkill /PID <PID> /F
   ```

### Performance Optimization
- Use `--release` flag for production builds
- Implement lazy loading for large codebases
- Cache analysis results

## Code Style Guidelines
- Use `cargo fmt` before committing
- Run `cargo clippy` for linting
- Follow Rust naming conventions
- Document public APIs

## Debugging Tips
1. Backend logs appear in terminal
2. Frontend logs in browser DevTools
3. Use `eprintln!()` for backend debugging
4. Use `log!()` macro for frontend debugging

## Release Process
1. Update version in `Cargo.toml` files
2. Run tests: `cargo test`
3. Build release: `cargo tauri build`
4. Test installer on clean system
5. Create GitHub release with changelog