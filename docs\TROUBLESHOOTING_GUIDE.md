# Troubleshooting Guide - Bad Character Scanner by <PERSON><PERSON>

**Comprehensive debugging and problem-solving guide for the Bad Character Scanner - Quick fixes to advanced diagnostics.**

---

## 🚨 **Emergency Quick Fixes**

### **Application Won't Start**
```powershell
# 1. Kill existing processes
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"} | Stop-Process

# 2. Clean build
cargo clean && trunk clean

# 3. Restart development
.\dev_startup.ps1
```

### **Build Failures**
```powershell
# Missing WASM target
rustup target add wasm32-unknown-unknown

# Update Rust toolchain
rustup update

# Reinstall tools
cargo install tauri-cli --version "^2.5" --force
cargo install trunk --locked --force
```

### **Port Conflicts**
```powershell
# Find processes using ports 1420/1421
netstat -ano | findstr :1420
netstat -ano | findstr :1421

# Kill specific process (replace PID)
taskkill /PID <PID> /F
```

---

## 🔍 **Common Issues & Solutions**

### **Development Environment Issues**

#### **"Command not found" Errors**
| Command | Solution |
|---------|----------|
| `cargo` | Install Rust: `winget install Rustlang.Rustup` |
| `npm` | Install Node.js: `winget install OpenJS.NodeJS` |
| `trunk` | Install: `cargo install trunk --locked` |
| `tauri` | Install: `cargo install tauri-cli --version "^2.5"` |

#### **Permission Errors**
```powershell
# Run as administrator if needed
Start-Process PowerShell -Verb RunAs

# Fix file permissions
icacls "C:\path\to\project" /grant Users:F /T
```

### **Frontend Issues**

#### **Leptos Compilation Errors**
```rust
// Common fix: Update signal usage
// Old (broken)
let value = signal();

// New (correct)
let value = signal.get();
let set_value = signal.set(new_value);
```

#### **CSS/Styling Issues**
- **Missing styles**: Check `style.css` is loaded
- **Tailwind not working**: Verify `tailwind.config.js` configuration
- **Dark mode issues**: Check theme context implementation

#### **Component Errors**
```rust
// Common fix: Proper context usage
let context = expect_context::<YourContext>();
// Instead of: use_context::<YourContext>().unwrap()
```

### **Backend Issues**

#### **Tauri Command Failures**
```rust
// Ensure commands are properly registered
.invoke_handler(tauri::generate_handler![
    your_command_name,  // Add your command here
])

// Check command signature matches frontend call
#[tauri::command]
pub async fn your_command(param: String) -> Result<String, String> {
    // Implementation
}
```

#### **File System Access Issues**
```json
// Update tauri.conf.json allowlist
{
  "tauri": {
    "allowlist": {
      "fs": {
        "readFile": true,
        "readDir": true,
        "scope": ["$DOCUMENT/*", "$DOWNLOAD/*", "$HOME/*"]
      }
    }
  }
}
```

---

## 🏗️ **Build & Compilation Issues**

### **Rust Compilation Errors**

#### **Dependency Conflicts**
```powershell
# Update Cargo.lock
cargo update

# Clean and rebuild
cargo clean
cargo build
```

#### **WASM Build Issues**
```powershell
# Ensure WASM target is installed
rustup target add wasm32-unknown-unknown

# Check trunk configuration
trunk --version
trunk build --release
```

### **Frontend Build Issues**

#### **Trunk Build Failures**
```powershell
# Clear trunk cache
trunk clean

# Rebuild with verbose output
trunk build --verbose

# Check for missing dependencies
npm install
```

#### **Asset Loading Issues**
- **Icons missing**: Check `src-tauri/icons/` directory
- **CSS not loading**: Verify `style.css` path in `index.html`
- **Fonts not loading**: Check font file paths and CSS imports

---

## 🚀 **Runtime Problems**

### **Performance Issues**

#### **Slow Analysis**
```rust
// Optimize analysis settings
let config = AnalysisConfig {
    max_threads: num_cpus::get(),
    batch_size: 100,
    memory_limit: 512 * 1024 * 1024, // 512MB
};
```

#### **Memory Usage**
- **High RAM usage**: Reduce batch size in settings
- **Memory leaks**: Check for unclosed file handles
- **Large file handling**: Implement streaming for files >10MB

### **UI Responsiveness**

#### **Frozen Interface**
```rust
// Use async operations for heavy tasks
spawn_local(async move {
    let result = heavy_operation().await;
    set_result.set(result);
});
```

#### **Progress Bar Issues**
- **Not showing**: Check if operation duration >0.4 seconds
- **Incorrect progress**: Verify progress calculation logic
- **Stuck progress**: Ensure progress updates in async operations

---

## 🐛 **Advanced Debugging**

### **Debug Logging**

#### **Backend Debugging**
```rust
// Add debug prints
eprintln!("Debug: {:#?}", variable);

// Use proper logging
use log::{debug, info, warn, error};
debug!("Analysis started for: {}", file_path);
```

#### **Frontend Debugging**
```rust
// Browser console logging
console_log!("Frontend debug: {}", message);

// Leptos debugging
log!("Component state: {:#?}", state);
```

### **Performance Profiling**

#### **Backend Profiling**
```rust
use std::time::Instant;

let start = Instant::now();
let result = expensive_operation();
eprintln!("Operation took: {:?}", start.elapsed());
```

#### **Frontend Profiling**
```javascript
// Browser DevTools Performance tab
// Or use console.time/timeEnd
console.time("analysis");
// ... operation
console.timeEnd("analysis");
```

---

## 🔧 **System Diagnostics**

### **Environment Check**
```powershell
# Check system requirements
rustc --version
node --version
npm --version

# Check installed tools
cargo --version
trunk --version
tauri --version

# Check available targets
rustup target list --installed
```

### **Project Health Check**
```powershell
# Verify project structure
ls src/
ls src-tauri/src/
ls docs/

# Check dependencies
cargo tree
npm list

# Verify configuration
cat Cargo.toml
cat package.json
cat tauri.conf.json
```

---

## 📊 **Error Code Reference**

### **Exit Codes**
| Code | Meaning | Solution |
|------|---------|----------|
| 0 | Success | No action needed |
| 1 | General error | Check logs for details |
| 2 | Invalid arguments | Verify command syntax |
| 3 | File not found | Check file path |
| 4 | Analysis failed | Check file format/permissions |
| 5 | Export failed | Check output directory permissions |
| 6 | Dependency missing | Install required tools |

### **Common Error Messages**
| Error | Cause | Solution |
|-------|-------|----------|
| "WASM target not found" | Missing target | `rustup target add wasm32-unknown-unknown` |
| "Port already in use" | Process conflict | Kill existing processes |
| "Permission denied" | File access | Check file permissions |
| "Command not found" | Missing tool | Install required tool |

---

## 🛠️ **Recovery Procedures**

### **Complete Environment Reset**
```powershell
# 1. Stop all processes
Get-Process | Where-Object {$_.ProcessName -like "*trunk*" -or $_.ProcessName -like "*tauri*"} | Stop-Process

# 2. Clean everything
cargo clean
trunk clean
Remove-Item -Recurse -Force node_modules
Remove-Item -Force package-lock.json

# 3. Reinstall dependencies
npm install
cargo build

# 4. Restart development
.\dev_startup.ps1
```

### **Backup and Restore**
```powershell
# Create backup before major changes
Copy-Item -Recurse "." "../backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# Restore from backup if needed
Copy-Item -Recurse "../backup-20250620-143000/*" "."
```

---

## 📞 **Getting Help**

### **Self-Help Resources**
1. **Check Logs**: Review terminal output and browser console
2. **Search Issues**: Look for similar problems in project issues
3. **Documentation**: Review relevant documentation sections
4. **Community**: Check discussions and forums

### **Reporting Issues**
When reporting issues, include:
- **Environment**: OS, Rust version, Node.js version
- **Steps to Reproduce**: Exact commands and actions
- **Error Messages**: Complete error output
- **Expected vs Actual**: What should happen vs what happens
- **Screenshots**: If UI-related

### **Support Channels**
- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and help
- **Documentation**: Check consolidated guides first
- **Emergency**: For critical security issues, use private channels

---

## 🎯 **Prevention Best Practices**

### **Development Hygiene**
- **Regular Updates**: Keep tools and dependencies updated
- **Clean Builds**: Regularly clean and rebuild
- **Version Control**: Commit working states frequently
- **Testing**: Test changes before committing

### **Environment Maintenance**
- **Backup Configurations**: Save working configurations
- **Document Changes**: Note any custom modifications
- **Monitor Resources**: Watch disk space and memory usage
- **Regular Cleanup**: Remove old build artifacts

---

*Most issues can be resolved quickly with these troubleshooting steps. When in doubt, try the emergency quick fixes first!*

**Bad Character Scanner by J.Shoy - 2025**
