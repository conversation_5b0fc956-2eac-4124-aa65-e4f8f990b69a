#!/usr/bin/env powershell
# Debug build failures for Bad Character Scanner

param(
    [switch]$Frontend,
    [switch]$Backend,
    [switch]$Verbose
)

Write-Host "`n🔍 DEBUGGING BUILD FAILURE" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent

# Check if we should debug frontend or backend
if (-not $Frontend -and -not $Backend) {
    $Frontend = $true
    $Backend = $true
}

# Frontend debugging
if ($Frontend) {
    Write-Host "`n📦 Frontend Build Debug:" -ForegroundColor Yellow
    
    Push-Location $project_root
    
    # Try to build with verbose output
    Write-Host "  Running WASM build with verbose output..." -ForegroundColor Gray
    $wasm_output = cargo build --target wasm32-unknown-unknown --verbose 2>&1
    
    # Look for specific error patterns
    $errors = $wasm_output | Select-String -Pattern "error\[E\d+\]|error:"
    
    if ($errors) {
        Write-Host "`n❌ Frontend Errors Found:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  $error" -ForegroundColor Red
        }
        
        # Extract file and line numbers
        $error_locations = $wasm_output | Select-String -Pattern "src\\components\\.*\.rs:\d+"
        if ($error_locations) {
            Write-Host "`n📍 Error Locations:" -ForegroundColor Yellow
            foreach ($loc in $error_locations | Select-Object -Unique) {
                Write-Host "  $loc" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "  ✅ No compilation errors detected" -ForegroundColor Green
    }
    
    # Check for common issues
    Write-Host "`n🔎 Common Issue Checks:" -ForegroundColor Yellow
    
    # Check for closure/move issues
    $closure_issues = Get-ChildItem "src\components" -Filter "*.rs" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match "move \|.*\|.*error_manager" -or $content -match "FnOnce.*Fn") {
            [PSCustomObject]@{ File = $_.Name; Issue = "Potential closure/move issue" }
        }
    }
    
    if ($closure_issues) {
        Write-Host "  ⚠️  Potential closure issues:" -ForegroundColor Yellow
        $closure_issues | ForEach-Object {
            Write-Host "    $($_.File): $($_.Issue)" -ForegroundColor Yellow
        }
    }
    
    Pop-Location
}

# Backend debugging
if ($Backend) {
    Write-Host "`n🔧 Backend Build Debug:" -ForegroundColor Yellow
    
    Push-Location "$project_root\src-tauri"
    
    Write-Host "  Running backend build check..." -ForegroundColor Gray
    $backend_output = cargo check 2>&1
    
    $backend_errors = $backend_output | Select-String -Pattern "error\[E\d+\]|error:"
    
    if ($backend_errors) {
        Write-Host "`n❌ Backend Errors Found:" -ForegroundColor Red
        foreach ($error in $backend_errors) {
            Write-Host "  $error" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✅ Backend compiles successfully" -ForegroundColor Green
    }
    
    Pop-Location
}

# Check for leptos-specific issues
Write-Host "`n🌐 Leptos-Specific Checks:" -ForegroundColor Yellow

# Check view! macro usage
$view_macros = Get-ChildItem "$project_root\src\components" -Filter "*.rs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $matches = [regex]::Matches($content, "view!\s*\{")
    if ($matches.Count -gt 0) {
        [PSCustomObject]@{ 
            File = $_.Name
            ViewMacros = $matches.Count
            LargeViews = ($content.Split("`n").Count -gt 500)
        }
    }
}

if ($view_macros) {
    Write-Host "  View macro usage:" -ForegroundColor Gray
    $view_macros | ForEach-Object {
        $color = if ($_.LargeViews) { "Yellow" } else { "Gray" }
        Write-Host "    $($_.File): $($_.ViewMacros) view! macros $(if ($_.LargeViews) { '(Large file!)' })" -ForegroundColor $color
    }
}

# Suggestions
Write-Host "`n💡 Debugging Suggestions:" -ForegroundColor Cyan

Write-Host "  1. Check the browser console for more detailed error messages" -ForegroundColor White
Write-Host "  2. Run 'trunk serve' separately to see trunk-specific errors" -ForegroundColor White
Write-Host "  3. Try 'cargo clean' and rebuild from scratch" -ForegroundColor White
Write-Host "  4. Check if all error_manager uses are properly cloned" -ForegroundColor White
Write-Host "  5. Look for any remaining 'unsafe' blocks that might cause issues" -ForegroundColor White

if ($Verbose) {
    Write-Host "`n📄 Full WASM Build Output:" -ForegroundColor Yellow
    Write-Host $wasm_output
}

Write-Host "`n🔧 Run '.\fix-compiler-errors.ps1' to attempt automatic fixes" -ForegroundColor Cyan