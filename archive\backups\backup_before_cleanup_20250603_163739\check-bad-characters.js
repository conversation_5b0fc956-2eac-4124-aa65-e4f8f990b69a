#!/usr/bin/env node

/**
 * Bad Character Detection Script for Codebase
 * 
 * This script scans the entire codebase for suspicious Unicode characters
 * that could cause issues for people with dyslexia or accessibility needs.
 * 
 * MISSION: Make code accessible for everyone!
 * Priority: CRITICAL - Accessibility Hero Mode
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Suspicious character patterns that could cause dyslexia issues
const SUSPICIOUS_PATTERNS = {
  // Zero-width characters
  zeroWidth: /[\u200B\u200C\u200D\u2060\uFEFF]/g,
  
  // Invisible separators
  invisibleSeparators: /[\u2028\u2029]/g,
  
  // Bidirectional overrides (can cause reading confusion)
  bidiOverrides: /[\u202D\u202E\u202A\u202B\u202C]/g,
  
  // Homoglyph attacks (characters that look similar)
  homoglyphs: /[а-я]|[А-Я]|[αβγδεζηθικλμνξοπρστυφχψω]|[ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]/g, // Cyrillic/Greek in Latin text
  
  // Non-breaking spaces that can cause layout issues
  nonBreakingSpaces: /[\u00A0\u2007\u202F]/g,
  
  // Byte Order Marks in middle of files
  bomInContent: /\uFEFF/g
};

const EXCLUDED_DIRS = [
  'node_modules',
  'target',
  'dist',
  '.git',
  'reports',
  'public'
];

const INCLUDED_EXTENSIONS = [
  '.rs', '.js', '.ts', '.jsx', '.tsx', 
  '.html', '.css', '.md', '.json', 
  '.toml', '.yml', '.yaml'
];

function shouldProcessFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return INCLUDED_EXTENSIONS.includes(ext);
}

function shouldProcessDir(dirPath) {
  const dirName = path.basename(dirPath);
  return !EXCLUDED_DIRS.includes(dirName);
}

function analyzeFileContent(content, filePath) {
  const issues = [];
  
  Object.entries(SUSPICIOUS_PATTERNS).forEach(([type, pattern]) => {
    const matches = content.match(pattern);
    if (matches) {
      const lines = content.split('\n');
      matches.forEach(match => {
        const lineIndex = lines.findIndex(line => line.includes(match));
        if (lineIndex !== -1) {
          issues.push({
            type,
            character: match,
            codePoint: `U+${match.codePointAt(0).toString(16).toUpperCase().padStart(4, '0')}`,
            line: lineIndex + 1,
            context: lines[lineIndex].substring(0, 100)
          });
        }
      });
    }
  });
  
  return issues;
}

function scanDirectory(dirPath) {
  const results = {
    totalFiles: 0,
    filesWithIssues: 0,
    totalIssues: 0,
    issues: []
  };
  
  function scanRecursive(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const itemPath = path.join(currentPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory() && shouldProcessDir(itemPath)) {
        scanRecursive(itemPath);
      } else if (stats.isFile() && shouldProcessFile(itemPath)) {
        results.totalFiles++;
        
        try {
          const content = fs.readFileSync(itemPath, 'utf8');
          const fileIssues = analyzeFileContent(content, itemPath);
          
          if (fileIssues.length > 0) {
            results.filesWithIssues++;
            results.totalIssues += fileIssues.length;
            results.issues.push({
              file: path.relative(process.cwd(), itemPath),
              issues: fileIssues
            });
          }
        } catch (error) {
          // Skip binary files or files with encoding issues
          if (!error.message.includes('invalid character')) {
            console.warn(`⚠️  Skipped ${itemPath}: ${error.message}`);
          }
        }
      }
    }
  }
  
  scanRecursive(dirPath);
  return results;
}

function generateReport(results) {
  console.log('\n🔍 BAD CHARACTER ANALYSIS REPORT');
  console.log('================================');
  console.log(`📁 Total files scanned: ${results.totalFiles}`);
  console.log(`⚠️  Files with issues: ${results.filesWithIssues}`);
  console.log(`🚨 Total issues found: ${results.totalIssues}`);
  
  if (results.totalIssues === 0) {
    console.log('✅ EXCELLENT! No bad characters found. This codebase is dyslexia-friendly! 🎉');
    return;
  }
  
  console.log('\n📋 DETAILED ISSUES:');
  console.log('===================');
  
  results.issues.forEach(fileResult => {
    console.log(`\n📄 ${fileResult.file}`);
    fileResult.issues.forEach(issue => {
      console.log(`   🚨 Line ${issue.line}: ${issue.type}`);
      console.log(`      Character: "${issue.character}" (${issue.codePoint})`);
      console.log(`      Context: ${issue.context.replace(/\s+/g, ' ').trim()}`);
    });
  });
  
  console.log('\n💡 RECOMMENDATION:');
  console.log('Run the Bad Character Scanner tool to clean these files!');
  console.log('This will make the codebase more accessible for people with dyslexia.');
}

function main() {
  console.log('🦸 ACCESSIBILITY HERO MODE ACTIVATED!');
  console.log('Scanning codebase for characters that could cause dyslexia issues...\n');
  
  const projectRoot = process.cwd();
  const results = scanDirectory(projectRoot);
  
  generateReport(results);
  
  // Exit with error code if issues found (for CI/CD)
  if (results.totalIssues > 0) {
    console.log('\n❌ Bad characters detected! Please clean them for accessibility.');
    process.exit(1);
  }
  
  console.log('\n🎉 Codebase is accessibility-friendly!');
}

main();
