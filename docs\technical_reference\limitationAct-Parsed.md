# Parsed Application Limitations

This file contains parsed data from the application's assets, intended for staged updates to the final documentation.

## Bad Character Definitions

### 1. Filename-Specific Issues

#### Forbidden Windows Characters

These characters are explicitly forbidden by the Windows filesystem.

| Character | Hex Code | Description                                      |
| :-------: | :------: | :----------------------------------------------- |
|    `<`    | `U+003C` | Forbidden in Windows filenames (less-than sign)    |
|    `>`    | `U+003E` | Forbidden in Windows filenames (greater-than sign) |
|    `:`    | `U+003A` | Forbidden in Windows filenames (colon)             |
|    `"`    | `U+0022` | Forbidden in Windows filenames (double quote)      |
|    `/`    | `U+002F` | Forbidden in Windows filenames (forward slash)     |
|   `\`    | `U+005C` | Forbidden in Windows filenames (backslash)         |
|    `&#124;`    | `U+007C` | Forbidden in Windows filenames (vertical bar)      |
|    `?`    | `U+003F` | Forbidden in Windows filenames (question mark)     |
|    `*`    | `U+002A` | Forbidden in Windows filenames (asterisk)          |

#### General Control Characters (C0)

These non-printable control characters (U+0000-U+001F and U+007F) are forbidden in filenames.

| Name                        | Hex    | Description                                   |
| :-------------------------- | :----: | :-------------------------------------------- |
| Null (NUL)                  | U+0000 | C0 control character. Forbidden in filenames. |
| Start of Heading (SOH)      | U+0001 | C0 control character. Forbidden in filenames. |
| Start of Text (STX)         | U+0002 | C0 control character. Forbidden in filenames. |
| End of Text (ETX)           | U+0003 | C0 control character. Forbidden in filenames. |
| End of Transmission (EOT)   | U+0004 | C0 control character. Forbidden in filenames. |
| Enquiry (ENQ)               | U+0005 | C0 control character. Forbidden in filenames. |
| Acknowledge (ACK)           | U+0006 | C0 control character. Forbidden in filenames. |
| Bell (BEL)                  | U+0007 | C0 control character. Forbidden in filenames. |
| Backspace (BS)              | U+0008 | C0 control character. Forbidden in filenames. |
| Horizontal Tab (HT)         | U+0009 | C0 control character. Forbidden in filenames. |
| Line Feed (LF)              | U+000A | C0 control character. Forbidden in filenames. |
| Vertical Tab (VT)           | U+000B | C0 control character. Forbidden in filenames. |
| Form Feed (FF)              | U+000C | C0 control character. Forbidden in filenames. |
| Carriage Return (CR)        | U+000D | C0 control character. Forbidden in filenames. |
| Shift Out (SO)              | U+000E | C0 control character. Forbidden in filenames. |
| Shift In (SI)               | U+000F | C0 control character. Forbidden in filenames. |
| Data Link Escape (DLE)      | U+0010 | C0 control character. Forbidden in filenames. |
| Device Control 1 (DC1)      | U+0011 | C0 control character. Forbidden in filenames. |
| Device Control 2 (DC2)      | U+0012 | C0 control character. Forbidden in filenames. |
| Device Control 3 (DC3)      | U+0013 | C0 control character. Forbidden in filenames. |
| Device Control 4 (DC4)      | U+0014 | C0 control character. Forbidden in filenames. |
| Negative Acknowledge (NAK)  | U+0015 | C0 control character. Forbidden in filenames. |
| Synchronous Idle (SYN)      | U+0016 | C0 control character. Forbidden in filenames. |
| End of Transmission Block (ETB)| U+0017 | C0 control character. Forbidden in filenames. |
| Cancel (CAN)                | U+0018 | C0 control character. Forbidden in filenames. |
| End of Medium (EM)          | U+0019 | C0 control character. Forbidden in filenames. |
| Substitute (SUB)            | U+001A | C0 control character. Forbidden in filenames. |
| Escape (ESC)                | U+001B | C0 control character. Forbidden in filenames. |
| File Separator (FS)         | U+001C | C0 control character. Forbidden in filenames. |
| Group Separator (GS)        | U+001D | C0 control character. Forbidden in filenames. |
| Record Separator (RS)       | U+001E | C0 control character. Forbidden in filenames. |
| Unit Separator (US)         | U+001F | C0 control character. Forbidden in filenames. |
| Delete (DEL)                | U+007F | C0 control character. Forbidden in filenames. |

#### C1 Control Characters

The C1 control characters (U+0080-U+009F) are also forbidden in filenames.

| Name                  | Range         | Description                                   |
| :-------------------- | :-----------: | :-------------------------------------------- |
| C1 Control Characters | U+0080-U+009F | C1 control characters. Forbidden in filenames. |

#### Other Problematic Filename Patterns

| Example                       | Description                                                                                |
| :---------------------------- | :----------------------------------------------------------------------------------------- |
| Leading or trailing space     | Filenames with leading or trailing spaces can cause confusion or errors on some platforms. |
| Filename ending with a period | Not allowed on Windows; can cause sync/copy issues.                                        |

### 2. Content-Specific Issues

A list of invisible or problematic Unicode characters that can cause issues for coders, especially when copying text from websites or other sources. These characters can lead to syntax errors, logical errors, or unexpected behavior that is often hard to debug due to their invisible nature. Categorized by estimated severity of the problems they cause.

#### Extremely Big Problems

Characters that are often completely invisible and can severely break code structure, tokenization, or logic, leading to hard-to-debug syntax or runtime errors. These are prime candidates for causing issues when copied from the web.

| Name                                           | Hex    | Description                                                                                                                                                                                                                                                          |
| :--------------------------------------------- | :----: | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Zero Width Space                               | U+200B | Completely invisible. If inserted within identifiers, keywords, or numbers, it breaks them into separate tokens, leading to syntax errors. Can also affect string comparisons or content-based hashing. Extremely difficult to spot visually.                               |
| Zero Width No-Break Space / Byte Order Mark (BOM) | U+FEFF | Dual nature: 1. As Zero Width No-Break Space (its original deprecated role when not at the start of a text stream): Acts like U+200B, completely invisible and can break tokens. 2. As Byte Order Mark (BOM): If present at the beginning of a UTF-8 encoded file where it's not expected (e.g., shell scripts after shebang, JSON files, some config files, PHP files before '<?php'), it can cause parsing errors or script failures. If copied mid-stream, it reverts to its ZWNBSP behavior. |
| Word Joiner / Zero Width No-Break Space        | U+2060 | Ensures that adjacent characters are kept on the same line (prevents a line break). It is zero-width and invisible. If accidentally inserted within an identifier or numeric literal, it can break tokenization and cause syntax errors, similar to U+200B.         |

#### High Problems

Characters that are visually subtle (e.g., may look like standard spaces but have different properties) or have significant disruptive effects (e.g., altering text direction, acting as unexpected newlines). They frequently cause errors or logical issues in code.

| Name                             | Hex    | Description                                                                                                                                                                                                                                                                                                                       |
| :------------------------------- | :----: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| No-Break Space                   | U+00A0 | Visually similar to a regular space, but prevents line breaks. If used instead of a regular space (U+0020) within code syntax (e.g., separating keywords, in identifiers if the language allows spaces, or in string literals where a specific space type is expected), it can cause parsing errors or failed string comparisons. |
| En Quad                          | U+2000 | A space character, typically wider than a standard space. Problematic if used instead of U+0020 in code syntax, causing parsing errors or unexpected string behavior.                                                                                                                                                             |
| Em Quad                          | U+2001 | A space character, typically the width of the point size (wider than En Quad). Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                              |
| En Space                         | U+2002 | A space character, typically half an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                    |
| Em Space                         | U+2003 | A space character, typically equal to the point size. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                       |
| Three-Per-Em Space               | U+2004 | A space character, one-third of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                      |
| Four-Per-Em Space                | U+2005 | A space character, one-fourth of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                     |
| Six-Per-Em Space                 | U+2006 | A space character, one-sixth of an em. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                      |
| Figure Space                     | U+2007 | A space character with the width of a digit. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                |
| Punctuation Space                | U+2008 | A space character with the width of a narrow punctuation mark. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                              |
| Thin Space                       | U+2009 | A narrow space character. Problematic if used instead of U+0020 in code syntax.                                                                                                                                                                                                                                                   |
| Hair Space                       | U+200A | A very narrow space character. Problematic if used instead of U+0020 in code syntax, can be almost invisible.                                                                                                                                                                                                                     |
| Narrow No-Break Space            | U+202F | A narrow version of the No-Break Space. Problematic for the same reasons as U+00A0 if used in code syntax.                                                                                                                                                                                                                        |
| Medium Mathematical Space        | U+205F | A space character used in mathematical formulae, typically 4/18 em. Problematic if used instead of U+0020 in general code syntax.                                                                                                                                                                                                 |
| Ideographic Space                | U+3000 | A wide space character used with East Asian scripts, typically the width of one CJK character. Visually distinct but will cause parsing errors if used as a standard space in code.                                                                                                                                               |
| Line Separator                   | U+2028 | Intended to unambiguously separate lines. Some languages (like JavaScript) treat it as a newline, but many others or tools might not, or might treat it as invalid whitespace. Can cause inconsistent line ending behavior or syntax errors if invisible in an editor.                                                            |
| Paragraph Separator              | U+2029 | Intended to unambiguously separate paragraphs. Similar issues to Line Separator (U+2028); may be treated as a newline or invalid character depending on the context.                                                                                                                                                              |
| Left-to-Right Mark (LRM)         | U+200E | Invisible character affecting bidirectional text rendering. Can cause confusion or errors if copied into string literals or comments, or if it affects the interpretation of surrounding tokens, although less likely to break syntax directly compared to other bidi controls.                                                   |
| Right-to-Left Mark (RLM)         | U+200F | Invisible character affecting bidirectional text rendering. Similar issues to LRM.                                                                                                                                                                                                                                                |
| Left-to-Right Embedding (LRE)    | U+202A | Forces subsequent text to be treated as left-to-right. Can drastically alter the visual appearance and logical order of code or string literals if copied, leading to confusion and errors. Must be paired with PDF (U+202C).                                                                                                     |
| Right-to-Left Embedding (RLE)    | U+202B | Forces subsequent text to be treated as right-to-left. Can drastically alter code or string literals. Must be paired with PDF (U+202C).                                                                                                                                                                                           |
| Pop Directional Formatting (PDF) | U+202C | Terminates explicit directional embeddings (LRE, RLE) or overrides (LRO, RLO). If mismatched or unexpected, can lead to incorrect text rendering.                                                                                                                                                                                 |
| Left-to-Right Override (LRO)     | U+202D | Forces all subsequent characters to be treated as strong left-to-right, ignoring their inherent properties. Can make code unreadable or malicious. Must be paired with PDF (U+202C).                                                                                                                                              |
| Right-to-Left Override (RLO)     | U+202E | Forces all subsequent characters to be treated as strong right-to-left. Infamous for reversing text segments, making code appear jumbled or obfuscated, leading to severe readability and debugging issues. Must be paired with PDF (U+202C).                                                                                             |
| Left-to-Right Isolate (LRI)      | U+2066 | Isolates a span of text for left-to-right display, protecting it from surrounding bidirectional context. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069).                                                                                                                             |
| Right-to-Left Isolate (RLI)      | U+2067 | Isolates a span of text for right-to-left display. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069).                                                                                                                                                                                  |
| First Strong Isolate (FSI)       | U+2068 | Isolates a span of text, with directionality determined by the first strong directional character within it. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069).                                                                                                                            |
| Pop Directional Isolate (PDI)    | U+2069 | Terminates explicit directional isolates (LRI, RLI, FSI). If mismatched or unexpected, can lead to incorrect text rendering.                                                                                                                                                                                                       |
| Next Line (NEL)                  | U+0085 | A C1 control character intended as a newline. If not consistently handled as a standard newline (LF or CRLF) by tools, parsers, or languages, it can cause unexpected line breaks or syntax errors.                                                                                                                                 |

#### Medium Problems

Characters that can cause issues such as unexpected string behavior, formatting issues, or errors with specific tools/parsers. They might be less common in web-copied code or their effects slightly less severe or more context-dependent than higher categories.

| Name                                           | Hex    | Description                                                                                                                                                                                                                                                           |
| :--------------------------------------------- | :----: | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Soft Hyphen (SHY)                              | U+00AD | Indicates an optional hyphenation point. Usually invisible, but becomes visible if a line breaks at that point. Can affect string length, comparisons, or tokenization if present within identifiers or string literals unexpectedly.                                      |
| Zero Width Non-Joiner (ZWNJ)                   | U+200C | Prevents adjacent characters from forming a ligature. Invisible. If accidentally inserted within an identifier or string, it can alter its interpretation, break ligatures in comments/strings where intended, or affect text processing.                                 |
| Zero Width Joiner (ZWJ)                        | U+200D | Causes adjacent characters to join or form a ligature (common in complex scripts and emoji sequences). Invisible. Can cause similar problems to ZWNJ if misplaced in code, altering token meaning or string comparisons.                                                   |
| Object Replacement Character                   | U+FFFC | Placeholder for an object (e.g., an image) embedded in text. Should not appear in source code. If copied, it's essentially garbage data that could cause parser errors or unexpected behavior.                                                                            |
| Null (NUL)                                     | U+0000 | Often used as a string terminator in C-like languages. If embedded within a string or code, it can truncate data prematurely or cause unexpected behavior in parsers and tools.                                                                                         |
| Start of Heading (SOH)                         | U+0001 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Start of Text (STX)                            | U+0002 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| End of Text (ETX)                              | U+0003 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| End of Transmission (EOT)                      | U+0004 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Enquiry (ENQ)                                  | U+0005 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Acknowledge (ACK)                              | U+0006 | C0 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Bell (BEL)                                     | U+0007 | C0 control character. Causes an audible or visual alert. Problematic if embedded in code or output.                                                                                                                                                                       |
| Backspace (BS)                                 | U+0008 | C0 control character. May cause overwriting of previous character in some terminal displays; problematic in string literals or code.                                                                                                                                      |
| Vertical Tabulation (VT)                       | U+000B | C0 control character. Treated as whitespace by some parsers, but non-standard and can cause formatting or parsing issues.                                                                                                                                                  |
| Form Feed (FF)                                 | U+000C | C0 control character. Often used as a page break. Can be treated as whitespace but is non-standard in most code contexts and can break formatting or parsing.                                                                                                             |
| Shift Out (SO)                                 | U+000E | C0 control character. Used for character set switching; highly problematic in modern Unicode-based code.                                                                                                                                                                    |
| Shift In (SI)                                  | U+000F | C0 control character. Used for character set switching; highly problematic.                                                                                                                                                                                                 |
| Data Link Escape (DLE)                         | U+0010 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Device Control One (DC1/XON)                   | U+0011 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Device Control Two (DC2)                       | U+0012 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Device Control Three (DC3/XOFF)                | U+0013 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Device Control Four (DC4)                      | U+0014 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Negative Acknowledge (NAK)                     | U+0015 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Synchronous Idle (SYN)                         | U+0016 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| End of Transmission Block (ETB)                | U+0017 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Cancel (CAN)                                   | U+0018 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| End of Medium (EM)                             | U+0019 | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Substitute (SUB)                               | U+001A | C0 control character. Often used to indicate an error or unrepresentable character. Problematic if embedded.                                                                                                                                                                |
| Escape (ESC)                                   | U+001B | C0 control character. Used to introduce escape sequences (e.g., ANSI color codes). Can wreak havoc if pasted into code not expecting it.                                                                                                                                    |
| File Separator (FS)                            | U+001C | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Group Separator (GS)                           | U+001D | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Record Separator (RS)                          | U+001E | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Unit Separator (US)                            | U+001F | C0 control character. Problematic in source code.                                                                                                                                                                                                                           |
| Delete (DEL)                                   | U+007F | Control character. Problematic if present in source code.                                                                                                                                                                                                                   |
| Padding Character (PAD)                        | U+0080 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| High Octet Preset (HOP)                        | U+0081 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Break Permitted Here (BPH)                     | U+0082 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| No Break Here (NBH)                            | U+0083 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Index (IND)                                    | U+0084 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Start of Guarded Area (SSA)                    | U+0086 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| End of Guarded Area (ESA)                      | U+0087 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Start of String (SOS)                          | U+0088 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Single Character Introducer / Privacy Message (SCI/PM) | U+0089 | C1 control character (Unicode lists U+009A as SCI, U+0089 as Character Tabulation With Justification). Problematic.                                                                                                                                                   |
| Application Program Command (APC)              | U+009F | C1 control character. Generally unexpected and problematic in source code. (Note: U+009F is APC, some charts differ for 0x80-0x9F range, using Unicode standard here).                                                                                                      |
| Device Control String (DCS)                    | U+0090 | C1 control character. Generally unexpected and problematic in source code.                                                                                                                                                                                                  |
| Partial Line Forward (PLD)                     | U+008B | C1 control character (Unicode name: PARTIAL LINE DOWN). Problematic.                                                                                                                                                                                                        |
| Partial Line Backward (PLU)                    | U+008C | C1 control character (Unicode name: PARTIAL LINE UP). Problematic.                                                                                                                                                                                                          |
| Reverse Line Feed (RI)                         | U+008D | C1 control character. Problematic.                                                                                                                                                                                                                                          |
| Single Shift Two (SS2)                         | U+008E | C1 control character. Problematic for character set shifting.                                                                                                                                                                                                               |
| Single Shift Three (SS3)                       | U+008F | C1 control character. Problematic for character set shifting.                                                                                                                                                                                                               |
| Start of Protected Area (SPA)                  | U+0096 | C1 control character (Unicode name: SET TRANSMIT STATE). Problematic.                                                                                                                                                                                                       |
| End of Protected Area (EPA)                    | U+0097 | C1 control character (Unicode name: CANCEL CHARACTER). Problematic.                                                                                                                                                                                                         |
| String Terminator (ST)                         | U+009C | C1 control character (Unicode lists as STRING TERMINATOR). Problematic.                                                                                                                                                                                                     |
| Cancel Character (CCH)                         | U+0094 | C1 control character. Problematic.                                                                                                                                                                                                                                          |
| Message Waiting (MW)                           | U+0095 | C1 control character. Problematic.                                                                                                                                                                                                                                          |
| Control Sequence Introducer (CSI)              | U+009B | C1 control character. Introduces control sequences, highly problematic if raw in code.                                                                                                                                                                                      |
| Private Use One (PU1)                          | U+0091 | C1 control character. Problematic.                                                                                                                                                                                                                                          |
| Private Use Two (PU2)                          | U+0092 | C1 control character. Problematic.                                                                                                                                                                                                                                          |
| Set Transmit State (STS)                       | U+0093 | C1 control character (Unicode lists as DEVICE CONTROL STRING). Problematic.                                                                                                                                                                                                 |
| Single Graphic Character Introducer (SGCI)     | U+009A | C1 control character (Unicode name: SINGLE CHARACTER INTRODUCER). Problematic.                                                                                                                                                                                              |
| Operating System Command (OSC)                 | U+009D | C1 control character. Used for terminal commands, problematic.                                                                                                                                                                                                              |
| Privacy Message (PM)                           | U+009E | C1 control character. Problematic.                                                                                                                                                                                                                                          |

#### Low Problems

Characters that are less likely to cause critical code errors but can lead to minor formatting inconsistencies, unexpected behavior in highly specific contexts, or represent 'corrupted' or unusual data if copied into code. Their invisibility might still cause confusion.

| Name                             | Hex             | Description                                                                                                                                                                                                                                                           |
| :------------------------------- | :-------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Function Application             | U+2061          | Invisible character used in mathematical notation. Unlikely in code, but could cause issues if accidentally present.                                                                                                                                                |
| Invisible Times                  | U+2062          | Invisible character used in mathematical notation for multiplication. Unlikely in code.                                                                                                                                                                              |
| Invisible Separator / Invisible Comma | U+2063          | Invisible character used in mathematical notation as a separator. Unlikely in code.                                                                                                                                                                                    |
| Invisible Plus                   | U+2064          | Invisible character used in mathematical notation. Unlikely in code.                                                                                                                                                                                                   |
| Variation Selector 1 through 16  | U+FE00 - U+FE0F | Invisible characters that select a specific glyph variant for the preceding character. If isolated or attached to an ASCII character in code, behavior is undefined or may cause subtle rendering differences. Unlikely to break parsing but can be confusing.         |
| Interlinear Annotation Anchor    | U+FFF9          | Marks the start of annotated text. Invisible and highly specialized; problematic if found in code.                                                                                                                                                                   |
| Interlinear Annotation Separator | U+FFFA          | Separates annotated text from the annotation. Invisible and specialized; problematic.                                                                                                                                                                                  |
| Interlinear Annotation Terminator| U+FFFB          | Marks the end of annotated text. Invisible and specialized; problematic.                                                                                                                                                                                               |
| Replacement Character            | U+FFFD          | Usually visible (often as a question mark in a diamond �), indicating a character that could not be decoded. While visible, its presence in copied code signifies data corruption or encoding issues and needs to be addressed, otherwise it might be treated as a literal character causing unexpected behavior. |

## 3. Scannable File Types

The application is designed to scan a wide variety of file types. This section outlines the major categories and common examples of file extensions that are processed. For a complete and detailed list of all supported file types, extensions, and their specific classifications, please refer to the `FileTypesSummary.json` asset located in the `assets/` directory of the project.

The file types are categorized based on their common usage and potential risks.

### Executable and System Files

Files that can directly execute code or are critical system components.
*Examples: .exe, .dll, .sys, .so, .elf, .bat, .sh, .ps1, .jar, .com, .app, .cpl, .scr*
(See `FileTypesSummary.json` under "Executable and System Files" for a full list.)

### Scripting Files

Files containing code interpreted by a scripting engine.
*Examples: .js, .py, .rb, .pl, .php, .vbs, .wsf, .lua, .tcl*
(See `FileTypesSummary.json` under "Scripting Files" for a full list.)

### Document and Office Formats

Files created by office suites and document processing software. These can contain macros or embedded objects.
*Examples: .doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf, .rtf, .odt, .ods, .odp, .wpd*
(See `FileTypesSummary.json` under "Document Formats" for a full list.)

### Archive and Compressed Files

Files that bundle or compress other files. Archives can obscure malicious content.
*Examples: .zip, .rar, .7z, .tar, .gz, .bz2, .xz, .cab, .iso, .img, .arj, .lzh*
(See `FileTypesSummary.json` under "Archive and Compressed Files" for a full list.)

### Image and Multimedia Files

Image, audio, and video files. Some formats can have vulnerabilities or carry metadata.
*Examples: .jpg, .png, .gif, .bmp, .tiff, .svg, .webp, .mp3, .wav, .mp4, .avi, .mkv, .mov*
(See `FileTypesSummary.json` under "Image Formats" and "Multimedia Formats" for full lists.)

### Web-related Files

Files commonly used in web development and browsing.
*Examples: .html, .htm, .css, .json, .xml, .asp, .aspx, .jsp, .xhtml, .rss, .atom*
(See `FileTypesSummary.json` under "Web Development Files" for a full list.)

### Source Code and Development Files

Files containing source code for various programming languages, project files, and development artifacts.
This is a broad category. Key sub-groups include:

- **Programming Languages:** .c, .cpp, .java, .cs, .go, .swift, .kt, .rs, .scala, .m, .pas, .f, .f90, .ada, .cob
- **Build Systems & Package Management:** Makefile, CMakeLists.txt, .gradle, .pom.xml, package.json, Gemfile, requirements.txt, .sln, .csproj, .vcproj
- **Version Control & Patches:** .patch, .diff, .gitattributes, .gitignore, .svn
- **IDE/Editor Specific:** .vscode/, .idea/, .project, .classpath, .sublime-project
- **Other Development Files:** .h, .hpp, .lib, .a, .def, .idl, .proto, .sql
(For an exhaustive list, please consult `FileTypesSummary.json` under "Source Code and Development Files" and its subcategories.)

### Configuration and Data Files

Files storing application settings, structured data, or logs.
*Examples: .ini, .conf, .cfg, .yaml, .yml, .toml, .log, .dat, .bak, .tmp, .env, .properties, .reg, .plist*
(See `FileTypesSummary.json` under "Configuration and Data Files" for a full list.)

### Database Files

Files used by database systems to store data.
*Examples: .db, .sqlite, .sqlite3, .mdb, .accdb, .dbf, .sqlitedb, .myd, .frm*
(See `FileTypesSummary.json` under "Database Files" for a full list.)

### Virtualization and Container Files

Files related to virtual machines, disk images, and container technologies.
*Examples: .vmdk, .vdi, .vhd, .vhdx, .ova, .ovf, .qcow2, .dockerfile, .ova, .box*
(See `FileTypesSummary.json` under "Virtualization and Container Files" for a full list.)

### Font Files

Files defining typefaces.
*Examples: .ttf, .otf, .woff, .woff2, .eot*
(See `FileTypesSummary.json` under "Font Files" for a full list.)

### Email Files

Files storing email messages or mailboxes.
*Examples: .eml, .msg, .pst, .mbox, .emlx*
(See `FileTypesSummary.json` under "Email Files" for a full list.)

### CAD and 3D Modeling Files

Files used in Computer-Aided Design and 3D modeling.
*Examples: .dwg, .dxf, .stl, .obj, .fbx, .3ds, .blend, .skp*
(See `FileTypesSummary.json` under "CAD and 3D Modeling Files" for a full list.)

### GIS (Geographic Information System) Files

Files used for storing and managing geospatial data.
*Examples: .shp, .shx, .gpx, .kml, .kmz, .geojson, .tab*
(See `FileTypesSummary.json` under "GIS Files" for a full list.)

### Financial Data Files

Files containing financial data, often from accounting or spreadsheet software.
*Examples: .qfx, .ofx, .qif, .tax2023, .money*
(See `FileTypesSummary.json` under "Financial Data Files" for a full list.)

### Scientific Data Formats

Files used in scientific research and data analysis.
*Examples: .fits, .cdf, .nc, .h5, .fasta, .pdb*
(See `FileTypesSummary.json` under "Scientific Data Formats" for a full list.)

### Ebook Formats

Files for storing electronic books.
*Examples: .epub, .mobi, .azw, .azw3, .iba*
(See `FileTypesSummary.json` under "Ebook Formats" for a full list.)

### Security Related Files

Files related to security certificates, keys, and cryptographic operations.
*Examples: .pem, .crt, .cer, .key, .pfx, .p12, .asc, .gpg, .kdbx*
(See `FileTypesSummary.json` under "Security Related Files" for a full list.)

### Miscellaneous Files

Other file types that may be relevant for scanning.
*Examples: .torrent, .url, .pif, .lnk, .diagcab, .ics, .msi, .msp, .mst*
(See `FileTypesSummary.json` under "Miscellaneous Files" and "Named Files" for a full list.)

---
*Note: The presence of a file extension in this list does not automatically imply it is malicious, only that it is a type of file the application can analyze. The actual risk depends on the file's content and context.*
