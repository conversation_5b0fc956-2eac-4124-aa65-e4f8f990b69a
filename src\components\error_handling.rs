use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AppError {
    pub id: String,
    pub message: String,
    pub error_type: ErrorCategory,
    pub timestamp: String,
    pub file_path: Option<String>,
    pub operation: Option<String>,
    pub recoverable: bool,
    pub details: Option<String>,
    pub suggestion: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ErrorCategory {
    FileAccess,
    Analysis,
    Network,
    Validation,
    System,
    UserInput,
    Configuration,
}

impl ErrorCategory {
    pub fn icon(&self) -> &'static str {
        match self {
            ErrorCategory::FileAccess => "📁",
            ErrorCategory::Analysis => "🔍",
            ErrorCategory::Network => "🌐",
            ErrorCategory::Validation => "⚠️",
            ErrorCategory::System => "🔧",
            ErrorCategory::UserInput => "👤",
            ErrorCategory::Configuration => "⚙️",
        }
    }

    pub fn color_class(&self) -> &'static str {
        match self {
            ErrorCategory::FileAccess => "border-orange-500 bg-orange-50 dark:bg-orange-900/20",
            ErrorCategory::Analysis => "border-blue-500 bg-blue-50 dark:bg-blue-900/20",
            ErrorCategory::Network => "border-purple-500 bg-purple-50 dark:bg-purple-900/20",
            ErrorCategory::Validation => "border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20",
            ErrorCategory::System => "border-red-500 bg-red-50 dark:bg-red-900/20",
            ErrorCategory::UserInput => "border-green-500 bg-green-50 dark:bg-green-900/20",
            ErrorCategory::Configuration => "border-gray-500 bg-gray-50 dark:bg-gray-900/20",
        }
    }

    pub fn text_color_class(&self) -> &'static str {
        match self {
            ErrorCategory::FileAccess => "text-orange-700 dark:text-orange-300",
            ErrorCategory::Analysis => "text-blue-700 dark:text-blue-300",
            ErrorCategory::Network => "text-purple-700 dark:text-purple-300",
            ErrorCategory::Validation => "text-yellow-700 dark:text-yellow-300",
            ErrorCategory::System => "text-red-700 dark:text-red-300",
            ErrorCategory::UserInput => "text-green-700 dark:text-green-300",
            ErrorCategory::Configuration => "text-gray-700 dark:text-gray-300",
        }
    }

    pub fn title(&self) -> &'static str {
        match self {
            ErrorCategory::FileAccess => "File Access Error",
            ErrorCategory::Analysis => "Analysis Error",
            ErrorCategory::Network => "Network Error",
            ErrorCategory::Validation => "Validation Error",
            ErrorCategory::System => "System Error",
            ErrorCategory::UserInput => "Input Error",
            ErrorCategory::Configuration => "Configuration Error",
        }
    }
}

impl AppError {
    pub fn new(
        message: String,
        error_type: ErrorCategory,
        operation: Option<String>,
        file_path: Option<String>,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            message,
            error_type,
            timestamp: web_sys::js_sys::Date::new_0().to_iso_string().as_string().unwrap_or_default(),
            file_path,
            operation,
            recoverable: true,
            details: None,
            suggestion: None,
        }
    }

    pub fn with_details(mut self, details: String) -> Self {
        self.details = Some(details);
        self
    }

    pub fn with_suggestion(mut self, suggestion: String) -> Self {
        self.suggestion = Some(suggestion);
        self
    }

    #[allow(dead_code)]
    pub fn non_recoverable(mut self) -> Self {
        self.recoverable = false;
        self
    }

    // Common error constructors
    #[allow(dead_code)]
    pub fn file_not_found(file_path: String) -> Self {
        Self::new(
            format!("File not found: {}", file_path),
            ErrorCategory::FileAccess,
            Some("File Access".to_string()),
            Some(file_path),
        ).with_suggestion("Please check if the file exists and you have permission to access it.".to_string())
    }

    #[allow(dead_code)]
    pub fn file_permission_denied(file_path: String) -> Self {
        Self::new(
            format!("Permission denied accessing: {}", file_path),
            ErrorCategory::FileAccess,
            Some("File Access".to_string()),
            Some(file_path),
        ).with_suggestion("Please check file permissions or run the application as administrator.".to_string())
    }

    pub fn analysis_failed(details: String) -> Self {
        Self::new(
            "Analysis operation failed".to_string(),
            ErrorCategory::Analysis,
            Some("Character Analysis".to_string()),
            None,
        ).with_details(details)
        .with_suggestion("Try reducing the input size or checking for unsupported characters.".to_string())
    }

    pub fn invalid_input(input_type: String) -> Self {
        Self::new(
            format!("Invalid {} provided", input_type),
            ErrorCategory::UserInput,
            Some("Input Validation".to_string()),
            None,
        ).with_suggestion("Please check your input and try again.".to_string())
    }

    #[allow(dead_code)]
    pub fn network_error(details: String) -> Self {
        Self::new(
            "Network operation failed".to_string(),
            ErrorCategory::Network,
            Some("Network Request".to_string()),
            None,
        ).with_details(details)
        .with_suggestion("Please check your internet connection and try again.".to_string())
    }
}

#[derive(Clone)]
pub struct ErrorManager {
    errors: WriteSignal<VecDeque<AppError>>,
    read_errors: ReadSignal<VecDeque<AppError>>,
}

impl ErrorManager {
    pub fn new() -> (ReadSignal<VecDeque<AppError>>, Self) {
        let (errors_read, errors_write) = create_signal(VecDeque::new());
        (errors_read, Self { errors: errors_write, read_errors: errors_read })
    }

    pub fn add_error(&self, error: AppError) {
        self.errors.update(|errors| {
            errors.push_back(error);
            
            // Keep only the last 100 errors to prevent memory issues
            if errors.len() > 100 {
                errors.pop_front();
            }
        });
    }

    pub fn remove_error(&self, error_id: &str) {
        self.errors.update(|errors| {
            errors.retain(|e| e.id != error_id);
        });
    }

    pub fn clear_all(&self) {
        self.errors.update(|errors| {
            errors.clear();
        });
    }

    #[allow(dead_code)]
    pub fn get_recent_errors(&self, count: usize) -> Vec<AppError> {
        let current_errors = self.read_errors.get();
        current_errors.iter().rev().take(count).cloned().collect()
    }
}

#[component]
fn ErrorItem<F>(
    error: AppError,
    on_remove: F,
) -> impl IntoView 
where
    F: Fn(String) + 'static,
{
    let error_id = error.id.clone();
    let error_operation = error.operation.clone();
    let error_file_path = error.file_path.clone();
    let error_suggestion = error.suggestion.clone();
    let error_details = error.details.clone();
    
    view! {
        <div class=format!(
            "border-l-4 p-4 rounded-lg shadow-lg backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 {}",
            error.error_type.color_class()
        )>
            <div class="flex items-start justify-between">
                <div class="flex items-start gap-3 flex-1">
                    <span class="text-lg mt-0.5">{error.error_type.icon()}</span>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-2 mb-1">
                            <h4 class=format!(
                                "font-semibold text-sm {}",
                                error.error_type.text_color_class()
                            )>
                                {error.error_type.title()}
                            </h4>
                            {error_operation.map(|op| view! {
                                <span class="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                                    {op}
                                </span>
                            })}
                        </div>
                        
                        <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">
                            {error.message}
                        </p>
                        
                        {error_file_path.map(|path| view! {
                            <p class="text-xs font-mono text-gray-500 dark:text-gray-400 mb-2 break-all">
                                {path}
                            </p>
                        })}
                        
                        {error_suggestion.map(|suggestion| view! {
                            <div class="mt-2 p-2 bg-white/50 dark:bg-gray-700/50 rounded text-xs">
                                <strong>"Suggestion: "</strong>
                                {suggestion}
                            </div>
                        })}
                        
                        {error_details.map(|details| view! {
                            <details class="mt-2">
                                <summary class="text-xs cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                    "Show Details"
                                </summary>
                                <div class="mt-1 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono">
                                    {details}
                                </div>
                            </details>
                        })}
                    </div>
                </div>
                
                <button
                    class="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    on:click=move |_| {
                        on_remove(error_id.clone());
                    }
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                {error.timestamp}
            </div>
        </div>
    }
}

#[component]
pub fn ErrorDisplay(
    errors: ReadSignal<VecDeque<AppError>>,
    error_manager: ErrorManager,
    max_visible: Option<usize>,
) -> impl IntoView {
    let max_errors = max_visible.unwrap_or(5);
    let (show_all, set_show_all) = create_signal(false);

    let visible_errors = create_memo(move |_| {
        let all_errors: Vec<AppError> = errors.get().iter().rev().cloned().collect();
        if show_all.get() {
            all_errors
        } else {
            all_errors.into_iter().take(max_errors).collect()
        }
    });

    let has_more_errors = create_memo(move |_| {
        errors.get().len() > max_errors
    });

    // Store error_manager to avoid move issues in closures
    let error_manager = store_value(error_manager);

    view! {
        <Show when=move || !errors.get().is_empty()>
            <div class="fixed top-4 right-4 z-40 max-w-md space-y-2">
                <For
                    each=move || visible_errors.get()
                    key=|error| error.id.clone()
                    children=move |error: AppError| {
                        let error_manager = error_manager.get_value();
                        let _error_id = error.id.clone();
                        
                        view! {
                            <ErrorItem 
                                error=error
                                on_remove=move |id: String| {
                                    error_manager.remove_error(&id);
                                }
                            />
                        }
                    }
                />
                
                <Show when=move || has_more_errors.get() && !show_all.get()>
                    <button
                        class="w-full p-2 text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded transition-colors"
                        on:click=move |_| set_show_all.set(true)
                    >
                        {move || format!("Show {} more errors", errors.get().len() - max_errors)}
                    </button>
                </Show>
                
                <Show when=move || { errors.get().len() > 1 }>
                    <div class="flex gap-2">
                        <Show when=move || show_all.get() && has_more_errors.get()>
                            <button
                                class="flex-1 p-2 text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded transition-colors"
                                on:click=move |_| set_show_all.set(false)
                            >
                                "Show Less"
                            </button>
                        </Show>
                        
                        <button
                            class="flex-1 p-2 text-sm bg-red-600 text-white hover:bg-red-700 rounded transition-colors"
                            on:click=move |_| {
                                error_manager.get_value().clear_all();
                                set_show_all.set(false);
                            }
                        >
                            "Clear All"
                        </button>
                    </div>
                </Show>
            </div>
        </Show>
    }
}

// Toast notification component for quick messages
#[component]
pub fn Toast(
    message: String,
    toast_type: ToastType,
    duration: Option<u32>, // Duration in milliseconds
    on_close: Callback<()>,
) -> impl IntoView {
    let duration_ms = duration.unwrap_or(5000);
    
    // Auto-close after duration
    create_effect(move |_| {
        if duration_ms > 0 {
            gloo_timers::callback::Timeout::new(duration_ms, move || {
                on_close.call(());
            }).forget();
        }
    });

    view! {
        <div class=format!(
            "fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg backdrop-blur-sm max-w-sm {}",
            toast_type.bg_class()
        )>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <span class="text-lg">{toast_type.icon()}</span>
                    <p class=format!("text-sm font-medium {}", toast_type.text_class())>
                        {message}
                    </p>
                </div>
                <button
                    class=format!("ml-2 p-1 hover:bg-black/10 rounded {}", toast_type.text_class())
                    on:click=move |_| on_close.call(())
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    }
}

#[derive(Clone)]
#[allow(dead_code)]
pub enum ToastType {
    Success,
    Warning,
    Error,
    Info,
}

impl ToastType {
    fn icon(&self) -> &'static str {
        match self {
            ToastType::Success => "✅",
            ToastType::Warning => "⚠️",
            ToastType::Error => "❌",
            ToastType::Info => "ℹ️",
        }
    }

    fn bg_class(&self) -> &'static str {
        match self {
            ToastType::Success => "bg-green-100 dark:bg-green-900/50 border border-green-300 dark:border-green-700",
            ToastType::Warning => "bg-yellow-100 dark:bg-yellow-900/50 border border-yellow-300 dark:border-yellow-700",
            ToastType::Error => "bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700",
            ToastType::Info => "bg-blue-100 dark:bg-blue-900/50 border border-blue-300 dark:border-blue-700",
        }
    }

    fn text_class(&self) -> &'static str {
        match self {
            ToastType::Success => "text-green-800 dark:text-green-200",
            ToastType::Warning => "text-yellow-800 dark:text-yellow-200",
            ToastType::Error => "text-red-800 dark:text-red-200",
            ToastType::Info => "text-blue-800 dark:text-blue-200",
        }
    }
}
