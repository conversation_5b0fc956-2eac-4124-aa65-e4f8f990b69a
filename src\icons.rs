use leptos::*;

/// Search icon - custom magnifying glass
#[component]
pub fn SearchIcon(#[prop(default = "icon-lg")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <circle
                cx="10"
                cy="10"
                r="7"
                stroke="currentColor"
                stroke-width="2.5"
                fill="none"
                stroke-linecap="round"
            />
            <path
                d="m16.5 16.5 4.5 4.5"
                stroke="currentColor"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    }
}

/// Chart/Analytics icon - bar chart
#[component]
pub fn ChartIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
        </svg>
    }
}

/// Security/Shield icon
#[component]
pub fn ShieldIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Target/Patterns icon
#[component]
pub fn TargetIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Document/Encoding icon
#[component]
pub fn DocumentIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Save/Export icon
#[component]
pub fn SaveIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z"></path>
        </svg>
    }
}

/// Check/Success icon
#[component]
pub fn CheckIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Eye/View icon - alternative to search
#[component]
pub fn EyeIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
            <path
                fill-rule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Settings/Cog icon
#[component]
pub fn CogIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Information icon
#[component]
pub fn InfoIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Terminal/CLI icon
#[component]
pub fn TerminalIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Close/X icon
#[component]
pub fn CloseIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Filter/Exclusion icon
#[component]
pub fn FilterIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path
                fill-rule="evenodd"
                d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                clip-rule="evenodd"
            ></path>
        </svg>
    }
}

/// Adjustments/Tune icon
#[component]
pub fn AdjustmentsIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
        </svg>
    }
}

/// Book/Documentation icon
#[component]
pub fn BookIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
        </svg>
    }
}

/// Theme toggle icon - sun/moon hybrid
#[component]
pub fn ThemeToggleIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <g stroke="currentColor" stroke-width="2" stroke-linecap="round">
                <line x1="12" y1="1" x2="12" y2="3" />
                <line x1="12" y1="21" x2="12" y2="23" />
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
                <line x1="1" y1="12" x2="3" y2="12" />
                <line x1="21" y1="12" x2="23" y2="12" />
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
            </g>
            <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2" fill="none" />
            <path d="M12 7a5 5 0 0 0 0 10 5 5 0 0 1 0-10z" fill="currentColor" opacity="0.4" />
        </svg>
    }
}

/// Bad Character Scanner Logo
#[component]
pub fn BCSLogo(#[prop(default = "icon-xl")] class: &'static str) -> impl IntoView {
    view! {
        <svg class=class fill="none" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M16 2L4 8v8c0 7.732 5.943 14.081 13.5 14.916.344.038.693.038 1 0C26.057 30.081 28 23.732 28 16V8L16 2z"
                fill="currentColor"
                opacity="0.1"
                stroke="currentColor"
                stroke-width="1.5"
            />
            <circle cx="14" cy="14" r="5" stroke="currentColor" stroke-width="2" fill="none" />
            <path d="M18 18l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
            <circle cx="9" cy="9" r="1" fill="currentColor" opacity="0.6" />
            <circle cx="23" cy="9" r="1" fill="currentColor" opacity="0.6" />
            <circle cx="9" cy="23" r="1" fill="currentColor" opacity="0.6" />
            <circle cx="23" cy="23" r="1" fill="currentColor" opacity="0.6" />
        </svg>
    }
}
