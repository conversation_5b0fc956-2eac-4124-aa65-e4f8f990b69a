use leptos::*;

/// Search icon - custom magnifying glass
#[component]
pub fn SearchIcon(#[prop(default = "icon-lg")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_009.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Search Icon" />
    }
}

/// Chart/Analytics icon - bar chart
#[component]
pub fn ChartIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_010.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Chart Icon" />
    }
}

/// Security/Shield icon
#[component]
pub fn ShieldIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_011.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Shield Icon" />
    }
}

/// Target/Patterns icon
#[component]
pub fn TargetIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_012.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Target Icon" />
    }
}

/// Document/Encoding icon
#[component]
pub fn DocumentIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_013.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Document Icon" />
    }
}

/// Save/Export icon
#[component]
pub fn SaveIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_014.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Save Icon" />
    }
}

/// Check/Success icon
#[component]
pub fn CheckIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_015.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Check Icon" />
    }
}

/// Eye/View icon - alternative to search
#[component]
pub fn EyeIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_016.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Eye Icon" />
    }
}

/// Settings/Cog icon
#[component]
pub fn CogIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_017.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Cog Icon" />
    }
}

/// Information icon
#[component]
pub fn InfoIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_018.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Info Icon" />
    }
}

/// Terminal/CLI icon
#[component]
pub fn TerminalIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_019.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Terminal Icon" />
    }
}

/// Close/X icon
#[component]
pub fn CloseIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_020.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Close Icon" />
    }
}

/// Filter/Exclusion icon
#[component]
pub fn FilterIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_021.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Filter Icon" />
    }
}

/// Adjustments/Tune icon
#[component]
pub fn AdjustmentsIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_022.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Adjustments Icon" />
    }
}

/// Book/Documentation icon
#[component]
pub fn BookIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_023.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Book Icon" />
    }
}

/// Theme toggle icon - sun/moon hybrid
#[component]
pub fn ThemeToggleIcon(#[prop(default = "icon-md")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_024.png" class="icon-40" style="width:40px;height:40px;object-fit:contain;" alt="Theme Toggle Icon" />
    }
}

/// Bad Character Scanner Logo
#[component]
pub fn BCSLogo(#[prop(default = "icon-xl")] class: &'static str) -> impl IntoView {
    view! {
        <img src="assets/images/ui_images_40x40/uiimg_025.ico" class="icon-xl" style="width:40px;height:40px;object-fit:contain;" alt="BCS Logo" />
    }
}
