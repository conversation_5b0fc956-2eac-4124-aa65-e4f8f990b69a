#!/usr/bin/env powershell
# Fix "Create Cleaned Copy" functionality (CODEBASE-6)

Write-Host "🧹 FIXING CREATE CLEANED COPY FUNCTIONALITY (CODEBASE-6)" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent

# Check files that need fixing
$frontend_file = Join-Path $project_root "src\lib.rs"
$backend_file = Join-Path $project_root "src-tauri\src\main_module.rs"

Write-Host "📋 Current Issues Analysis:" -ForegroundColor Yellow
Write-Host "1. Missing Progress Indication - No loading bar during cleaning operation" -ForegroundColor White
Write-Host "2. Bad Characters Not Removed - Cleaned copy still contains suspicious characters" -ForegroundColor White

# Check frontend for progress indication
if (Test-Path $frontend_file) {
    $frontend_content = Get-Content $frontend_file -Raw
    
    Write-Host "`n🔍 Frontend Analysis:" -ForegroundColor Yellow
    
    if ($frontend_content -match "create_cleaned_copy.*loading") {
        Write-Host "✅ Loading state management found" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: Loading state for create_cleaned_copy" -ForegroundColor Red
    }
    
    if ($frontend_content -match "progress.*cleaning") {
        Write-Host "✅ Progress indication found" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: Progress bar for cleaning operation" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Frontend file not found" -ForegroundColor Red
}

# Check backend for cleaning logic
if (Test-Path $backend_file) {
    $backend_content = Get-Content $backend_file -Raw
    
    Write-Host "`n🔍 Backend Analysis:" -ForegroundColor Yellow
    
    if ($backend_content -match "create_cleaned_copy.*command") {
        Write-Host "✅ Backend command found" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: Backend create_cleaned_copy command" -ForegroundColor Red
    }
    
    if ($backend_content -match "remove.*suspicious.*characters") {
        Write-Host "✅ Character removal logic found" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: Character removal implementation" -ForegroundColor Red
    }
    
    if ($backend_content -match "progress.*emit") {
        Write-Host "✅ Progress emission found" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: Progress updates during cleaning" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Backend file not found" -ForegroundColor Red
}

Write-Host "`n🛠️ REQUIRED FIXES:" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

Write-Host "`n1. 📊 Add Progress Indication (Frontend):" -ForegroundColor Yellow
Write-Host "   - Add cleaning_progress signal" -ForegroundColor White
Write-Host "   - Show progress bar during operation" -ForegroundColor White
Write-Host "   - Disable button during cleaning" -ForegroundColor White

Write-Host "`n2. 🧹 Fix Character Removal (Backend):" -ForegroundColor Yellow
Write-Host "   - Implement actual character replacement logic" -ForegroundColor White
Write-Host "   - Use character database to remove suspicious chars" -ForegroundColor White
Write-Host "   - Emit progress updates for each file processed" -ForegroundColor White

Write-Host "`n3. 🔄 Add Real-time Updates:" -ForegroundColor Yellow
Write-Host "   - Listen for progress events in frontend" -ForegroundColor White
Write-Host "   - Update progress bar with file count" -ForegroundColor White
Write-Host "   - Show current file being processed" -ForegroundColor White

# Create example implementation template
$template_file = Join-Path $project_root "scripts\cleaning-fix-template.md"
$template_content = @'
# Create Cleaned Copy - Implementation Template

## Frontend Changes (src/lib.rs)

### 1. Add Progress State
```rust
// Add to signals
let (cleaning_progress, set_cleaning_progress) = create_signal(0.0);
let (is_cleaning, set_is_cleaning) = create_signal(false);
let (current_cleaning_file, set_current_cleaning_file) = create_signal(String::new());
```

### 2. Update Button with Progress
```rust
// Replace existing button
button(
    class="btn-primary",
    disabled=move || is_cleaning.get(),
    on:click=move |_| {
        set_is_cleaning.set(true);
        set_cleaning_progress.set(0.0);
        // Call backend command...
    }
) {
    {move || if is_cleaning.get() {
        format!("🧹 Cleaning... {:.1}%", cleaning_progress.get() * 100.0)
    } else {
        "🧹 Create Cleaned Copy".to_string()
    }}
}
```

### 3. Add Progress Bar
```rust
// Add progress bar component
div(class="progress-container") {
    div(
        class="progress-bar",
        style=move || format!("width: {:.1}%", cleaning_progress.get() * 100.0)
    )
    p(class="progress-text") {
        {move || if is_cleaning.get() {
            format!("Processing: {}", current_cleaning_file.get())
        } else {
            String::new()
        }}
    }
}
```

## Backend Changes (src-tauri/src/main_module.rs)

### 1. Add Progress Events
```rust
// Add to create_cleaned_copy function
app.emit_all("cleaning_progress", CleaningProgress {
    progress: current_file as f64 / total_files as f64,
    current_file: file_path.clone(),
    files_processed: current_file,
    total_files: total_files,
}).ok();
```

### 2. Implement Character Removal
```rust
fn remove_suspicious_characters(content: &str, character_db: &HashMap<char, Vec<char>>) -> String {
    let mut cleaned = String::new();
    
    for ch in content.chars() {
        if character_db.contains_key(&ch) {
            // Replace with safe alternative or remove
            if let Some(alternatives) = character_db.get(&ch) {
                if let Some(safe_char) = alternatives.first() {
                    cleaned.push(*safe_char);
                }
                // else skip the character (remove it)
            }
        } else {
            cleaned.push(ch);
        }
    }
    
    cleaned
}
```

### 3. Add File Processing Loop
```rust
for (index, file_detail) in analysis_result.file_details.iter().enumerate() {
    // Read original file
    let content = fs::read_to_string(&file_detail.file_path)?;
    
    // Clean the content
    let cleaned_content = remove_suspicious_characters(&content, &character_db);
    
    // Write to cleaned folder
    let cleaned_path = output_folder.join(&file_detail.relative_path);
    fs::create_dir_all(cleaned_path.parent().unwrap())?;
    fs::write(cleaned_path, cleaned_content)?;
    
    // Emit progress
    app.emit_all("cleaning_progress", CleaningProgress {
        progress: (index + 1) as f64 / analysis_result.file_details.len() as f64,
        current_file: file_detail.relative_path.clone(),
        files_processed: index + 1,
        total_files: analysis_result.file_details.len(),
    }).ok();
}
```
'@

$template_content | Out-File -FilePath $template_file -Encoding UTF8

Write-Host "`n📝 Implementation template created: scripts\cleaning-fix-template.md" -ForegroundColor Green

Write-Host "`n🎯 Action Items:" -ForegroundColor Magenta
Write-Host "1. 📝 Review template: scripts\cleaning-fix-template.md" -ForegroundColor White
Write-Host "2. 🔧 Implement frontend progress components" -ForegroundColor White
Write-Host "3. 🧹 Fix backend character removal logic" -ForegroundColor White
Write-Host "4. 🧪 Test with actual folders containing bad characters" -ForegroundColor White
Write-Host "5. ✅ Update CODEBASE-6 ticket status" -ForegroundColor White

Write-Host "`n💡 Testing Tip:" -ForegroundColor Blue
Write-Host "Use test_files\ folder to test cleaning functionality!" -ForegroundColor White
