use leptos::*;
use leptos_meta::*;
use leptos_router::*;

#[component]
pub fn App() -> impl IntoView {
    provide_meta_context();

    view! {
        <Html lang="en" dir="ltr" attr:data-theme="light"/>
        <Title text="Leptos Tauri v2 - Bad Character Scanner"/>
        <Meta charset="utf-8"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1"/>

        <Router>
            <Routes>
                <Route path="" view=HomePage/>
            </Routes>
        </Router>
    }
}

#[component]
fn HomePage() -> impl IntoView {
    let (input_text, set_input_text) = create_signal(String::new());
    let (analysis_result, set_analysis_result) = create_signal(String::new());

    let analyze_text = move |_| {
        let text = input_text.get();
        if text.is_empty() {
            set_analysis_result.set("Please enter some text to analyze.".to_string());
            return;
        }

        // Simple character analysis for now (will connect to <PERSON><PERSON> later)
        let char_count = text.chars().count();
        let byte_count = text.len();
        let has_non_ascii = text.chars().any(|c| !c.is_ascii());
        let suspicious_chars: Vec<char> = text.chars()
            .filter(|&c| {
                // Look for potentially suspicious characters
                c.is_control() ||
                matches!(c, '\u{200B}'..='\u{200D}' | '\u{FEFF}' | '\u{202A}'..='\u{202E}')
            })
            .collect();

        let result = format!(
            "Analysis Results:\n• Characters: {}\n• Bytes: {}\n• Contains non-ASCII: {}\n• Suspicious characters found: {}\n• Details: {}",
            char_count,
            byte_count,
            has_non_ascii,
            suspicious_chars.len(),
            if suspicious_chars.is_empty() {
                "None detected".to_string()
            } else {
                format!("{:?}", suspicious_chars)
            }
        );

        set_analysis_result.set(result);
    };

    view! {
        <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="container mx-auto px-4 py-8">
                <div class="max-w-4xl mx-auto">
                    // Header
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-gray-900 mb-4">
                            "🔍 Bad Character Scanner"
                        </h1>
                        <p class="text-xl text-gray-600">
                            "" <span class="font-semibold text-indigo-600">""</span>
                        </p>
                        <p class="text-gray-500 mt-2">
                            "Detect invisible characters, control codes, and suspicious Unicode sequences"
                        </p>
                    </div>

                    // Main input section
                    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                        <label for="text-input" class="block text-sm font-medium text-gray-700 mb-2">
                            "Enter text to analyze:"
                        </label>
                        <textarea
                            id="text-input"
                            rows="8"
                            class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                            placeholder="Paste your text here... (supports Unicode, control characters, and invisible characters)"
                            on:input=move |ev| {
                                set_input_text.set(event_target_value(&ev));
                            }
                            prop:value=input_text
                        />
                        
                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                "Characters: " {move || input_text.get().chars().count()}
                                " | Bytes: " {move || input_text.get().len()}
                            </div>
                            <button
                                on:click=analyze_text
                                class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors"
                            >
                                "Analyze Text"
                            </button>
                        </div>
                    </div>

                    // Results section
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">"📊 Analysis Results"</h2>
                        <pre class="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap font-mono text-sm border">
                            {move || analysis_result.get()}
                        </pre>
                    </div>

                    // Info section
                    <div class="mt-8 bg-indigo-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-indigo-900 mb-3">
                            "🛡️ What This Scanner Detects"
                        </h3>
                        <div class="grid md:grid-cols-2 gap-4 text-sm text-indigo-800">
                            <ul class="space-y-2">
                                <li class="flex items-center">
                                    <span class="mr-2">"•"</span>
                                    "Control characters (invisible formatting)"
                                </li>
                                <li class="flex items-center">
                                    <span class="mr-2">"•"</span>
                                    "Zero-width characters (ZWSP, ZWNJ, ZWJ)"
                                </li>
                            </ul>
                            <ul class="space-y-2">
                                <li class="flex items-center">
                                    <span class="mr-2">"•"</span>
                                    "Byte order marks (BOM)"
                                </li>
                                <li class="flex items-center">
                                    <span class="mr-2">"•"</span>
                                    "Bidirectional text override characters"
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}
