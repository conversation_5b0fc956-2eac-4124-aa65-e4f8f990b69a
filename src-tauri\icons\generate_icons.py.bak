from PIL import Image
import os

def generate_icons(base_image_path):
    sizes = [16, 32, 48, 64, 128, 192, 256, 512]
    base_name = os.path.splitext(os.path.basename(base_image_path))[0]
    img = Image.open(base_image_path)
    # Save PNGs at all sizes
    for size in sizes:
        out_path = f"{base_name}_{size}x{size}.png"
        img_resized = img.resize((size, size), Image.LANCZOS)
        img_resized.save(out_path)
        print(f"Saved {out_path}")
    # Save ICO (multi-size)
    ico_path = f"{base_name}.ico"
    img.save(ico_path, sizes=[(sz, sz) for sz in sizes if sz <= 256])
    print(f"Saved {ico_path}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        base_image_path_arg = sys.argv[1]
        if not os.path.exists(base_image_path_arg):
            raise FileNotFoundError(f"{base_image_path_arg} not found in current directory.")
        generate_icons(base_image_path_arg)
    else:
        print("Usage: python generate_icons.py <base_image_path>")
