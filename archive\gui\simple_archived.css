/* Basic styles for Bad Character Scanner */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}

.min-h-screen {
  min-height: 100vh;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.text-center {
  text-align: center;
}

.mb-8 {
  margin-bottom: 2rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray-900 {
  color: #111827;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-gray-600 {
  color: #4b5563;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.border {
  border-width: 1px;
}

.border-red-400 {
  border-color: #f87171;
}

.text-red-700 {
  color: #b91c1c;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.bg-white {
  background-color: #ffffff;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.overflow-hidden {
  overflow: hidden;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.flex {
  display: flex;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-indigo-500 {
  border-color: #6366f1;
}

.text-indigo-600 {
  color: #4f46e5;
}

.border-transparent {
  border-color: transparent;
}

.text-gray-500 {
  color: #6b7280;
}

.hover\:text-gray-700:hover {
  color: #374151;
}

.p-6 {
  padding: 1.5rem;
}

.block {
  display: block;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-gray-700 {
  color: #374151;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.w-full {
  width: 100%;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px #6366f1;
}

.focus\:ring-indigo-500:focus {
  box-shadow: 0 0 0 2px #6366f1;
}

.focus\:border-indigo-500:focus {
  border-color: #6366f1;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, monospace;
}

.mt-4 {
  margin-top: 1rem;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.bg-indigo-600 {
  background-color: #4f46e5;
}

.text-white {
  color: #ffffff;
}

.hover\:bg-indigo-700:hover {
  background-color: #4338ca;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

button {
  cursor: pointer;
  border: none;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

button:disabled {
  cursor: not-allowed;
}

textarea {
  resize: vertical;
  min-height: 200px;
}

.mt-8 {
  margin-top: 2rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-400 {
  color: #9ca3af;
}

/* Grid system */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.rounded {
  border-radius: 0.25rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-blue-600 {
  color: #2563eb;
}

.text-orange-600 {
  color: #ea580c;
}

.text-green-600 {
  color: #16a34a;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.mt-6 {
  margin-top: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.font-semibold {
  font-weight: 600;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.border-red-200 {
  border-color: #fecaca;
}

.text-red-800 {
  color: #991b1b;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.p-2 {
  padding: 0.5rem;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.border-green-200 {
  border-color: #bbf7d0;
}

.text-green-800 {
  color: #166534;
}

.bg-gray-600 {
  background-color: #4b5563;
}

.hover\:bg-gray-700:hover {
  background-color: #374151;
}

.focus\:ring-gray-500:focus {
  box-shadow: 0 0 0 2px #6b7280;
}

.flex-1 {
  flex: 1 1 0%;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.text-red-600 {
  color: #dc2626;
}
