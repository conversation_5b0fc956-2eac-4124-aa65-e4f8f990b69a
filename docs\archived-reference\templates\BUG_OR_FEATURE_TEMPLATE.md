# [BUG/FEATURE] - Brief Description

**Type:** Bug Report / Feature Request  
**Priority:** Critical/High/Medium/Low  
**Status:** 🟢 Open / 🟡 In Progress / ✅ Resolved  
**Created:** YYYY-MM-DD  
**Updated:** YYYY-MM-DD  
**Assigned To:** @username  
**Related Issues:** #123, #124  
**Affected Version:** v0.2.0

## Description
[Provide a clear and concise description of the bug or feature request]

## For Bug Reports
### Steps to Reproduce
1. [Step 1]
2. [Step 2]
3. [Observed Behavior]

### Expected Behavior
[Describe what you expected to happen]

### Actual Behavior
[Describe what actually happened]

### Environment
- OS: [e.g., Windows 11, macOS 14, Ubuntu 22.04]
- Browser (if web): [e.g., Chrome 123, Firefox 121]
- App Version: [e.g., v0.2.0]
- Tauri Version: [e.g., v2.5.1]

### Screenshots/Logs
```
[Relevant logs or error messages]
```

## For Feature Requests
### Problem Statement
[Describe the problem this feature would solve]

### Proposed Solution
[Describe your proposed solution]

### Alternatives Considered
[Describe any alternative solutions or features you've considered]

### Additional Context
[Add any other context, mockups, or references here]

## Acceptance Criteria
- [ ] Criteria 1
- [ ] Criteria 2
- [ ] Criteria 3

## Implementation Notes
[Any technical notes or considerations for implementation]

## Testing Instructions
1. [Test step 1]
2. [Test step 2]
3. [Expected result]

## Related Documentation
- [Related documentation links]

## Dependencies
- [ ] Dependency 1
- [ ] Dependency 2

## Labels
`bug` or `enhancement`, `priority:high`, `needs-triage`
