# GUI/UX Test Suite

This directory contains all GUI and UX tests for the Leptos/Tauri app.

## Structure
- `test_smoke.rs` — Basic app launch and render test
- `helpers.rs` — Shared helpers/utilities
- `TICKET_GUI_TEST_SUITE.md` — Master plan and checklist
- (Planned) `test_characters_tab.rs`, `test_export_tab.rs`, etc. — Per-component tests
- (Planned) `e2e/` — End-to-end user flow tests
- (Planned) `visual/` — Visual regression/screenshot tests

## How to Run
- For Rust-based tests: `cargo test --test gui/*`
- For E2E/Playwright: see instructions in `TICKET_GUI_TEST_SUITE.md` (to be added)

## Contribution
- Follow the guidelines in `TICKET_GUI_TEST_SUITE.md` before adding or refactoring tests.
- Keep tests modular and focused.
- Add new test files for new UI areas or flows.

---

*This suite is under active development. See the ticket for roadmap and coverage goals.*
