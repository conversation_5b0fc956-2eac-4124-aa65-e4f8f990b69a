#!/usr/bin/env powershell
# Development workflow automation for Bad Character Scanner
# Provides common development tasks and workflows

param(
    [Parameter(Position=0)]
    [ValidateSet('start', 'build', 'test', 'clean', 'release', 'commit', 'pr', 'analyze', 'fix')]
    [string]$Command = 'start',
    
    [string]$Message,
    [switch]$Skip,
    [switch]$Force
)

$project_root = $PSScriptRoot | Split-Path -Parent
$ErrorActionPreference = "Stop"

function Write-Step {
    param([string]$Message, [string]$Color = "Cyan")
    Write-Host "`n➤ $Message" -ForegroundColor $Color
}

function Run-Command {
    param([string]$Command, [string]$WorkingDir = $null)
    if ($WorkingDir) {
        Push-Location $WorkingDir
    }
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -ne 0) {
            throw "Command failed: $Command"
        }
    } finally {
        if ($WorkingDir) {
            Pop-Location
        }
    }
}

switch ($Command) {
    'start' {
        Write-Host "`n🚀 STARTING DEVELOPMENT SERVER" -ForegroundColor Cyan
        
        # Pre-flight checks
        if (-not $Skip) {
            Write-Step "Running quick health check..."
            & "$PSScriptRoot\quick-doctor.ps1"
            if ($LASTEXITCODE -ne 0) {
                Write-Host "`n⚠️  Issues detected. Run doctor.ps1 for details." -ForegroundColor Yellow
                if (-not $Force) { exit 1 }
            }
        }
        
        # Check for recent changes
        Write-Step "Checking for uncommitted changes..."
        $git_status = git status --porcelain 2>$null
        if ($git_status) {
            Write-Host "  📝 You have uncommitted changes" -ForegroundColor Yellow
        }
        
        # Start development server
        Write-Step "Starting Tauri development server..."
        Write-Host "  🌐 Frontend: http://localhost:1420" -ForegroundColor Gray
        Write-Host "  🔧 Backend: Rust hot-reload enabled" -ForegroundColor Gray
        Write-Host "  📊 Logs: %LOCALAPPDATA%\bad-character-scanner\logs" -ForegroundColor Gray
        
        cargo tauri dev
    }
    
    'build' {
        Write-Host "`n🔨 BUILDING APPLICATION" -ForegroundColor Cyan
        
        # Clean previous builds
        if (-not $Skip) {
            Write-Step "Cleaning previous builds..."
            Remove-Item -Path "$project_root\dist" -Recurse -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$project_root\src-tauri\target\release" -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        # Build frontend
        Write-Step "Building frontend..."
        Run-Command "trunk build --release"
        
        # Build backend
        Write-Step "Building backend..."
        Run-Command "cargo build --release" -WorkingDir "$project_root\src-tauri"
        
        # Build Tauri app
        Write-Step "Building Tauri application..."
        Run-Command "cargo tauri build"
        
        Write-Host "`n✅ Build complete! Check src-tauri/target/release/" -ForegroundColor Green
    }
    
    'test' {
        Write-Host "`n🧪 RUNNING TESTS" -ForegroundColor Cyan
        
        # Backend tests
        Write-Step "Running backend tests..."
        Run-Command "cargo test" -WorkingDir "$project_root\src-tauri"
        
        # Frontend tests
        Write-Step "Running frontend tests..."
        Run-Command "cargo test --target wasm32-unknown-unknown"
        
        # Integration tests
        Write-Step "Running integration tests..."
        & "$PSScriptRoot\test-development-build.ps1" -SkipBuild
        
        # Self-scan
        Write-Step "Running bad character self-scan..."
        & "$PSScriptRoot\scan-this-project.ps1"
        
        Write-Host "`n✅ All tests passed!" -ForegroundColor Green
    }
    
    'clean' {
        Write-Host "`n🧹 CLEANING PROJECT" -ForegroundColor Cyan
        
        $dirs_to_clean = @(
            "$project_root\dist",
            "$project_root\target",
            "$project_root\src-tauri\target",
            "$project_root\node_modules",
            "$project_root\.parcel-cache"
        )
        
        foreach ($dir in $dirs_to_clean) {
            if (Test-Path $dir) {
                Write-Step "Removing $(Split-Path $dir -Leaf)..."
                Remove-Item -Path $dir -Recurse -Force
            }
        }
        
        Write-Step "Cleaning Cargo cache..."
        Run-Command "cargo clean" -WorkingDir "$project_root\src-tauri"
        
        Write-Host "`n✅ Project cleaned!" -ForegroundColor Green
        Write-Host "Run 'npm install' to restore dependencies." -ForegroundColor Yellow
    }
    
    'release' {
        Write-Host "`n📦 CREATING RELEASE" -ForegroundColor Cyan
        
        # Check git status
        Write-Step "Checking git status..."
        $git_status = git status --porcelain
        if ($git_status -and -not $Force) {
            Write-Host "❌ Uncommitted changes detected!" -ForegroundColor Red
            Write-Host "Commit your changes or use -Force flag." -ForegroundColor Yellow
            exit 1
        }
        
        # Run tests
        if (-not $Skip) {
            Write-Step "Running tests..."
            & $PSScriptRoot\dev-workflow.ps1 test
        }
        
        # Update version
        Write-Step "Current versions:"
        $cargo_toml = Get-Content "$project_root\src-tauri\Cargo.toml" -Raw
        if ($cargo_toml -match 'version\s*=\s*"([^"]+)"') {
            Write-Host "  Backend: $($matches[1])" -ForegroundColor Gray
        }
        
        # Build release
        Write-Step "Building release..."
        & $PSScriptRoot\dev-workflow.ps1 build
        
        Write-Host "`n✅ Release build complete!" -ForegroundColor Green
    }
    
    'commit' {
        Write-Host "`n💾 COMMITTING CHANGES" -ForegroundColor Cyan
        
        if (-not $Message) {
            $Message = Read-Host "Enter commit message"
        }
        
        # Check for issues
        Write-Step "Running pre-commit checks..."
        & "$PSScriptRoot\quick-doctor.ps1"
        
        # Stage changes
        Write-Step "Staging changes..."
        git add -A
        
        # Show what's being committed
        Write-Step "Changes to commit:"
        git status --short
        
        # Commit
        Write-Step "Committing..."
        git commit -m $Message
        
        Write-Host "`n✅ Changes committed!" -ForegroundColor Green
    }
    
    'pr' {
        Write-Host "`n🔄 CREATING PULL REQUEST" -ForegroundColor Cyan
        
        # Check branch
        $current_branch = git branch --show-current
        if ($current_branch -eq "main" -or $current_branch -eq "master") {
            Write-Host "❌ Cannot create PR from main branch!" -ForegroundColor Red
            Write-Host "Create a feature branch first." -ForegroundColor Yellow
            exit 1
        }
        
        # Push changes
        Write-Step "Pushing to remote..."
        git push -u origin $current_branch
        
        # Create PR using GitHub CLI if available
        if (Get-Command gh -ErrorAction SilentlyContinue) {
            Write-Step "Creating pull request..."
            gh pr create --web
        } else {
            Write-Host "Install GitHub CLI (gh) for automatic PR creation" -ForegroundColor Yellow
            Write-Host "https://github.com/cli/cli#installation" -ForegroundColor Gray
        }
    }
    
    'analyze' {
        Write-Host "`n🔍 ANALYZING CODEBASE" -ForegroundColor Cyan
        
        # Code statistics
        Write-Step "Code statistics:"
        $rust_files = Get-ChildItem -Path $project_root -Filter "*.rs" -Recurse | Where-Object { $_.FullName -notmatch "target|node_modules" }
        $ts_files = Get-ChildItem -Path $project_root -Filter "*.ts" -Recurse | Where-Object { $_.FullName -notmatch "target|node_modules" }
        
        Write-Host "  Rust files: $($rust_files.Count)" -ForegroundColor Gray
        Write-Host "  TypeScript files: $($ts_files.Count)" -ForegroundColor Gray
        
        # Complexity analysis
        Write-Step "Checking code complexity..."
        $complex_functions = $rust_files | ForEach-Object {
            $content = Get-Content $_.FullName -Raw
            $matches = [regex]::Matches($content, "fn\s+\w+.*?\{[\s\S]*?\n\}")
            $matches | Where-Object { $_.Value.Split("`n").Count -gt 50 } | ForEach-Object {
                [PSCustomObject]@{
                    File = $_.Name
                    Lines = $_.Value.Split("`n").Count
                }
            }
        }
        
        if ($complex_functions) {
            Write-Host "  Complex functions (>50 lines):" -ForegroundColor Yellow
            $complex_functions | ForEach-Object {
                Write-Host "    $($_.File): $($_.Lines) lines" -ForegroundColor Yellow
            }
        }
        
        # Dependency analysis
        Write-Step "Analyzing dependencies..."
        Push-Location "$project_root\src-tauri"
        cargo tree --depth 1 | Select-Object -First 20
        Pop-Location
        
        # Bad character scan
        Write-Step "Scanning for bad characters..."
        & "$PSScriptRoot\scan-this-project.ps1" -OutputFormat summary
    }
    
    'fix' {
        Write-Host "`n🔧 FIXING COMMON ISSUES" -ForegroundColor Cyan
        
        # Run doctor with fix
        Write-Step "Running doctor with auto-fix..."
        & "$PSScriptRoot\doctor.ps1" -Fix
        
        # Format code
        Write-Step "Formatting Rust code..."
        Run-Command "cargo fmt" -WorkingDir "$project_root\src-tauri"
        
        # Fix lints
        Write-Step "Fixing Rust lints..."
        Run-Command "cargo clippy --fix --allow-dirty" -WorkingDir "$project_root\src-tauri"
        
        # Update dependencies
        if ($Force) {
            Write-Step "Updating dependencies..."
            Run-Command "cargo update" -WorkingDir "$project_root\src-tauri"
            Run-Command "npm update"
        }
        
        Write-Host "`n✅ Fixes applied!" -ForegroundColor Green
    }
    
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Write-Host "`nAvailable commands:" -ForegroundColor Yellow
        Write-Host "  start   - Start development server (default)" -ForegroundColor Gray
        Write-Host "  build   - Build the application" -ForegroundColor Gray
        Write-Host "  test    - Run all tests" -ForegroundColor Gray
        Write-Host "  clean   - Clean build artifacts" -ForegroundColor Gray
        Write-Host "  release - Create a release build" -ForegroundColor Gray
        Write-Host "  commit  - Commit changes with checks" -ForegroundColor Gray
        Write-Host "  pr      - Create pull request" -ForegroundColor Gray
        Write-Host "  analyze - Analyze codebase" -ForegroundColor Gray
        Write-Host "  fix     - Fix common issues" -ForegroundColor Gray
    }
}

Write-Host "`n🦸 Fighting for accessibility!" -ForegroundColor Cyan