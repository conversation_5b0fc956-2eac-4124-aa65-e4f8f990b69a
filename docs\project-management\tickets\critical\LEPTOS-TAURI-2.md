# LEPTOS-TAURI-2 - Fix PostMessage Protocol Fallback Issues

**Status:** 🔴 Critical  
**Priority:** P0 (Blocking)  
**Type:** 🐛 Bug Fix  
**Created:** 2025-06-12  
**Estimated Effort:** 1-3 hours  

## 🎯 Problem Statement

**Error**: `IPC custom protocol failed, <PERSON><PERSON> will now use the postMessage interface instead`

When the primary IPC fails, <PERSON><PERSON> attempts to fall back to postMessage protocol, but this fallback also fails, leaving no working communication channel between Leptos frontend and Tauri backend.

## 🔍 Symptoms

- IPC protocol fails first (see LEPTOS-TAURI-1)
- <PERSON><PERSON> attempts postMessage fallback
- PostMessage fallback also fails with `TypeError: Failed to fetch`
- Complete communication breakdown between frontend/backend

## 🎯 Root Cause Analysis

### Potential Issues:
1. **PostMessage Handler Missing**: Backend not configured for postMessage
2. **CORS Issues**: Cross-origin restrictions blocking postMessage
3. **Window Context Problems**: postMessage target window not available
4. **Message Format Issues**: Incorrect message structure for postMessage

## ✅ Acceptance Criteria

- [ ] PostMessage fallback works when IPC fails
- [ ] Back<PERSON> receives and processes postMessage commands
- [ ] Front<PERSON> receives proper responses via postMessage
- [ ] No `TypeError: Failed to fetch` errors in postMessage mode

## 🔧 Investigation Steps

### Phase 1: Verify PostMessage Configuration
- [ ] Check if Tauri is configured to handle postMessage
- [ ] Verify window.postMessage listeners are set up
- [ ] Confirm message routing in Tauri core

### Phase 2: Test PostMessage Directly
- [ ] Send manual postMessage to test communication
- [ ] Verify message format matches Tauri expectations
- [ ] Check browser security restrictions

### Phase 3: Protocol Priority Settings
- [ ] Review Tauri protocol priority configuration
- [ ] Consider forcing postMessage for development
- [ ] Test both protocols independently

## 🧪 Testing Plan

1. **Manual PostMessage Test**:
   ```javascript
   window.postMessage({
     cmd: 'test_connection',
     callback: 'test_callback',
     error: 'test_error'
   }, '*');
   ```

2. **Backend PostMessage Handler**:
   ```rust
   // Verify postMessage handler exists in Tauri setup
   ```

3. **Expected Result**: Manual postMessage should trigger backend response

## 🚨 Quick Fixes to Try

1. **Force PostMessage Mode**: Configure Tauri to use postMessage primarily
2. **Check Window Targets**: Ensure correct window targeting for messages
3. **Verify CORS Settings**: Check if CORS is blocking postMessage
4. **Update Tauri Config**: Ensure postMessage is enabled in configuration

## 📋 Technical Details

- **Primary Protocol**: Tauri IPC (failing)
- **Fallback Protocol**: PostMessage (also failing)
- **Error Type**: `TypeError: Failed to fetch`
- **Impact**: Complete frontend-backend communication failure

## 🔗 Related Issues

- Depends on LEPTOS-TAURI-1 (primary IPC failure)
- May require LEPTOS-TAURI-3 (command binding fixes)
- Blocking all user functionality

## 💡 Development Strategy

1. **Short-term**: Get postMessage working as fallback
2. **Medium-term**: Fix primary IPC (LEPTOS-TAURI-1)
3. **Long-term**: Ensure both protocols work reliably

---

**Next Steps**: Focus on getting ONE working communication channel first (either IPC or postMessage).

**Success Metric**: Backend commands execute successfully via at least one protocol.
