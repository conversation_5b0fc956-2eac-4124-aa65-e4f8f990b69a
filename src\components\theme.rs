use leptos::*;
use wasm_bindgen::prelude::*;
use web_sys::window;

// Theme context for app-wide state management
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum ThemeMode {
    Light,
    Dark,
    System,
}

impl ThemeMode {
    pub fn as_str(&self) -> &'static str {
        match self {
            ThemeMode::Light => "light",
            ThemeMode::Dark => "dark", 
            ThemeMode::System => "system",
        }
    }

    pub fn from_str(s: &str) -> Self {
        match s {
            "dark" => ThemeMode::Dark,
            "light" => ThemeMode::Light,
            _ => ThemeMode::System,
        }
    }
}

#[derive(Clone, Copy)]
pub struct ThemeContext {
    pub theme: ReadSignal<ThemeMode>,
    pub set_theme: WriteSignal<ThemeMode>,
    pub is_dark: Memo<bool>,
}

// Theme provider component
#[component]
pub fn ThemeProvider(children: Children) -> impl IntoView {
    // Initialize theme from localStorage or default to System
    let initial_theme = get_stored_theme().unwrap_or(ThemeMode::System);
    let (theme, set_theme) = create_signal(initial_theme);
    
    // Derived signal for actual dark mode state
    let is_dark = create_memo(move |_| {
        match theme.get() {
            ThemeMode::Dark => true,
            ThemeMode::Light => false,
            ThemeMode::System => is_system_dark_mode(),
        }
    });

    // Provide context to child components
    provide_context(ThemeContext {
        theme,
        set_theme,
        is_dark,
    });

    // Apply theme changes to document
    create_effect(move |_| {
        let dark = is_dark.get();
        apply_theme_to_document(dark);
        store_theme(theme.get());
    });

    // Listen for system theme changes when in System mode
    create_effect(move |_| {
        if theme.get() == ThemeMode::System {
            setup_system_theme_listener(set_theme);
        }
    });

    view! { <div class="theme-provider">{children()}</div> }
}

// Theme toggle component
#[component]
pub fn ThemeToggle() -> impl IntoView {
    let theme_context = expect_context::<ThemeContext>();
    let theme = theme_context.theme;
    let set_theme = theme_context.set_theme;
    let is_dark = theme_context.is_dark;

    let cycle_theme = move |_| {
        let current = theme.get();
        let next = match current {
            ThemeMode::Light => ThemeMode::Dark,
            ThemeMode::Dark => ThemeMode::System,
            ThemeMode::System => ThemeMode::Light,
        };
        set_theme.set(next);
    };

    view! {
        <button
            on:click=cycle_theme
            class="relative bg-gray-200 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            style="width: 48px; height: 24px;"
            class:bg-gray-800=move || is_dark.get()
            title=move || format!("Current theme: {} (click to cycle)", theme.get().as_str())
        >
            // Toggle slider
            <div
                class="absolute bg-white rounded-full transition-all duration-300 flex items-center justify-center shadow-sm"
                style="top: 2px; left: 2px; width: 20px; height: 20px;"
                style:transform=move || if is_dark.get() { "translateX(24px)" } else { "translateX(0)" }
            >
                // Theme icons
                <Show when=move || theme.get() == ThemeMode::System>
                    <svg class="icon-40 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                            clip-rule="evenodd"
                        />
                    </svg>
                </Show>
                <Show when=move || theme.get() == ThemeMode::Light>
                    <svg class="icon-40 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                            clip-rule="evenodd"
                        />
                    </svg>
                </Show>
                <Show when=move || theme.get() == ThemeMode::Dark>
                    <svg class="icon-40 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                </Show>
            </div>
        </button>
    }
}

// Compact theme toggle for settings
#[component]
pub fn CompactThemeToggle() -> impl IntoView {
    let theme_context = expect_context::<ThemeContext>();
    let theme = theme_context.theme;
    let set_theme = theme_context.set_theme;

    view! {
        <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-700">"Theme:"</span>
            <select
                class="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                on:change=move |ev| {
                    let value = event_target_value(&ev);
                    set_theme.set(ThemeMode::from_str(&value));
                }
                prop:value=move || theme.get().as_str()
            >
                <option value="system">"System"</option>
                <option value="light">"Light"</option>
                <option value="dark">"Dark"</option>
            </select>
        </div>
    }
}

// Helper functions for theme management
fn get_stored_theme() -> Option<ThemeMode> {
    if let Ok(Some(storage)) = window()?.local_storage() {
        if let Ok(Some(theme_str)) = storage.get_item("theme") {
            return Some(ThemeMode::from_str(&theme_str));
        }
    }
    None
}

fn store_theme(theme: ThemeMode) {
    if let Ok(Some(storage)) = window().unwrap().local_storage() {
        let _ = storage.set_item("theme", theme.as_str());
    }
}

fn is_system_dark_mode() -> bool {
    if let Some(window) = window() {
        if let Ok(media_query) = window.match_media("(prefers-color-scheme: dark)") {
            // Fixed: Remove incorrect Ok() pattern matching - media_query returns Option<MediaQueryList>
            if let Some(mql) = media_query {
                return mql.matches();
            }
        }
    }
    false
}

fn apply_theme_to_document(is_dark: bool) {
    if let Some(document) = window().unwrap().document() {
        if let Some(html) = document.document_element() {
            if is_dark {
                let _ = html.class_list().add_1("dark");
                let _ = html.set_attribute("data-theme", "dark");
            } else {
                let _ = html.class_list().remove_1("dark");
                let _ = html.set_attribute("data-theme", "light");
            }
        }
    }
}

// Theme management system - By John Shoy - 2025
// Handles dark/light/system theme switching with proper web-sys compatibility

fn setup_system_theme_listener(set_theme: WriteSignal<ThemeMode>) {
    use wasm_bindgen::closure::Closure;
    
    if let Some(window) = window() {
        if let Ok(media_query) = window.match_media("(prefers-color-scheme: dark)") {
            if let Some(mql) = media_query {
                // Fixed: Use correct web-sys API - MediaQueryListEvent doesn't exist in current version
                let callback = Closure::wrap(Box::new(move |_: web_sys::Event| {
                    // Trigger a re-evaluation when system theme changes
                    set_theme.set(ThemeMode::System);
                }) as Box<dyn FnMut(_)>);
                
                let _ = mql.add_listener_with_opt_callback(Some(callback.as_ref().unchecked_ref()));
                callback.forget(); // Keep the callback alive
            }
        }
    }
}

// CSS utility classes for dark mode support
#[allow(dead_code)]
pub fn get_theme_classes() -> &'static str {
    r#"
    /* Dark mode base styles */
    .dark {
        color-scheme: dark;
    }
    
    .dark body {
        background-color: #0f172a;
        color: #e2e8f0;
    }
    
    .dark .bg-white {
        background-color: #1e293b !important;
    }
    
    .dark .bg-gray-50 {
        background-color: #334155 !important;
    }
    
    .dark .bg-gray-100 {
        background-color: #475569 !important;
    }
    
    .dark .text-gray-900 {
        color: #e2e8f0 !important;
    }
    
    .dark .text-gray-700 {
        color: #cbd5e1 !important;
    }
    
    .dark .text-gray-600 {
        color: #94a3b8 !important;
    }
    
    .dark .text-gray-500 {
        color: #64748b !important;
    }
    
    .dark .border-gray-200 {
        border-color: #475569 !important;
    }
    
    .dark .border-gray-300 {
        border-color: #64748b !important;
    }
    
    /* Smooth transitions for theme changes */
    * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
    "#
}
