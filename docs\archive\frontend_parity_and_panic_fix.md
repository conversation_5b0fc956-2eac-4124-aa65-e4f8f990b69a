# Ticket: Frontend-Backend Feature Parity & Analyze Codebase Panic Fix

## Summary
Our current Leptos + Tauri v2 frontend is too simple and does not expose or utilize all the features supported by our backend. Additionally, there is a runtime panic caused by missing the required `folderPath` argument to the `analyze_codebase` command. This ticket covers both fixing the immediate panic and planning for a richer frontend that matches backend capabilities.

---

## Step 1: Fix `analyze_codebase` Panic
- Ensure the frontend always provides the required `folderPath` argument when invoking the `analyze_codebase` Tauri command.
- Add error handling to prevent panics if required arguments are missing.
- Test the fix by running the app and invoking the analysis feature.

## Step 2: Audit Backend Features
- List all backend commands, endpoints, and features currently available.
- Document what each does and what UI/UX is needed to expose them.

## Step 3: Design Improved Frontend
- For each backend feature, draft a UI component or workflow that would allow users to access it through the frontend.
- Prioritize features based on user value and backend readiness.

## Step 4: Incremental Frontend Implementation
- For each backend feature, create a sub-ticket to implement the corresponding frontend component.
- Ensure all new work strictly adheres to Tauri v2 compatibility.

## Step 5: Reference Old Frontend
- Create a folder in `docs/` (or similar) to store reference materials.
- Download and save an old version of the frontend from GitHub for reference and inspiration.

## Step 6: Periodic Review
- Regularly review and update all Tauri-related dependencies to the latest compatible Tauri v2 versions (see project memory for current versions).
- Document lessons learned and update the plan as needed.

---

**This ticket is created to ensure structured, Tauri v2-compatible development and to drive frontend-backend feature parity.**
