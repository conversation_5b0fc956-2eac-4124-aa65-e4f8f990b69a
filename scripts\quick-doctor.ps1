#!/usr/bin/env powershell
# Quick doctor script - Fast health check for common issues

Write-Host "`n🏥 QUICK HEALTH CHECK" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$all_good = $true

# Quick checks with simple fixes
function Quick-Check {
    param([string]$Name, [scriptblock]$Check, [string]$Fix)
    
    Write-Host -NoNewline "Checking $Name... "
    $result = & $Check
    if ($result) {
        Write-Host "✓" -ForegroundColor Green
    } else {
        Write-Host "✗" -ForegroundColor Red
        if ($Fix) { Write-Host "  Fix: $Fix" -ForegroundColor Yellow }
        $script:all_good = $false
    }
}

# Essential checks only
Quick-Check "Rust" { Get-Command cargo -ErrorAction SilentlyContinue } "Install from https://rustup.rs"
Quick-Check "Node.js" { Get-Command node -ErrorAction SilentlyContinue } "Install from https://nodejs.org"
Quick-Check "Tauri CLI" { cargo tauri --version 2>$null; $LASTEXITCODE -eq 0 } "Run: cargo install tauri-cli"
Quick-Check "Dependencies" { Test-Path "$project_root\node_modules" } "Run: npm install"
Quick-Check "WASM Target" { rustup target list --installed | Select-String "wasm32-unknown-unknown" } "Run: rustup target add wasm32-unknown-unknown"

# Check for current errors
Write-Host "`nChecking for compilation errors..."
Push-Location $project_root
$cargo_check = cargo check 2>&1 | Select-String -Pattern "error" -SimpleMatch
Pop-Location

if ($cargo_check) {
    Write-Host "❌ Compilation errors found!" -ForegroundColor Red
    $all_good = $false
} else {
    Write-Host "✅ No compilation errors" -ForegroundColor Green
}

# Summary
Write-Host "`n" + ("-" * 30) -ForegroundColor Gray
if ($all_good) {
    Write-Host "✨ All checks passed! Run 'cargo tauri dev' to start." -ForegroundColor Green
} else {
    Write-Host "⚠️  Some issues found. Run '.\doctor.ps1' for detailed diagnostics." -ForegroundColor Yellow
    Write-Host "   Or run '.\doctor.ps1 -Fix' to attempt automatic fixes." -ForegroundColor Gray
}