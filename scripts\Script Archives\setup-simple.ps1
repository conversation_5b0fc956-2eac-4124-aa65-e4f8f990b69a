#!/usr/bin/env powershell
# Simple Ultimate Development Environment Setup for Bad Character Scanner

Write-Host "Setting up ultimate dev environment for Bad Character Scanner!" -ForegroundColor Cyan
$ErrorActionPreference = "Continue"
$project_root = $PSScriptRoot | Split-Path -Parent

# Function to check dependencies
function Test-Dependencies {
    Write-Host "`nChecking dependencies..." -ForegroundColor Yellow
    
    $all_good = $true
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
        } else {
            Write-Host "Node.js: Not found" -ForegroundColor Red
            $all_good = $false
        }
    } catch {
        Write-Host "Node.js: Not found" -ForegroundColor Red
        $all_good = $false
    }
    
    # Check Rust
    try {
        $rustVersion = cargo --version 2>$null
        if ($rustVersion) {
            Write-Host "Rust: $rustVersion" -ForegroundColor Green
        } else {
            Write-Host "Rust: Not found" -ForegroundColor Red
            $all_good = $false
        }
    } catch {
        Write-Host "Rust: Not found" -ForegroundColor Red
        $all_good = $false
    }
    
    # Check Trunk
    try {
        trunk --version 2>$null | Out-Null
        Write-Host "Trunk: Available" -ForegroundColor Green
    } catch {
        Write-Host "Installing Trunk..." -ForegroundColor Yellow
        cargo install trunk
    }
    
    return $all_good
}

# Create basic VS Code tasks
function Setup-VSCodeTasks {
    Write-Host "`nSetting up VS Code tasks..." -ForegroundColor Yellow
    
    $vscode_dir = Join-Path $project_root ".vscode"
    if (-not (Test-Path $vscode_dir)) {
        New-Item -ItemType Directory -Path $vscode_dir -Force | Out-Null
    }
    
    $tasks_content = @'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Dev Server",
            "type": "shell",
            "command": "cargo",
            "args": ["tauri", "dev"],
            "group": "build",
            "isBackground": true
        },
        {
            "label": "Check Bad Characters",
            "type": "shell",
            "command": "node",
            "args": ["scripts/check-bad-characters.js"],
            "group": "test"
        },
        {
            "label": "Build Release",
            "type": "shell",
            "command": "cargo",
            "args": ["tauri", "build"],
            "group": "build"
        }
    ]
}
'@
    
    $tasks_file = Join-Path $vscode_dir "tasks.json"
    $tasks_content | Out-File -FilePath $tasks_file -Encoding UTF8
    Write-Host "VS Code tasks configured!" -ForegroundColor Green
}

# Create development scripts
function Create-DevScripts {
    Write-Host "`nCreating development scripts..." -ForegroundColor Yellow
    
    # Simple dev server script
    $dev_script_content = @'
#!/usr/bin/env powershell
Write-Host "Starting Bad Character Scanner development server..." -ForegroundColor Cyan
cd $PSScriptRoot | Split-Path -Parent
cargo tauri dev
'@
    
    $dev_script_path = Join-Path $project_root "scripts\start-dev.ps1"
    $dev_script_content | Out-File -FilePath $dev_script_path -Encoding UTF8
    
    # Quality check script
    $quality_script_content = @'
#!/usr/bin/env powershell
Write-Host "Running comprehensive quality checks..." -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent
cd $project_root

Write-Host "`n1. Checking for bad characters..."
node "scripts\check-bad-characters.js"
$result1 = $LASTEXITCODE

Write-Host "`n2. Checking Rust formatting..."
cargo fmt --check
$result2 = $LASTEXITCODE

Write-Host "`n3. Running tests..."
cargo test --quiet
$result3 = $LASTEXITCODE

Write-Host "`n4. Running Clippy..."
cargo clippy -- -D warnings
$result4 = $LASTEXITCODE

$total_issues = $result1 + $result2 + $result3 + $result4
if ($total_issues -eq 0) {
    Write-Host "`nAll checks passed!" -ForegroundColor Green
} else {
    Write-Host "`nSome checks failed. Please review the output above." -ForegroundColor Red
}
'@
    
    $quality_script_path = Join-Path $project_root "scripts\check-quality.ps1"
    $quality_script_content | Out-File -FilePath $quality_script_path -Encoding UTF8
    
    Write-Host "Development scripts created!" -ForegroundColor Green
}

# Main execution
Write-Host "Ultimate Development Environment Setup" -ForegroundColor Magenta
Write-Host "=====================================" -ForegroundColor Magenta

if (-not (Test-Dependencies)) {
    Write-Host "`nPlease install missing dependencies and run again." -ForegroundColor Red
    exit 1
}

Setup-VSCodeTasks
Create-DevScripts

Write-Host "`nSetup complete!" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host "Dependencies verified" -ForegroundColor White
Write-Host "VS Code tasks configured" -ForegroundColor White
Write-Host "Development scripts created" -ForegroundColor White

Write-Host "`nReady to develop!" -ForegroundColor Cyan
Write-Host "Commands available:" -ForegroundColor Yellow
Write-Host "  .\scripts\start-dev.ps1      # Start development server" -ForegroundColor White
Write-Host "  .\scripts\check-quality.ps1  # Run quality checks" -ForegroundColor White
Write-Host "  node scripts\check-bad-characters.js # Scan for bad chars" -ForegroundColor White

Write-Host "`nVS Code users: Use Ctrl+Shift+P -> 'Tasks: Run Task'" -ForegroundColor Blue
