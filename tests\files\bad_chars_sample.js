/* JavaScript test file with various suspicious characters */

// Zero-width joiner and non-joiner
const userName = "admin‌‍user";

// Bidirectional text override
const malicious = "filename‮.txt.exe";

// Soft hyphen (invisible in most displays)
const hiddenText = "soft­hyphen";

// Various Unicode spaces
const weirdSpaces = "normal space　em space space";

// Right-to-left override
const rtlOverride = "public‮/evil.js";

// Some normal JavaScript
function calculateHash(input) {
    // Normal comment
    return input.split('').reduce((hash, char) => {
        return ((hash << 5) - hash) + char.charCodeAt(0);
    }, 0);
}

export { calculateHash };
