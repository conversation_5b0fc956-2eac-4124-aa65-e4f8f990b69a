use leptos::*;
use crate::components::help_modal::{HelpButton, HelpTopic};

#[component]
pub fn FileTypesDisplay(
    #[prop(default = false)] expanded: bool,
    #[prop(default = false)] show_help_link: bool,
) -> impl IntoView {
    let (is_expanded, set_is_expanded) = create_signal(expanded);
    
    // Common file types to show in collapsed view
    let common_types = vec![
        ".js", ".ts", ".rs", ".py", ".java", ".cpp", ".c", ".cs", 
        ".html", ".xml", ".json", ".md", ".txt", ".yaml", ".toml"
    ];
    
    // Categorized file types based on FileTypesSummary.json
    let programming_languages = create_memo(move |_| vec![
        // C Family
        ".c", ".h", ".cpp", ".hpp", ".cc", ".cxx", ".ino",
        // Java/JVM
        ".java", ".kt", ".scala", ".clj", ".cljs",
        // Python
        ".py", ".pyw", ".pyi",
        // JavaScript/TypeScript
        ".js", ".ts", ".jsx", ".tsx", ".d.ts",
        // Other Languages
        ".cs", ".csx", ".vb", ".fs", ".fsi", ".fsx",
        ".php", ".phtml", ".phps", ".inc",
        ".swift", ".m", ".mm", ".go", ".rs", ".rlib",
        ".sh", ".bash", ".zsh", ".ksh", ".csh", ".tcsh",
        ".pl", ".pm", ".t", ".pod", ".lua",
        ".rb", ".erb", ".rake", ".gemspec", ".ru",
        ".hs", ".lhs", ".dart", ".elm", ".ex", ".exs"
    ]);

    let config_data = create_memo(move |_| vec![
        ".json", ".jsonc", ".yaml", ".yml", ".xml", ".toml", ".ini", ".cfg", ".conf",
        ".env", ".properties", ".reg", ".plist"
    ]);

    let web_files = create_memo(move |_| vec![
        ".html", ".htm", ".css", ".vue", ".svelte", ".twig", ".hbs", ".handlebars",
        ".ejs", ".njk", ".coffee", ".sass", ".scss", ".less", ".styl"
    ]);

    let document_formats = create_memo(move |_| vec![
        ".md", ".txt", ".rtf", ".pdf", ".doc", ".docx", ".xls", ".xlsx",
        ".ppt", ".pptx", ".odt", ".ods", ".odp"
    ]);

    let database_files = create_memo(move |_| vec![
        ".sql", ".psql", ".pgsql", ".mysql", ".sqlite", ".dump", ".db", ".sqlite3",
        ".mdb", ".accdb", ".dbf"
    ]);

    view! {
        <div class="file-types-container">
            <div class="flex items-center gap-2 text-xs text-gray-500">
                <svg class="icon-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>
                    "Supported file types: "
                    {common_types.join(", ")}
                    <button 
                        class="text-blue-600 hover:text-blue-800 underline ml-1"
                        on:click=move |_| set_is_expanded.update(|expanded| *expanded = !*expanded)
                    >
                        {move || if is_expanded.get() { "...less" } else { "...more" }}
                    </button>
                </span>
                {show_help_link.then(|| view! {
                    <HelpButton
                        topic=HelpTopic::FileTypes
                        text="Help"
                        class="text-blue-600 hover:text-blue-800 underline ml-2"
                    />
                })}
            </div>
            
            <Show when=move || is_expanded.get()>
                <div class="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-sm text-gray-700 mb-2">"Programming Languages"</h4>
                            <div class="text-xs text-gray-600 flex flex-wrap gap-1">
                                {move || programming_languages.get().into_iter().map(|ext| view! {
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">{ext}</span>
                                }).collect::<Vec<_>>()}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-sm text-gray-700 mb-2">"Configuration & Data"</h4>
                            <div class="text-xs text-gray-600 flex flex-wrap gap-1">
                                {move || config_data.get().into_iter().map(|ext| view! {
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded">{ext}</span>
                                }).collect::<Vec<_>>()}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-sm text-gray-700 mb-2">"Web Development"</h4>
                            <div class="text-xs text-gray-600 flex flex-wrap gap-1">
                                {move || web_files.get().into_iter().map(|ext| view! {
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">{ext}</span>
                                }).collect::<Vec<_>>()}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-sm text-gray-700 mb-2">"Documents"</h4>
                            <div class="text-xs text-gray-600 flex flex-wrap gap-1">
                                {move || document_formats.get().into_iter().map(|ext| view! {
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">{ext}</span>
                                }).collect::<Vec<_>>()}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-sm text-gray-700 mb-2">"Database Files"</h4>
                            <div class="text-xs text-gray-600 flex flex-wrap gap-1">
                                {move || database_files.get().into_iter().map(|ext| view! {
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded">{ext}</span>
                                }).collect::<Vec<_>>()}
                            </div>
                        </div>
                        
                        <div class="pt-2 border-t border-gray-300">
                            <p class="text-xs text-gray-500">
                                "...and many more! The application supports "
                                <strong>"2000+ file types"</strong>
                                " across all major categories including archives, images, executables, scientific data, CAD files, and more."
                            </p>
                            <p class="text-xs text-gray-500 mt-1">
                                "For the complete list, see "
                                <code class="bg-gray-200 px-1 rounded">"assets/FileTypesSummary.json"</code>
                                " or check the technical documentation."
                            </p>
                        </div>
                    </div>
                </div>
            </Show>
        </div>
    }
}
