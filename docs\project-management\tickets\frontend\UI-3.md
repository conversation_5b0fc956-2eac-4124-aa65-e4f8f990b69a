# UI-3: Update File Types Support Display

## Status
🔴 **Open** - Medium Priority

## Type
Enhancement - User Interface

## Description
The current file types display shows only a limited list of supported file types, but our `FileTypesSummary.json` actually supports thousands of file types. We need to update the UI to better represent our comprehensive support.

## Current Behavior
Shows: "Supported file types: .js, .ts, .rs, .py, .java, .cpp, .c, .h, .css, .html, .xml, .json, .md"

This is misleading as we actually support 2,800+ file extensions across multiple categories.

## Expected Behavior
Replace the current display with a more user-friendly approach:

### Option 1: Abbreviated List with "etc."
"Supported file types: .js, .ts, .rs, .py, .java, .cpp, .c, .h, .css, .html, .xml, .json, .md, and 2,800+ more..."

### Option 2: Interactive Full List
- Show abbreviated list with hover tooltip showing more types
- Add "View Full List" button that opens modal/dropdown
- Full list should be categorized by type (Programming Languages, Document Formats, etc.)

### Option 3: Smart Categories
"Supports: Programming Languages, Document Formats, Configuration Files, and more (2,800+ file types)"

## Implementation Requirements

### Data Source
- Read from `assets/FileTypesSummary.json` (58,147 bytes, 2,800+ extensions)
- Use existing categories:
  - Programming Languages
  - Document Formats  
  - Configuration Files
  - Image Formats
  - Video Formats
  - Audio Formats
  - Archive Formats
  - Database Formats
  - And more...

### UI Components Needed
1. **Compact Display**: Short, user-friendly summary
2. **Expandable List**: Modal or accordion with full list
3. **Category Organization**: Group by file type categories
4. **Search Functionality**: Allow users to search for specific extensions
5. **Hover Tooltips**: Show additional info on hover

### Similar Enhancement for Bad Characters
Also implement expandable list for bad characters detection:
- Currently shows limited info about character detection
- Should show categories from `Bad_Characters.json`:
  - Control Characters (C0/C1)
  - Unicode Problems
  - Homograph Attacks
  - Suspicious Characters
  - And more...

## Technical Implementation

### Frontend (Leptos)
```rust
// Component structure
- FileTypesDisplay
  - CompactView (default)
  - ExpandedModal (on click)
  - CategoryFilter
  - SearchBox
```

### Data Processing
- Parse `FileTypesSummary.json` at runtime
- Create category mappings
- Implement search/filter functionality
- Calculate total counts per category

## Acceptance Criteria
- [ ] Replace misleading short list with accurate representation
- [ ] Implement expandable full list functionality
- [ ] Organize file types by categories
- [ ] Add search functionality for finding specific extensions
- [ ] Show total count of supported file types
- [ ] Ensure responsive design for mobile devices
- [ ] Similar functionality for bad characters display
- [ ] Performance optimization for large lists

## User Experience Goals
1. **Immediate Understanding**: Users quickly understand broad support
2. **Detailed Discovery**: Users can explore full capabilities
3. **Specific Lookup**: Users can verify support for their file types
4. **Trust Building**: Accurate representation builds confidence

## Implementation Steps
1. [ ] Design UI mockups for different approaches
2. [ ] Implement data parsing from JSON assets
3. [ ] Create category mapping system
4. [ ] Build compact display component
5. [ ] Implement expandable modal/list
6. [ ] Add search and filter functionality
7. [ ] Style and make responsive
8. [ ] Test with real user feedback
9. [ ] Apply similar pattern to bad characters display

## Related Tickets
- UI-2: Implement Drag & Drop Functionality
- CORE-2: Enhance file processing capabilities

## Priority
Medium - Improves user understanding and trust

## Estimated Effort
Small-Medium (1-2 days)

## Assets Referenced
- `assets/FileTypesSummary.json` (2,800+ file extensions)
- `assets/Bad_Characters.json` (Character detection rules)

## Notes
- Consider A/B testing different display approaches
- Ensure accessibility with screen readers
- Monitor performance with large data sets
- Consider caching parsed data
