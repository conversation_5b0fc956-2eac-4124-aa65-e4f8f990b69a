use serde::{Deserialize, Serialize};
use fancy_regex::Regex;
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PatternThreat {
    pub pattern_type: PatternType,
    pub risk_level: RiskLevel,
    pub position: usize,
    pub length: usize,
    pub matched_text: String,
    pub description: String,
    pub mitigation: String,
    pub context: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum PatternType {
    SuspiciousEncoding,
    ObfuscatedCode,
    PotentialInjection,
    HiddenCharacters,
    MaliciousPattern,
    DataExfiltration,
    AuthenticationBypass,
    CommandInjection,
    CodeObfuscation,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum RiskLevel {
    Critical,
    High,
    Medium,
    Low,
}

pub struct PatternAnalyzer {
    patterns: HashMap<PatternType, Vec<PatternRule>>,
}

#[derive(Debug, <PERSON>lone)]
struct PatternRule {
    regex: Regex,
    description: String,
    risk_level: RiskLevel,
    mitigation: String,
    file_types: Vec<String>,
}

impl PatternAnalyzer {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut analyzer = PatternAnalyzer {
            patterns: HashMap::new(),
        };
        
        analyzer.initialize_patterns()?;
        Ok(analyzer)
    }

    fn initialize_patterns(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Suspicious encoding patterns
        self.add_pattern(
            PatternType::SuspiciousEncoding,
            r"\\u[0-9a-fA-F]{4}",
            "Unicode escape sequences that may hide malicious characters",
            RiskLevel::Medium,
            "Review escaped characters for suspicious homoglyphs or invisible characters",
            vec!["js", "ts", "json", "py"],
        )?;

        self.add_pattern(
            PatternType::SuspiciousEncoding,
            r"\\x[0-9a-fA-F]{2}",
            "Hexadecimal escape sequences potentially hiding malicious content",
            RiskLevel::Medium,
            "Decode and verify the actual characters being represented",
            vec!["js", "ts", "py", "c", "cpp"],
        )?;        // Obfuscated code patterns
        self.add_pattern(
            PatternType::ObfuscatedCode,
            r#"eval\s*\(\s*['"][^'"]*['"]\s*\)"#,
            "Dynamic code execution via eval() - high risk for code injection",
            RiskLevel::Critical,
            "Replace eval() with safer alternatives or strict input validation",
            vec!["js", "ts"],
        )?;

        self.add_pattern(
            PatternType::ObfuscatedCode,
            r#"Function\s*\(\s*['"][^'"]*['"]\s*\)"#,
            "Dynamic function creation - potential code injection vector",
            RiskLevel::High,
            "Use predefined functions instead of dynamic function creation",
            vec!["js", "ts"],
        )?;

        self.add_pattern(
            PatternType::ObfuscatedCode,
            r#"exec\s*\(\s*['"][^'"]*['"]\s*\)"#,
            "Dynamic code execution in Python - potential command injection",
            RiskLevel::Critical,
            "Use ast.literal_eval() or safer alternatives",
            vec!["py"],
        )?;

        // Potential injection patterns
        self.add_pattern(
            PatternType::PotentialInjection,
            r#"SELECT\s+.*\s+WHERE\s+.*=\s*['"][^'"]*\+"#,
            "Potential SQL injection via string concatenation",
            RiskLevel::High,
            "Use parameterized queries instead of string concatenation",
            vec!["js", "ts", "py", "php", "sql"],
        )?;        self.add_pattern(
            PatternType::CommandInjection,
            r#"(system|exec|shell_exec|passthru|popen)\s*\(\s*['"][^'"]*\$"#,
            "Command injection via user input concatenation",
            RiskLevel::Critical,
            "Sanitize input and use parameterized commands",
            vec!["php", "py", "sh"],
        )?;

        // Hidden characters detection
        self.add_pattern(
            PatternType::HiddenCharacters,
            r"[\u{200B}\u{200C}\u{200D}\u{FEFF}\u{2060}]+",
            "Invisible Unicode characters that may hide malicious content",
            RiskLevel::High,
            "Remove or replace invisible characters with visible equivalents",
            vec!["*"],
        )?;

        // Malicious patterns
        self.add_pattern(
            PatternType::MaliciousPattern,
            r"(document\.cookie|localStorage|sessionStorage)\s*=",
            "Potential data theft via browser storage manipulation",
            RiskLevel::Medium,
            "Ensure proper access controls and data sanitization",
            vec!["js", "ts", "html"],
        )?;

        self.add_pattern(
            PatternType::DataExfiltration,
            r#"fetch\s*\(\s*['"]https?:\/\/[^'"]*\?\w*="#,
            "Potential data exfiltration via external HTTP requests",
            RiskLevel::High,
            "Validate all external requests and implement CSP headers",
            vec!["js", "ts"],
        )?;

        // Authentication bypass patterns
        self.add_pattern(
            PatternType::AuthenticationBypass,
            r#"(password|token|auth)\s*=\s*['"].*['"]"#,
            "Hardcoded credentials or authentication tokens",
            RiskLevel::Critical,
            "Move credentials to secure environment variables",
            vec!["*"],
        )?;

        self.add_pattern(
            PatternType::AuthenticationBypass,
            r#"if\s*\(\s*(true|false|\d+)\s*\)\s*\{\s*//\s*(auth|login|security)"#,
            "Commented or hardcoded authentication bypasses",
            RiskLevel::High,
            "Remove debug authentication bypasses from production code",
            vec!["*"],
        )?;

        // Code obfuscation patterns
        self.add_pattern(
            PatternType::CodeObfuscation,
            r"String\.fromCharCode\s*\(\s*\d+(?:\s*,\s*\d+)+\s*\)",
            "Character code obfuscation - may hide malicious strings",
            RiskLevel::Medium,
            "Decode and verify the actual string content",
            vec!["js", "ts"],
        )?;

        self.add_pattern(
            PatternType::CodeObfuscation,
            r#"atob\s*\(\s*['"][A-Za-z0-9+/=]+['"]"#,
            "Base64 decoding - may contain obfuscated malicious code",
            RiskLevel::Medium,
            "Decode and review base64 content for malicious code",
            vec!["js", "ts"],
        )?;

        Ok(())
    }

    fn add_pattern(
        &mut self,
        pattern_type: PatternType,
        regex_str: &str,
        description: &str,
        risk_level: RiskLevel,
        mitigation: &str,
        file_types: Vec<&str>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let regex = Regex::new(regex_str)?;
        let rule = PatternRule {
            regex,
            description: description.to_string(),
            risk_level,
            mitigation: mitigation.to_string(),
            file_types: file_types.iter().map(|s| s.to_string()).collect(),
        };

        self.patterns.entry(pattern_type)
            .or_insert_with(Vec::new)
            .push(rule);

        Ok(())
    }

    pub fn analyze_content(&self, content: &str, file_type: &str) -> Vec<PatternThreat> {
        let mut threats = Vec::new();

        for (pattern_type, rules) in &self.patterns {
            for rule in rules {
                // Check if this rule applies to the file type
                if !rule.file_types.contains(&"*".to_string()) && 
                   !rule.file_types.contains(&file_type.to_string()) {
                    continue;
                }

                // Find all matches for this pattern
                for result in rule.regex.find_iter(content) {
                    if let Ok(mat) = result {
                        let position = mat.start();
                        let matched_text = mat.as_str().to_string();
                        let context = self.extract_context(content, position, 100);

                        threats.push(PatternThreat {
                            pattern_type: pattern_type.clone(),
                            risk_level: rule.risk_level.clone(),
                            position,
                    length: mat.end() - mat.start(),
                            matched_text,
                            description: rule.description.clone(),
                            mitigation: rule.mitigation.clone(),
                            context,
                        });
                    }
                }
            }
        }

        threats
    }

    fn extract_context(&self, content: &str, position: usize, window: usize) -> String {
        let start = position.saturating_sub(window);
        let end = (position + window).min(content.len());

        // Ensure we're at valid UTF-8 character boundaries
        let safe_start = content.char_indices()
            .find(|(i, _)| *i >= start)
            .map(|(i, _)| i)
            .unwrap_or(content.len());

        let safe_end = content.char_indices()
            .rev()
            .find(|(i, _)| *i <= end)
            .map(|(i, _)| i)
            .unwrap_or(0);

        if safe_start >= safe_end {
            return String::new();
        }

        // Get the substring and replace newlines for cleaner display
        let context = &content[safe_start..safe_end];
        context.replace('\n', "\\n").replace('\r', "\\r")
    }

    pub fn get_risk_summary(&self, threats: &[PatternThreat]) -> HashMap<RiskLevel, usize> {
        let mut summary = HashMap::new();
        
        for threat in threats {
            *summary.entry(threat.risk_level.clone()).or_insert(0) += 1;
        }
        
        summary
    }

    pub fn calculate_security_score(&self, threats: &[PatternThreat], total_files: usize) -> f32 {
        if threats.is_empty() {
            return 100.0;
        }

        let mut score_deduction = 0.0;
        
        for threat in threats {
            score_deduction += match threat.risk_level {
                RiskLevel::Critical => 20.0,
                RiskLevel::High => 10.0,
                RiskLevel::Medium => 5.0,
                RiskLevel::Low => 1.0,
            };
        }

        // Normalize by number of files
        let normalized_deduction = score_deduction / (total_files as f32).max(1.0);
        
        (100.0 - normalized_deduction).max(0.0)
    }
}
