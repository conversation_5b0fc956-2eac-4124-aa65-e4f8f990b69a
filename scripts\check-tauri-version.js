#!/usr/bin/env node

/**
 * Tauri Version Check Script
 *
 * This script checks if the installed Tauri version is 2.x or higher.
 * It's automatically run during `npm install` via the `preinstall` script.
 *
 * TICKET: TAU-101 - Ensure Tauri v2.x+ Compatibility
 * Priority: High
 * Status: In Progress
 *
 * This script is part of our migration to Tauri v2.x which includes:
 * - API updates for better performance and security
 * - Breaking changes that require code updates
 * - New features we want to leverage
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function checkTauriVersion() {
  try {
    console.log('🔍 Checking Tauri version compatibility for Leptos project...');

    // Check Cargo.toml in src-tauri for Tauri dependency
    const cargoPath = path.join(process.cwd(), 'src-tauri', 'Cargo.toml');
    if (fs.existsSync(cargoPath)) {
      const cargoContent = fs.readFileSync(cargoPath, 'utf8');

      const tauriPatterns = [
        /tauri\s*=\s*"([^"]+)"/,
        /tauri\s*=\s*{\s*version\s*=\s*"([^"]+)"/,
      ];

      let tauriVersion = null;
      for (const pattern of tauriPatterns) {
        const match = cargoContent.match(pattern);
        if (match) {
          tauriVersion = match[1];
          break;
        }
      }

      if (tauriVersion) {
        console.log(`   ✅ tauri (Rust): ${tauriVersion}`);
        if (!tauriVersion.startsWith('2.') && !tauriVersion.includes('2.')) {
          console.error('❌ ERROR: This Leptos project requires Tauri v2.x');
          console.error('   Please update src-tauri/Cargo.toml to use Tauri 2.x');
          process.exit(1);
        }
      } else {
        console.log('   ⚠️  No tauri dependency found in src-tauri/Cargo.toml');
      }
    }

    // Check for Leptos dependencies in main Cargo.toml
    const mainCargoPath = path.join(process.cwd(), 'Cargo.toml');
    if (fs.existsSync(mainCargoPath)) {
      const cargoContent = fs.readFileSync(mainCargoPath, 'utf8');
      if (cargoContent.includes('leptos')) {
        console.log('   ✅ Leptos frontend detected');
      } else {
        console.log('   ⚠️  Leptos dependency not found in main Cargo.toml');
      }
    }

    // Check for Trunk.toml
    const trunkPath = path.join(process.cwd(), 'Trunk.toml');
    if (fs.existsSync(trunkPath)) {
      console.log('   ✅ Trunk configuration found');
    } else {
      console.log('   ⚠️  Trunk.toml not found - needed for Leptos builds');
    }

    console.log('✅ Leptos + Tauri v2 project configuration verified!');

  } catch (error) {
    console.error('❌ Error checking project configuration:', error.message);
    process.exit(1);
  }
}

// Make the script directory if it doesn't exist
const scriptsDir = __dirname;
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir, { recursive: true });
}

checkTauriVersion();
