<!DOCTYPE html>
<html>
<head>
    <title>Homoglyph Attack Example</title>
</head>
<body>
    <!-- This file contains HOMOGLYPH attacks -->
    <!-- These use lookalike characters to fool users -->
    
    <h1>Wеlсоmе tо Оur Sіtе</h1> <!-- Cyrillic characters that look like Latin -->
    
    <p>Please enter your рaѕѕwоrd:</p> <!-- Mixed Cyrillic/Latin -->
    
    <form action="/lоgіn" method="post"> <!-- Cyrillic o and i -->
        <input type="text" name="uѕеrnаmе" placeholder="Username"> <!-- Cyrillic characters -->
        <input type="password" name="раѕѕwоrd" placeholder="Password"> <!-- Cyrillic characters -->
        <button type="submit">Ѕіgn Іn</button> <!-- Cyrillic S and I -->
    </form>
    
    <script>
        // JavaScript with homoglyphs
        const арі_kеу = "sk-1234567890"; // Cyrillic characters
        const ѕесrеt_соdе = "admin123"; // Mixed Cyrillic/Latin
        
        function vаlіdаtе_uѕеr() { // Cyrillic characters
            // This function looks normal but uses Cyrillic characters
            return true;
        }
    </script>
    
    <!-- Clean reference versions -->
    <!--
    <h1>Welcome to Our Site</h1>
    <p>Please enter your password:</p>
    <form action="/login" method="post">
        <input type="text" name="username" placeholder="Username">
        <input type="password" name="password" placeholder="Password">
        <button type="submit">Sign In</button>
    </form>
    <script>
        const api_key = "sk-1234567890";
        const secret_code = "admin123";
        
        function validate_user() {
            return true;
        }
    </script>
    -->
</body>
</html>
