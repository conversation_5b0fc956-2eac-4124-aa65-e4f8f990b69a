# 📖 Bad Character Scanner - User Manual

**Complete user guide for the Bad Character Scanner v0.3.1**

---

## 🚀 **Quick Start Guide**

### **⚡ Get Started in 5 Minutes**
| Task | Action | Time |
|------|--------|------|
| **Download** | Get latest release from GitHub | 30 seconds |
| **Install** | Run installer for your platform | 2 minutes |
| **Launch** | Open Bad Character Scanner | 10 seconds |
| **First Scan** | Drop a file or paste text | 30 seconds |
| **View Results** | See analysis and threats | 1 minute |

### **🎯 Choose Your Interface**
| Interface | Best For | Quick Access |
|-----------|----------|--------------|
| **🖥️ Desktop GUI** | General users, interactive analysis | Launch app |
| **⌨️ Command Line** | Developers, automation, CI/CD | `bcs-cli analyze` |
| **📜 Bash Scripts** | Advanced automation, batch processing | `./analyze.sh` |

---

## 📋 **User Manual Contents**

### **🎯 Quick Navigation**
- [**PART I: CLI COMMANDS & AUTOMATION**](#part-i-cli-commands--automation) - For developers, automation, and power users
- [**PART II: GUI USER INTERFACE**](#part-ii-gui-user-interface) - For desktop application users

### **📚 Detailed Contents**

**PART I: CLI COMMANDS & AUTOMATION**
1. [CLI Overview and Setup](#1-cli-overview-and-setup)
2. [CLI Binary Commands](#2-cli-binary-commands)
3. [Bash Script Interface](#3-bash-script-interface)
4. [Output Formats and Export](#4-output-formats-and-export)
5. [CLI Automation and Integration](#5-cli-automation-and-integration)

**PART II: GUI USER INTERFACE**
6. [GUI Installation and Setup](#6-gui-installation-and-setup)
7. [Desktop Application Guide](#7-desktop-application-guide)
8. [GUI Features and Usage](#8-gui-features-and-usage)

**SHARED RESOURCES**
9. [System Requirements](#9-system-requirements)
10. [Security Features](#10-security-features)
11. [Error Handling and Troubleshooting](#11-error-handling-and-troubleshooting)
12. [Advanced Features and Tips](#12-advanced-features-and-tips)
13. [FAQ](#13-faq)

---

## 🛡️ **What is Bad Character Scanner?**

The Bad Character Scanner (BCS) v0.3.1 is a **production-ready desktop application** designed to detect, analyze, and clean suspicious Unicode characters from text files and entire codebases. Built with **Leptos + Tauri v2**, it provides enterprise-grade security analysis with a modern user experience.

### **🎯 Security Threats Detected**
| Threat Type | Description | Risk Level |
|-------------|-------------|------------|
| **Homograph Attacks** | Visually similar characters from different scripts | 🔴 High |
| **Invisible Characters** | Zero-width spaces, control characters | 🔴 High |
| **Bidirectional Attacks** | Manipulating text direction | 🟠 Medium |
| **Script Mixing** | Malicious combination of writing systems | 🟠 Medium |
| **Encoding Issues** | Character encoding problems | 🟡 Low |

### **🎨 Available Interfaces**
1. **🖥️ Desktop GUI** - Professional desktop application with intuitive interface
2. **⌨️ Command Line** - Direct binary for automation and CI/CD integration
3. **📜 Bash Scripts** - Advanced scripting wrapper with batch processing capabilities

---

# PART I: CLI COMMANDS & AUTOMATION

*This section covers all command-line interfaces, automation, and scripting capabilities*

---

## 1. CLI Overview and Setup

### 1.1. Three Command-Line Interfaces

The Bad Character Scanner provides three powerful command-line interfaces:

1. **CLI Binary** (`analyzer_cli.exe`) - Direct, minimal interface for automation
2. **Bash Script** (`scripts/codebase_analyzer.sh`) - Enhanced wrapper with colors and features  
3. **PowerShell Integration** - Native Windows scripting support

### 1.2. Quick CLI Setup (Windows)

```powershell
# Navigate to project directory
cd "C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS"

# Build CLI binary (first time only)
cd src-tauri
cargo build --release --bin analyzer_cli
cd ..

# Test CLI is working
.\target\release\analyzer_cli.exe

# Output should show usage information
```

### 1.3. CLI Advantages

- **Automation-ready**: Perfect exit codes for scripting
- **CI/CD integration**: Works in build pipelines
- **Batch processing**: Handle multiple files efficiently
- **No GUI overhead**: Faster for large-scale analysis
- **Cross-platform**: Works on Windows, macOS, Linux

---

## 2. CLI Binary Commands

### 2.1. Command Syntax

```bash
# Windows
.\target\release\analyzer_cli.exe <command> <path> [options]

# Linux/macOS
./target/release/analyzer_cli <command> <path> [options]
```

### 2.2. Available Commands

#### **analyze** - Analyze files for suspicious characters

**Purpose**: Scan files for Unicode security threats and generate analysis reports.

**Syntax**:
```bash
.\target\release\analyzer_cli.exe analyze <file_path> [output_format]
```

**Parameters**:
- `<file_path>`: Path to file to analyze (required)
- `[output_format]`: json (default), text, markdown

**Examples**:
```powershell
# Basic analysis (JSON output)
.\target\release\analyzer_cli.exe analyze "myfile.js"

# Human-readable text output
.\target\release\analyzer_cli.exe analyze "script.py" text

# Markdown format for documentation
.\target\release\analyzer_cli.exe analyze "config.xml" markdown

# Real test file
.\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json
```

**Sample Output (JSON)**:
```json
{
  "total_files": 1,
  "files_with_issues": 1,
  "total_suspicious_chars": 7,
  "health_score": 99.29,
  "file_details": [
    {
      "file_path": "test_data_secure\\zero_width_attack.js",
      "suspicious_characters": 7,
      "issues": [
        "ZERO WIDTH SPACE (U+200B)",
        "ZERO WIDTH NON-JOINER (U+200C)"
      ]
    }
  ]
}
```

#### **export** - Convert analysis results to different formats

**Purpose**: Transform existing JSON analysis results into other formats.

**Syntax**:
```bash
.\target\release\analyzer_cli.exe export <analysis_file> [output_format]
```

**Parameters**:
- `<analysis_file>`: Path to JSON analysis file (required)
- `[output_format]`: json, text, markdown, html

**❗ IMPORTANT**: The export command requires a JSON file that was created by the analyze command. You must first run analyze and save its output to a file, then use that file with export.

**Analyze-to-Export Workflow**:
```powershell
# Step 1: Run analysis and save JSON output to file (UTF-8 encoding)
.\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json | Out-File -FilePath "analysis_results.json" -Encoding UTF8

# Step 2: Convert the JSON file to other formats
.\target\release\analyzer_cli.exe export "analysis_results.json" markdown
.\target\release\analyzer_cli.exe export "analysis_results.json" text
```

**Complete Workflow Examples**:
```powershell
# Analyze a file and create multiple report formats
.\target\release\analyzer_cli.exe analyze "myfile.js" json | Out-File -FilePath "scan_results.json" -Encoding UTF8
.\target\release\analyzer_cli.exe export "scan_results.json" markdown > report.md
.\target\release\analyzer_cli.exe export "scan_results.json" text > report.txt

# Working example with test files
.\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json | Out-File -FilePath "test_results.json" -Encoding UTF8
.\target\release\analyzer_cli.exe export "test_results.json" markdown
```

**Examples**:
```powershell
# Convert to markdown report
.\target\release\analyzer_cli.exe export "results.json" markdown

# Convert to human-readable text
.\target\release\analyzer_cli.exe export "scan_results.json" text

# Re-format JSON (clean up formatting)
.\target\release\analyzer_cli.exe export "messy_results.json" json
```

#### **clean** - Remove suspicious characters from files

**Purpose**: Create cleaned versions of files with suspicious characters removed.

**Syntax**:
```bash
.\target\release\analyzer_cli.exe clean <input_file> [output_file]
```

**Parameters**:
- `<input_file>`: Path to file to clean (required)
- `[output_file]`: Output path (optional, auto-generated if not provided)

**Examples**:
```powershell
# Clean file (auto-generate output name)
.\target\release\analyzer_cli.exe clean "suspicious_file.txt"
# Creates: suspicious_file_cleaned.txt

# Clean with specific output name
.\target\release\analyzer_cli.exe clean "input.js" "safe_output.js"

# Clean test file
.\target\release\analyzer_cli.exe clean "test_data_secure\zero_width_attack.js"
# Creates: zero_width_attack_cleaned.js
```

**Sample Output**:
```json
{
  "status": "success",
  "input_file": "test_data_secure\\zero_width_attack.js",
  "output_file": "zero_width_attack_cleaned.js",
  "message": "File cleaned successfully"
}
```

### 2.3. CLI Exit Codes

The CLI uses standard exit codes for automation:

- **0**: Success - Operation completed without issues
- **1**: General error - Unexpected failure
- **2**: Invalid arguments - Check command syntax
- **3**: File/directory not found - Path doesn't exist
- **4**: Analysis failed - File processing error
- **5**: Export failed - Format conversion error
- **6**: Dependency missing - Required tools not found

**PowerShell Exit Code Handling**:
```powershell
.\target\release\analyzer_cli.exe analyze "file.txt" json
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Analysis successful"
} else {
    Write-Host "❌ Analysis failed with code $LASTEXITCODE"
}
```

### 2.4. Complete Working Example

Here's a step-by-step demonstration using the actual CLI with test data:

#### **Step 1: Analyze a Malicious File**
```powershell
# Analyze file with suspicious characters
PS C:\Leptos_TaurieV2_BCS> .\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json

# Output: Clean JSON with analysis results
{
  "analysis_time_ms": 100,
  "export_timestamp": "2025-06-13 01:11:57 UTC-0700",
  "export_version": "1.0",
  "file_details": [
    {
      "analysis_status": "success",
      "encoding": "UTF-8",
      "file_path": "test_data_secure\\zero_width_attack.js",
      "file_size": 1003,
      "file_type": "js",
      "issues": [
        "ZERO WIDTH SPACE (U+200B)",
        "ZERO WIDTH NON-JOINER (U+200C)",
        "ZERO WIDTH JOINER (U+200D)"
      ],
      "suspicious_characters": 7,
      "total_characters": 987
    }
  ],
  "files_with_issues": 1,
  "health_score": 99.29,
  "tool_version": "Bad Character Scanner v0.2.0",
  "total_files": 1,
  "total_suspicious_chars": 7
}
```

#### **Step 2: Save Analysis to File and Export Reports**
```powershell
# Save analysis results (using manual file creation for reliability)
PS C:\Leptos_TaurieV2_BCS> .\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json > temp.json
# Copy output to properly encoded JSON file manually

# Export to markdown report
PS C:\Leptos_TaurieV2_BCS> .\target\release\analyzer_cli.exe export "analysis_results.json" markdown

# Output: Professional markdown report
# 📊 Codebase Analysis Report
**Generated:** 2025-06-13 01:13:10
**Health Score:** 99.3% 🟢 Excellent

## 🔍 Files Requiring Attention
### 1. zero_width_attack.js
- **Suspicious Characters**: 7
- **Issues**: ZERO WIDTH SPACE (U+200B), ZERO WIDTH NON-JOINER (U+200C), ZERO WIDTH JOINER (U+200D)
```

#### **Step 3: Clean Malicious File**
```powershell
# Remove suspicious characters
PS C:\Leptos_TaurieV2_BCS> .\target\release\analyzer_cli.exe clean "test_data_secure\zero_width_attack.js"

# Output: Confirmation of successful cleaning
{
  "status": "success",
  "input_file": "test_data_secure\\zero_width_attack.js",
  "output_file": "zero_width_attack_cleaned.js", 
  "message": "File cleaned successfully"
}

# File size reduction: 1003 bytes → 982 bytes (21 bytes of malicious characters removed)
```

#### **Step 4: Verify Cleaning Success**
```powershell
# Analyze the cleaned file
PS C:\Leptos_TaurieV2_BCS> .\target\release\analyzer_cli.exe analyze "zero_width_attack_cleaned.js" json

# Output: Clean file with no issues
{
  "files_with_issues": 0,
  "total_suspicious_chars": 0,
  "health_score": 100.0,
  "file_details": [
    {
      "analysis_status": "success",
      "suspicious_characters": 0,
      "issues": []
    }
  ]
}
```

---

## 3. Bash Script Interface

### 3.1. Enhanced Bash Wrapper

The Bash script (`scripts/codebase_analyzer.sh`) provides an enhanced interface with:
- **Visual feedback**: Colors, emojis, progress indicators
- **Enhanced logging**: Verbose output and error handling
- **Built-in testing**: System validation and health checks
- **Demonstration mode**: Sample data and examples

### 3.2. Bash Script Setup

#### Windows (Git Bash/WSL)
```powershell
# Using Git Bash
cd "C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS"
& "C:\Program Files\Git\bin\bash.exe" -c "chmod +x scripts/codebase_analyzer.sh"

# Using WSL (if installed)
wsl chmod +x scripts/codebase_analyzer.sh
```

#### Linux/macOS
```bash
cd /path/to/project
chmod +x scripts/codebase_analyzer.sh
```

### 3.3. Bash Script Commands

#### **Basic Syntax**
```bash
./scripts/codebase_analyzer.sh [OPTIONS] <COMMAND> [ARGS...]
```

#### **analyze** - Enhanced directory analysis
```bash
# Basic directory analysis
./scripts/codebase_analyzer.sh analyze /path/to/codebase

# With specific output format
./scripts/codebase_analyzer.sh --format markdown analyze /project

# Verbose output for debugging
./scripts/codebase_analyzer.sh --verbose analyze /code

# Custom output directory
./scripts/codebase_analyzer.sh --output ./security_reports analyze /project
```

#### **export** - Enhanced format conversion
```bash
# Export with visual feedback
./scripts/codebase_analyzer.sh export analysis_results.json

# Convert to specific format
./scripts/codebase_analyzer.sh --format text export results.json
```

#### **scan** - Quick single file analysis
```bash
# Quick file scan with summary
./scripts/codebase_analyzer.sh scan suspicious_file.js

# Detailed scan with verbose output
./scripts/codebase_analyzer.sh --verbose scan malware.py
```

#### **test** - Built-in system testing
```bash
# Run comprehensive system tests
./scripts/codebase_analyzer.sh test

# Test with detailed output
./scripts/codebase_analyzer.sh --verbose test

# Dry run tests (show what would be tested)
./scripts/codebase_analyzer.sh --dry-run test
```

#### **demo** - Demonstration mode
```bash
# Run demonstration with sample data
./scripts/codebase_analyzer.sh demo

# Demo with specific output format
./scripts/codebase_analyzer.sh --format markdown demo
```

#### **health** - System diagnostics
```bash
# Check system health and dependencies
./scripts/codebase_analyzer.sh health

# Detailed health check
./scripts/codebase_analyzer.sh --verbose health
```

### 3.4. Bash Script Options

#### Global Options
- `-f, --format FORMAT`: Output format (json, markdown, text)
- `-o, --output DIR`: Output directory (default: ./reports)
- `-v, --verbose`: Enable detailed logging
- `-d, --dry-run`: Show actions without executing
- `-q, --quiet`: Suppress non-error output
- `-h, --help`: Show help message

#### Option Examples
```bash
# Combine multiple options
./scripts/codebase_analyzer.sh --verbose --format markdown --output /tmp/reports analyze /project

# Quiet mode for automation
./scripts/codebase_analyzer.sh --quiet scan file.js > results.log

# Dry run to preview actions
./scripts/codebase_analyzer.sh --dry-run analyze /large/project
```

### 3.5. Bash Script Dependencies

#### Required Dependencies
- **cargo**: Rust toolchain (for building analyzer)
- **jq**: JSON processor (for enhanced output formatting)

#### Dependency Installation
```bash
# Ubuntu/Debian
sudo apt-get install jq

# CentOS/RHEL  
sudo yum install jq

# macOS
brew install jq

# Windows (Chocolatey)
choco install jq

# Windows (Scoop)
scoop install jq
```

#### Dependency Check
```bash
# Check what's missing
./scripts/codebase_analyzer.sh health

# Sample output:
# ✅ Rust: rustc 1.87.0
# ✅ Cargo: cargo 1.87.0  
# ❌ jq: Not found
```

---

## 4. Output Formats and Export

### 4.1. JSON Format (Machine-Readable)

**Use Cases**: Automation, API integration, data processing

**Structure**:
```json
{
  "analysis_metadata": {
    "version": "0.2.0",
    "timestamp": "2025-06-13T00:30:26Z",
    "analysis_type": "file_scan"
  },
  "codebase_analysis": {
    "total_files": 1,
    "files_with_issues": 1,
    "total_suspicious_chars": 7,
    "health_score": 99.29
  },
  "file_details": [
    {
      "file_path": "test_data_secure\\zero_width_attack.js",
      "file_size": 1003,
      "suspicious_characters": 7,
      "issues": [
        "ZERO WIDTH SPACE (U+200B)",
        "ZERO WIDTH NON-JOINER (U+200C)"
      ],
      "analysis_status": "success"
    }
  ]
}
```

### 4.2. Text Format (Human-Readable)

**Use Cases**: Quick review, terminal output, logging

**Sample**:
```
BAD CHARACTER SCANNER ANALYSIS REPORT
====================================

File: zero_width_attack.js
Size: 1,003 bytes
Status: Issues Found (7)

Issues:
1. Zero-width space (U+200B) at position 245
2. Zero-width non-joiner (U+200C) at position 340

Health Score: 99.3%
Recommendation: Clean suspicious characters
```

### 4.3. Markdown Format (Documentation)

**Use Cases**: Reports, documentation, team sharing

**Sample**:
```markdown
# 📊 Security Analysis Report

**File**: zero_width_attack.js  
**Health Score**: 99.3% ✅

## 🔍 Issues Found

| Position | Character | Unicode | Risk Level |
|----------|-----------|---------|------------|
| 245 | (invisible) | U+200B | Medium |
| 340 | (invisible) | U+200C | Medium |

## 📋 Recommendations

1. **High Priority**: Remove zero-width characters
2. **Medium Priority**: Review file source for tampering
```

---

## 5. CLI Automation and Integration

### 5.1. PowerShell Automation

#### Pre-commit Hook
```powershell
# .git/hooks/pre-commit.ps1
$changedFiles = git diff --cached --name-only --diff-filter=ACM
foreach ($file in $changedFiles) {
    if (Test-Path $file) {
        & ".\target\release\analyzer_cli.exe" analyze $file json | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "❌ Suspicious characters found in $file"
            exit 1
        }
    }
}
Write-Host "✅ All files passed security scan"
```

#### Batch Security Audit
```powershell
# security_audit.ps1
param(
    [Parameter(Mandatory=$true)][string]$ProjectPath,
    [string]$OutputDir = ".\security_reports"
)

$fileTypes = @("*.js", "*.ts", "*.py", "*.java", "*.cpp", "*.cs")
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$reportFile = "$OutputDir\security_audit_$timestamp.json"

New-Item -ItemType Directory -Force -Path $OutputDir

$allResults = @()
foreach ($pattern in $fileTypes) {
    $files = Get-ChildItem -Path $ProjectPath -Filter $pattern -Recurse
    foreach ($file in $files) {
        Write-Host "Scanning: $($file.Name)"
        $result = & ".\target\release\analyzer_cli.exe" analyze $file.FullName json
        if ($LASTEXITCODE -eq 0) {
            $allResults += $result | ConvertFrom-Json
        }
    }
}

$allResults | ConvertTo-Json -Depth 10 | Out-File $reportFile
Write-Host "✅ Security audit complete: $reportFile"
```

### 5.2. CI/CD Integration

#### GitHub Actions
```yaml
# .github/workflows/security-scan.yml
name: Unicode Security Scan
on: [push, pull_request]

jobs:
  security-scan:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Build Scanner
        run: |
          cd src-tauri
          cargo build --release --bin analyzer_cli
          
      - name: Scan Source Files
        run: |
          $failed = $false
          Get-ChildItem -Include "*.js","*.ts","*.py" -Recurse | ForEach-Object {
            Write-Host "Scanning: $($_.Name)"
            & ".\target\release\analyzer_cli.exe" analyze $_.FullName json
            if ($LASTEXITCODE -ne 0) { $failed = $true }
          }
          if ($failed) { exit 1 }
        shell: pwsh
        
      - name: Upload Security Report
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: security-scan-results
          path: security_reports/
```

#### Azure DevOps Pipeline
```yaml
# azure-pipelines.yml
trigger:
- main
- develop

pool:
  vmImage: 'windows-latest'

steps:
- task: PowerShell@2
  displayName: 'Unicode Security Scan'
  inputs:
    targetType: 'inline'
    script: |
      cd src-tauri
      cargo build --release --bin analyzer_cli
      cd ..
      
      $hasIssues = $false
      Get-ChildItem -Include "*.cs","*.js","*.ts" -Recurse | ForEach-Object {
        Write-Host "##[command]Scanning: $($_.Name)"
        $result = & ".\target\release\analyzer_cli.exe" analyze $_.FullName json
        if ($LASTEXITCODE -ne 0) {
          Write-Host "##[warning]Issues found in: $($_.Name)"
          $hasIssues = $true
        }
      }
      
      if ($hasIssues) {
        Write-Host "##[error]Security issues detected in codebase"
        exit 1
      }
```

### 5.3. Cross-Platform Scripting

#### Linux/macOS Shell Script
```bash
#!/bin/bash
# scan_project.sh - Cross-platform security scanner

PROJECT_DIR="${1:-./}"
OUTPUT_DIR="./security_reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

mkdir -p "$OUTPUT_DIR"

echo "🔍 Starting security scan of: $PROJECT_DIR"

# Build scanner if needed
if [ ! -f "./target/release/analyzer_cli" ]; then
    echo "🔧 Building scanner..."
    cd src-tauri && cargo build --release --bin analyzer_cli && cd ..
fi

# Scan files
find "$PROJECT_DIR" -type f \( -name "*.js" -o -name "*.py" -o -name "*.java" \) | while read -r file; do
    echo "📄 Scanning: $(basename "$file")"
    if ! ./target/release/analyzer_cli analyze "$file" json > /dev/null; then
        echo "⚠️  Issues found in: $file"
        echo "$file" >> "$OUTPUT_DIR/flagged_files_$TIMESTAMP.txt"
    fi
done

echo "✅ Scan complete. Results in: $OUTPUT_DIR/"
```

---

## PART II: GUI USER INTERFACE

*This section covers the desktop application interface and visual features*

---

## 6. GUI Installation and Setup

### 6.1. Desktop Application Overview

The Bad Character Scanner GUI is a modern desktop application built with Leptos and Tauri v2, providing:

- **Intuitive Interface**: User-friendly tabs and visual feedback
- **Real-time Analysis**: Instant character detection and reporting
- **Drag & Drop**: Easy file and folder selection
- **Visual Reports**: Charts, tables, and formatted output
- **Export System**: Multiple format downloads
- **Cross-platform**: Windows, macOS, and Linux support

### 6.2. Installation Methods

#### Option 1: Pre-built Application (Recommended)
1. **Download**: Get the latest release for your platform
   - **Windows**: `Bad_Character_Scanner_0.2.0_x64_en-US.msi`
   - **macOS**: `Bad Character Scanner.app.tar.gz`
   - **Linux**: `bad-character-scanner_0.2.0_amd64.deb`

2. **Install**:
   - **Windows**: Run the MSI installer or extract portable EXE
   - **macOS**: Extract and drag to Applications folder
   - **Linux**: `sudo dpkg -i bad-character-scanner_0.2.0_amd64.deb`

#### Option 2: Build from Source
```bash
# Prerequisites: Rust + Trunk
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
cargo install trunk wasm-bindgen-cli

# Clone and build
git clone <repository-url>
cd Leptos_TaurieV2_BCS
cargo tauri build
```

### 6.3. Windows 11 Startup Instructions

#### Method 1: Desktop Application (GUI)
```powershell
# Launch development version
PS C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS> cargo tauri dev
```

**Step-by-step**:
1. **Open PowerShell or Terminal**
2. **Navigate to project directory**:
   ```powershell
   cd "C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS"
   ```
3. **Start development server**:
   ```powershell
   cargo tauri dev
   ```
4. **Application launches automatically** with hot reloading enabled

#### Method 2: Production Application
1. **Locate installed application**:
   - Start Menu → "Bad Character Scanner"
   - Or double-click `laptos-tauri.exe` in installation folder

2. **First launch security**:
   - Windows may show "Unknown publisher" warning
   - Click "More info" → "Run anyway" (first time only)

3. **Create desktop shortcut** (optional):
   - Right-click executable → "Create shortcut"
   - Move to Desktop for quick access

---

## 7. Desktop Application Guide

### 7.1. Main Interface Overview

The GUI features a clean, modern interface with intuitive navigation:

#### **Application Layout**
```
┌─────────────────────────────────────────────────────────┐
│ 🔍 Bad Character Scanner v0.2.0          [─] [□] [×]    │
├─────────────────────────────────────────────────────────┤
│ [Text Analysis] [Codebase Analysis] [Reports] [Settings] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                  MAIN CONTENT AREA                      │
│            (Changes based on selected tab)              │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ Status: Ready | Health Score: --% | Files: 0 | Issues: 0│
└─────────────────────────────────────────────────────────┘
```

#### **Tab Navigation**
- **Text Analysis**: Single text/file analysis
- **Codebase Analysis**: Directory and project scanning  
- **Reports & Results**: View and export analysis results
- **Settings**: Configuration and preferences

### 7.2. Text Analysis Tab

#### **Purpose**: Analyze individual text snippets or small files

#### **Interface Elements**:
```
┌─────────────────────────────────────────────────────────┐
│ 📝 Text Analysis                                        │
├─────────────────────────────────────────────────────────┤
│ Input Text:                              [Load File]    │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Paste or type your text here...                     │ │
│ │                                                     │ │
│ │                                                     │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ [Analyze Text] [Clear] [Copy Cleaned]                  │
├─────────────────────────────────────────────────────────┤
│ Results:                                                │
│ ✅ Characters: 1,250 | Suspicious: 3 | Health: 99.2%   │
│                                                         │
│ Issues Found:                                           │
│ • Zero-width space (U+200B) at position 245            │
│ • Right-to-left override (U+202E) at position 890      │
└─────────────────────────────────────────────────────────┘
```

#### **Step-by-Step Usage**:

1. **Input Text**:
   - **Type/Paste**: Direct text input in the text area
   - **Load File**: Click "Load File" to import text files
   - **Drag & Drop**: Drop text files directly onto the interface

2. **Analyze**:
   - Click **"Analyze Text"** button
   - Real-time processing with progress indicator
   - Results appear instantly below

3. **Review Results**:
   - **Character Count**: Total characters processed
   - **Suspicious Count**: Number of problematic characters found
   - **Health Score**: Overall safety rating (0-100%)
   - **Issue List**: Detailed breakdown of each problem

4. **Clean Text** (optional):
   - Click **"Clean Text"** to remove suspicious characters
   - Cleaned version appears in results area
   - Use **"Copy Cleaned"** to copy safe text

#### **Visual Indicators**:
- 🟢 **Green**: Clean text (no issues)
- 🟡 **Yellow**: Minor issues (warnings)
- 🔴 **Red**: Critical security threats
- 📊 **Health Score Bar**: Visual percentage indicator

### 7.3. Codebase Analysis Tab

#### **Purpose**: Scan entire directories, projects, and codebases

#### **Interface Elements**:
```
┌─────────────────────────────────────────────────────────┐
│ 📁 Codebase Analysis                                    │
├─────────────────────────────────────────────────────────┤
│ Selected Folder:                                        │
│ ┌─────────────────────────────────────┐ [Browse] [×]    │
│ │ C:\Users\<USER>\MyProject             │                 │
│ └─────────────────────────────────────┘                 │
│                                                         │
│ Options:                                                │
│ ☑ Include subdirectories    ☑ Common file types        │
│ ☐ Include hidden files      ☐ Advanced patterns        │
│                                                         │
│ [Analyze Codebase] [Stop] [Clear Results]              │
├─────────────────────────────────────────────────────────┤
│ Progress: ████████░░ 80% (120/150 files)               │
│ Current: src/components/UserAuth.tsx                    │
│ Estimated: 2 minutes remaining                          │
├─────────────────────────────────────────────────────────┤
│ Results Summary:                                        │
│ 📊 Health Score: 94.2% | 🗂️ Files: 150 | ⚠️ Issues: 12 │
└─────────────────────────────────────────────────────────┘
```

#### **Step-by-Step Usage**:

1. **Select Target Directory**:
   - **Browse Button**: Open folder picker dialog
   - **Drag & Drop**: Drag folder directly onto interface
   - **Recent Folders**: Quick access to previously scanned directories

2. **Configure Options**:
   - **Include subdirectories**: Recursive scanning (recommended)
   - **File type filters**: Focus on specific file types
   - **Hidden files**: Include dot-files and system files
   - **Advanced patterns**: Custom exclusion rules

3. **Start Analysis**:
   - Click **"Analyze Codebase"**
   - **Real-time Progress**: Progress bar with file counts
   - **Current File Display**: Shows file being processed
   - **Time Estimation**: Estimated completion time
   - **Cancel Option**: Stop analysis at any time

4. **Monitor Progress**:
   ```
   Progress Indicators:
   ████████░░ 80% (120/150 files)
   
   Status Messages:
   • "Scanning directory structure..."
   • "Processing: src/main.js (45/150)"
   • "Analysis complete!"
   ```

5. **Review Results**:
   - **Health Score**: Overall codebase security rating
   - **File Statistics**: Total files vs. files with issues
   - **Issue Summary**: Count and types of problems found
   - **Detailed Breakdown**: Per-file analysis results

### 7.4. Reports & Results Tab

#### **Purpose**: View detailed results and export analysis data

#### **Interface Elements**:
```
┌─────────────────────────────────────────────────────────┐
│ 📊 Reports & Results                                    │
├─────────────────────────────────────────────────────────┤
│ Analysis: MyProject_20250613_143022                     │
│ Generated: June 13, 2025 at 2:30 PM                    │
│                                                         │
│ ┌─ Executive Summary ────────────────────────────────┐   │
│ │ Health Score: 94.2% 🟢                            │   │
│ │ Files Scanned: 150 | Issues Found: 12            │   │
│ │ Risk Level: Low | Recommendation: Review flagged │   │
│ └───────────────────────────────────────────────────┘   │
│                                                         │
│ ┌─ Export Options ──────────────┐                       │
│ │ Format: ○ JSON ● Markdown     │ [Export Report]        │
│ │         ○ HTML ○ Plain Text   │                       │
│ └───────────────────────────────┘                       │
├─────────────────────────────────────────────────────────┤
│ Detailed Results:                                       │
│ ▼ Files with Issues (12)                               │
│   📄 src/auth/login.js - 3 issues                      │
│   📄 lib/utils/text.py - 2 issues                      │
│   📄 config/settings.xml - 1 issue                     │
│ ▼ Clean Files (138)                                    │
│   📄 src/main.js ✅                                     │
│   📄 README.md ✅                                       │
└─────────────────────────────────────────────────────────┘
```

#### **Features**:

1. **Executive Summary**:
   - Overall health score with visual indicator
   - Quick statistics (files, issues, risk level)
   - Actionable recommendations

2. **Export System**:
   - **Format Selection**: JSON, Markdown, HTML, Plain Text
   - **Native File Dialog**: OS-integrated save dialog
   - **Instant Export**: One-click report generation
   - **Success Feedback**: Confirmation messages

3. **Detailed Breakdown**:
   - **Expandable Sections**: Click to expand/collapse
   - **File-by-File Results**: Individual file analysis
   - **Issue Details**: Specific character problems found
   - **Quick Actions**: Jump to file, copy path, re-analyze

4. **Interactive Elements**:
   - **Click file names**: Open file location
   - **Hover for details**: Character information tooltips
   - **Copy buttons**: Copy file paths or issue descriptions
   - **Filter options**: Show only files with issues

### 7.5. Visual Feedback System

#### **Color-Coded Status Indicators**
- 🟢 **Green**: Clean, no security issues
- 🟡 **Yellow**: Minor warnings, review recommended  
- 🔴 **Red**: Critical security threats, immediate action needed
- 🔵 **Blue**: Information, statistics, help text
- ⚫ **Gray**: Disabled, processing, or inactive elements

#### **Interactive Elements**
```
Visual Feedback Examples:

Health Score Meter:
[████████░░] 94.2% 🟢 Excellent

Progress Bars:
Analysis: [██████████] 100% Complete ✅
Export:   [████░░░░░░] 40% Processing...

Status Messages:
✅ Analysis completed successfully
⚠️ 3 files contain suspicious characters  
❌ Failed to read file: permission denied
ℹ️ Tip: Use drag-and-drop for easy file selection
```

### 7.6. Drag & Drop Functionality

#### **Supported Drop Targets**
- **Text Analysis**: Drop text files (.txt, .md, .js, .py, etc.)
- **Codebase Analysis**: Drop directories/folders
- **Reports**: Drop existing JSON analysis files

#### **Drop Zones**
```
Visual Drop Zones:

┌─────────────────────────────────┐
│  📁 Drop folder here            │
│     or click Browse             │
│                                 │
│  Supported: Any directory       │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  📄 Drop text file here         │
│     or paste content            │
│                                 │
│  Supported: .txt, .js, .py, etc│
└─────────────────────────────────┘
```

### 7.7. Keyboard Shortcuts

#### **Global Shortcuts**
- `Ctrl+O`: Open file/folder dialog
- `Ctrl+S`: Save/Export current results
- `Ctrl+N`: New analysis (clear current)
- `Ctrl+R`: Refresh/Re-analyze
- `F5`: Reload application
- `Escape`: Cancel current operation
- `F1`: Show help/about dialog

#### **Tab Navigation**
- `Ctrl+1`: Text Analysis tab
- `Ctrl+2`: Codebase Analysis tab  
- `Ctrl+3`: Reports & Results tab
- `Ctrl+4`: Settings tab
- `Ctrl+Tab`: Next tab
- `Ctrl+Shift+Tab`: Previous tab

#### **Text Area Shortcuts**
- `Ctrl+A`: Select all text
- `Ctrl+C`: Copy selected text
- `Ctrl+V`: Paste text
- `Ctrl+Z`: Undo text changes
- `Ctrl+Y`: Redo text changes

### 7.8. Settings and Configuration

#### **Application Settings**
```
Settings Panel:

┌─ General Settings ────────────────────────────────┐
│ ☑ Auto-analyze on text change                    │
│ ☑ Show character details in tooltips             │
│ ☑ Play sounds for notifications                  │
│ ☐ Remember window size and position              │
└───────────────────────────────────────────────────┘

┌─ Analysis Options ────────────────────────────────┐
│ Sensitivity: ○ Low ● Medium ○ High               │
│ File size limit: [100] MB                        │
│ ☑ Include common programming files               │
│ ☐ Scan binary files                              │
└───────────────────────────────────────────────────┘

┌─ Export Preferences ──────────────────────────────┐
│ Default format: [Markdown     ▼]                 │
│ Output directory: [C:\Reports...] [Browse]       │
│ ☑ Include timestamps in filenames                │
│ ☑ Open folder after export                       │
└───────────────────────────────────────────────────┘
```

#### **Theme and Appearance**
- **Light/Dark Mode**: Automatic or manual toggle
- **Font Size**: Adjustable for accessibility
- **Color Schemes**: Multiple options for colorblind users
- **UI Density**: Compact or comfortable spacing

### 7.9. Error Handling and User Feedback

#### **Error Messages**
```
User-Friendly Error Messages:

❌ File Access Error
   Could not read "locked_file.txt"
   → Try running as administrator
   → Check if file is in use by another program

⚠️ Large File Warning  
   File "huge_log.txt" is 500MB
   → Consider using CLI for better performance
   → Continue anyway? [Yes] [No]

ℹ️ Processing Info
   Analyzing large directory...
   → 1,250 files found
   → Estimated time: 5 minutes
   → [Cancel] to stop anytime
```

#### **Success Notifications**
```
Success Messages:

✅ Analysis Complete!
   📊 Health Score: 98.5%
   📁 150 files scanned
   ⚠️ 3 files need attention
   → [View Results] [Export Report]

✅ Export Successful!
   📄 Report saved to: C:\Reports\analysis_20250613.md
   → [Open File] [Open Folder] [Share]

✅ File Cleaned!
   🧹 Removed 7 suspicious characters
   💾 Saved as: document_cleaned.txt
   → [Open Cleaned File] [Compare Files]
```

### 7.10. Performance and Optimization

#### Large File Handling
- **Use CLI instead of GUI** for files > 100MB
- **Break large analyses into smaller chunks**
- **Use --quiet flag** to reduce output overhead
- **Consider using parallel processing** (batch multiple files)

#### Memory Management
- **Close other applications** during analysis
- **Use text format** instead of JSON for large outputs
- **Process files in batches** rather than all at once
- **Use streaming analysis** for very large files

#### Network Storage
- **Copy files locally** before analysis if on network storage
- **Use local output directory** even if source is remote
- **Consider compression** for large report files

---

## 8. Command-Line Interface (CLI) Guide

### 8.1. CLI Binary Overview

The CLI binary (`analyzer_cli.exe` on Windows, `analyzer_cli` on Unix) provides direct access to the core analysis functionality without the GUI overhead.

### 8.2. Basic Usage

```bash
# Basic syntax
analyzer_cli <command> <path> [options]

# Available commands
analyzer_cli analyze <file>         # Analyze a single file
analyzer_cli export <analysis_file> # Export existing analysis
analyzer_cli clean <file>           # Clean a file
```

### 8.3. Commands Reference

#### **analyze** Command
Analyzes a file for suspicious Unicode characters.

```bash
# Basic analysis (JSON output)
.\target\release\analyzer_cli.exe analyze myfile.js

# Specify output format
.\target\release\analyzer_cli.exe analyze myfile.js json      # JSON format (default)
.\target\release\analyzer_cli.exe analyze myfile.js text      # Human-readable text
.\target\release\analyzer_cli.exe analyze myfile.js markdown  # Markdown format

# Windows Examples
.\target\release\analyzer_cli.exe analyze "C:\Users\<USER>\Documents\code.py" json
.\target\release\analyzer_cli.exe analyze "test_data_secure\zero_width_attack.js" json

# Linux/macOS Examples
./target/release/analyzer_cli analyze /home/<USER>/script.sh text
```

**Output**: Analysis results in specified format to stdout

#### **export** Command
Converts existing JSON analysis results to different formats.

```bash
# Convert JSON analysis to other formats
.\target\release\analyzer_cli.exe export analysis_results.json markdown
.\target\release\analyzer_cli.exe export analysis_results.json text
.\target\release\analyzer_cli.exe export analysis_results.json json  # Re-format JSON

# Windows Examples
.\target\release\analyzer_cli.exe export "results_20241201_143022.json" html
.\target\release\analyzer_cli.exe export ".\reports\scan_results.json" text
```

**Output**: Converted results in specified format to stdout

#### **clean** Command
Creates a cleaned version of a file by removing suspicious characters.

```bash
# Clean file (auto-generate output name)
.\target\release\analyzer_cli.exe clean suspicious_file.txt

# Clean file with specific output name
.\target\release\analyzer_cli.exe clean input.js output_cleaned.js

# Windows Examples
.\target\release\analyzer_cli.exe clean "malicious_code.py"
.\target\release\analyzer_cli.exe clean "input.txt" "safe_output.txt"
```

**Output**: JSON status message and cleaned file creation

### 8.4. CLI Output Formats

#### JSON Format (Default)
```json
{
  "total_files": 1,
  "files_with_issues": 1,
  "total_suspicious_chars": 3,
  "health_score": 85.5,
  "analysis_time_ms": 245,
  "file_details": [
    {
      "file_path": "/path/to/file.js",
      "relative_path": "file.js",
      "file_size": 1024,
      "total_characters": 500,
      "suspicious_characters": 3,
      "issues": [
        "Zero-width space (U+200B)",
        "Right-to-left override (U+202E)"
      ],
      "file_type": "js",
      "encoding": "UTF-8",
      "analysis_status": "success"
    }
  ]
}
```

#### Text Format (Human-Readable)
```
📄 File Analysis Report
====================

File: example.js
Path: /home/<USER>/example.js
Size: 1,024 bytes
Type: JavaScript
Encoding: UTF-8

Analysis Results:
✅ Total Characters: 500
⚠️  Suspicious Characters: 3
📊 Health Score: 85.5%

Issues Found:
🔍 Zero-width space (U+200B) at position 125
🔍 Right-to-left override (U+202E) at position 340
🔍 Zero-width non-joiner (U+200C) at position 456

Recommendations:
- Review suspicious characters for potential security issues
- Consider cleaning the file to remove threats
```

#### Markdown Format (Documentation)
```markdown
# Bad Character Scanner Analysis Report

**Generated**: December 1, 2024 at 14:30:22 UTC  
**Version**: 0.2.0  
**Target**: /path/to/project  

## 📊 Summary

- **Total Files Analyzed**: 150
- **Files with Issues**: 12 (8.0%)
- **Total Suspicious Characters**: 35
- **Health Score**: 92.3% ✅
- **Analysis Time**: 5.42 seconds

## 🔍 Detailed Results

### src/main.js
- **File Size**: 2,048 bytes
- **Total Characters**: 1,500
- **Issues Found**: 2

**Problems Detected**:
1. 🚩 Zero-width space (U+200B) at position 245
2. 🚩 Right-to-left override (U+202E) at position 890

### lib/utils.py
- **File Size**: 1,024 bytes
- **Total Characters**: 800
- **Status**: ✅ Clean

## 📋 Recommendations

1. **High Priority**: Review files with RTL override characters
2. **Medium Priority**: Clean zero-width spaces from source files
3. **Low Priority**: Consider adding pre-commit hooks for continuous scanning

## 📈 Statistics

| File Type | Files Scanned | Files with Issues | Issue Rate |
|-----------|---------------|-------------------|------------|
| JavaScript | 45 | 5 | 11.1% |
| Python | 38 | 3 | 7.9% |
| HTML | 25 | 2 | 8.0% |
| CSS | 20 | 1 | 5.0% |
| Other | 22 | 1 | 4.5% |
```

#### HTML Format (Rich Reports)

**Use cases**: Presentation, detailed analysis, web viewing

**Features**:
- Interactive elements (collapsible sections, hover details)
- Syntax highlighting for code samples
- Charts and visualizations
- Responsive design for different screen sizes
- Print-friendly styles

---

---

## 9. Error Handling and Troubleshooting

### 9.1. Common Error Messages

#### "File not found" Errors
```
❌ [ERROR] File not found: /path/to/file.txt
```
**Solution**: Verify the file path is correct and the file exists.

#### "Directory not found" Errors
```
❌ [ERROR] Directory not found: /nonexistent/path
```
**Solution**: Check the directory path and ensure it exists.

#### "Permission denied" Errors
```
❌ [ERROR] Permission denied: /restricted/file.txt
```
**Solution**: Ensure you have read permissions for the file/directory.

#### "Analysis failed" Errors
```
❌ [ERROR] Analysis failed: Invalid UTF-8 sequence
```
**Solution**: The file may contain invalid encoding. Try specifying a different encoding or use a text editor to fix encoding issues.

#### "Export failed" Errors
```
❌ [ERROR] Export failed: Unable to write to output directory
```
**Solution**: Check write permissions for the output directory.

#### "Missing dependencies" Errors
```
❌ [ERROR] Missing dependencies:
  ❌ cargo (Rust toolchain)
  ❌ jq (JSON processor)
```
**Solution**: Install the missing dependencies:
- **Rust**: https://rustup.rs/
- **jq**: https://stedolan.github.io/jq/download/

### 9.2. CLI-Specific Troubleshooting

#### **Export Command Issues**

**Error**: `Export failed: No such file or directory`
```powershell
❌ Export failed: No such file or directory (os error 2)
```
**Cause**: The JSON analysis file doesn't exist.
**Solution**: 
1. First run analyze command and save output to file:
   ```powershell
   .\target\release\analyzer_cli.exe analyze "myfile.js" json > results.json
   ```
2. Then run export on the created file:
   ```powershell
   .\target\release\analyzer_cli.exe export "results.json" markdown
   ```

**Error**: `Export failed: Invalid JSON format`
```powershell
❌ Export failed: EOF while parsing a value at line 1 column 0
```
**Cause**: The input file is not valid JSON or is empty.
**Solution**: 
1. Check if the JSON file has content: `Get-Content results.json`
2. Verify JSON format: `Get-Content results.json | ConvertFrom-Json`
3. Re-run the analyze command to generate a fresh JSON file

**Error**: `Export failed: Format conversion error`
```powershell
❌ Export failed: Format conversion error
```
**Cause**: Unknown or unsupported output format.
**Solution**: Use only supported formats: `json`, `text`, `markdown`

#### **Analyze Command Issues**

**Error**: `Analysis failed: Path does not exist`
```powershell
❌ Analysis failed: Path does not exist: nonexistent_file.js  
```
**Solution**: Check file path and ensure file exists:
```powershell
# Check if file exists
Test-Path "myfile.js"
# Use absolute path if needed
.\target\release\analyzer_cli.exe analyze "C:\full\path\to\file.js"
```

**Error**: `Analysis failed: Permission denied`
```powershell
❌ Analysis failed: Permission denied (os error 5)
```
**Solution**: Run PowerShell as Administrator or check file permissions.

#### **Clean Command Issues**

**Error**: `Clean failed: Unable to create output file`
```powershell
❌ Clean failed: Unable to create output file
```
**Solution**: 
1. Check write permissions in the target directory
2. Specify a different output location:
   ```powershell
   .\target\release\analyzer_cli.exe clean "input.js" "C:\temp\cleaned_output.js"
   ```

### 9.3. GUI Troubleshooting

#### Application Won't Start
1. **Check system requirements** - Ensure your system meets minimum requirements
2. **Run as administrator** - Try running with elevated privileges
3. **Check antivirus** - Temporarily disable antivirus to test
4. **Reinstall application** - Uninstall and reinstall the application

#### UI Freezing or Slow Performance
1. **Reduce analysis scope** - Analyze smaller directories first
2. **Close other applications** - Free up system resources
3. **Check available memory** - Ensure sufficient RAM is available
4. **Use CLI for large analyses** - Command-line interface is more efficient for large datasets

#### Export Not Working
1. **Check output directory** - Ensure the directory exists and is writable
2. **Verify file permissions** - Check that you have write access
3. **Try different format** - Test with different export formats
4. **Check disk space** - Ensure sufficient free space

### 9.4. Bash Script Troubleshooting

#### Script Not Executable
```bash
# Error: Permission denied
# Solution: Make script executable
chmod +x scripts/codebase_analyzer.sh
```

#### Bash Not Available
```bash
# Error: /bin/bash: No such file or directory
# Solution: Install bash or use different shell
# Windows: Install Git Bash or WSL
# macOS: bash is pre-installed
# Linux: sudo apt-get install bash
```

#### Script Fails with Dependencies
```bash
# Error: Missing dependencies
# Solution: Install required tools
# Ubuntu/Debian: sudo apt-get install jq
# CentOS/RHEL: sudo yum install jq
# macOS: brew install jq
# Windows: Download from https://stedolan.github.io/jq/download/
```

### 9.5. Performance Optimization

#### Large File Analysis
- **Use CLI instead of GUI** for files > 100MB
- **Break large analyses into smaller chunks**
- **Use --quiet flag** to reduce output overhead
- **Consider using parallel processing** (batch multiple files)

#### Memory Management
- **Close other applications** during analysis
- **Use text format** instead of JSON for large outputs
- **Process files in batches** rather than all at once
- **Use streaming analysis** for very large files

#### Network Storage
- **Copy files locally** before analysis if on network storage
- **Use local output directory** even if source is remote
- **Consider compression** for large report files

---

## 10. Advanced Features and Tips

### 10.1. Automation and Scripting

#### Pre-commit Hook Integration
```bash
#!/bin/bash
# .git/hooks/pre-commit
# Scan staged files for suspicious characters

staged_files=$(git diff --cached --name-only --diff-filter=ACM)
for file in $staged_files; do
    if ./analyzer_cli analyze "$file" >/dev/null 2>&1; then
        echo "✅ $file: Clean"
    else
        echo "❌ $file: Contains suspicious characters"
        echo "Commit aborted. Please clean the file first."
        exit 1
    fi
done
```

#### CI/CD Pipeline Integration
```yaml
# .github/workflows/security-scan.yml
name: Security Character Scan
on: [push, pull_request]

jobs:
  scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - name: Build scanner
        run: cargo build --release --bin analyzer_cli
      - name: Scan codebase
        run: |
          find . -type f \( -name "*.py" -o -name "*.js" -o -name "*.ts" \) \
            -exec ./target/release/analyzer_cli analyze {} \; \
            -exec echo "Scanned: {}" \;
```

#### Batch Processing Script
```bash
#!/bin/bash
# batch_analyze.sh - Analyze multiple projects

projects=(
    "/path/to/project1"
    "/path/to/project2"
    "/path/to/project3"
)

for project in "${projects[@]}"; do
    echo "Analyzing: $project"
    ./scripts/codebase_analyzer.sh \
        --format markdown \
        --output "./reports/$(basename "$project")" \
        analyze "$project"
done

# Generate summary report
echo "# Security Scan Summary" > ./reports/summary.md
echo "Generated: $(date)" >> ./reports/summary.md
for project in "${projects[@]}"; do
    health_score=$(grep "Health Score" "./reports/$(basename "$project")"/*.md | head -1)
    echo "- $(basename "$project"): $health_score" >> ./reports/summary.md
done
```

### 10.2. Custom Configuration

#### File Type Filtering
```bash
# Analyze only specific file types
find /project -name "*.js" -o -name "*.ts" -o -name "*.jsx" | \
while read file; do
    ./analyzer_cli analyze "$file" json
done
```

#### Custom Exclusion Patterns
```bash
# Skip certain directories
./scripts/codebase_analyzer.sh analyze /project | \
grep -v "node_modules\|\.git\|target\|build"
```

#### Environment-Specific Configuration
```bash
# development.conf
export BCS_OUTPUT_DIR="./dev_reports"
export BCS_FORMAT="text"
export BCS_VERBOSE="true"

# production.conf
export BCS_OUTPUT_DIR="/var/log/security_scans"
export BCS_FORMAT="json"
export BCS_VERBOSE="false"

# Load configuration
source ./production.conf
./scripts/codebase_analyzer.sh analyze /production/code
```

### 10.3. Integration with Development Tools

#### VS Code Extension Integration
```json
{
  "tasks": [
    {
      "label": "Scan for Bad Characters",
      "type": "shell",
      "command": "./analyzer_cli",
      "args": ["analyze", "${file}", "text"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    }
  ]
}
```

#### Sublime Text Build System
```json
{
  "shell_cmd": "./analyzer_cli analyze \"$file\" text",
  "file_regex": "^(.+):(\\d+):(\\d+): (.+)$",
  "working_dir": "${project_path}",
  "selector": "source"
}
```

### 10.4. Performance Tuning

#### Parallel Processing
```bash
# Process multiple files in parallel
find /large/project -name "*.py" | \
xargs -P 4 -I {} ./analyzer_cli analyze {} json
```

#### Memory-Efficient Processing
```bash
# Process large directories in chunks
find /huge/project -name "*.js" | \
split -l 100 - /tmp/batch_
for batch in /tmp/batch_*; do
    cat "$batch" | while read file; do
        ./analyzer_ci analyze "$file" json
    done
done
```

#### Caching Results
```bash
# Cache analysis results to avoid re-processing
cache_file="/tmp/analysis_cache_$(echo "$1" | md5sum | cut -d' ' -f1)"
if [ -f "$cache_file" ]; then
    cat "$cache_file"
else
    ./analyzer_cli analyze "$1" json | tee "$cache_file"
fi
```

---

---

## SHARED RESOURCES

*This section covers features and information common to both CLI and GUI interfaces*

---

## 9. System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, Linux (Ubuntu 18.04+)
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 500 MB free space
- **Processor**: Intel Core i3 or AMD equivalent

### For CLI/Bash Interface
- **Windows**: PowerShell 5.1+ or Windows Subsystem for Linux (WSL)
- **macOS/Linux**: Bash 4.0+
- **Dependencies**: 
  - Rust toolchain (cargo) - for building from source
  - jq (JSON processor) - for enhanced output formatting

### For GUI Interface
- **Graphics**: DirectX 11 compatible (Windows), Metal (macOS), OpenGL 3.3 (Linux)
- **Display**: 1024x768 minimum resolution, 1920x1080 recommended
- **Network**: Internet connection for updates (optional)

---

## 10. Security Features

### 11.1. Character Categories Detected

#### Invisible Characters
- **Zero-Width Space (U+200B)**: Used to create invisible breakpoints
- **Zero-Width Non-Joiner (U+200C)**: Prevents character joining
- **Zero-Width Joiner (U+200D)**: Forces character joining
- **Zero-Width No-Break Space (U+FEFF)**: Byte Order Mark (BOM)
- **Word Joiner (U+2060)**: Invisible word connector

#### Directional Control Characters
- **Left-to-Right Override (U+202D)**: Forces LTR text direction
- **Right-to-Left Override (U+202E)**: Forces RTL text direction
- **Left-to-Right Embedding (U+202A)**: Embeds LTR text
- **Right-to-Left Embedding (U+202B)**: Embeds RTL text
- **Pop Directional Formatting (U+202C)**: Ends directional formatting

#### Homograph Characters
- **Cyrillic lookalikes**: а (U+0430) vs a (U+0061)
- **Greek lookalikes**: ο (U+03BF) vs o (U+006F)
- **Mathematical symbols**: 𝒂 (U+1D482) vs a (U+0061)
- **Fullwidth characters**: ａ (U+FF41) vs a (U+0061)

### 11.2. Attack Vectors Detected

#### Domain Spoofing
```
Example: google.com vs gοοgle.com (contains Greek omicron)
Detection: Mixed script analysis and homograph detection
```

#### Code Injection
```
Example: eval('alert("hello")'); // Invisible comment​<script>
Detection: Zero-width character detection in code contexts
```

#### Social Engineering
```
Example: "Click here: http://bank.com" (contains RTL override)
Detection: Bidirectional text analysis
```

#### Data Hiding
```
Example: Normal text with embedded zero-width spaces encoding data
Detection: Pattern analysis and statistical methods
```

### 11.3. Security Assessment Levels

#### Health Score Calculation
- **100%**: No suspicious characters detected
- **90-99%**: Minor formatting issues only
- **70-89%**: Some suspicious characters, likely benign
- **50-69%**: Multiple suspicious characters, review recommended
- **0-49%**: High number of suspicious characters, immediate review required

#### Risk Categories
- 🟢 **Low Risk**: Common Unicode characters, proper encoding
- 🟡 **Medium Risk**: Unusual characters, potential formatting issues
- 🔴 **High Risk**: Known attack vectors, security-relevant characters
- ⚫ **Critical**: Multiple attack indicators, immediate action required

### 11.4. Recommended Security Practices

#### Development Guidelines
1. **Use UTF-8 encoding** consistently across all files
2. **Avoid copying code** from untrusted sources without scanning
3. **Review all external input** before including in codebase
4. **Implement pre-commit hooks** for automatic scanning
5. **Regular security audits** of existing codebases

#### Incident Response
1. **Immediate isolation** of affected files
2. **Analysis of impact** and potential data exposure
3. **Cleaning and validation** of all affected content
4. **Root cause analysis** to prevent future incidents
5. **Documentation and reporting** of security findings

---

---

## 13. FAQ

### Q: What file types are supported?
**A**: The scanner supports all text-based files including:
- **Source code**: .js, .py, .java, .cpp, .rs, .go, .php, etc.
- **Web files**: .html, .css, .xml, .json, .yaml
- **Documentation**: .md, .txt, .rst, .tex
- **Configuration**: .conf, .ini, .cfg, .env
- **Scripts**: .sh, .bat, .ps1, .pl

### Q: How large files can be analyzed?
**A**: 
- **GUI**: Recommended limit of 100MB per file
- **CLI**: Can handle files up to 1GB (limited by available RAM)
- **Bash script**: No hard limit, but performance may degrade with very large files

### Q: Is the analysis safe for sensitive code?
**A**: Yes, the analysis is performed locally on your machine. No data is sent to external servers. All processing happens offline.

### Q: Can I customize what characters are considered suspicious?
**A**: Currently, the suspicious character list is built-in based on security research. Custom configuration will be available in future versions.

### Q: Does the scanner modify my original files?
**A**: No, the analysis functions are read-only. Only the explicit "clean" commands create new files, leaving originals untouched.

### Q: What's the difference between the CLI and Bash script?
**A**: 
- **CLI binary**: Direct, minimal interface for automation
- **Bash script**: Enhanced wrapper with colors, logging, testing, and user-friendly features

### Q: Can I use this in my CI/CD pipeline?
**A**: Yes, the CLI binary is designed for automation and returns proper exit codes for script integration.

### Q: How accurate is the detection?
**A**: The scanner has a very low false-positive rate. Most detections are genuine security concerns, but context matters. Review all findings.

### Q: What should I do if suspicious characters are found?
**A**: 
1. **Review the context** - determine if the characters are intentional
2. **Use the clean function** if the characters are unwanted
3. **Investigate the source** - how did these characters get into your files?
4. **Implement preventive measures** - pre-commit hooks, regular scanning

### Q: Does the scanner work offline?
**A**: Yes, the scanner works completely offline. No internet connection is required for analysis.

### Q: How often should I scan my codebase?
**A**: Recommendations:
- **Real-time**: Pre-commit hooks for all changes
- **Daily**: Automated scans in CI/CD pipeline
- **Weekly**: Manual comprehensive scans
- **Before releases**: Complete security audit

### Q: Can I contribute to the project?
**A**: This is a private project, but feedback and bug reports are welcome through the established channels.

---

## 📞 Support and Resources

### Documentation
- **Complete Documentation**: `docs/README.md`
- **Quick Reference**: `docs/guides/QUICK_REFERENCE.md`
- **Features Guide**: `docs/guides/FEATURES.md`
- **Architecture**: `docs/project/ARCHITECTURE.md`

### Getting Help
- **Built-in Help**: Use `--help` flag with any command
- **Error Messages**: All error messages include helpful suggestions
- **Log Files**: Check temporary log files for detailed error information
- **Health Check**: Use `health` command to diagnose system issues

### Updates and Maintenance
- **Current Version**: 0.2.0 (Production Ready)
- **Update Check**: Built-in version information in all interfaces
- **Backward Compatibility**: All output formats are versioned

---

**© 2025 Bad Character Scanner - Production Ready v0.2.0**  
**MIT License - Built with Leptos + Tauri v2**
