// Enhanced Analysis Engine for Bad Character Scanner
// This module provides advanced analysis capabilities including:
// - Complex pattern detection and learning
// - Risk assessment and security scoring
// - Trend analysis and reporting
// - Performance profiling and optimization

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::path::Path;
use std::fs;
use std::time::Instant;
use fancy_regex::Regex;
use crate::modules::{CharacterAnalyzer, CharacterInfo}; // CharacterInfo is from data_structures via modules re-export
use crate::modules::data_structures::{
    CharacterFinding as DS_CharacterFinding, // Alias for clarity if needed, or use directly
    SecurityVulnerability,
    VulnerabilityLocation,
    SeverityLevel as DS_SeverityLevel, // Aliased
    // Removed unused IssueLocation import
    Recommendation as DS_Recommendation, // Aliased
    RecommendationCategory as DS_RecommendationCategory, // Aliased
    // ... other necessary structs from data_structures
};

// Simple placeholder types that are missing
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProcessingStats {
    pub start_time: String,
    pub end_time: String,
    pub duration_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ComplianceStatus {
    pub owasp_compliant: bool,
    pub iso_compliant: bool,
    pub custom_checks: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CharacterAnalysisDetails {
    pub total_characters_analyzed: usize,
    pub suspicious_characters_found: usize,
    pub character_frequency_map: HashMap<String, usize>,
    pub normalization_issues: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct FileTypeStats {
    pub file_count: usize,
    pub total_size: usize,
    pub issues_found: usize,
    pub average_risk_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct EncodingAnalysisDetails {
    pub detected_encodings: Vec<String>,
    pub encoding_conflicts: Vec<String>,
    pub mixed_encoding_files: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct FalsePositiveAnalysis {
    pub total_potential_false_positives: usize,
    pub false_positive_rate: f64,
    pub confidence_adjustments: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternLocation {
    pub line: usize,
    pub column: usize,
    pub length: usize,
    pub file_path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternContext {
    pub surrounding_text: String,
    pub affected_function: Option<String>,
    pub code_language: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BusinessImpact {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RemediationEffort {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum ExposureRating {
    #[default]
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EstimatedEffort {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImpactLevel {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplexityLevel {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct IOMetrics {
    pub files_read: usize,
    pub bytes_processed: usize,
    pub read_operations: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationSuggestion {
    pub suggestion_id: String,
    pub category: String,
    pub title: String,
    pub description: String,
    pub impact: ImpactLevel,
    pub implementation_complexity: ComplexityLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImplementationGuide {
    pub steps: Vec<String>,
    pub code_examples: Vec<String>,
    pub tools_required: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AttackSurfaceAnalysis {
    pub total_attack_vectors: usize,
    pub high_risk_areas: Vec<String>,
    pub exposure_rating: ExposureRating,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ThreatModelingResults {
    pub identified_threats: Vec<String>,
    pub threat_scenarios: Vec<String>,
    pub risk_matrix: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemediationRoadmap {
    pub immediate_actions: Vec<String>,
    pub short_term_goals: Vec<String>,
    pub long_term_strategy: Vec<String>,
    pub estimated_timeline_days: u32,
}

impl Default for RemediationRoadmap {
    fn default() -> Self {
        Self {
            immediate_actions: Vec::new(),
            short_term_goals: Vec::new(),
            long_term_strategy: Vec::new(),
            estimated_timeline_days: 30,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomTemplate {
    pub name: String,
    pub config: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmergingPattern {
    pub pattern_id: String,
    pub confidence: f64,
    pub first_seen: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternCorrelation {
    pub pattern1: String,
    pub pattern2: String,
    pub correlation_strength: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MLInsights {
    pub model_confidence: f64,
    pub feature_importance: HashMap<String, f64>,
    pub anomaly_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoricalDataPoint {
    pub timestamp: DateTime<Utc>,
    pub issue_count: usize,
    pub risk_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TrendMetrics {
    pub issue_count_trend: Vec<f64>,
    pub risk_score_trend: Vec<f64>,
    pub performance_trend: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PredictiveAnalysis {
    pub predicted_issue_count: f64,
    pub confidence_interval: (f64, f64),
    pub risk_forecast: Vec<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeasonalPattern {
    pub pattern_name: String,
    pub seasonality_score: f64,
    pub peak_periods: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AnomalyDetection {
    pub anomalies_detected: Vec<String>,
    pub anomaly_threshold: f64,
    pub detection_algorithm: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceGap { // Local definition
    pub standard: String,
    pub requirement: String,
    pub current_status: String,
    pub gap_severity: String, // This is a String, not DS_SeverityLevel
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCategory {
    pub category_name: String,
    pub risk_score: f64,
    pub likelihood: f64,
    pub impact: f64,
    pub findings: Vec<String>,
    pub mitigations: Vec<String>,
    pub residual_risk: f64,
}

// Enhanced Analysis Result Structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedAnalysisResult {
    pub metadata: AnalysisMetadata,
    pub executive_summary: ExecutiveSummary,
    pub detailed_findings: DetailedFindings,
    pub pattern_analysis: PatternAnalysis,
    pub security_assessment: SecurityAssessment,
    pub trend_analysis: Option<TrendAnalysis>,
    pub performance_metrics: PerformanceMetrics,
    pub recommendations: Vec<DS_Recommendation>, // Use aliased type
    pub compliance_status: ComplianceStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisMetadata {
    pub analysis_id: String,
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub template_used: String,
    pub severity_level: DS_SeverityLevel, // Use aliased type
    pub target_path: String,
    pub analyzer_config: AnalyzerConfig,
    pub processing_stats: ProcessingStats,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ExecutiveSummary {
    pub overall_risk_score: f64,
    pub total_files_analyzed: usize,
    pub files_with_issues: usize,
    pub total_suspicious_chars: usize,
    pub critical_issues: usize,
    pub high_issues: usize,
    pub medium_issues: usize,
    pub low_issues: usize,
    pub health_score: f64,
    pub improvement_over_last_scan: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DetailedFindings {
    pub character_analysis: CharacterAnalysisDetails,
    pub file_type_breakdown: HashMap<String, FileTypeStats>,
    pub encoding_analysis: EncodingAnalysisDetails,
    pub vulnerability_catalog: Vec<VulnerabilityFinding>, // Uses local VulnerabilityFinding
    pub false_positive_analysis: FalsePositiveAnalysis,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PatternAnalysis {
    pub detected_patterns: Vec<DetectedPattern>, // Uses local DetectedPattern
    pub pattern_frequencies: HashMap<String, usize>,
    pub emerging_patterns: Vec<EmergingPattern>,
    pub pattern_correlations: Vec<PatternCorrelation>,
    pub machine_learning_insights: Option<MLInsights>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SecurityAssessment {
    pub risk_categories: HashMap<String, RiskCategory>, // Uses local RiskCategory
    pub attack_surface_analysis: AttackSurfaceAnalysis,
    pub threat_modeling: ThreatModelingResults,
    pub compliance_gaps: Vec<ComplianceGap>, // Uses local ComplianceGap
    pub remediation_roadmap: RemediationRoadmap,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TrendAnalysis {
    pub historical_data: Vec<HistoricalDataPoint>,
    pub trend_metrics: TrendMetrics,
    pub predictive_analysis: PredictiveAnalysis,
    pub seasonal_patterns: Vec<SeasonalPattern>,
    pub anomaly_detection: AnomalyDetection,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PerformanceMetrics {
    pub analysis_duration_ms: u64,
    pub memory_usage_mb: f64,
    pub cpu_utilization: f64,
    pub io_operations: IOMetrics,
    pub cache_hit_rate: f64,
    pub optimization_suggestions: Vec<OptimizationSuggestion>,
}

// REMOVED local SeverityLevel enum, will use DS_SeverityLevel from data_structures

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportTemplate {
    Minimal,
    Standard,
    Comprehensive,
    Security,
    Performance,
    Compliance,
    Custom(CustomTemplate),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyzerConfig {
    pub enable_pattern_detection: bool,
    pub enable_machine_learning: bool,
    pub enable_trend_analysis: bool,
    pub enable_performance_profiling: bool,
    pub batch_size: usize,
    pub max_file_size_mb: f64,
    pub confidence_threshold: f64,
    pub pattern_learning_enabled: bool,
    pub auto_fix_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectedPattern { // Local definition
    pub pattern_id: String,
    pub pattern_type: PatternType, // Uses local PatternType
    pub pattern_name: String,
    pub description: String,
    pub severity: DS_SeverityLevel, // Use aliased type
    pub confidence: f64,
    pub locations: Vec<PatternLocation>,
    pub context: PatternContext,
    pub remediation_guidance: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PatternType { // Local definition
    BidirectionalOverride,
    ZeroWidthCharacter,
    HomographAttack,
    SupplyChainIndicator,
    DataExfiltration,
    InjectionVector,
    PhishingIndicator,
    EncodingAnomaly,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityFinding { // Local definition
    pub vulnerability_id: String,
    pub cve_references: Vec<String>,
    pub owasp_category: String,
    pub severity: DS_SeverityLevel, // Use aliased type
    pub exploitability_score: f64,
    pub business_impact: BusinessImpact, // Uses local BusinessImpact
    pub affected_files: Vec<String>,
    pub proof_of_concept: Option<String>,
    pub remediation_effort: RemediationEffort, // Uses local RemediationEffort
}

// Definition for SingleFileAnalysisResult (used locally)
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SingleFileAnalysisResult {
    pub file_path: String,
    pub file_size: usize,
    pub character_findings: Vec<DS_CharacterFinding>, // Uses data_structures::CharacterFinding (aliased)
    pub encoding_info: EncodingAnalysisDetails,    // Local struct
    pub patterns_found: Vec<DetectedPattern>,      // Local struct (uses DS_SeverityLevel via its definition)
    pub security_issues: Vec<SecurityVulnerability>, // Uses data_structures::SecurityVulnerability
    pub line_count: usize,
}


// Enhanced Analysis Engine Implementation
pub struct EnhancedAnalysisEngine {
    analyzer: CharacterAnalyzer,
    config: AnalyzerConfig,
}

impl EnhancedAnalysisEngine {
    pub fn new(config: AnalyzerConfig) -> Result<Self, anyhow::Error> {
        Ok(Self {
            analyzer: CharacterAnalyzer::new()?,
            config,
        })
    }

    /// Perform comprehensive analysis on a directory or file
    pub async fn analyze_comprehensive<P: AsRef<Path>>(&self, target_path: P) -> anyhow::Result<EnhancedAnalysisResult> {
        let start_time = Instant::now();
        let target_path_ref = target_path.as_ref(); // Keep a reference for display
        
        log::info!("Starting enhanced analysis of: {}", target_path_ref.display());
        
        let analysis_id = Uuid::new_v4().to_string();
        let timestamp = Utc::now();
        
        let mut result = EnhancedAnalysisResult {
            metadata: AnalysisMetadata {
                analysis_id: analysis_id.clone(),
                timestamp,
                version: "2.0.0".to_string(),
                template_used: "comprehensive".to_string(),
                severity_level: DS_SeverityLevel::High, // Use aliased type
                target_path: target_path_ref.display().to_string(),
                analyzer_config: self.config.clone(),
                processing_stats: ProcessingStats::default(),
            },
            executive_summary: ExecutiveSummary::default(),
            detailed_findings: DetailedFindings::default(),
            pattern_analysis: PatternAnalysis::default(),
            security_assessment: SecurityAssessment::default(),
            trend_analysis: None,
            performance_metrics: PerformanceMetrics::default(),
            recommendations: Vec::new(),
            compliance_status: ComplianceStatus::default(),
        };

        let files_to_analyze = self.collect_files(target_path_ref).await?;
        let total_files = files_to_analyze.len();
        
        log::info!("Found {} files to analyze", total_files);
        
        let mut all_findings: Vec<SingleFileAnalysisResult> = Vec::new(); // Explicit type
        let mut character_stats_map: HashMap<String, usize> = HashMap::new(); // Renamed for clarity
        let mut file_type_stats_map: HashMap<String, FileTypeStats> = HashMap::new(); // Renamed for clarity
        
        for (i, file_path) in files_to_analyze.iter().enumerate() {
            if i % 100 == 0 {
                log::info!("Processing file {}/{}: {}", i + 1, total_files, file_path.display());
            }
            
            match self.analyze_single_file(file_path).await {
                Ok(file_result) => {
                    // Before pushing, ensure file_result.character_findings is Vec<DS_CharacterFinding>
                    // and file_result.security_issues is Vec<SecurityVulnerability>
                    // This should be handled by analyze_single_file's return type.
                    self.update_statistics(&mut character_stats_map, &mut file_type_stats_map, &file_result);
                    all_findings.push(file_result);
                }
                Err(e) => {
                    log::warn!("Failed to analyze file {}: {}", file_path.display(), e);
                }
            }
        }

        result.executive_summary = self.generate_executive_summary(&all_findings, total_files);
        result.detailed_findings = self.generate_detailed_findings(&all_findings, character_stats_map, file_type_stats_map);
        result.pattern_analysis = self.perform_pattern_analysis(&all_findings).await;
        result.security_assessment = self.perform_security_assessment(&all_findings).await;
        
        // Collect all CharacterFinding instances from all_findings for recommendations
        let all_char_findings: Vec<DS_CharacterFinding> = all_findings.iter()
            .flat_map(|f_res| f_res.character_findings.clone()) // f_res.character_findings is Vec<DS_CharacterFinding>
            .collect();
        result.recommendations = self.generate_recommendations(&all_char_findings, &result.security_assessment);
        
        let analysis_duration = start_time.elapsed();
        result.performance_metrics = PerformanceMetrics {
            analysis_duration_ms: analysis_duration.as_millis() as u64,
            memory_usage_mb: self.get_memory_usage(),
            cpu_utilization: 0.0,
            io_operations: IOMetrics::default(),
            cache_hit_rate: 0.0,
            optimization_suggestions: self.generate_optimization_suggestions(&result.executive_summary),
        };
        
        log::info!("Enhanced analysis completed in {:?}", analysis_duration);
        Ok(result)
    }

    /// Collect all files that should be analyzed
    async fn collect_files<P: AsRef<Path>>(&self, target_path: P) -> anyhow::Result<Vec<std::path::PathBuf>> {
        let target_path = target_path.as_ref();
        let mut files = Vec::new();
        
        if target_path.is_file() {
            files.push(target_path.to_path_buf());
        } else if target_path.is_dir() {
            self.collect_files_recursive(target_path, &mut files)?;
        }
        
        // Filter files based on configuration
        files = files.into_iter()
            .filter(|f| self.should_analyze_file(f))
            .collect();
            
        Ok(files)
    }

    fn collect_files_recursive<P: AsRef<Path>>(&self, dir: P, files: &mut Vec<std::path::PathBuf>) -> anyhow::Result<()> {
        let dir = dir.as_ref();
        
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                // Skip common directories that shouldn't be analyzed
                if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                    if matches!(name, "node_modules" | ".git" | "target" | "build" | "dist" | "__pycache__") {
                        continue;
                    }
                }
                self.collect_files_recursive(&path, files)?;
            } else if path.is_file() {
                files.push(path);
            }
        }
        
        Ok(())
    }

    fn should_analyze_file<P: AsRef<Path>>(&self, file_path: P) -> bool {
        let file_path = file_path.as_ref();
        
        // Check file size
        if let Ok(metadata) = file_path.metadata() {
            let size_mb = metadata.len() as f64 / (1024.0 * 1024.0);
            if size_mb > self.config.max_file_size_mb {
                return false;
            }
        }
        
        // Check file extension - focus on text files
        if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
            let text_extensions = [
                "rs", "js", "ts", "py", "java", "cpp", "c", "h", "hpp",
                "html", "css", "scss", "json", "xml", "yaml", "yml",
                "md", "txt", "cfg", "ini", "conf", "sh", "bat", "ps1",
                "go", "rb", "php", "sql", "vue", "svelte", "jsx", "tsx"
            ];
            return text_extensions.contains(&ext.to_lowercase().as_str());
        }
        
        true
    }

    /// Analyze a single file for suspicious characters and patterns
    async fn analyze_single_file<P: AsRef<Path>>(&self, file_path: P) -> anyhow::Result<SingleFileAnalysisResult> {
        let file_path_ref = file_path.as_ref();
        let content = fs::read_to_string(file_path_ref)?;
        
        let mut result = SingleFileAnalysisResult {
            file_path: file_path_ref.display().to_string(),
            file_size: content.len(),
            character_findings: Vec::new(), // Will be Vec<DS_CharacterFinding>
            encoding_info: EncodingAnalysisDetails::default(),
            patterns_found: Vec::new(), // Will be Vec<DetectedPattern> (local, uses DS_SeverityLevel)
            security_issues: Vec::new(), // Will be Vec<SecurityVulnerability> from data_structures
            line_count: content.lines().count(),
        };
        
        for (line_idx, line) in content.lines().enumerate() {
            for (char_idx, ch) in line.char_indices() {
                let char_info = self.analyzer.analyze_character(ch, char_idx); // CharacterInfo from data_structures
                if char_info.is_suspicious {
                    result.character_findings.push(DS_CharacterFinding { // Use aliased type
                        finding_id: Uuid::new_v4().to_string(), 
                        character: ch,
                        line_number: line_idx + 1,
                        column_number: char_idx + 1,
                        character_info: char_info.clone(),
                        context: self.extract_context(line, char_idx),
                        severity: self.assess_character_severity(&char_info), // Returns DS_SeverityLevel
                    });
                }
            }
        }

        result.patterns_found = self.detect_patterns(&content); // DetectedPattern.severity is DS_SeverityLevel
        result.security_issues = self.analyze_security_issues(&content, &result.character_findings); // Expects &[DS_CharacterFinding]
        
        Ok(result)
    }

    fn extract_context(&self, line: &str, char_idx: usize) -> String {
        let start = char_idx.saturating_sub(10);
        let end = std::cmp::min(char_idx + 10, line.len());
        line[start..end].to_string()
    }

    fn assess_character_severity(&self, char_info: &CharacterInfo) -> DS_SeverityLevel { // Return DS_SeverityLevel
        match char_info.category.as_str() {
            "Format" if char_info.unicode_name.contains("DIRECTIONAL") => DS_SeverityLevel::High,
            "Format" if char_info.unicode_name.contains("ZERO WIDTH") => DS_SeverityLevel::Medium,
            "Other" if char_info.unicode_name.contains("OBJECT REPLACEMENT") => DS_SeverityLevel::High,
            // Add mapping for Critical and Paranoid if needed, or ensure they are covered
            _ => DS_SeverityLevel::Low,
        }
    }

    fn detect_patterns(&self, content: &str) -> Vec<DetectedPattern> { // DetectedPattern.severity is DS_SeverityLevel
        let mut patterns = Vec::new();
        if let Ok(regex) = Regex::new(r"[\\u202D\\u202E]") {
            match regex.find_iter(content) {
                Ok(iter) => {
                    for mat in iter {
                        patterns.push(DetectedPattern {
                            pattern_id: Uuid::new_v4().to_string(),
                            pattern_type: PatternType::BidirectionalOverride,
                            pattern_name: "Bidirectional Text Override".to_string(),
                            description: "Detected bidirectional text override characters that can be used for text spoofing attacks".to_string(),
                            severity: DS_SeverityLevel::High, // Use aliased type
                            confidence: 0.9,
                            locations: vec![PatternLocation {
                                line: content[..mat.start()].lines().count(), // Calculate line
                                column: mat.start() - content[..mat.start()].rfind('\n').map_or(0, |i| i + 1), // Calculate column
                                length: mat.len(),
                                file_path: String::new(), // Placeholder, should be actual file path if available
                            }],
                            context: PatternContext {
                                surrounding_text: content[mat.start().saturating_sub(20)..std::cmp::min(mat.end() + 20, content.len())].to_string(),
                                affected_function: None,
                                code_language: None,
                            },
                            remediation_guidance: "Remove or replace bidirectional override characters with proper text formatting".to_string(),
                        });
                    }
                }
                Err(_) => {}
            }
        }
        if let Ok(regex) = Regex::new(r"[\\u200B\\u200C\\u200D\\u2060\\uFEFF]") {
            match regex.find_iter(content) {
                Ok(iter) => {
                    for mat in iter {
                        patterns.push(DetectedPattern {
                            pattern_id: Uuid::new_v4().to_string(),
                            pattern_type: PatternType::ZeroWidthCharacter,
                            pattern_name: "Zero-Width Characters".to_string(),
                            description: "Detected zero-width characters that may be used to hide malicious code or create visual confusion".to_string(),
                            severity: DS_SeverityLevel::Medium, // Use aliased type
                            confidence: 0.8,
                            locations: vec![PatternLocation {
                                line: content[..mat.start()].lines().count(), // Calculate line
                                column: mat.start() - content[..mat.start()].rfind('\n').map_or(0, |i| i + 1), // Calculate column
                                length: mat.len(),
                                file_path: String::new(), // Placeholder
                            }],
                            context: PatternContext {
                                surrounding_text: content[mat.start().saturating_sub(20)..std::cmp::min(mat.end() + 20, content.len())].to_string(),
                                affected_function: None,
                                code_language: None,
                            },
                            remediation_guidance: "Remove zero-width characters unless they serve a legitimate purpose".to_string(),
                        });
                    }
                }
                Err(_) => {}
            }
        }
        patterns
    }

    // Refactored to return Vec<SecurityVulnerability> and accept &[DS_CharacterFinding]
    fn analyze_security_issues(&self, _content: &str, character_findings: &[DS_CharacterFinding]) -> Vec<SecurityVulnerability> {
        let mut issues: Vec<SecurityVulnerability> = Vec::new(); // Uses data_structures::SecurityVulnerability

        for finding in character_findings { // finding is &DS_CharacterFinding
            if finding.character_info.suspicion_reasons.iter().any(|r| r.contains("SQL keyword")) { 
                issues.push(SecurityVulnerability { 
                    vulnerability_id: Uuid::new_v4().to_string(),
                    vulnerability_type: "Potential SQL Injection Vector".to_string(),
                    severity: DS_SeverityLevel::High, 
                    description: format!(
                        "Character '{}' found at line {}, col {} could be part of an SQL injection attack.", 
                        finding.character, finding.line_number, finding.column_number
                    ),
                    locations: vec![VulnerabilityLocation { 
                        line: finding.line_number, 
                        column: finding.column_number, 
                        span: None 
                    }],
                    remediation_advice: "Use parameterized queries or proper escaping for all user inputs.".to_string(),
                    confidence_score: 0.6, 
                    cvss_score: None, 
                    cwe_id: Some("CWE-89".to_string()),
                });
            }
            // Add more security issue detection logic here based on character_findings or content
        }
        issues 
    }

    // Updated to use DS_Recommendation, DS_RecommendationCategory and accept &[DS_CharacterFinding]
    fn generate_recommendations(&self, character_findings: &[DS_CharacterFinding], 
                                _security_assessment: &SecurityAssessment) -> Vec<DS_Recommendation> {
        let mut recommendations = Vec::new();

        for finding in character_findings { // finding is &DS_CharacterFinding
            if finding.character_info.is_suspicious { 
                recommendations.push(DS_Recommendation { 
                    recommendation_id: Uuid::new_v4().to_string(),
                    category: DS_RecommendationCategory::CharacterAnomaly, 
                    title: format!("Review suspicious character '{}'", finding.character),
                    description: format!(
                        "Review suspicious character '{}' (U+{:04X}) at line {}, column {}. Context: \"{}\"",
                        finding.character, finding.character_info.codepoint, finding.line_number, finding.column_number, finding.context
                    ),
                    priority: 3, 
                    action_steps: vec![
                        "Verify if the character is legitimate in this context.".to_string(),
                        "If malicious, remove or replace the character.".to_string(),
                        "Consider updating input validation rules.".to_string()
                    ],
                    related_findings: vec![finding.finding_id.clone()], 
                    implementation_guide: Some("Refer to documentation on Unicode security best practices.".to_string()),
                    estimated_effort: Some("Low".to_string()),
                    business_value: Some("Medium".to_string()),
                    status: "Open".to_string(),
                });
            }
        }
        // Add recommendations based on security_assessment if needed
        recommendations
    }
    
    // Stubbed/Placeholder implementations for other major functions
    // Ensure these methods are defined on EnhancedAnalysisEngine
    fn update_statistics(
        &self,
        _character_stats_map: &mut HashMap<String, usize>,
        _file_type_stats_map: &mut HashMap<String, FileTypeStats>,
        _file_result: &SingleFileAnalysisResult,
    ) {
        // Placeholder: Implement logic to update overall statistics based on a single file's result.
        // For example, update character frequencies, file type counts, issues per file type, etc.
        if let Some(stats) = _file_type_stats_map.get_mut(&_file_result.file_path) { // This key might need to be extension
            stats.file_count += 1;
            stats.total_size += _file_result.file_size;
            stats.issues_found += _file_result.character_findings.len() + _file_result.security_issues.len();
        } else {
             // Simplified: using file_path as key for demonstration, usually it would be file extension
            _file_type_stats_map.insert(_file_result.file_path.clone(), FileTypeStats {
                file_count: 1,
                total_size: _file_result.file_size,
                issues_found: _file_result.character_findings.len() + _file_result.security_issues.len(),
                average_risk_score: 0.0, // Calculate based on severities
            });
        }

        for cf in &_file_result.character_findings {
            *_character_stats_map.entry(cf.character.to_string()).or_insert(0) += 1;
        }
    }
    
    async fn perform_security_assessment(&self, all_findings: &[SingleFileAnalysisResult]) -> SecurityAssessment {
        // This function should build a SecurityAssessment based on the vulnerabilities found.
        // For now, it returns a default structure.
        // It would iterate `all_findings`, extract `security_issues` (Vec<SecurityVulnerability>),
        // and then populate the fields of `SecurityAssessment`.
        let mut risk_categories = HashMap::new();
        let mut total_compliance_gaps = Vec::new();

        // Example: Aggregate vulnerabilities into risk categories (simplified)
        for finding_result in all_findings {
            for vulnerability in &finding_result.security_issues {
                let category_entry = risk_categories.entry(vulnerability.vulnerability_type.clone()).or_insert_with(|| RiskCategory {
                    category_name: vulnerability.vulnerability_type.clone(),
                    risk_score: 0.0, // Calculate based on severity, count, etc.
                    likelihood: 0.0, // Estimate
                    impact: 0.0, // Estimate
                    findings: Vec::new(),
                    mitigations: Vec::new(),
                    residual_risk: 0.0,
                });
                category_entry.findings.push(vulnerability.vulnerability_id.clone());
                // Update risk_score, likelihood, impact based on vulnerability.severity, confidence, etc.
                match vulnerability.severity {
                    DS_SeverityLevel::Critical => category_entry.risk_score += 10.0,
                    DS_SeverityLevel::High => category_entry.risk_score += 5.0,
                    DS_SeverityLevel::Medium => category_entry.risk_score += 2.0,
                    DS_SeverityLevel::Low | DS_SeverityLevel::Paranoid => category_entry.risk_score += 1.0,
                }
            }
        }
        
        // Example: Create a default compliance gap (if any logic dictates it)
        // This part is highly dependent on actual compliance requirements.
        if all_findings.iter().any(|fr| !fr.security_issues.is_empty()) {
             total_compliance_gaps.push(ComplianceGap {
                standard: "General Security Best Practices".to_string(),
                requirement: "No critical or high vulnerabilities".to_string(),
                current_status: "Potential Issues Found".to_string(),
                gap_severity: "Medium".to_string(), // As per local ComplianceGap.gap_severity: String
            });
        }


        SecurityAssessment {
            risk_categories,
            attack_surface_analysis: AttackSurfaceAnalysis::default(), // Placeholder
            threat_modeling: ThreatModelingResults::default(), // Placeholder
            compliance_gaps: total_compliance_gaps, // Use aggregated gaps
            remediation_roadmap: RemediationRoadmap::default(), // Placeholder
        }
    }
    
    fn generate_executive_summary(&self, all_findings: &[SingleFileAnalysisResult], total_files_analyzed: usize) -> ExecutiveSummary {
        let mut summary = ExecutiveSummary {
            total_files_analyzed,
            ..Default::default()
        };
        let mut total_suspicious_chars = 0;

        for fr in all_findings {
            if !fr.character_findings.is_empty() || !fr.security_issues.is_empty() || !fr.patterns_found.is_empty() {
                summary.files_with_issues += 1;
            }
            total_suspicious_chars += fr.character_findings.len(); // Or a more nuanced count

            for issue in &fr.security_issues {
                match issue.severity {
                    DS_SeverityLevel::Critical => summary.critical_issues += 1,
                    DS_SeverityLevel::High => summary.high_issues += 1,
                    DS_SeverityLevel::Medium => summary.medium_issues += 1,
                    DS_SeverityLevel::Low | DS_SeverityLevel::Paranoid => summary.low_issues += 1,
                }
            }
        }
        summary.total_suspicious_chars = total_suspicious_chars;
        // Calculate overall_risk_score, health_score, etc. based on collected data
        summary.overall_risk_score = (summary.critical_issues * 10 + summary.high_issues * 5 + summary.medium_issues * 2 + summary.low_issues) as f64;
        if total_files_analyzed > 0 {
            summary.health_score = 100.0 - (summary.files_with_issues as f64 / total_files_analyzed as f64 * 100.0);
            if summary.health_score < 0.0 { summary.health_score = 0.0; }
        } else {
            summary.health_score = 100.0;
        }
        summary
    }

    fn generate_detailed_findings(
        &self, 
        all_findings: &[SingleFileAnalysisResult], 
        character_stats_map: HashMap<String, usize>, 
        file_type_stats_map: HashMap<String, FileTypeStats> 
    ) -> DetailedFindings {
        let mut detailed = DetailedFindings::default();
        let mut total_chars = 0;
        let mut total_suspicious = 0;
        let mut char_freq_map = HashMap::new();

        for fr in all_findings {
            total_chars += fr.file_size; // Approximation, or sum of fr.character_findings.len() if more accurate
            for cf in &fr.character_findings {
                total_suspicious += 1;
                *char_freq_map.entry(cf.character.to_string()).or_insert(0) += 1;
            }
            // Populate vulnerability_catalog from fr.security_issues
            // This requires converting SecurityVulnerability to VulnerabilityFinding (local struct)
            // For now, let's assume they are compatible or skip this conversion
        }
        
        detailed.character_analysis = CharacterAnalysisDetails {
            total_characters_analyzed: total_chars,
            suspicious_characters_found: total_suspicious,
            character_frequency_map: character_stats_map, // Use passed arg
            normalization_issues: Vec::new(), // Placeholder
        };
        detailed.file_type_breakdown = file_type_stats_map; // Use passed arg
        // detailed.encoding_analysis = ... // Needs aggregation
        // detailed.vulnerability_catalog = ... // Needs aggregation and potential conversion
        // detailed.false_positive_analysis = ... // Needs logic

        detailed
    }

    async fn perform_pattern_analysis(&self, all_findings: &[SingleFileAnalysisResult]) -> PatternAnalysis {
        let mut pa = PatternAnalysis::default();
        for fr in all_findings {
            for p in &fr.patterns_found {
                pa.detected_patterns.push(p.clone());
                *pa.pattern_frequencies.entry(p.pattern_name.clone()).or_insert(0) +=1;
            }
        }
        // Populate emerging_patterns, pattern_correlations, machine_learning_insights if enabled/available
        pa
    }
    
    fn get_memory_usage(&self) -> f64 {
        // Placeholder for actual memory usage retrieval
        0.0 
    }

    fn generate_optimization_suggestions(&self, _summary: &ExecutiveSummary) -> Vec<OptimizationSuggestion> {
        // Placeholder
        Vec::new()
    }
}

// REMOVED local struct CharacterFinding {}
// REMOVED local struct IssueLocation {}
// REMOVED local pub struct Recommendation {}
// REMOVED local pub enum RecommendationCategory {}
