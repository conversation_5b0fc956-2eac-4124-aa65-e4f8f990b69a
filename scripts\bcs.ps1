#!/usr/bin/env powershell
# BCS - Bad Character Scanner unified command interface
# A single entry point for all development tasks

param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [Parameter(Position=1, ValueFromRemainingArguments)]
    [string[]]$Args
)

$script_dir = $PSScriptRoot
$project_root = $script_dir | Split-Path -Parent

# Define command mappings
$commands = @{
    # Development
    "start"    = @{ Script = "dev-workflow.ps1"; Args = "start"; Description = "Start development server" }
    "build"    = @{ Script = "dev-workflow.ps1"; Args = "build"; Description = "Build the application" }
    "test"     = @{ Script = "dev-workflow.ps1"; Args = "test"; Description = "Run all tests" }
    "clean"    = @{ Script = "dev-workflow.ps1"; Args = "clean"; Description = "Clean build artifacts" }
    "watch"    = @{ Script = "watch-dev.ps1"; Args = "-AutoFix"; Description = "Watch for changes" }
    
    # Health & Diagnostics
    "doctor"   = @{ Script = "doctor.ps1"; Args = ""; Description = "Run health check" }
    "status"   = @{ Script = "project-status.ps1"; Args = ""; Description = "Show project status" }
    "perf"     = @{ Script = "performance-check.ps1"; Args = ""; Description = "Check performance" }
    
    # Scanning
    "scan"     = @{ Script = "scan-this-project.ps1"; Args = ""; Description = "Scan for bad characters" }
    "analyze"  = @{ Script = "dev-workflow.ps1"; Args = "analyze"; Description = "Analyze codebase" }
    
    # Fixes
    "fix"      = @{ Script = "dev-workflow.ps1"; Args = "fix"; Description = "Fix common issues" }
    "fix-errors" = @{ Script = "fix-compiler-errors.ps1"; Args = ""; Description = "Fix compiler errors" }
    
    # Tickets
    "tickets"  = @{ Script = "ticket-manager-fixed.ps1"; Args = ""; Description = "Show ticket summary" }
    "ticket"   = @{ Script = "ticket-manager-fixed.ps1"; Args = "-Action"; Description = "Ticket management" }
    
    # Git
    "commit"   = @{ Script = "dev-workflow.ps1"; Args = "commit"; Description = "Commit changes" }
    "pr"       = @{ Script = "dev-workflow.ps1"; Args = "pr"; Description = "Create pull request" }
}

function Show-Help {
    Write-Host "`n🦸 BAD CHARACTER SCANNER CLI" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    Write-Host "Fighting for accessibility!" -ForegroundColor Gray
    
    Write-Host "`nUsage: bcs <command> [options]" -ForegroundColor White
    
    Write-Host "`n📋 Available Commands:" -ForegroundColor Yellow
    
    # Group commands by category
    $categories = @{
        "Development" = @("start", "build", "test", "clean", "watch")
        "Diagnostics" = @("doctor", "status", "perf")
        "Analysis" = @("scan", "analyze")
        "Fixes" = @("fix", "fix-errors")
        "Tickets" = @("tickets", "ticket")
        "Git" = @("commit", "pr")
    }
    
    foreach ($category in $categories.Keys | Sort-Object) {
        Write-Host "`n$category:" -ForegroundColor Cyan
        foreach ($cmd in $categories[$category]) {
            if ($commands.ContainsKey($cmd)) {
                Write-Host ("  {0,-12} {1}" -f $cmd, $commands[$cmd].Description) -ForegroundColor White
            }
        }
    }
    
    Write-Host "`n⚡ Quick Examples:" -ForegroundColor Yellow
    Write-Host "  bcs start              # Start development server" -ForegroundColor Gray
    Write-Host "  bcs doctor -Fix        # Run health check with auto-fix" -ForegroundColor Gray
    Write-Host "  bcs scan -SaveReport   # Scan and save report" -ForegroundColor Gray
    Write-Host "  bcs ticket high        # Show high priority tickets" -ForegroundColor Gray
    
    Write-Host "`n💡 Tips:" -ForegroundColor Yellow
    Write-Host "  - Use 'bcs <command> -Help' for command-specific help" -ForegroundColor Gray
    Write-Host "  - Most commands support additional flags" -ForegroundColor Gray
    Write-Host "  - Check scripts\README.md for detailed documentation" -ForegroundColor Gray
}

# Main command router
if ($Command -eq "help" -or $Command -eq "h" -or $Command -eq "?") {
    Show-Help
    exit 0
}

if (-not $commands.ContainsKey($Command)) {
    Write-Host "❌ Unknown command: $Command" -ForegroundColor Red
    Write-Host "Use 'bcs help' to see available commands" -ForegroundColor Yellow
    exit 1
}

# Execute the command
$cmd_info = $commands[$Command]
$script_path = Join-Path $script_dir $cmd_info.Script

if (-not (Test-Path $script_path)) {
    Write-Host "❌ Script not found: $($cmd_info.Script)" -ForegroundColor Red
    exit 1
}

# Build argument list
$script_args = @()
if ($cmd_info.Args) {
    $script_args += $cmd_info.Args
}
$script_args += $Args

# Show what we're running
Write-Host "➤ Running: $($cmd_info.Script) $($script_args -join ' ')" -ForegroundColor DarkGray

# Execute
& $script_path @script_args