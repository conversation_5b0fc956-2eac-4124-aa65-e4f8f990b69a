[INFO] analyzer_cli: 'analyze' command received for path: C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox
[DEBUG] analyzer_cli: Calling analyze_codebase for path: C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox
Laptos_Tauri Backend: analyze_codebase called with path: C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox
Laptos_Tauri Backend: analyze_codebase completed. Files analyzed: 3, Suspicious chars: 3
{
  "total_files": 3,
  "files_with_issues": 1,
  "total_suspicious_chars": 3,
  "health_score": 66.66666666666666,
  "file_details": [
    {
      "file_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\safe_file.txt",
      "relative_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\safe_file.txt",
      "file_size": 39,
      "total_characters": 39,
      "suspicious_characters": 0,
      "issues": [],
      "file_type": "txt",
      "encoding": "UTF-8",
      "analysis_status": "success",
      "error_message": null
    },
    {
      "file_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\sub_dir\\another_safe_file.txt",
      "relative_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\sub_dir\\another_safe_file.txt",
      "file_size": 38,
      "total_characters": 38,
      "suspicious_characters": 0,
      "issues": [],
      "file_type": "txt",
      "encoding": "UTF-8",
      "analysis_status": "success",
      "error_message": null
    },
    {
      "file_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\unsafe_file.txt",
      "relative_path": "C:/Users/<USER>/Documents/Software/Leptos_TaurieV2_BCS/cli_test_sandbox\\unsafe_file.txt",
      "file_size": 79,
      "total_characters": 74,
      "suspicious_characters": 3,
      "issues": [
        "Suspicious character '​' at position 20",
        "Suspicious character '​' at position 28",
        "Suspicious character 'е' at position 55"
      ],
      "file_type": "txt",
      "encoding": "UTF-8",
      "analysis_status": "success",
      "error_message": null
    }
  ],
  "analysis_time_ms": 0
}
