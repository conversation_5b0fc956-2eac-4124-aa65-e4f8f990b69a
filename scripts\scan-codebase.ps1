<#
.SYNOPSIS
    Scans a codebase for bad characters using the Bad Character Scanner
    
.DESCRIPTION
    This script provides a PowerShell interface to scan entire codebases
    
.PARAMETER Path
    Path to the directory to scan
    
.PARAMETER Include
    File patterns to include (e.g., "*.js", "*.ts")
    
.PARAMETER Exclude
    Patterns to exclude (e.g., "node_modules", "*.min.js")
    
.PARAMETER ShowProgress
    Show progress during scanning
    
.PARAMETER AsJson
    Return results as JSON
    
.EXAMPLE
    .\scan-codebase.ps1 -Path "C:\MyProject"
    
.EXAMPLE
    .\scan-codebase.ps1 -Path "." -Include "*.js","*.ts" -Exclude "node_modules"
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$Path,
    
    [string[]]$Include,
    
    [string[]]$Exclude,
    
    [switch]$ShowProgress,
    
    [switch]$AsJson
)

$projectRoot = Split-Path -Parent $PSScriptRoot
$cliPath = Join-Path $projectRoot "src-tauri"

# Build the CLI if needed
if (-not (Test-Path (Join-Path $projectRoot "target\release\analyzer_cli.exe"))) {
    Write-Host "Building analyzer CLI..." -ForegroundColor Yellow
    Push-Location $cliPath
    cargo build --bin analyzer_cli --release
    Pop-Location
}

# Convert to absolute path
$Path = [System.IO.Path]::GetFullPath($Path)

# Prepare command
$args = @("run", "--bin", "analyzer_cli", "--", "scan", $Path)

if ($Include) {
    $args += @("--include", ($Include -join ","))
}

if ($Exclude) {
    $args += @("--exclude", ($Exclude -join ","))
}

if ($AsJson) {
    $args += "--json"
}

# Run scanner
Push-Location $cliPath
try {
    if ($ShowProgress) {
        Write-Host "Scanning codebase: $Path" -ForegroundColor Cyan
        Write-Host "This may take a while for large codebases..." -ForegroundColor Yellow
    }
    
    $output = & cargo $args 2>&1
    
    if ($AsJson) {
        $output | ConvertFrom-Json
    } else {
        # Parse and display results
        $output | ForEach-Object {
            if ($_ -match "Files scanned:") {
                Write-Host $_ -ForegroundColor Green
            } elseif ($_ -match "Issues found:") {
                Write-Host $_ -ForegroundColor Yellow
            } elseif ($_ -match "Error:") {
                Write-Host $_ -ForegroundColor Red
            } else {
                Write-Host $_
            }
        }
    }
} finally {
    Pop-Location
}