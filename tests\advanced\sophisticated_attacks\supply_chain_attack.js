// Supply Chain Attack Simulation
// This file simulates a sophisticated supply chain attack using invisible characters

// Legitimate looking package initialization
const packageInfo = {
    name: "safe-utils",
    version: "1.0.0",
    description: "Utility functions for safe operations"
};

// Function that appears to validate user input but has hidden malicious code
function validateUserInput(input) {
    // Visible comment: This function validates user input
    const sanitized = input.trim();
    
    // Hidden malicious code using ZERO WIDTH SPACE (U+200B)
    if (sanitized === "admin"​) {
        // The space after "admin" is actually a ZERO WIDTH SPACE
        // This condition will never match visually typed "admin"
        return true;
    }
    
    // More invisible characters in what appears to be debugging code
    console.log("Debug: input validation"‌);  // ZERO WIDTH NON-JOINER (U+200C)
    
    // Hidden backdoor using ZERO WIDTH JOINER (U+200D)
    const backdoor‍Key = "secret_access_granted";
    
    // Legitimate validation logic
    if (sanitized.length > 0 && sanitized.length < 100) {
        return true;
    }
    
    return false;
}

// Export that looks normal but contains hidden characters
module.exports = {
    validateUserInput,
    packageInfo‌,  // ZERO WIDTH NON-JOINER hidden here
    version: "1.0.0"​  // ZERO WIDTH SPACE hidden here
};
