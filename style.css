/* Base Theme Variables */
:root {
  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #252525;
  --bg-card: #1e1e1e;
  --bg-hover: #2a2a2a;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-tertiary: #808080;
  --text-muted: #606060;
  
  /* Brand Colors */
  --purple-primary: #8b5cf6;
  --purple-secondary: #7c3aed;
  --purple-light: #a78bfa;
  --purple-dark: #6d28d9;
  
  /* Accent Colors */
  --blue-primary: #3b82f6;
  --blue-light: #60a5fa;
  --green-primary: #10b981;
  --green-light: #34d399;
  --orange-primary: #f97316;
  --red-primary: #ef4444;
  --yellow-primary: #f59e0b;
  
  /* Neutral Colors */
  --border-color: #333333;
  --shadow-color: rgba(0, 0, 0, 0.5);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
}

/* Icon Sizing Standards - CRITICAL FOR PROPER DISPLAY */
.icon-xs {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.icon-sm {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.icon-md {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.icon-lg {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.icon-xl {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

/* Responsive icon behavior */
.icon-responsive {
  display: inline-block;
  vertical-align: middle;
}

/* Strictly enforce 40x40px for all SVGs/icons */
svg[class*="icon-"], .icon-40, .emoji, .nav-icon, .emoji-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}

/* Force size constraints on all SVG icons */
svg[class*="icon-"] {
  flex-shrink: 0;
  display: inline-block;
}

/* Prevent emoji and symbol oversizing */
.emoji, .symbol {
  width: 40px;
  height: 40px;
  font-size: 40px;
  line-height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}

/* Navigation icon sizing (for emojis in nav buttons) */
.nav-icon {
  font-size: 1.2em;
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.5rem;
  line-height: 1;
}

/* General emoji constraint for all contexts */
span[class*="icon"], .nav-icon, .emoji-icon {
  width: 40px;
  height: 40px;
  font-size: 40px;
  line-height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}

/* Responsive scaling for smaller screens */
@media (max-width: 768px) {
  .icon-xl { width: 28px; height: 28px; }
  .icon-lg { width: 20px; height: 20px; }
  .icon-md { width: 18px; height: 18px; }
}

/* High DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2) {
  svg[class*="icon-"] {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--purple-light) 0%, var(--purple-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* App Container */
.app-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  background-color: rgba(26, 26, 26, 0.9);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.brand h1 {
  font-size: 1.5rem;
  margin: 0;
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px var(--shadow-color);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.card-icon {
  font-size: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.card-subtitle {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  outline: none;
}

.btn:focus {
  outline: 2px solid var(--purple-primary);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--purple-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--purple-secondary);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-hover);
}

.btn-success {
  background-color: var(--green-primary);
  color: white;
}

.btn-success:hover {
  background-color: var(--green-light);
}

.btn-danger {
  background-color: var(--red-primary);
  color: white;
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.btn-icon {
  padding: var(--spacing-sm);
  width: 2.5rem;
  height: 2.5rem;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.125rem;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.875rem;
}

/* Input Fields */
.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.input, .textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: var(--purple-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.textarea {
  min-height: 200px;
  resize: vertical;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* Tabs */
.tabs {
  display: flex;
  gap: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-xl);
}

.tab {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-tertiary);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.tab:hover {
  color: var(--text-secondary);
}

.tab.active {
  color: var(--purple-primary);
  border-bottom-color: var(--purple-primary);
}

/* Statistics Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Risk Indicators */
.risk-high {
  color: var(--red-primary);
}

.risk-medium {
  color: var(--orange-primary);
}

.risk-low {
  color: var(--yellow-primary);
}

.risk-none {
  color: var(--green-primary);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: var(--purple-primary);
  transition: width var(--transition-normal);
}

/* Quick Test Buttons */
.quick-test-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.quick-test-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quick-test-btn:hover {
  background-color: var(--purple-primary);
  color: white;
  border-color: var(--purple-primary);
}

/* Drag and Drop Zone */
.drop-zone {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: all var(--transition-fast);
  background-color: var(--bg-tertiary);
}

.drop-zone:hover {
  border-color: var(--purple-primary);
  background-color: rgba(139, 92, 246, 0.05);
}

.drop-zone.dragging {
  border-color: var(--purple-primary);
  background-color: rgba(139, 92, 246, 0.1);
}

.drop-zone-icon {
  font-size: 3rem;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

/* Analysis Results */
.results-container {
  margin-top: var(--spacing-xl);
}

.result-item {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.result-title {
  font-weight: 600;
  color: var(--text-primary);
}

.result-severity {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-critical {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--red-primary);
}

.severity-high {
  background-color: rgba(249, 115, 22, 0.1);
  color: var(--orange-primary);
}

.severity-medium {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--yellow-primary);
}

.severity-low {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--green-primary);
}

/* Code Preview */
.code-preview {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Loading States */
.loading {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--purple-primary);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error States */
.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--red-primary);
  margin-bottom: var(--spacing-md);
}

/* Success States */
.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--green-primary);
  margin-bottom: var(--spacing-md);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-tertiary);
}

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* Navigation */
.nav-sections {
  display: flex;
  gap: var(--spacing-xs);
}

.nav-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.9375rem;
}

.nav-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.nav-btn.active {
  background-color: var(--purple-primary);
  color: white;
  border-color: var(--purple-primary);
}

/* Removed duplicate - consolidated with main nav-icon definition above */

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Section Layouts */
.text-analysis-section,
.codebase-section {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  margin-bottom: var(--spacing-xl);
}

/* Input Stats */
.input-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Folder Selection */
.folder-selection {
  padding: var(--spacing-lg);
}

.selected-folder {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.folder-path {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--purple-light);
}

.analysis-actions {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

/* Tab Content */
.tab-content {
  padding: var(--spacing-xl) 0;
}

.overview-content {
  padding: var(--spacing-md);
}

/* Utility - Margin Left Auto */
.ml-auto {
  margin-left: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  
  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}