use leptos::*;

#[component]
pub fn ProgressBar(progress: ReadSignal<f64>) -> impl IntoView {
    view! {
        <div class="progress-bar">
            <div class="progress" style=move || format!("width: {}%", progress.get() * 100.0)></div>
        </div>
    }
}

#[component]
pub fn ProgressIndicator(
    #[prop(optional)] label: Option<String>,
    #[prop(optional)] progress: Option<ReadSignal<f64>>,
    #[prop(default = create_signal(false).0)] is_active: ReadSignal<bool>,
) -> impl IntoView {
    view! {
        <Show when=move || is_active.get() fallback=|| ()>
            <div class="progress-indicator">
                {label.clone().map(|l| view! { <div class="progress-label">{l}</div> })}
                
                {if let Some(progress) = progress {
                    view! {
                        <div class="progress-bar-wrapper">
                            <div class="progress-bar-background">
                                <div 
                                    class="progress-bar-fill"
                                    style=move || format!("width: {}%", progress.get() * 100.0)
                                >
                                    <div class="progress-bar-glow"></div>
                                </div>
                            </div>
                            <span class="progress-percentage">
                                {move || format!("{:.0}%", progress.get() * 100.0)}
                            </span>
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <div class="progress-bar-wrapper">
                            <div class="progress-bar-background">
                                <div class="progress-bar-indeterminate">
                                    <div class="progress-bar-glow"></div>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                }}
            </div>
        </Show>
    }
}

#[component] 
pub fn CircularProgress(
    #[prop(optional)] size: Option<u32>,
) -> impl IntoView {
    let size = size.unwrap_or(40);
    let stroke_width = 3;
    let radius = (size / 2) - stroke_width;
    let circumference = 2.0 * std::f32::consts::PI * radius as f32;
    
    view! {
        <svg 
            class="circular-progress"
            width=size
            height=size
            viewBox=format!("0 0 {} {}", size, size)
        >
            <circle
                class="circular-progress-bg"
                cx=size/2
                cy=size/2
                r=radius
                stroke-width=stroke_width
                fill="none"
            />
            <circle
                class="circular-progress-fill"
                cx=size/2
                cy=size/2
                r=radius
                stroke-width=stroke_width
                fill="none"
                stroke-dasharray=circumference
                stroke-dashoffset=circumference
            />
        </svg>
    }
}