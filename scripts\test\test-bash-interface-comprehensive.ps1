#!/usr/bin/env pwsh
# Comprehensive Auto-Test Script for Bash Interface
# Tests all functions, error handling, verbose output, and edge cases

param(
    [switch]$Verbose,
    [switch]$SkipBuild,
    [string]$TestFilter = ""
)

# Configuration
$ErrorActionPreference = "Continue"
$ProgressPreference = "SilentlyContinue"
$ProjectRoot = $PWD
$BashScript = Join-Path $ProjectRoot -ChildPath "scripts" | Join-Path -ChildPath "codebase_analyzer.sh"
$TestDir = Join-Path $ProjectRoot -ChildPath "test_bash_interface"
$LogFile = Join-Path $TestDir -ChildPath "test_results.log"
$TestResults = @()

# Colors for output
$Colors = @{
    Reset = "`e[0m"
    Red = "`e[91m"
    Green = "`e[92m"
    Yellow = "`e[93m"
    Blue = "`e[94m"
    Magenta = "`e[95m"
    Cyan = "`e[96m"
    White = "`e[97m"
}

# Test result tracking
$TestStats = @{
    Total = 0
    Passed = 0
    Failed = 0
    Skipped = 0
}

function Write-ColoredOutput {
    param([string]$Text, [string]$Color = "White")
    Write-Host "$($Colors[$Color])$Text$($Colors.Reset)"
}

function Write-TestHeader {
    param([string]$Title)
    Write-Host ""
    Write-ColoredOutput "=" * 80 "Cyan"
    Write-ColoredOutput "TEST: $Title" "Cyan"
    Write-ColoredOutput "=" * 80 "Cyan"
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = "",
        [string]$ExpectedOutput = "",
        [string]$ActualOutput = ""
    )
    
    $TestStats.Total++
    
    if ($Passed) {
        $TestStats.Passed++
        Write-ColoredOutput "✅ PASS: $TestName" "Green"
    } else {
        $TestStats.Failed++
        Write-ColoredOutput "❌ FAIL: $TestName" "Red"
        if ($Details) {
            Write-ColoredOutput "   Details: $Details" "Yellow"
        }
        if ($ExpectedOutput) {
            Write-ColoredOutput "   Expected: $ExpectedOutput" "Yellow"
        }
        if ($ActualOutput) {
            Write-ColoredOutput "   Actual: $ActualOutput" "Yellow"
        }
    }
    
    # Log result
    $result = @{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        ExpectedOutput = $ExpectedOutput
        ActualOutput = $ActualOutput
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    $script:TestResults += $result
    
    # Write to log file
    $logEntry = "[$($result.Timestamp)] $(if($Passed){'PASS'}else{'FAIL'}): $TestName"
    if ($Details) { $logEntry += " - $Details" }
    Add-Content -Path $LogFile -Value $logEntry
}

function Invoke-BashScript {
    param(
        [string]$Arguments,
        [int]$ExpectedExitCode = 0,
        [switch]$CaptureOutput = $true,
        [int]$TimeoutSeconds = 30
    )
    
    # Try different bash executables
    $bashPaths = @(
        "bash.exe",
        "C:\Program Files\Git\bin\bash.exe",
        "C:\Windows\System32\bash.exe",
        "wsl",
        "C:\msys64\usr\bin\bash.exe"
    )
    
    $bashExe = $null
    foreach ($path in $bashPaths) {
        try {
            if (Get-Command $path -ErrorAction SilentlyContinue) {
                $bashExe = $path
                break
            }
        } catch {
            continue
        }
    }
    
    if (-not $bashExe) {
        return @{
            ExitCode = -1
            Output = ""
            Error = "No bash executable found. Please install Git Bash or WSL."
        }
    }
    
    $process = @{
        FilePath = $bashExe
        ArgumentList = @($BashScript) + ($Arguments -split " " | Where-Object { $_ -ne "" })
        NoNewWindow = $true
        Wait = $true
        PassThru = $true
    }
    
    if ($CaptureOutput) {
        $process.RedirectStandardOutput = $true
        $process.RedirectStandardError = $true
    }
    
    try {
        $proc = Start-Process @process
        
        # Handle timeout
        if (-not $proc.WaitForExit($TimeoutSeconds * 1000)) {
            $proc.Kill()
            return @{
                ExitCode = -1
                Output = ""
                Error = "Process timed out after $TimeoutSeconds seconds"
            }
        }
        
        $result = @{
            ExitCode = $proc.ExitCode
            Output = if ($CaptureOutput -and $proc.StandardOutput) { $proc.StandardOutput.ReadToEnd() } else { "" }
            Error = if ($CaptureOutput -and $proc.StandardError) { $proc.StandardError.ReadToEnd() } else { "" }
        }
        
        return $result
    } catch {
        return @{
            ExitCode = -1
            Output = ""
            Error = $_.Exception.Message
        }
    }
}

function Test-BashScriptExists {
    Write-TestHeader "Bash Script Existence and Permissions"
    
    # Test 1: Script exists
    $exists = Test-Path $BashScript
    Write-TestResult "Bash script exists" $exists "Path: $BashScript"
    
    if ($exists) {
        # Test 2: Script is readable
        $readable = $true
        try {
            $content = Get-Content $BashScript -First 5
            $readable = $content.Count -gt 0
        } catch {
            $readable = $false
        }
        Write-TestResult "Bash script is readable" $readable
        
        # Test 3: Script has shebang
        $hasShebang = $false
        try {
            $firstLine = Get-Content $BashScript -First 1
            $hasShebang = $firstLine -like "#!/*"
        } catch {
            $hasShebang = $false
        }
        Write-TestResult "Bash script has valid shebang" $hasShebang "First line: $((Get-Content $BashScript -First 1))"
    }
}

function Test-BasicCommands {
    Write-TestHeader "Basic Command Testing"
    
    # Test 1: Help command
    $result = Invoke-BashScript "--help"
    $helpWorks = $result.ExitCode -eq 0 -and $result.Output -like "*USAGE*"
    Write-TestResult "Help command works" $helpWorks "Exit code: $($result.ExitCode)"
    
    # Test 2: No arguments (should show help)
    $result = Invoke-BashScript ""
    $noArgsWorks = $result.ExitCode -eq 0 -and $result.Output -like "*USAGE*"
    Write-TestResult "No arguments shows help" $noArgsWorks "Exit code: $($result.ExitCode)"
    
    # Test 3: Invalid command
    $result = Invoke-BashScript "invalid-command"
    $invalidWorks = $result.ExitCode -eq 2
    Write-TestResult "Invalid command returns error code 2" $invalidWorks "Exit code: $($result.ExitCode)"
    
    # Test 4: Version/health check
    $result = Invoke-BashScript "health"
    $healthWorks = $result.ExitCode -eq 0
    Write-TestResult "Health command works" $healthWorks "Exit code: $($result.ExitCode)"
}

function Test-VerboseOutput {
    Write-TestHeader "Verbose Output Testing"
    
    # Test 1: Verbose flag increases output
    $normalResult = Invoke-BashScript "health"
    $verboseResult = Invoke-BashScript "--verbose health"
    
    $verboseHasMoreOutput = $verboseResult.Output.Length -gt $normalResult.Output.Length -or
                           $verboseResult.Error.Length -gt $normalResult.Error.Length
    
    Write-TestResult "Verbose flag increases output" $verboseHasMoreOutput `
        "Normal: $($normalResult.Output.Length + $normalResult.Error.Length) chars, Verbose: $($verboseResult.Output.Length + $verboseResult.Error.Length) chars"
    
    # Test 2: Verbose shows debug messages
    $hasDebugMessages = $verboseResult.Error -like "*DEBUG*" -or $verboseResult.Output -like "*DEBUG*"
    Write-TestResult "Verbose shows debug messages" $hasDebugMessages
    
    # Test 3: Quiet flag reduces output
    $quietResult = Invoke-BashScript "--quiet health"
    $quietHasLessOutput = $quietResult.Output.Length -le $normalResult.Output.Length
    Write-TestResult "Quiet flag reduces output" $quietHasLessOutput `
        "Normal: $($normalResult.Output.Length) chars, Quiet: $($quietResult.Output.Length) chars"
}

function Test-DryRunMode {
    Write-TestHeader "Dry Run Mode Testing"
    
    # Create test directory
    $testPath = Join-Path $TestDir "dry_run_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "console.log('test');" | Out-File -FilePath (Join-Path $testPath "test.js") -Encoding UTF8
    
    # Test 1: Dry run analyze
    $result = Invoke-BashScript "--dry-run analyze `"$testPath`""
    $dryRunWorks = $result.ExitCode -eq 0 -and ($result.Output -like "*DRY RUN*" -or $result.Error -like "*DRY RUN*")
    Write-TestResult "Dry run analyze works" $dryRunWorks "Exit code: $($result.ExitCode)"
    
    # Test 2: Dry run doesn't create files
    $reportsDir = Join-Path $ProjectRoot "reports"
    $filesBefore = if (Test-Path $reportsDir) { (Get-ChildItem $reportsDir).Count } else { 0 }
    
    $result = Invoke-BashScript "--dry-run analyze `"$testPath`""
    
    $filesAfter = if (Test-Path $reportsDir) { (Get-ChildItem $reportsDir).Count } else { 0 }
    $noFilesCreated = $filesAfter -eq $filesBefore
    Write-TestResult "Dry run doesn't create output files" $noFilesCreated "Before: $filesBefore, After: $filesAfter"
}

function Test-ErrorHandling {
    Write-TestHeader "Error Handling Testing"
    
    # Test 1: Missing directory
    $result = Invoke-BashScript "analyze /nonexistent/directory"
    $missingDirHandled = $result.ExitCode -eq 3
    Write-TestResult "Missing directory returns exit code 3" $missingDirHandled "Exit code: $($result.ExitCode)"
    
    # Test 2: Missing file for scan
    $result = Invoke-BashScript "scan /nonexistent/file.js"
    $missingFileHandled = $result.ExitCode -eq 3
    Write-TestResult "Missing file returns exit code 3" $missingFileHandled "Exit code: $($result.ExitCode)"
    
    # Test 3: Missing file for export
    $result = Invoke-BashScript "export /nonexistent/analysis.json"
    $missingAnalysisHandled = $result.ExitCode -eq 3
    Write-TestResult "Missing analysis file returns exit code 3" $missingAnalysisHandled "Exit code: $($result.ExitCode)"
    
    # Test 4: Invalid format
    $testPath = Join-Path $TestDir "error_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "console.log('test');" | Out-File -FilePath (Join-Path $testPath "test.js") -Encoding UTF8
    
    $result = Invoke-BashScript "--format invalid analyze `"$testPath`""
    $invalidFormatHandled = $result.ExitCode -eq 2
    Write-TestResult "Invalid format returns exit code 2" $invalidFormatHandled "Exit code: $($result.ExitCode)"
    
    # Test 5: Command missing arguments
    $result = Invoke-BashScript "analyze"
    $missingArgsHandled = $result.ExitCode -eq 2
    Write-TestResult "Missing arguments returns exit code 2" $missingArgsHandled "Exit code: $($result.ExitCode)"
}

function Test-OutputFormats {
    Write-TestHeader "Output Format Testing"
    
    # Create test directory with sample files
    $testPath = Join-Path $TestDir "format_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    
    # Create test files
    "console.log('clean file');" | Out-File -FilePath (Join-Path $testPath "clean.js") -Encoding UTF8
    "console.log('test');$(([char]0x200B))" | Out-File -FilePath (Join-Path $testPath "suspicious.js") -Encoding UTF8 -NoNewline
    
    $formats = @("json", "markdown", "text")
    
    foreach ($format in $formats) {
        $result = Invoke-BashScript "--format $format analyze `"$testPath`""
        $formatWorks = $result.ExitCode -eq 0
        Write-TestResult "$format format works" $formatWorks "Exit code: $($result.ExitCode)"
        
        if ($formatWorks) {
            # Check if output looks correct for format
            switch ($format) {
                "json" {
                    $hasJsonStructure = $result.Output -like "*{*}*" -or (Test-Path (Join-Path $ProjectRoot "reports" "*.$format"))
                    Write-TestResult "$format output has correct structure" $hasJsonStructure
                }
                "markdown" {
                    $hasMarkdownStructure = $result.Output -like "*#*" -or (Test-Path (Join-Path $ProjectRoot "reports" "*.$format"))
                    Write-TestResult "$format output has correct structure" $hasMarkdownStructure
                }
                "text" {
                    $hasTextStructure = $result.Output.Length -gt 0 -or (Test-Path (Join-Path $ProjectRoot "reports" "*.$format"))
                    Write-TestResult "$format output has correct structure" $hasTextStructure
                }
            }
        }
    }
}

function Test-ScanFunction {
    Write-TestHeader "File Scan Function Testing"
    
    # Create test files
    $testPath = Join-Path $TestDir "scan_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    
    # Clean file
    $cleanFile = Join-Path $testPath "clean.js"
    "console.log('This is a clean file');" | Out-File -FilePath $cleanFile -Encoding UTF8
    
    # Suspicious file
    $suspiciousFile = Join-Path $testPath "suspicious.js"
    "console.log('test');$([char]0x200B)// zero-width space" | Out-File -FilePath $suspiciousFile -Encoding UTF8 -NoNewline
    
    # Test 1: Scan clean file
    $result = Invoke-BashScript "scan `"$cleanFile`""
    $cleanScanWorks = $result.ExitCode -eq 0 -and ($result.Output -like "*Clean*" -or $result.Error -like "*Clean*")
    Write-TestResult "Scan clean file works" $cleanScanWorks "Exit code: $($result.ExitCode)"
    
    # Test 2: Scan suspicious file
    $result = Invoke-BashScript "scan `"$suspiciousFile`""
    $suspiciousScanWorks = $result.ExitCode -eq 0 -and ($result.Output -like "*Issues*" -or $result.Error -like "*Issues*")
    Write-TestResult "Scan suspicious file detects issues" $suspiciousScanWorks "Exit code: $($result.ExitCode)"
}

function Test-AnalyzeFunction {
    Write-TestHeader "Directory Analysis Function Testing"
    
    # Create comprehensive test directory
    $testPath = Join-Path $TestDir "analyze_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    
    # Create subdirectories
    $jsDir = Join-Path $testPath "js"
    $cssDir = Join-Path $testPath "css"
    New-Item -ItemType Directory -Path $jsDir -Force | Out-Null
    New-Item -ItemType Directory -Path $cssDir -Force | Out-Null
    
    # Create test files
    "console.log('clean JavaScript');" | Out-File -FilePath (Join-Path $jsDir "clean.js") -Encoding UTF8
    "console.log('test');$([char]0x200B)" | Out-File -FilePath (Join-Path $jsDir "suspicious.js") -Encoding UTF8 -NoNewline
    "body { color: blue; }" | Out-File -FilePath (Join-Path $cssDir "styles.css") -Encoding UTF8
    "# README" | Out-File -FilePath (Join-Path $testPath "README.md") -Encoding UTF8
    
    # Test 1: Basic analysis
    $result = Invoke-BashScript "analyze `"$testPath`""
    $analysisWorks = $result.ExitCode -eq 0
    Write-TestResult "Directory analysis works" $analysisWorks "Exit code: $($result.ExitCode)"
    
    # Test 2: Check if reports are created
    $reportsDir = Join-Path $ProjectRoot "reports"
    $reportsCreated = Test-Path $reportsDir
    if ($reportsCreated) {
        $reportFiles = Get-ChildItem $reportsDir -Name "analysis_*.json" | Measure-Object | Select-Object -ExpandProperty Count
        $reportsCreated = $reportFiles -gt 0
    }
    Write-TestResult "Analysis creates report files" $reportsCreated "Reports directory: $reportsDir"
    
    # Test 3: Custom output directory
    $customOutput = Join-Path $TestDir "custom_reports"
    $result = Invoke-BashScript "--output `"$customOutput`" analyze `"$testPath`""
    $customOutputWorks = $result.ExitCode -eq 0 -and (Test-Path $customOutput)
    Write-TestResult "Custom output directory works" $customOutputWorks
}

function Test-ExportFunction {
    Write-TestHeader "Export Function Testing"
    
    # First create an analysis to export
    $testPath = Join-Path $TestDir "export_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "console.log('test');" | Out-File -FilePath (Join-Path $testPath "test.js") -Encoding UTF8
    
    # Create analysis
    $result = Invoke-BashScript "analyze `"$testPath`""
    if ($result.ExitCode -eq 0) {
        # Find the created analysis file
        $reportsDir = Join-Path $ProjectRoot "reports"
        $analysisFile = Get-ChildItem $reportsDir -Name "analysis_*.json" | Select-Object -First 1
        
        if ($analysisFile) {
            $analysisPath = Join-Path $reportsDir $analysisFile
            
            # Test export to different formats
            $formats = @("markdown", "text", "json")
            foreach ($format in $formats) {
                $result = Invoke-BashScript "--format $format export `"$analysisPath`""
                $exportWorks = $result.ExitCode -eq 0
                Write-TestResult "Export to $format works" $exportWorks "Exit code: $($result.ExitCode)"
            }
        } else {
            Write-TestResult "Export test setup failed" $false "No analysis file found"
        }
    } else {
        Write-TestResult "Export test setup failed" $false "Analysis creation failed"
    }
}

function Test-TestCommand {
    Write-TestHeader "Built-in Test Command Testing"
    
    # Test the built-in test command
    $result = Invoke-BashScript "test" -TimeoutSeconds 60
    $testCommandWorks = $result.ExitCode -eq 0 -or $result.ExitCode -eq 1  # Some tests might fail, that's ok
    Write-TestResult "Built-in test command runs" $testCommandWorks "Exit code: $($result.ExitCode)"
    
    if ($testCommandWorks) {
        # Check if test output contains expected sections
        $hasTestResults = ($result.Output -like "*Test Summary*" -or $result.Error -like "*Test Summary*")
        Write-TestResult "Test command shows results summary" $hasTestResults
        
        $hasIndividualTests = ($result.Output -like "*Test [0-9]*:*" -or $result.Error -like "*Test [0-9]*:*")
        Write-TestResult "Test command shows individual test results" $hasIndividualTests
    }
}

function Test-DemoCommand {
    Write-TestHeader "Demo Command Testing"
    
    # Test the demo command
    $result = Invoke-BashScript "demo" -TimeoutSeconds 45
    $demoWorks = $result.ExitCode -eq 0
    Write-TestResult "Demo command works" $demoWorks "Exit code: $($result.ExitCode)"
    
    if ($demoWorks) {
        # Check if demo creates expected output
        $hasDemoOutput = ($result.Output -like "*Demo*" -or $result.Error -like "*Demo*")
        Write-TestResult "Demo command produces output" $hasDemoOutput
        
        # Check if demo creates sample files
        $reportsDir = Join-Path $ProjectRoot "reports"
        $demoReportsCreated = Test-Path $reportsDir
        Write-TestResult "Demo creates analysis reports" $demoReportsCreated
    }
}

function Test-EdgeCases {
    Write-TestHeader "Edge Cases and Boundary Testing"
    
    # Test 1: Empty directory
    $emptyDir = Join-Path $TestDir "empty_dir"
    New-Item -ItemType Directory -Path $emptyDir -Force | Out-Null
    
    $result = Invoke-BashScript "analyze `"$emptyDir`""
    $emptyDirHandled = $result.ExitCode -eq 0
    Write-TestResult "Empty directory analysis handled" $emptyDirHandled "Exit code: $($result.ExitCode)"
    
    # Test 2: Directory with special characters in name
    $specialDir = Join-Path $TestDir "special dir with spaces & symbols!"
    New-Item -ItemType Directory -Path $specialDir -Force | Out-Null
    "test" | Out-File -FilePath (Join-Path $specialDir "test.txt") -Encoding UTF8
    
    $result = Invoke-BashScript "analyze `"$specialDir`""
    $specialDirHandled = $result.ExitCode -eq 0
    Write-TestResult "Directory with special characters handled" $specialDirHandled "Exit code: $($result.ExitCode)"
    
    # Test 3: Very long path
    $longPath = Join-Path $TestDir ("long" * 20)
    try {
        New-Item -ItemType Directory -Path $longPath -Force | Out-Null
        "test" | Out-File -FilePath (Join-Path $longPath "test.txt") -Encoding UTF8
        
        $result = Invoke-BashScript "analyze `"$longPath`""
        $longPathHandled = $result.ExitCode -eq 0 -or $result.ExitCode -eq 3  # Either works or properly fails
        Write-TestResult "Very long path handled" $longPathHandled "Exit code: $($result.ExitCode)"
    } catch {
        Write-TestResult "Very long path test skipped" $true "Path too long for filesystem"
    }
    
    # Test 4: Binary file
    $binaryFile = Join-Path $TestDir "binary.bin"
    [byte[]]$binaryData = 0..255
    [System.IO.File]::WriteAllBytes($binaryFile, $binaryData)
    
    $result = Invoke-BashScript "scan `"$binaryFile`""
    $binaryFileHandled = $result.ExitCode -eq 0
    Write-TestResult "Binary file scan handled" $binaryFileHandled "Exit code: $($result.ExitCode)"
}

function Test-ConcurrentAccess {
    Write-TestHeader "Concurrent Access Testing"
    
    # Create test directory
    $testPath = Join-Path $TestDir "concurrent_test"
    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
    "console.log('test');" | Out-File -FilePath (Join-Path $testPath "test.js") -Encoding UTF8
    
    # Start multiple analyses concurrently
    $jobs = @()
    for ($i = 1; $i -le 3; $i++) {
        $job = Start-Job -ScriptBlock {
            param($ScriptPath, $TestPath)
            & bash $ScriptPath analyze $TestPath
        } -ArgumentList $BashScript, $testPath
        $jobs += $job
    }
    
    # Wait for jobs to complete
    $results = $jobs | Wait-Job | Receive-Job
    $jobs | Remove-Job
    
    # Check if concurrent access was handled properly
    $concurrentOk = $true
    foreach ($result in $results) {
        if ($result -like "*error*" -or $result -like "*failed*") {
            $concurrentOk = $false
            break
        }
    }
    
    Write-TestResult "Concurrent access handled" $concurrentOk "Jobs completed: $($results.Count)"
}

function Initialize-TestEnvironment {
    Write-ColoredOutput "Initializing test environment..." "Yellow"
    
    # Create test directory
    if (Test-Path $TestDir) {
        Remove-Item $TestDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TestDir -Force | Out-Null
    
    # Initialize log file
    "Bash Interface Comprehensive Test Log - $(Get-Date)" | Out-File -FilePath $LogFile -Encoding UTF8
      # Check if bash is available
    $bashPaths = @(
        "bash.exe",
        "C:\Program Files\Git\bin\bash.exe",
        "C:\Windows\System32\bash.exe",
        "wsl"
    )
    
    $bashFound = $false
    foreach ($path in $bashPaths) {
        try {
            if (Get-Command $path -ErrorAction SilentlyContinue) {
                $bashVersion = & $path --version 2>&1 | Select-Object -First 1
                Write-ColoredOutput "Bash found: $path - $bashVersion" "Green"
                $bashFound = $true
                break
            }
        } catch {
            continue
        }
    }
    
    if (-not $bashFound) {
        Write-ColoredOutput "WARNING: No bash executable found. Installing Git Bash is recommended." "Yellow"
        Write-ColoredOutput "Some tests may fail without bash support." "Yellow"
    }
    
    Write-ColoredOutput "Test environment initialized." "Green"
}

function Write-TestSummary {
    Write-Host ""
    Write-ColoredOutput "=" * 80 "Cyan"
    Write-ColoredOutput "TEST SUMMARY" "Cyan"
    Write-ColoredOutput "=" * 80 "Cyan"
    
    Write-ColoredOutput "Total Tests: $($TestStats.Total)" "White"
    Write-ColoredOutput "Passed: $($TestStats.Passed)" "Green"
    Write-ColoredOutput "Failed: $($TestStats.Failed)" "Red"
    Write-ColoredOutput "Skipped: $($TestStats.Skipped)" "Yellow"
    
    $successRate = if ($TestStats.Total -gt 0) { 
        [math]::Round(($TestStats.Passed / $TestStats.Total) * 100, 2) 
    } else { 0 }
    
    Write-ColoredOutput "Success Rate: $successRate%" $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" })
    
    # Write detailed results to log
    Add-Content -Path $LogFile -Value "`n=== TEST SUMMARY ==="
    Add-Content -Path $LogFile -Value "Total: $($TestStats.Total), Passed: $($TestStats.Passed), Failed: $($TestStats.Failed), Success Rate: $successRate%"
    
    if ($TestStats.Failed -gt 0) {
        Write-ColoredOutput "`nFailed Tests:" "Red"
        $script:TestResults | Where-Object { -not $_.Passed } | ForEach-Object {
            Write-ColoredOutput "  - $($_.TestName): $($_.Details)" "Red"
        }
    }
    
    Write-ColoredOutput "`nDetailed test log: $LogFile" "Cyan"
    
    # Exit with appropriate code
    if ($TestStats.Failed -eq 0) {
        Write-ColoredOutput "`n🎉 All tests passed!" "Green"
        exit 0
    } else {
        Write-ColoredOutput "`n❌ Some tests failed. Check the log for details." "Red"
        exit 1
    }
}

# Main execution
function Main {
    Write-ColoredOutput "🚀 Bash Interface Comprehensive Test Suite" "Cyan"
    Write-ColoredOutput "Testing script: $BashScript" "White"
    
    Initialize-TestEnvironment
    
    $testFunctions = @(
        "Test-BashScriptExists",
        "Test-BasicCommands",
        "Test-VerboseOutput",
        "Test-DryRunMode",
        "Test-ErrorHandling",
        "Test-OutputFormats",
        "Test-ScanFunction",
        "Test-AnalyzeFunction",
        "Test-ExportFunction",
        "Test-TestCommand",
        "Test-DemoCommand",
        "Test-EdgeCases",
        "Test-ConcurrentAccess"
    )
    
    foreach ($testFunction in $testFunctions) {
        if ($TestFilter -and $testFunction -notlike "*$TestFilter*") {
            continue
        }
        
        try {
            & $testFunction
        } catch {
            Write-ColoredOutput "❌ Test function $testFunction failed with exception: $($_.Exception.Message)" "Red"
            $TestStats.Failed++
        }
    }
    
    Write-TestSummary
}

# Run the main function
Main
