use serde::{Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

/// Main error type for the Bad Character Scanner application
#[derive(Debug, Error)]
pub enum AppError {
    #[error("File access error: {message}")]
    FileAccess {
        message: String,
        #[source]
        source: Option<std::io::Error>,
        path: Option<String>,
    },

    #[error("Analysis error: {message}")]
    Analysis {
        message: String,
        details: Option<String>,
    },

    #[error("Network error: {message}")]
    Network {
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    #[error("Validation error: {message}")]
    Validation {
        message: String,
        field: Option<String>,
    },

    #[error("System error: {message}")]
    System {
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    #[error("User input error: {message}")]
    UserInput {
        message: String,
        input_type: Option<String>,
    },

    #[error("Configuration error: {message}")]
    Configuration {
        message: String,
        config_key: Option<String>,
    },

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("UTF-8 conversion error: {0}")]
    Utf8(#[from] std::string::FromUtf8Error),

    #[error("Tauri error: {0}")]
    Tauri(String),

    #[error("Unknown error: {0}")]
    Unknown(String),
}

/// Error response that gets sent to the frontend
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ErrorResponse {
    pub id: String,
    pub message: String,
    pub error_type: ErrorCategory,
    pub timestamp: String,
    pub file_path: Option<String>,
    pub operation: Option<String>,
    pub recoverable: bool,
    pub details: Option<String>,
    pub suggestion: Option<String>,
}

/// Error category matching the frontend enum
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "PascalCase")]
pub enum ErrorCategory {
    FileAccess,
    Analysis,
    Network,
    Validation,
    System,
    UserInput,
    Configuration,
}

impl AppError {
    /// Convert the error to a user-friendly error response
    pub fn to_error_response(&self, operation: Option<String>) -> ErrorResponse {
        let (category, message, suggestion, file_path, details, recoverable) = match self {
            AppError::FileAccess { message, path, .. } => (
                ErrorCategory::FileAccess,
                self.sanitized_message(message),
                Some("Please check if the file exists and you have permission to access it.".to_string()),
                path.clone(),
                None,
                true,
            ),
            AppError::Analysis { message, details } => (
                ErrorCategory::Analysis,
                message.clone(),
                Some("Try reducing the input size or checking for unsupported characters.".to_string()),
                None,
                details.clone(),
                true,
            ),
            AppError::Network { message, .. } => (
                ErrorCategory::Network,
                message.clone(),
                Some("Please check your internet connection and try again.".to_string()),
                None,
                None,
                true,
            ),
            AppError::Validation { message, field } => (
                ErrorCategory::Validation,
                message.clone(),
                Some("Please check your input and try again.".to_string()),
                None,
                field.as_ref().map(|f| format!("Field: {}", f)),
                true,
            ),
            AppError::System { message, .. } => (
                ErrorCategory::System,
                self.sanitized_message(message),
                Some("This is a system error. Please try again or contact support.".to_string()),
                None,
                None,
                false,
            ),
            AppError::UserInput { message, input_type } => (
                ErrorCategory::UserInput,
                message.clone(),
                Some("Please check your input and try again.".to_string()),
                None,
                input_type.as_ref().map(|t| format!("Input type: {}", t)),
                true,
            ),
            AppError::Configuration { message, config_key } => (
                ErrorCategory::Configuration,
                message.clone(),
                Some("Please check your configuration settings.".to_string()),
                None,
                config_key.as_ref().map(|k| format!("Config key: {}", k)),
                false,
            ),
            AppError::Serialization(_) => (
                ErrorCategory::System,
                "Data processing error occurred".to_string(),
                Some("The data format may be invalid. Please try again.".to_string()),
                None,
                None,
                true,
            ),
            AppError::Io(e) => (
                ErrorCategory::FileAccess,
                self.sanitized_io_error(e),
                Some("Please check file permissions and try again.".to_string()),
                None,
                None,
                true,
            ),
            AppError::Utf8(_) => (
                ErrorCategory::Analysis,
                "Text encoding error occurred".to_string(),
                Some("The file may contain invalid UTF-8 characters.".to_string()),
                None,
                None,
                true,
            ),
            AppError::Tauri(msg) => (
                ErrorCategory::System,
                self.sanitized_message(msg),
                None,
                None,
                None,
                false,
            ),
            AppError::Unknown(msg) => (
                ErrorCategory::System,
                self.sanitized_message(msg),
                Some("An unexpected error occurred. Please try again.".to_string()),
                None,
                None,
                false,
            ),
        };

        ErrorResponse {
            id: uuid::Uuid::new_v4().to_string(),
            message,
            error_type: category,
            timestamp: chrono::Utc::now().to_rfc3339(),
            file_path,
            operation,
            recoverable,
            details,
            suggestion,
        }
    }

    /// Sanitize error messages to prevent information leakage
    fn sanitized_message(&self, message: &str) -> String {
        // Remove file paths and system-specific information
        let sanitized = message
            .replace(r"C:\", "")
            .replace(r"/home/", "")
            .replace(r"/Users/", "")
            .replace(r"\\", "/");
        
        // Truncate very long messages
        if sanitized.len() > 200 {
            format!("{}...", &sanitized[..197])
        } else {
            sanitized
        }
    }

    /// Sanitize IO errors specifically
    fn sanitized_io_error(&self, error: &std::io::Error) -> String {
        match error.kind() {
            std::io::ErrorKind::NotFound => "File or directory not found".to_string(),
            std::io::ErrorKind::PermissionDenied => "Permission denied".to_string(),
            std::io::ErrorKind::AlreadyExists => "File already exists".to_string(),
            std::io::ErrorKind::InvalidInput => "Invalid input provided".to_string(),
            std::io::ErrorKind::InvalidData => "Invalid data encountered".to_string(),
            std::io::ErrorKind::TimedOut => "Operation timed out".to_string(),
            std::io::ErrorKind::Interrupted => "Operation was interrupted".to_string(),
            std::io::ErrorKind::UnexpectedEof => "Unexpected end of file".to_string(),
            std::io::ErrorKind::OutOfMemory => "Out of memory".to_string(),
            _ => "File operation failed".to_string(),
        }
    }

    /// Check if the error is recoverable
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            AppError::FileAccess { .. }
                | AppError::Analysis { .. }
                | AppError::Network { .. }
                | AppError::Validation { .. }
                | AppError::UserInput { .. }
                | AppError::Serialization(_)
                | AppError::Io(_)
                | AppError::Utf8(_)
        )
    }

    /// Get the error category
    pub fn category(&self) -> ErrorCategory {
        match self {
            AppError::FileAccess { .. } | AppError::Io(_) => ErrorCategory::FileAccess,
            AppError::Analysis { .. } | AppError::Utf8(_) => ErrorCategory::Analysis,
            AppError::Network { .. } => ErrorCategory::Network,
            AppError::Validation { .. } => ErrorCategory::Validation,
            AppError::System { .. } | AppError::Tauri(_) | AppError::Unknown(_) => ErrorCategory::System,
            AppError::UserInput { .. } => ErrorCategory::UserInput,
            AppError::Configuration { .. } => ErrorCategory::Configuration,
            AppError::Serialization(_) => ErrorCategory::System,
        }
    }

    /// Log the error with appropriate context
    pub fn log(&self) {
        use tracing::{error, warn};
        
        match self.category() {
            ErrorCategory::System | ErrorCategory::Configuration => {
                error!(error = ?self, "System error occurred");
            }
            ErrorCategory::FileAccess | ErrorCategory::Analysis => {
                warn!(error = ?self, "Operation error occurred");
            }
            _ => {
                warn!(error = ?self, "User error occurred");
            }
        }
    }
}

impl fmt::Display for ErrorCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorCategory::FileAccess => write!(f, "File Access"),
            ErrorCategory::Analysis => write!(f, "Analysis"),
            ErrorCategory::Network => write!(f, "Network"),
            ErrorCategory::Validation => write!(f, "Validation"),
            ErrorCategory::System => write!(f, "System"),
            ErrorCategory::UserInput => write!(f, "User Input"),
            ErrorCategory::Configuration => write!(f, "Configuration"),
        }
    }
}

/// Convert AppError to Tauri command result
impl From<AppError> for String {
    fn from(error: AppError) -> Self {
        error.log();
        serde_json::to_string(&error.to_error_response(None))
            .unwrap_or_else(|_| r#"{"message": "An error occurred", "error_type": "System"}"#.to_string())
    }
}

/// Convenience type for Tauri command results
pub type CommandResult<T> = Result<T, AppError>;

/// Helper trait for adding context to errors
pub trait ErrorContext<T> {
    fn context(self, msg: &str) -> Result<T, AppError>;
    fn with_context<F>(self, f: F) -> Result<T, AppError>
    where
        F: FnOnce() -> String;
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: Into<AppError>,
{
    fn context(self, msg: &str) -> Result<T, AppError> {
        self.map_err(|e| {
            let base_error = e.into();
            match base_error {
                AppError::Io(io_err) => AppError::FileAccess {
                    message: msg.to_string(),
                    source: Some(io_err),
                    path: None,
                },
                _ => AppError::Unknown(format!("{}: {}", msg, base_error)),
            }
        })
    }

    fn with_context<F>(self, f: F) -> Result<T, AppError>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| {
            let base_error = e.into();
            let context = f();
            match base_error {
                AppError::Io(io_err) => AppError::FileAccess {
                    message: context,
                    source: Some(io_err),
                    path: None,
                },
                _ => AppError::Unknown(format!("{}: {}", context, base_error)),
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_sanitization() {
        let error = AppError::FileAccess {
            message: "Cannot read file at C:\\Users\\<USER>\\Documents\\secret.txt".to_string(),
            source: None,
            path: Some("C:\\Users\\<USER>\\Documents\\secret.txt".to_string()),
        };

        let response = error.to_error_response(None);
        assert!(!response.message.contains("JohnDoe"));
        assert!(!response.message.contains("C:\\"));
    }

    #[test]
    fn test_error_categories() {
        let io_error = AppError::Io(std::io::Error::new(std::io::ErrorKind::NotFound, "test"));
        assert_eq!(io_error.category(), ErrorCategory::FileAccess);

        let validation_error = AppError::Validation {
            message: "Invalid input".to_string(),
            field: Some("email".to_string()),
        };
        assert_eq!(validation_error.category(), ErrorCategory::Validation);
    }

    #[test]
    fn test_recoverable_errors() {
        let recoverable = AppError::UserInput {
            message: "Invalid format".to_string(),
            input_type: None,
        };
        assert!(recoverable.is_recoverable());

        let non_recoverable = AppError::System {
            message: "Critical failure".to_string(),
            source: None,
        };
        assert!(!non_recoverable.is_recoverable());
    }
}