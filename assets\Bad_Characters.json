{"filenameSpecificIssues": {"forbiddenWindowsCharacters": [{"char": "<", "hex": "U+003C", "description": "Forbidden in Windows filenames (less-than sign)"}, {"char": ">", "hex": "U+003E", "description": "Forbidden in Windows filenames (greater-than sign)"}, {"char": ":", "hex": "U+003A", "description": "Forbidden in Windows filenames (colon)"}, {"char": "\"", "hex": "U+0022", "description": "Forbidden in Windows filenames (double quote)"}, {"char": "/", "hex": "U+002F", "description": "Forbidden in Windows filenames (forward slash)"}, {"char": "\\", "hex": "U+005C", "description": "Forbidden in Windows filenames (backslash)"}, {"char": "|", "hex": "U+007C", "description": "Forbidden in Windows filenames (vertical bar)"}, {"char": "?", "hex": "U+003F", "description": "Forbidden in Windows filenames (question mark)"}, {"char": "*", "hex": "U+002A", "description": "Forbidden in Windows filenames (asterisk)"}], "generalControlCharactersForFilenames": [{"name": "<PERSON><PERSON> (NUL)", "hex": "U+0000", "description": "C0 control character. Forbidden in filenames."}, {"name": "Start of Heading (SOH)", "hex": "U+0001", "description": "C0 control character. Forbidden in filenames."}, {"name": "Start of Text (STX)", "hex": "U+0002", "description": "C0 control character. Forbidden in filenames."}, {"name": "End of Text (ETX)", "hex": "U+0003", "description": "C0 control character. Forbidden in filenames."}, {"name": "End of Transmission (EOT)", "hex": "U+0004", "description": "C0 control character. Forbidden in filenames."}, {"name": "Enquiry (ENQ)", "hex": "U+0005", "description": "C0 control character. Forbidden in filenames."}, {"name": "Acknowledge (ACK)", "hex": "U+0006", "description": "C0 control character. Forbidden in filenames."}, {"name": "Bell (BEL)", "hex": "U+0007", "description": "C0 control character. Forbidden in filenames."}, {"name": "Backspace (BS)", "hex": "U+0008", "description": "C0 control character. Forbidden in filenames."}, {"name": "Horizontal Tab (HT)", "hex": "U+0009", "description": "C0 control character. Forbidden in filenames."}, {"name": "Line Feed (LF)", "hex": "U+000A", "description": "C0 control character. Forbidden in filenames."}, {"name": "Vertical Tab (VT)", "hex": "U+000B", "description": "C0 control character. Forbidden in filenames."}, {"name": "Form Feed (FF)", "hex": "U+000C", "description": "C0 control character. Forbidden in filenames."}, {"name": "Carriage Return (CR)", "hex": "U+000D", "description": "C0 control character. Forbidden in filenames."}, {"name": "Shift Out (SO)", "hex": "U+000E", "description": "C0 control character. Forbidden in filenames."}, {"name": "Shift In (SI)", "hex": "U+000F", "description": "C0 control character. Forbidden in filenames."}, {"name": "Data Link Escape (DLE)", "hex": "U+0010", "description": "C0 control character. Forbidden in filenames."}, {"name": "Device Control 1 (DC1)", "hex": "U+0011", "description": "C0 control character. Forbidden in filenames."}, {"name": "Device Control 2 (DC2)", "hex": "U+0012", "description": "C0 control character. Forbidden in filenames."}, {"name": "Device Control 3 (DC3)", "hex": "U+0013", "description": "C0 control character. Forbidden in filenames."}, {"name": "Device Control 4 (DC4)", "hex": "U+0014", "description": "C0 control character. Forbidden in filenames."}, {"name": "Negative Acknowledge (NAK)", "hex": "U+0015", "description": "C0 control character. Forbidden in filenames."}, {"name": "Synchronous Idle (SYN)", "hex": "U+0016", "description": "C0 control character. Forbidden in filenames."}, {"name": "End of Transmission Block (ETB)", "hex": "U+0017", "description": "C0 control character. Forbidden in filenames."}, {"name": "Cancel (CAN)", "hex": "U+0018", "description": "C0 control character. Forbidden in filenames."}, {"name": "End of Medium (EM)", "hex": "U+0019", "description": "C0 control character. Forbidden in filenames."}, {"name": "Substitute (SUB)", "hex": "U+001A", "description": "C0 control character. Forbidden in filenames."}, {"name": "Escape (ESC)", "hex": "U+001B", "description": "C0 control character. Forbidden in filenames."}, {"name": "File Separator (FS)", "hex": "U+001C", "description": "C0 control character. Forbidden in filenames."}, {"name": "Group Separator (GS)", "hex": "U+001D", "description": "C0 control character. Forbidden in filenames."}, {"name": "Record Separator (RS)", "hex": "U+001E", "description": "C0 control character. Forbidden in filenames."}, {"name": "Unit Separator (US)", "hex": "U+001F", "description": "C0 control character. Forbidden in filenames."}, {"name": "Delete (DEL)", "hex": "U+007F", "description": "C0 control character. Forbidden in filenames."}], "c1ControlCharactersForFilenames": [{"name": "C1 Control Characters", "range": "U+0080-U+009F", "description": "C1 control characters. Forbidden in filenames."}], "problematicUnicodeForFilenames": [{"example": "Leading or trailing space", "description": "Filenames with leading or trailing spaces can cause confusion or errors on some platforms."}, {"example": "Filename ending with a period", "description": "Not allowed on Windows; can cause sync/copy issues."}]}, "contentSpecificIssues": {"invisibleAndFormattingCharacters": {"description": "A list of invisible or problematic Unicode characters that can cause issues for coders, especially when copying text from websites or other sources. These characters can lead to syntax errors, logical errors, or unexpected behavior that is often hard to debug due to their invisible nature. Categorized by estimated severity of the problems they cause.", "subCategories": {"extremelyBigProblems": {"displayName": "Extremely Big Problems", "description": "Characters that are often completely invisible and can severely break code structure, tokenization, or logic, leading to hard-to-debug syntax or runtime errors. These are prime candidates for causing issues when copied from the web.", "characters": [{"name": "Zero Width Space", "hex": "U+200B", "description": "Completely invisible. If inserted within identifiers, keywords, or numbers, it breaks them into separate tokens, leading to syntax errors. Can also affect string comparisons or content-based hashing. Extremely difficult to spot visually."}, {"name": "Zero Width No-Break Space / Byte Order Mark (BOM)", "hex": "U+FEFF", "description": "Dual nature: 1. As Zero Width No-Break Space (its original deprecated role when not at the start of a text stream): Acts like U+200B, completely invisible and can break tokens. 2. As Byte Order Mark (BOM): If present at the beginning of a UTF-8 encoded file where it's not expected (e.g., shell scripts after shebang, JSON files, some config files, PHP files before '<?php'), it can cause parsing errors or script failures. If copied mid-stream, it reverts to its ZWNBSP behavior."}, {"name": "Word Joiner / Zero Width No-Break Space", "hex": "U+2060", "description": "Ensures that adjacent characters are kept on the same line (prevents a line break). It is zero-width and invisible. If accidentally inserted within an identifier or numeric literal, it can break tokenization and cause syntax errors, similar to U+200B."}]}, "highProblems": {"displayName": "High Problems", "description": "Characters that are visually subtle (e.g., may look like standard spaces but have different properties) or have significant disruptive effects (e.g., altering text direction, acting as unexpected newlines). They frequently cause errors or logical issues in code.", "characters": [{"name": "No-Break Space", "hex": "U+00A0", "description": "Visually similar to a regular space, but prevents line breaks. If used instead of a regular space (U+0020) within code syntax (e.g., separating keywords, in identifiers if the language allows spaces, or in string literals where a specific space type is expected), it can cause parsing errors or failed string comparisons."}, {"name": "En Quad", "hex": "U+2000", "description": "A space character, typically wider than a standard space. Problematic if used instead of U+0020 in code syntax, causing parsing errors or unexpected string behavior."}, {"name": "Em Quad", "hex": "U+2001", "description": "A space character, typically the width of the point size (wider than En Quad). Problematic if used instead of U+0020 in code syntax."}, {"name": "En Space", "hex": "U+2002", "description": "A space character, typically half an em. Problematic if used instead of U+0020 in code syntax."}, {"name": "Em Space", "hex": "U+2003", "description": "A space character, typically equal to the point size. Problematic if used instead of U+0020 in code syntax."}, {"name": "Three-Per-Em Space", "hex": "U+2004", "description": "A space character, one-third of an em. Problematic if used instead of U+0020 in code syntax."}, {"name": "Four-Per-Em Space", "hex": "U+2005", "description": "A space character, one-fourth of an em. Problematic if used instead of U+0020 in code syntax."}, {"name": "Six-Per-Em Space", "hex": "U+2006", "description": "A space character, one-sixth of an em. Problematic if used instead of U+0020 in code syntax."}, {"name": "Figure Space", "hex": "U+2007", "description": "A space character with the width of a digit. Problematic if used instead of U+0020 in code syntax."}, {"name": "Punctuation Space", "hex": "U+2008", "description": "A space character with the width of a narrow punctuation mark. Problematic if used instead of U+0020 in code syntax."}, {"name": "Thin Space", "hex": "U+2009", "description": "A narrow space character. Problematic if used instead of U+0020 in code syntax."}, {"name": "Hair Space", "hex": "U+200A", "description": "A very narrow space character. Problematic if used instead of U+0020 in code syntax, can be almost invisible."}, {"name": "Narrow No-Break Space", "hex": "U+202F", "description": "A narrow version of the No-Break Space. Problematic for the same reasons as U+00A0 if used in code syntax."}, {"name": "Medium Mathematical Space", "hex": "U+205F", "description": "A space character used in mathematical formulae, typically 4/18 em. Problematic if used instead of U+0020 in general code syntax."}, {"name": "Ideographic Space", "hex": "U+3000", "description": "A wide space character used with East Asian scripts, typically the width of one CJK character. Visually distinct but will cause parsing errors if used as a standard space in code."}, {"name": "Line Separator", "hex": "U+2028", "description": "Intended to unambiguously separate lines. Some languages (like JavaScript) treat it as a newline, but many others or tools might not, or might treat it as invalid whitespace. Can cause inconsistent line ending behavior or syntax errors if invisible in an editor."}, {"name": "Paragraph Separator", "hex": "U+2029", "description": "Intended to unambiguously separate paragraphs. Similar issues to Line Separator (U+2028); may be treated as a newline or invalid character depending on the context."}, {"name": "Left-to-Right Mark (LRM)", "hex": "U+200E", "description": "Invisible character affecting bidirectional text rendering. Can cause confusion or errors if copied into string literals or comments, or if it affects the interpretation of surrounding tokens, although less likely to break syntax directly compared to other bidi controls."}, {"name": "Right-to-Left Mark (RLM)", "hex": "U+200F", "description": "Invisible character affecting bidirectional text rendering. Similar issues to LRM."}, {"name": "Left-to-Right Embedding (LRE)", "hex": "U+202A", "description": "Forces subsequent text to be treated as left-to-right. Can drastically alter the visual appearance and logical order of code or string literals if copied, leading to confusion and errors. Must be paired with PDF (U+202C)."}, {"name": "Right-to-Left Embedding (RLE)", "hex": "U+202B", "description": "Forces subsequent text to be treated as right-to-left. Can drastically alter code or string literals. Must be paired with PDF (U+202C)."}, {"name": "Pop Directional Formatting (PDF)", "hex": "U+202C", "description": "Terminates explicit directional embeddings (LRE, RLE) or overrides (LRO, RLO). If mismatched or unexpected, can lead to incorrect text rendering."}, {"name": "Left-to-Right Override (LRO)", "hex": "U+202D", "description": "Forces all subsequent characters to be treated as strong left-to-right, ignoring their inherent properties. Can make code unreadable or malicious. Must be paired with PDF (U+202C)."}, {"name": "Right-to-Left Override (RLO)", "hex": "U+202E", "description": "Forces all subsequent characters to be treated as strong right-to-left. Infamous for reversing text segments, making code appear jumbled or obfuscated, leading to severe readability and debugging issues. Must be paired with PDF (U+202C)."}, {"name": "Left-to-Right Isolate (LRI)", "hex": "U+2066", "description": "Isolates a span of text for left-to-right display, protecting it from surrounding bidirectional context. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069)."}, {"name": "Right-to-Left Isolate (RLI)", "hex": "U+2067", "description": "Isolates a span of text for right-to-left display. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069)."}, {"name": "First Strong Isolate (FSI)", "hex": "U+2068", "description": "Isolates a span of text, with directionality determined by the first strong directional character within it. Invisible and can cause subtle rendering issues if misused. Must be paired with PDI (U+2069)."}, {"name": "Pop Directional Isolate (PDI)", "hex": "U+2069", "description": "Terminates explicit directional isolates (LRI, RLI, FSI). If mismatched or unexpected, can lead to incorrect text rendering."}, {"name": "Next Line (NEL)", "hex": "U+0085", "description": "A C1 control character intended as a newline. If not consistently handled as a standard newline (LF or CRLF) by tools, parsers, or languages, it can cause unexpected line breaks or syntax errors."}]}, "mediumProblems": {"displayName": "Medium Problems", "description": "Characters that can cause issues such as unexpected string behavior, formatting issues, or errors with specific tools/parsers. They might be less common in web-copied code or their effects slightly less severe or more context-dependent than higher categories.", "characters": [{"name": "Soft Hyphen (SHY)", "hex": "U+00AD", "description": "Indicates an optional hyphenation point. Usually invisible, but becomes visible if a line breaks at that point. Can affect string length, comparisons, or tokenization if present within identifiers or string literals unexpectedly."}, {"name": "<PERSON> Width Non-Joiner (ZWNJ)", "hex": "U+200C", "description": "Prevents adjacent characters from forming a ligature. Invisible. If accidentally inserted within an identifier or string, it can alter its interpretation, break ligatures in comments/strings where intended, or affect text processing."}, {"name": "<PERSON> (ZWJ)", "hex": "U+200D", "description": "Causes adjacent characters to join or form a ligature (common in complex scripts and emoji sequences). Invisible. Can cause similar problems to ZWNJ if misplaced in code, altering token meaning or string comparisons."}, {"name": "Object Replacement Character", "hex": "U+FFFC", "description": "Placeholder for an object (e.g., an image) embedded in text. Should not appear in source code. If copied, it's essentially garbage data that could cause parser errors or unexpected behavior."}, {"name": "<PERSON><PERSON> (NUL)", "hex": "U+0000", "description": "Often used as a string terminator in C-like languages. If embedded within a string or code, it can truncate data prematurely or cause unexpected behavior in parsers and tools."}, {"name": "Start of Heading (SOH)", "hex": "U+0001", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "Start of Text (STX)", "hex": "U+0002", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "End of Text (ETX)", "hex": "U+0003", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "End of Transmission (EOT)", "hex": "U+0004", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "Enquiry (ENQ)", "hex": "U+0005", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "Acknowledge (ACK)", "hex": "U+0006", "description": "C0 control character. Generally unexpected and problematic in source code."}, {"name": "Bell (BEL)", "hex": "U+0007", "description": "C0 control character. Causes an audible or visual alert. Problematic if embedded in code or output."}, {"name": "Backspace (BS)", "hex": "U+0008", "description": "C0 control character. May cause overwriting of previous character in some terminal displays; problematic in string literals or code."}, {"name": "Vertical Tabulation (VT)", "hex": "U+000B", "description": "C0 control character. Treated as whitespace by some parsers, but non-standard and can cause formatting or parsing issues."}, {"name": "Form Feed (FF)", "hex": "U+000C", "description": "C0 control character. Often used as a page break. Can be treated as whitespace but is non-standard in most code contexts and can break formatting or parsing."}, {"name": "Shift Out (SO)", "hex": "U+000E", "description": "C0 control character. Used for character set switching; highly problematic in modern Unicode-based code."}, {"name": "Shift In (SI)", "hex": "U+000F", "description": "C0 control character. Used for character set switching; highly problematic."}, {"name": "Data Link Escape (DLE)", "hex": "U+0010", "description": "C0 control character. Problematic in source code."}, {"name": "Device Control One (DC1/XON)", "hex": "U+0011", "description": "C0 control character. Problematic in source code."}, {"name": "Device Control Two (DC2)", "hex": "U+0012", "description": "C0 control character. Problematic in source code."}, {"name": "Device Control Three (DC3/XOFF)", "hex": "U+0013", "description": "C0 control character. Problematic in source code."}, {"name": "Device Control Four (DC4)", "hex": "U+0014", "description": "C0 control character. Problematic in source code."}, {"name": "Negative Acknowledge (NAK)", "hex": "U+0015", "description": "C0 control character. Problematic in source code."}, {"name": "Synchronous Idle (SYN)", "hex": "U+0016", "description": "C0 control character. Problematic in source code."}, {"name": "End of Transmission Block (ETB)", "hex": "U+0017", "description": "C0 control character. Problematic in source code."}, {"name": "Cancel (CAN)", "hex": "U+0018", "description": "C0 control character. Problematic in source code."}, {"name": "End of Medium (EM)", "hex": "U+0019", "description": "C0 control character. Problematic in source code."}, {"name": "Substitute (SUB)", "hex": "U+001A", "description": "C0 control character. Often used to indicate an error or unrepresentable character. Problematic if embedded."}, {"name": "Escape (ESC)", "hex": "U+001B", "description": "C0 control character. Used to introduce escape sequences (e.g., ANSI color codes). Can wreak havoc if pasted into code not expecting it."}, {"name": "File Separator (FS)", "hex": "U+001C", "description": "C0 control character. Problematic in source code."}, {"name": "Group Separator (GS)", "hex": "U+001D", "description": "C0 control character. Problematic in source code."}, {"name": "Record Separator (RS)", "hex": "U+001E", "description": "C0 control character. Problematic in source code."}, {"name": "Unit Separator (US)", "hex": "U+001F", "description": "C0 control character. Problematic in source code."}, {"name": "Delete (DEL)", "hex": "U+007F", "description": "Control character. Problematic if present in source code."}, {"name": "Padding Character (PAD)", "hex": "U+0080", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "High Octet Preset (HOP)", "hex": "U+0081", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Break Permitted Here (BPH)", "hex": "U+0082", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "No Break Here (NBH)", "hex": "U+0083", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Index (IND)", "hex": "U+0084", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Start of Guarded Area (SSA)", "hex": "U+0086", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "End of Guarded Area (ESA)", "hex": "U+0087", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Start of String (SOS)", "hex": "U+0088", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Single Character Introducer / Privacy Message (SCI/PM)", "hex": "U+0089", "description": "C1 control character (Unicode lists U+009A as SCI, U+0089 as Character Tabulation With Justification). Problematic."}, {"name": "Application Program Command (APC)", "hex": "U+009F", "description": "C1 control character. Generally unexpected and problematic in source code. (Note: U+009F is APC, some charts differ for 0x80-0x9F range, using Unicode standard here)."}, {"name": "Device Control String (DCS)", "hex": "U+0090", "description": "C1 control character. Generally unexpected and problematic in source code."}, {"name": "Partial Line Forward (PLD)", "hex": "U+008B", "description": "C1 control character (Unicode name: PARTIAL LINE DOWN). Problematic."}, {"name": "Partial Line Backward (PLU)", "hex": "U+008C", "description": "C1 control character (Unicode name: PARTIAL LINE UP). Problematic."}, {"name": "Reverse Line Feed (RI)", "hex": "U+008D", "description": "C1 control character. Problematic."}, {"name": "Single Shift Two (SS2)", "hex": "U+008E", "description": "C1 control character. Problematic for character set shifting."}, {"name": "Single Shift Three (SS3)", "hex": "U+008F", "description": "C1 control character. Problematic for character set shifting."}, {"name": "Start of Protected Area (SPA)", "hex": "U+0096", "description": "C1 control character (Unicode name: SET TRANSMIT STATE). Problematic."}, {"name": "End of Protected Area (EPA)", "hex": "U+0097", "description": "C1 control character (Unicode name: CANCEL CHARACTER). Problematic."}, {"name": "String Terminator (ST)", "hex": "U+009C", "description": "C1 control character (Unicode lists as STRING TERMINATOR). Problematic."}, {"name": "Cancel Character (CCH)", "hex": "U+0094", "description": "C1 control character. Problematic."}, {"name": "Message Waiting (MW)", "hex": "U+0095", "description": "C1 control character. Problematic."}, {"name": "Control Sequence Introducer (CSI)", "hex": "U+009B", "description": "C1 control character. Introduces control sequences, highly problematic if raw in code."}, {"name": "Private Use One (PU1)", "hex": "U+0091", "description": "C1 control character. Problematic."}, {"name": "Private Use Two (PU2)", "hex": "U+0092", "description": "C1 control character. Problematic."}, {"name": "Set Transmit State (STS)", "hex": "U+0093", "description": "C1 control character (Unicode lists as DEVICE CONTROL STRING). Problematic."}, {"name": "Single Graphic Character Introducer (SGCI)", "hex": "U+009A", "description": "C1 control character (Unicode name: SINGLE CHARACTER INTRODUCER). Problematic."}, {"name": "Operating System Command (OSC)", "hex": "U+009D", "description": "C1 control character. Used for terminal commands, problematic."}, {"name": "Privacy Message (PM)", "hex": "U+009E", "description": "C1 control character. Problematic."}]}, "lowProblems": {"displayName": "Low Problems", "description": "Characters that are less likely to cause critical code errors but can lead to minor formatting inconsistencies, unexpected behavior in highly specific contexts, or represent 'corrupted' or unusual data if copied into code. Their invisibility might still cause confusion.", "characters": [{"name": "Function Application", "hex": "U+2061", "description": "Invisible character used in mathematical notation. Unlikely in code, but could cause issues if accidentally present."}, {"name": "Invisible Times", "hex": "U+2062", "description": "Invisible character used in mathematical notation for multiplication. Unlikely in code."}, {"name": "Invisible Separator / Invisible Comma", "hex": "U+2063", "description": "Invisible character used in mathematical notation as a separator. Unlikely in code."}, {"name": "Invisible Plus", "hex": "U+2064", "description": "Invisible character used in mathematical notation. Unlikely in code."}, {"name": "Variation Selector 1 through 16", "hex": "U+FE00 - U+FE0F", "description": "Invisible characters that select a specific glyph variant for the preceding character. If isolated or attached to an ASCII character in code, behavior is undefined or may cause subtle rendering differences. Unlikely to break parsing but can be confusing."}, {"name": "Interlinear Annotation Anchor", "hex": "U+FFF9", "description": "Marks the start of annotated text. Invisible and highly specialized; problematic if found in code."}, {"name": "Interlinear Annotation Separator", "hex": "U+FFFA", "description": "Separates annotated text from the annotation. Invisible and specialized; problematic."}, {"name": "Interlinear Annotation Terminator", "hex": "U+FFFB", "description": "Marks the end of annotated text. Invisible and specialized; problematic."}, {"name": "Replacement Character", "hex": "U+FFFD", "description": "Usually visible (often as a question mark in a diamond �), indicating a character that could not be decoded. While visible, its presence in copied code signifies data corruption or encoding issues and needs to be addressed, otherwise it might be treated as a literal character causing unexpected behavior."}]}}}, "advancedHomoglyphAttacks": {"description": "Advanced homoglyph attack patterns that can be used to bypass security filters and deceive users with lookalike characters from different Unicode scripts.", "subCategories": {"criticalHomoglyphs": {"displayName": "Critical Homoglyph Threats", "description": "Extremely dangerous character substitutions that can be used in sophisticated attacks including domain spoofing, credential theft, and code injection bypasses.", "characters": [{"name": "Cyrillic А (Capital A)", "hex": "U+0410", "lookalike": "A", "description": "Cyrillic capital letter A is visually identical to Latin A but has different Unicode properties. Commonly used in domain spoofing attacks against major websites."}, {"name": "Cyrillic Е (Capital E)", "hex": "U+0415", "lookalike": "E", "description": "Cyrillic capital letter E looks identical to Latin E. Frequently used in phishing attacks targeting email addresses and corporate domains."}, {"name": "Cyrillic О (Capital O)", "hex": "U+041E", "lookalike": "O", "description": "Cyrillic capital letter O is indistinguishable from Latin O visually. Critical threat in banking and financial phishing attacks."}, {"name": "Cyrillic Р (Capital P)", "hex": "U+0420", "lookalike": "P", "description": "Cyrillic capital letter P appears identical to Latin P. Used in sophisticated payment processor spoofing attacks."}, {"name": "Cyrillic Х (Capital X)", "hex": "U+0425", "lookalike": "X", "description": "Cyrillic capital letter X looks exactly like Latin X. Exploited in cryptocurrency and trading platform attacks."}, {"name": "Greek Α (Capital Alpha)", "hex": "U+0391", "lookalike": "A", "description": "Greek capital letter Alpha is visually identical to Latin A. Used in academic and research institution phishing campaigns."}, {"name": "Greek Ο (Capital Omicron)", "hex": "U+039F", "lookalike": "O", "description": "Greek capital letter Omicron appears identical to Latin O. Critical for government and military domain spoofing."}]}, "mathematicalAlphanumeric": {"displayName": "Mathematical Alphanumeric Symbols", "description": "Mathematical font variants that appear similar to regular letters but have different Unicode properties. Used to bypass content filters and security systems.", "characters": [{"name": "Mathematical Bold Capital A", "hex": "U+1D400", "lookalike": "A", "description": "Bold mathematical A that can bypass text filters. Used in advanced social engineering attacks."}, {"name": "Mathematical Italic Capital A", "hex": "U+1D434", "lookalike": "A", "description": "Italic mathematical A variant. Exploited in document-based attacks and malicious PDFs."}, {"name": "Mathematical Bold Digit Zero", "hex": "U+1D7CE", "lookalike": "0", "description": "Bold mathematical zero that looks identical to regular 0. Critical threat in financial and numerical data manipulation."}, {"name": "Mathematical Double-Struck Capital O", "hex": "U+1D546", "lookalike": "O", "description": "Double-struck mathematical O. Used in academic credential fraud and research manipulation."}]}, "fullwidthCharacters": {"displayName": "Fullwidth Character Substitutions", "description": "East Asian fullwidth characters that appear wider but similar to regular ASCII characters. Used to evade keyword detection and content analysis.", "characters": [{"name": "Fullwidth Latin Capital Letter A", "hex": "U+FF21", "lookalike": "A", "description": "Fullwidth A character used in East Asian typography. Exploited to bypass ASCII-based security filters."}, {"name": "Fullwidth Digit Zero", "hex": "U+FF10", "lookalike": "0", "description": "Fullwidth zero that appears similar to regular 0 but is wider. Used in financial fraud and data manipulation attacks."}, {"name": "Fullwidth Commercial At", "hex": "U+FF20", "lookalike": "@", "description": "Fullwidth @ symbol used to create fake email addresses that bypass validation."}]}}}, "aiGeneratedContentMarkers": {"description": "Patterns and characteristics commonly found in AI-generated text and code that can help identify artificial content.", "subCategories": {"highConfidenceMarkers": {"displayName": "High Confidence AI Markers", "description": "Strong indicators that text or code was generated by AI systems. These patterns are rarely found in human-authored content.", "patterns": [{"name": "AI Assistant Language", "pattern": "(?i)(?:i'm\\s+(?:an\\s+)?ai|as\\s+an\\s+ai|i\\s+(?:cannot|can't|am\\s+unable)|artificial\\s+intelligence)", "description": "Direct references to being an AI system or artificial intelligence limitations."}, {"name": "AI Helper Phrases", "pattern": "(?i)(?:i'll\\s+(?:help|assist|show|guide)|let\\s+me\\s+(?:help|show|explain)|hope\\s+this\\s+helps)", "description": "Typical AI assistant offer-to-help language patterns."}, {"name": "AI Disclaimers", "pattern": "(?i)(?:please\\s+(?:adapt|modify|adjust|customize)|you\\s+(?:may\\s+need\\s+to|might\\s+want\\s+to|should)\\s+(?:adapt|modify|adjust))", "description": "Common AI disclaimer language about customizing or adapting provided content."}]}, "mediumConfidenceMarkers": {"displayName": "Medium Confidence AI Markers", "description": "Moderately strong indicators of AI-generated content. May occasionally appear in human writing but are more common in AI output.", "patterns": [{"name": "Structured Response Format", "pattern": "(?i)(?:(?:here\\s+are\\s+the\\s+)?steps?|(?:step\\s+)?\\d+[.:)]|first[,\\s]+(?:you\\s+)?(?:need\\s+to|should)|next[,\\s]|finally[,\\s])", "description": "Highly structured step-by-step format typical of AI responses."}, {"name": "Generic Placeholders", "pattern": "(?i)(?:\\[(?:your|insert|add|replace)[^\\]]*\\]|<(?:your|insert|add|replace)[^>]*>|{(?:your|insert|add|replace)[^}]*})", "description": "Generic placeholder text commonly used in AI-generated templates."}, {"name": "Qualification Language", "pattern": "(?i)(?:(?:please\\s+)?(?:note|keep\\s+in\\s+mind)\\s+that|it's\\s+(?:important\\s+to|worth\\s+noting)|(?:bear\\s+in\\s+mind|remember)\\s+that)", "description": "Excessive use of qualification and disclaimer language."}]}, "lowConfidenceMarkers": {"displayName": "Low Confidence AI Markers", "description": "Weak indicators that might suggest AI generation but are also common in human writing, especially in educational or instructional contexts.", "patterns": [{"name": "Uncertainty Language", "pattern": "(?i)(?:might\\s+(?:be|work|help)|could\\s+(?:be|work|try)|perhaps\\s+(?:try|consider)|(?:may|might)\\s+be\\s+(?:worth|helpful))", "description": "Hedging language that expresses uncertainty, common in AI responses."}, {"name": "Generic Code Comments", "pattern": "(?i)(?://\\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)|#\\s*(?:TODO:|FIXME:|NOTE:|IMPORTANT:)).*(?:replace|modify|customize|adapt|adjust)", "description": "Generic placeholder comments in code that suggest template generation."}]}}}, "advancedSteganography": {"description": "Advanced steganographic techniques using Unicode characters to hide data or create covert communication channels.", "subCategories": {"dataEncodingPatterns": {"displayName": "Data Encoding Patterns", "description": "Patterns that suggest data is being encoded using invisible or special Unicode characters.", "techniques": [{"name": "Zero-Width Steganography", "pattern": "[\\u200B\\u200C\\u200D\\u2060]{8,}", "description": "Long sequences of zero-width characters that could encode binary data using presence/absence patterns."}, {"name": "Variation Selector Encoding", "pattern": "[\\uFE00-\\uFE0F]{3,}", "description": "Multiple variation selectors that could encode information through different font rendering choices."}, {"name": "Combining Character Abuse", "pattern": "[\\u0300-\\u036F]{5,}", "description": "Excessive combining diacritical marks that could hide data through mark positioning."}]}, "covertChannels": {"displayName": "Covert Communication Channels", "description": "Methods for hiding messages within seemingly normal text using Unicode properties.", "techniques": [{"name": "Bidirectional Text Hiding", "pattern": "[\\u202A-\\u202E][^\\u202C]*[\\u202C]?", "description": "Using bidirectional text controls to hide or obscure portions of messages."}, {"name": "Homoglyph Substitution Cipher", "pattern": "[а-я]{2,}.*[a-z]{2,}|[a-z]{2,}.*[а-я]{2,}", "description": "Mixed script text that could encode messages through specific character substitutions."}, {"name": "Invisible Character Spacing", "pattern": "([\\u200B\\u200C\\u200D]){2,}[a-zA-Z0-9]+([\\u200B\\u200C\\u200D]){2,}", "description": "Invisible characters used to create spacing patterns that encode information."}]}}}}}