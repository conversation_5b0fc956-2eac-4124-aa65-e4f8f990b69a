#!/usr/bin/env powershell
# Ultimate Development Environment Setup for Bad Character Scanner
# Accessibility-focused development tools for dyslexia support

Write-Host "🦸 ACCESSIBILITY HERO MODE: Setting up ultimate dev environment!" -ForegroundColor Cyan
Write-Host "Helping people with dyslexia by creating the perfect codebase scanner..." -ForegroundColor Green

$ErrorActionPreference = "Continue"
$project_root = $PSScriptRoot | Split-Path -Parent

# Function to check and install dependencies
function Test-Command {
    param($Command)
    $null = Get-Command $Command -ErrorAction SilentlyContinue
    return $?
}

function Install-Dependencies {
    Write-Host "`n🔧 CHECKING DEPENDENCIES..." -ForegroundColor Yellow
    
    # Check Node.js
    if (-not (Test-Command "node")) {
        Write-Host "❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org" -ForegroundColor Red
        exit 1
    } else {
        $nodeVersion = node --version
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    }
    
    # Check Rust
    if (-not (Test-Command "cargo")) {
        Write-Host "❌ Rust not found. Please install Rust from https://rustup.rs" -ForegroundColor Red
        exit 1
    } else {
        $rustVersion = cargo --version
        Write-Host "✅ Rust: $rustVersion" -ForegroundColor Green
    }
    
    # Check Trunk
    if (-not (Test-Command "trunk")) {
        Write-Host "📦 Installing Trunk (Leptos build tool)..." -ForegroundColor Yellow
        cargo install trunk
    } else {
        Write-Host "✅ Trunk: Available" -ForegroundColor Green
    }
    
    # Check Tauri CLI
    Write-Host "📦 Installing/Updating Tauri CLI v2..." -ForegroundColor Yellow
    cargo install tauri-cli --version "^2.0"
    
    Write-Host "✅ All dependencies verified!" -ForegroundColor Green
}

function Setup-VSCodeTasks {
    Write-Host "`n⚙️ SETTING UP VS CODE TASKS..." -ForegroundColor Yellow
    
    $vscode_dir = Join-Path $project_root ".vscode"
    if (-not (Test-Path $vscode_dir)) {
        New-Item -ItemType Directory -Path $vscode_dir -Force | Out-Null
    }
    
    $tasks_file = Join-Path $vscode_dir "tasks.json"    $tasks_config = @{
        version = "2.0.0"
        tasks = @(
            @{
                label = "Dev Server (Tauri + Leptos)"
                type = "shell"
                command = "cargo"
                args = @("tauri", "dev")
                group = "build"
                isBackground = $true
                presentation = @{
                    echo = $true
                    reveal = "always"
                    focus = $false
                    panel = "new"
                }
                problemMatcher = @()
            }
            @{
                label = "Check Bad Characters"
                type = "shell"
                command = "node"
                args = @("scripts/check-bad-characters.js")
                group = "test"
                presentation = @{
                    echo = $true
                    reveal = "always"
                    focus = $true
                }
            }
            @{
                label = "Build Release"
                type = "shell"
                command = "cargo"
                args = @("tauri", "build")
                group = "build"
                presentation = @{
                    echo = $true
                    reveal = "always"
                    focus = $true
                }
            }
            @{
                label = "Clean Build"
                type = "shell"
                command = "cargo"
                args = @("clean")
                group = "build"
            }
            @{
                label = "Run Tests"
                type = "shell"
                command = "cargo"
                args = @("test")
                group = "test"
            }
            @{
                label = "Check Code Quality"
                type = "shell"
                command = "powershell"
                args = @("-File", "scripts/check-code-quality.ps1")
                group = "test"
            }
        )
    }
    
    $tasks_config | ConvertTo-Json -Depth 10 | Out-File -FilePath $tasks_file -Encoding UTF8
    Write-Host "✅ VS Code tasks configured!" -ForegroundColor Green
}

function Setup-GitHooks {
    Write-Host "`n🔗 SETTING UP GIT HOOKS..." -ForegroundColor Yellow
    
    $hooks_dir = Join-Path $project_root ".git\hooks"
    if (Test-Path $hooks_dir) {
        $pre_commit = Join-Path $hooks_dir "pre-commit"
          $hook_content = @"
#!/bin/sh
# Pre-commit hook: Check for bad characters
echo "🔍 Checking for accessibility issues..."
node scripts/check-bad-characters.js
if [ `$? -ne 0 ]; then
    echo "❌ Bad characters detected! Please fix them before committing."
    echo "Run: node scripts/check-bad-characters.js"
    exit 1
fi
echo "✅ No accessibility issues found!"
"@
        
        $hook_content | Out-File -FilePath $pre_commit -Encoding UTF8
        Write-Host "✅ Git pre-commit hook installed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Not a git repository - skipping git hooks" -ForegroundColor Yellow
    }
}

function Create-DevScripts {
    Write-Host "`n📝 CREATING DEVELOPMENT SCRIPTS..." -ForegroundColor Yellow
    
    # Code quality checker
    $quality_script = Join-Path $project_root "scripts\check-code-quality.ps1"
    $quality_content = @'
#!/usr/bin/env powershell
# Comprehensive code quality checker for accessibility

Write-Host "🔍 COMPREHENSIVE CODE QUALITY CHECK" -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent

Write-Host "`n1. 🦸 Checking for bad characters (accessibility)..."
node "$project_root\scripts\check-bad-characters.js"
$bad_chars_result = $LASTEXITCODE

Write-Host "`n2. 🔧 Checking Rust code formatting..."
cargo fmt --check
$fmt_result = $LASTEXITCODE

Write-Host "`n3. 🧪 Running Rust tests..."
cargo test --quiet
$test_result = $LASTEXITCODE

Write-Host "`n4. 📋 Running Clippy lints..."
cargo clippy -- -D warnings
$clippy_result = $LASTEXITCODE

Write-Host "`n5. ⚙️ Checking Tauri configuration..."
node "$project_root\scripts\check-tauri-version.js"
$tauri_result = $LASTEXITCODE

Write-Host "`n📊 QUALITY REPORT SUMMARY" -ForegroundColor Yellow
Write-Host "================================"
Write-Host "Bad Characters: $(if ($bad_chars_result -eq 0) { '✅ PASS' } else { '❌ FAIL' })"
Write-Host "Code Formatting: $(if ($fmt_result -eq 0) { '✅ PASS' } else { '❌ FAIL' })"
Write-Host "Tests: $(if ($test_result -eq 0) { '✅ PASS' } else { '❌ FAIL' })"
Write-Host "Clippy Lints: $(if ($clippy_result -eq 0) { '✅ PASS' } else { '❌ FAIL' })"
Write-Host "Tauri Config: $(if ($tauri_result -eq 0) { '✅ PASS' } else { '❌ FAIL' })"

$overall_result = $bad_chars_result + $fmt_result + $test_result + $clippy_result + $tauri_result
if ($overall_result -eq 0) {
    Write-Host "`n🎉 ALL CHECKS PASSED! Ready for accessibility mission!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Some checks failed. Please fix issues before proceeding." -ForegroundColor Red
    exit 1
}
'@
    
    $quality_content | Out-File -FilePath $quality_script -Encoding UTF8
    
    # Development server script
    $dev_script = Join-Path $project_root "scripts\dev-server.ps1"
    $dev_content = @'
#!/usr/bin/env powershell
# Development server with accessibility monitoring

Write-Host "🦸 ACCESSIBILITY DEV SERVER STARTING..." -ForegroundColor Cyan
$project_root = $PSScriptRoot | Split-Path -Parent

# Check for bad characters before starting
Write-Host "🔍 Pre-flight accessibility check..."
node "$project_root\scripts\check-bad-characters.js"
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Bad characters detected but starting anyway..." -ForegroundColor Yellow
    Write-Host "Consider running the scanner to clean them!" -ForegroundColor Yellow
}

Write-Host "`n🚀 Starting Tauri development server..."
Write-Host "📝 Access the app at: http://localhost:1420" -ForegroundColor Green
Write-Host "🔄 Hot reload enabled for both frontend and backend" -ForegroundColor Green
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Yellow

cd $project_root
cargo tauri dev
'@
    
    $dev_content | Out-File -FilePath $dev_script -Encoding UTF8
    
    Write-Host "✅ Development scripts created!" -ForegroundColor Green
}

function Setup-DocumentationWatcher {
    Write-Host "`n📚 SETTING UP DOCUMENTATION TOOLS..." -ForegroundColor Yellow
    
    # Documentation validator
    $doc_script = Join-Path $project_root "scripts\validate-docs.ps1"
    $doc_content = @'
#!/usr/bin/env powershell
# Documentation validator for accessibility project

Write-Host "📚 DOCUMENTATION VALIDATION" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$docs_issues = 0

# Check README completeness
$readme = Join-Path $project_root "README.md"
if (Test-Path $readme) {
    $readme_content = Get-Content $readme -Raw
    $required_sections = @(
        "# Bad Character Scanner",
        "## Features",
        "## Installation", 
        "## Usage",
        "## Development"
    )
    
    foreach ($section in $required_sections) {
        if ($readme_content -notmatch [regex]::Escape($section)) {
            Write-Host "❌ Missing README section: $section" -ForegroundColor Red
            $docs_issues++
        }
    }
    
    if ($docs_issues -eq 0) {
        Write-Host "✅ README.md complete" -ForegroundColor Green
    }
} else {
    Write-Host "❌ README.md not found" -ForegroundColor Red
    $docs_issues++
}

# Check ticket system
$tickets_dir = Join-Path $project_root "docs\tickets"
if (Test-Path $tickets_dir) {
    $ticket_count = (Get-ChildItem $tickets_dir -Filter "*.md").Count
    Write-Host "✅ Ticket system: $ticket_count tickets found" -ForegroundColor Green
} else {
    Write-Host "⚠️ No ticket system found" -ForegroundColor Yellow
}

# Check for accessibility documentation
$accessibility_docs = @(
    "docs\COMPREHENSIVE_TESTING_GUIDE.md",
    "ENHANCED_TESTING_GUIDE.md",
    "MANUAL_TESTING_GUIDE.md"
)

foreach ($doc in $accessibility_docs) {
    $doc_path = Join-Path $project_root $doc
    if (Test-Path $doc_path) {
        Write-Host "✅ Found: $doc" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Missing: $doc" -ForegroundColor Yellow
    }
}

if ($docs_issues -eq 0) {
    Write-Host "`n🎉 Documentation validation passed!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Documentation issues found: $docs_issues" -ForegroundColor Red
    exit 1
}
'@
    
    $doc_content | Out-File -FilePath $doc_script -Encoding UTF8
    Write-Host "✅ Documentation tools configured!" -ForegroundColor Green
}

function Setup-CIPipeline {
    Write-Host "`n🔄 SETTING UP CI PIPELINE TEMPLATE..." -ForegroundColor Yellow
    
    $github_dir = Join-Path $project_root ".github\workflows"
    if (-not (Test-Path $github_dir)) {
        New-Item -ItemType Directory -Path $github_dir -Force | Out-Null
    }
      $ci_file = Join-Path $github_dir "accessibility-check.yml"    $ci_content = @'
name: Accessibility Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  accessibility-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
          components: rustfmt, clippy
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Check for bad characters
        run: node scripts/check-bad-characters.js
      - name: Check code formatting
        run: cargo fmt --check
      - name: Run Clippy
        run: cargo clippy -- -D warnings
      - name: Run tests
        run: cargo test
      - name: Validate Tauri configuration
        run: node scripts/check-tauri-version.js
'@
    
    $ci_content | Out-File -FilePath $ci_file -Encoding UTF8
    Write-Host "✅ CI pipeline template created!" -ForegroundColor Green
}

# Main execution
Write-Host "🚀 STARTING ULTIMATE DEV ENVIRONMENT SETUP..." -ForegroundColor Magenta

Install-Dependencies
Setup-VSCodeTasks
Setup-GitHooks
Create-DevScripts
Setup-DocumentationWatcher  
Setup-CIPipeline

Write-Host "`n🎉 ULTIMATE DEVELOPMENT ENVIRONMENT SETUP COMPLETE!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "✅ Dependencies verified and installed" -ForegroundColor White
Write-Host "✅ VS Code tasks configured" -ForegroundColor White
Write-Host "✅ Git hooks installed" -ForegroundColor White
Write-Host "✅ Development scripts created" -ForegroundColor White
Write-Host "✅ Documentation tools configured" -ForegroundColor White
Write-Host "✅ CI pipeline template ready" -ForegroundColor White

Write-Host "`n🦸 READY TO FIGHT FOR ACCESSIBILITY!" -ForegroundColor Cyan
Write-Host "Run these commands to start developing:" -ForegroundColor Yellow
Write-Host "  🚀 .\scripts\dev-server.ps1          # Start development server" -ForegroundColor White
Write-Host "  📊 .\scripts\check-code-quality.ps1  # Run quality checks" -ForegroundColor White
Write-Host "  🔍 node scripts\check-bad-characters.js # Scan for bad chars" -ForegroundColor White
Write-Host "  📚 .\scripts\validate-docs.ps1       # Check documentation" -ForegroundColor White

Write-Host "`n💡 VS Code users: Use Ctrl+Shift+P -> 'Tasks: Run Task' to access all tasks!" -ForegroundColor Blue
