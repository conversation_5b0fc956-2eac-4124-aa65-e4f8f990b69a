#!/usr/bin/env powershell
# Fix current compiler errors in the project

Write-Host "`n🔧 FIXING COMPILER ERRORS" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$project_root = $PSScriptRoot | Split-Path -Parent
$errors_fixed = 0

function Fix-File {
    param(
        [string]$Path,
        [string]$Description,
        [scriptblock]$Fix
    )
    
    Write-Host "`n📄 Fixing: $Description" -ForegroundColor Yellow
    Write-Host "   File: $Path" -ForegroundColor Gray
    
    try {
        & $Fix
        Write-Host "   ✅ Fixed!" -ForegroundColor Green
        $script:errors_fixed++
    } catch {
        Write-Host "   ❌ Failed: $_" -ForegroundColor Red
    }
}

# Fix 1: error_handling.rs - Closure trait issue
Fix-File -Path "src\components\error_handling.rs" -Description "Closure FnOnce vs Fn trait issue" -Fix {
    $file_path = Join-Path $project_root "src\components\error_handling.rs"
    $content = Get-Content $file_path -Raw
    
    # The issue is that error_manager is being moved in nested closures
    # We need to ensure it's cloned at the right level
    
    # First, let's check the current state around line 232
    $lines = Get-Content $file_path
    
    Write-Host "   Current line 232: $($lines[231])" -ForegroundColor DarkGray
    
    # The fix is to clone error_manager before any closure that uses it
    # Look for the pattern where we're creating the error list
    
    # Replace the problematic section
    $new_content = $content -replace '(\{)\s*\n\s*(let error_manager = error_manager\.clone\(\);)\s*\n\s*(move \|\|)', '$1
                    let error_manager = error_manager.clone();
                    move ||'
    
    # Also ensure we're not moving error_manager in the button closure
    $new_content = $new_content -replace 'let error_id = error\.id\.clone\(\);\s*\n\s*view!', 'let error_id = error.id.clone();
                        let error_manager_clone = error_manager.clone();
                        
                        view!'
    
    # Update the remove button to use the clone
    $new_content = $new_content -replace 'on:click=move \|_\| error_manager\.remove_error', 'on:click=move |_| error_manager_clone.remove_error'
    
    Set-Content -Path $file_path -Value $new_content -Encoding UTF8
}

# Fix 2: mod.rs - Unused import warning
Fix-File -Path "src\components\mod.rs" -Description "Unused ErrorCategory import" -Fix {
    $file_path = Join-Path $project_root "src\components\mod.rs"
    $content = Get-Content $file_path -Raw
    
    # ErrorCategory is already exported in the pub use statement
    # This is just a warning, but let's check if it's actually needed
    
    Write-Host "   This is just a warning - ErrorCategory is re-exported" -ForegroundColor Gray
    Write-Host "   No fix needed, but could be cleaned up later" -ForegroundColor Gray
}

# Fix 3: simple_text_analyzer.rs - Unsafe blocks
Fix-File -Path "src\components\simple_text_analyzer.rs" -Description "Unnecessary unsafe blocks" -Fix {
    $file_path = Join-Path $project_root "src\components\simple_text_analyzer.rs"
    $content = Get-Content $file_path -Raw
    
    # Remove unsafe blocks around invoke calls (they're not needed in recent Tauri)
    $new_content = $content -replace 'unsafe\s*\{\s*invoke\("([^"]+)",\s*([^}]+)\)\.await\s*\}', 'invoke("$1", $2).await'
    
    Set-Content -Path $file_path -Value $new_content -Encoding UTF8
}

# Now let's create a more robust fix for the error_handling.rs issue
Write-Host "`n🔍 Applying comprehensive fix for error_handling.rs..." -ForegroundColor Cyan

$error_handling_path = Join-Path $project_root "src\components\error_handling.rs"
$content = Get-Content $error_handling_path -Raw

# Find the view! macro section and fix the closure issue
$pattern = '(view!\s*\{[\s\S]*?<Show when=move \|\| !errors\.get\(\)\.is_empty\(\)>[\s\S]*?<div[^>]*>)\s*\{([^}]*let error_manager = error_manager\.clone\(\);)?'
$replacement = '$1
                {
                    let error_manager = error_manager.clone();'

$content = $content -replace $pattern, $replacement

# Ensure all closures that use error_manager have proper clones
$content = $content -replace '(current_errors\.into_iter\(\)\.map\(\|error\|\s*\{)\s*(let error_manager = error_manager\.clone\(\);)?', '$1
                        let error_manager = error_manager.clone();'

# Save the fixed content
Set-Content -Path $error_handling_path -Value $content -Encoding UTF8

Write-Host "`n📊 Summary:" -ForegroundColor Cyan
Write-Host "   Errors fixed: $errors_fixed" -ForegroundColor Green
Write-Host "   Warnings: 1 (can be ignored)" -ForegroundColor Yellow

Write-Host "`n🔄 Running cargo check to verify fixes..." -ForegroundColor Cyan
Push-Location $project_root
cargo check 2>&1 | Select-String -Pattern "error\[E" | ForEach-Object {
    Write-Host "   $_" -ForegroundColor Red
}
Pop-Location

Write-Host "`n💡 Next steps:" -ForegroundColor Cyan
Write-Host "   1. If errors persist, run this script again" -ForegroundColor Gray
Write-Host "   2. Check the specific line numbers in the error messages" -ForegroundColor Gray
Write-Host "   3. Use '.\dev-workflow.ps1 fix' for additional fixes" -ForegroundColor Gray

Write-Host "`n✨ Fighting for error-free code!" -ForegroundColor Cyan