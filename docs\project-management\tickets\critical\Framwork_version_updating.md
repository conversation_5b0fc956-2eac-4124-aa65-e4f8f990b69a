\
<!-- filepath: c:\\\\Users\\\\<USER>\\\\Documents\\\\Software\\\\Leptos_TaurieV2_BCS\\\\docs\\\\tickets\\\\Framwork_version_updating.m- [ ] **Documentation:**
    - [x] Document the findings and resolution for `tauri-sys`. *(See below)*
    - [ ] Document the decision and rationale regarding a future Tauri v2/v3 migration.

## Investigation Results

### tauri-sys Resolution (COMPLETED)
**Status:** ✅ RESOLVED  
**Date:** June 11, 2025  
**Finding:** The `tauri-sys` dependency issue has been resolved. The dependency is commented out in the root `Cargo.toml` with the explanation: `# tauri-sys = "0.4"  # Not available on crates.io - will use JS API directly`

**Technical Details:**
- `tauri-sys` v0.4 is an outdated crate not available on crates.io
- The project has been configured to use JavaScript API bindings instead
- No action required - this is the correct modern approach for Tauri v2
- The scanning tool flagged it as an issue, but it's actually properly handled

### Tauri Dependencies Status
**Current versions in `src-tauri/Cargo.toml`:**
- `tauri = "~2.5.1"` ✅ Latest for v2
- `tauri-plugin-shell = "~2.2.1"` ✅ Up to date
- `tauri-plugin-dialog = "~2.2.2"` ✅ Up to date  
- `tauri-plugin-fs = "~2.3.0"` ✅ Up to date
- `tauri-build = "~2.2.0"` ✅ Up to date

### Build Issues Resolution ✅
**Status:** ✅ RESOLVED
**Date:** June 19, 2025
**Resolution:** All build errors have been successfully resolved:
- ✅ WASM compilation issues fixed (lifetime and type mismatches in security_analysis_tabs.rs)
- ✅ Cargo build working for wasm32-unknown-unknown target
- ✅ Trunk build pipeline functional
- ✅ Development server operational
- ✅ All security analysis components working correctly

**Verification Complete:**
1. ✅ cargo build --target=wasm32-unknown-unknown - SUCCESS
2. ✅ trunk build - SUCCESS
3. ✅ trunk serve - SUCCESS (http://127.0.0.1:1420/)
4. ✅ All frontend components rendering correctly
# Ticket: BCS-101 - Update Tauri Framework and Dependencies

**Status:** 🟢 Open
**Priority:** High
**Created:** 2025-06-11
**Updated:** 2025-06-11
**Assigned To:** @Shoy
**Related Issues:** #123, #124
**Labels:** `enhancement`, `dependency-update`, `tauri`, `v1`

## Description

A recent scan of the codebase using `Tauri Version Checker v1.0.0` has identified that several Tauri framework components and plugins are outdated or require investigation. The project is currently on Tauri v1 (or an unknown older version). This ticket focuses on updating dependencies to their latest stable versions compatible with Tauri v2 and thoroughly investigating the status of `tauri-sys`. A potential migration to Tauri v2/v3 will be considered and documented, but its implementation will be handled in a separate ticket if pursued.

The scan summary indicates 2 outdated components, but the detailed list highlights specific versions for `@tauri-apps` packages and flags `tauri-plugin-shell` in backup files.

### Scan Output Summary:

```
Tauri Version: Not found (v1 or unknown)

📦 Summary:
• Total Components: 15
• Up to date: 13
• Outdated: 2

⚠ Version Warning:
• This project is not using Tauri v2 or v3.
• Consider upgrading to the latest Tauri v2 for better features and security.
```

### Detailed Scan Output:

```
C:\\Users\\<USER>\\Documents\\Software\\BadCharcters\\Test_program_4.1> node dist/index.js "C:\\Users\\<USER>\\Documents\\Software\\Leptos_TaurieV2_BCS"
Scanning C:\\Users\\<USER>\\Documents\\Software\\Leptos_TaurieV2_BCS for Tauri components...

Failed to fetch latest version for tauri-sys: Request failed with status code 404
Warning: Could not fetch latest version for tauri-sys from crate

 ╭──────────────────────────────────────── Tauri Version Checker v1.0.0 ─────────────────────────────────────────╮ 
 │                                                                                                               │ 
 │   Tauri Version: Not found (v1 or unknown)                                                                    │ 
 │                                                                                                               │ 
 │   📦 Summary:                                                                                                 │ 
 │   • Total Components: 15                                                                                      │ 
 │   • Up to date: 13                                                                                            │ 
 │   • Outdated: 2                                                                                               │ 
 │   • Core Components: 0                                                                                        │ 
 │   • Plugins: 9                                                                                                │ 
 │                                                                                                               │ 
 │   🔍 Components:                                                                                              │ 
 │   ❓ tauri-sys                      0.4             Investigation needed (Cargo.toml) - Failed to fetch       │ 
 │   ✓  tauri-plugin-shell             ~2.2.1          Update available: ~2.2.1 → 2.2.1 (src-tauri\\Cargo.toml)   │ 
 │   ✓  tauri-plugin-dialog            ~2.2.2          Update available: ~2.2.2 → 2.2.2 (src-tauri\\Cargo.toml)   │ 
 │   ✓  tauri-plugin-fs                ~2.3.0          Update available: ~2.3.0 → 2.3.0 (src-tauri\\Cargo.toml)   │ 
 │   ⚠  tauri-plugin-shell             ~2.2.0          Update available: ~2.2.0 → 2.2.1                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014403\\Cargo.toml)                                                  │ 
 │   ✓  tauri-plugin-dialog            ~2.2.2          Update available: ~2.2.2 → 2.2.2                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014403\\Cargo.toml)                                                  │ 
 │   ✓  tauri-plugin-fs                ~2.3.0          Update available: ~2.3.0 → 2.3.0                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014403\\Cargo.toml)                                                  │ 
 │   ⚠  tauri-plugin-shell             ~2.2.0          Update available: ~2.2.0 → 2.2.1                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014746\\Cargo.toml)                                                  │ 
 │   ✓  tauri-plugin-dialog            ~2.2.2          Update available: ~2.2.2 → 2.2.2                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014746\\Cargo.toml)                                                  │ 
 │   ✓  tauri-plugin-fs                ~2.3.0          Update available: ~2.3.0 → 2.3.0                          │ 
 │   (scripts\\backups\\MASTER_BACKUP_20250604_014746\\Cargo.toml)                                                  │ 
 │   ✓  @tauri-apps/api                ~2.5.0          Update available: ~2.5.0 → 2.5.0 (package.json)           │ 
 │   ✓  @tauri-apps/plugin-dialog      ~2.2.2          Up to date (package.json)                                 │ 
 │   ✓  @tauri-apps/plugin-fs          ~2.3.0          Update available: ~2.3.0 → 2.3.0 (package.json)           │ 
 │   ✓  @tauri-apps/plugin-shell       ~2.2.1          Up to date (package.json)                                 │ 
 │   ✓  @tauri-apps/cli                ~2.5.0          Update available: ~2.5.0 → 2.5.0 (package.json)           │ 
 │                                                                                                               │ 
 │   ⚠  Recommendations:                                                                                         │ 
 │   • Run the following to update your Tauri dependencies:                                                      │ 
 │   cargo update -p tauri --precise <version>                                                                   │ 
 │   npm update @tauri-apps/cli @tauri-apps/api                                                                  │ 
 │                                                                                                               │ 
 │   ⚠  Version Warning:                                                                                         │ 
 │   • This project is not using Tauri v2 or v3.                                                                 │ 
 │   • Consider upgrading to the latest Tauri v2 for better features and security.                               │ 
 │                                                                                                               │ 
 ╰───────────────────────────────────────────────────────────────────────────────────────────────────────────────╯ 


✨ Scan completed in 5.40s
```

## Acceptance Criteria

- [ ] `@tauri-apps/plugin-dialog` in `package.json` is updated from `~2.2.0` to `~2.2.2`.
- [ ] `@tauri-apps/plugin-shell` in `package.json` is updated from `~2.2.0` to `~2.2.1`.
- [ ] Confirm `tauri-plugin-shell` (currently `~2.2.1`), `tauri-plugin-dialog` (currently `~2.2.2`), and `tauri-plugin-fs` (currently `~2.3.0`) in `src-tauri/Cargo.toml` are correctly constrained and up-to-date within those constraints for Tauri v1.
- [ ] Outdated `tauri-plugin-shell` (from `~2.2.0` to `~2.2.1`) in backup `Cargo.toml` files (`scripts/backups/...`) are updated, or a decision is made to remove these backup files if they are no longer necessary.
- [x] The `tauri-sys` dependency (version `0.4` in `src-tauri/Cargo.toml`):
    - [x] Its definition and usage in the codebase are investigated. *(RESOLVED - commented out, using JS API)*
    - [x] A resolution for the "Failed to fetch latest version" error is found and documented. *(Not an error - correctly commented out)*
- [x] `npm install` completes successfully after `package.json` changes. *(Completed 2025-06-11)*
- [x] `cargo check` and `cargo build` complete successfully in `src-tauri`. *(RESOLVED 2025-06-19)*
- [x] `npm run tauri dev` runs successfully. *(RESOLVED 2025-06-19)*
- [ ] The project successfully builds (`cargo check`, `cargo build` in `src-tauri`) and runs (`npm run tauri dev` or equivalent) after all dependency updates.
- [ ] A decision is made and documented regarding the feasibility and plan for a future migration to Tauri v2/v3. If migration is pursued, a separate ticket should be created.

## Technical Details

- Primary files for dependency updates: `src-tauri/Cargo.toml` and `package.json`.
- Backup `Cargo.toml` files in `scripts/backups/` also list `tauri-plugin-shell ~2.2.0`.
- `tauri-sys` (version `0.4`): Appears to be `crates.io/crates/tauri-sys`, last updated 3 years ago, likely specific to Tauri v1. Its necessity and potential alternatives for Tauri v1 should be evaluated.
- Recommended `npm` command for frontend plugins: `npm update @tauri-apps/plugin-dialog@2.2.2 @tauri-apps/plugin-shell@2.2.1` (or edit `package.json` and run `npm install`).
- The `cargo update -p tauri --precise <version>` command is for updating the Tauri v1 core. This ticket primarily addresses plugins; a core Tauri v1 update might be considered if a specific compatible version is identified as beneficial.
- The main goal for this ticket is to stabilize and update the current Tauri v1 setup.

## Dependencies

- [ ] Access to the project's Git repository for committing changes.
- [ ] Node.js and Cargo environments set up for building and testing.

## Progress

- [x] **Investigation:**
    - [x] Determine the latest stable *Tauri v1 compatible* versions for all Tauri dependencies (especially for `src-tauri/Cargo.toml` plugins). *(Completed 2025-06-11)*
    - [x] Investigate the `tauri-sys` crate issue (definition, usage, resolution for v0.4). *(RESOLVED - See investigation notes below)*
    - [ ] Evaluate the feasibility and impact of migrating to Tauri v2/v3.
- [x] **Implementation (Main Project Files):**
    - [x] Update dependencies in `src-tauri/Cargo.toml`. *(Already up-to-date)*
    - [x] Update dependencies in `package.json`. *(Completed 2025-06-11)*
    - [x] Run `npm install` to apply `package.json` changes. *(Completed 2025-06-11)*
- [ ] **Implementation (Backup Files):**
    - [ ] Update or remove outdated dependencies in `scripts/backups/**/*.Cargo.toml`.
- [ ] **Testing:**
    - [ ] Run `cargo check` and `cargo build` in `src-tauri`.
    - [ ] Run `npm run tauri dev` (or equivalent build/dev script) *after successful `npm install`*.
    - [ ] Perform basic application functionality testing.
- [ ] **Documentation:**
    - [ ] Document the findings and resolution for `tauri-sys`.
    - [ ] Document the decision and rationale regarding Tauri v2/v3 migration.

## Notes

- Prioritize updating dependencies in the main project files (`package.json`, `src-tauri/Cargo.toml`).
- The Tauri v2/v3 migration is a significant effort and should be planned as a separate initiative if approved. This ticket aims to bring the current v1 setup to its best possible state.
- If `tauri-sys` is deeply integrated and cannot be easily removed or updated for v1, document this as a known limitation of staying on Tauri v1.

---
*Last updated: 2025-06-11*


## How to run the framwoekre tester in CLI:  To run your CLI tool from anywhere on your Windows 11 machine as a terminal command, follow these steps:

1. Install your CLI tool globally
From your project directory (
C:\Users\<USER>\Documents\Software\BadCharcters\Test_program_4.1
), run:

powershell
npm install -g .
This tells npm to link your CLI tool globally, using the "bin" field from your 
package.json
. It will create a command called tauri-v2-check (or whatever you named in the "bin" section).

2. Run your CLI from anywhere
Now, open any terminal (PowerShell, Command Prompt, or Windows Terminal) and simply type:

powershell
tauri-v2-check "C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS"
You can replace the path with any folder you want to scan.

3. Troubleshooting
If you get a "command not found" error, make sure your global npm directory is in your system PATH. You can check this by running:
powershell
npm bin -g
and making sure that directory is listed in your system PATH environment variable.
You may need to restart your terminal after installing globally for the new command to be recognized.
Summary:

npm install -g . (from your CLI project root)
Then use tauri-v2-check <folder> anywhere!
Let me know if you want to add more features, publish to npm, or need help with anything else!
