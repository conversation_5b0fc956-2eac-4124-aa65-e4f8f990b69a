#!/usr/bin/env powershell
# Quick script to scan this project for bad characters
# Uses our own bad character scanner to check ourselves!

param(
    [string]$OutputFormat = "summary",
    [switch]$SaveReport,
    [switch]$OpenReport
)

Write-Host "`n=== SELF-SCAN: BAD CHARACTER SCANNER ===" -ForegroundColor Cyan
Write-Host "Using our own tools to scan for accessibility issues" -ForegroundColor Gray

$project_root = $PSScriptRoot | Split-Path -Parent
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Try different scanners in order of preference
$scanners = @(
    @{
        Name = "Node.js Bad Character Detector"
        Type = "node"
        Path = "scripts\check-bad-characters.js"
        Command = { node $scanner_path $project_root }
    },
    @{
        Name = "Enhanced PowerShell Analyzer"
        Type = "powershell"
        Path = "scripts\enhanced_analyzer.ps1"
        Command = { & $scanner_path -Path $project_root -OutputFormat $OutputFormat }
    },
    @{
        Name = "PowerShell Codebase Analyzer"
        Type = "powershell"
        Path = "scripts\codebase_analyzer.ps1"
        Command = { & $scanner_path analyze -p $project_root -f $OutputFormat }
    },
    @{
        Name = "Rust CLI Analyzer"
        Type = "rust"
        Path = "src-tauri\target\debug\analyzer_cli.exe"
        Command = { & $scanner_path $project_root --output-format $OutputFormat }
    }
)

$scan_completed = $false
$scan_results = $null

foreach ($scanner in $scanners) {
    $scanner_path = Join-Path $project_root $scanner.Path
    
    if (Test-Path $scanner_path) {
        Write-Host "`nTrying: $($scanner.Name)" -ForegroundColor Yellow
        
        try {
            if ($scanner.Type -eq "node" -and -not (Get-Command node -ErrorAction SilentlyContinue)) {
                Write-Host "  Node.js not available" -ForegroundColor Red
                continue
            }
            
            $scan_results = & $scanner.Command
            $scan_completed = $true
            Write-Host "  Success!" -ForegroundColor Green
            
            # Display results
            Write-Host "`n=== SCAN RESULTS ===" -ForegroundColor Cyan
            $scan_results | Out-Host
            
            # Save report if requested
            if ($SaveReport) {
                $report_dir = Join-Path $project_root "reports\self-scans"
                if (-not (Test-Path $report_dir)) {
                    New-Item -ItemType Directory -Path $report_dir -Force | Out-Null
                }
                
                $report_file = Join-Path $report_dir "self_scan_$timestamp.txt"
                $scan_results | Out-File -FilePath $report_file -Encoding UTF8
                Write-Host "`nReport saved to: $report_file" -ForegroundColor Green
                
                if ($OpenReport) {
                    Start-Process notepad $report_file
                }
            }
            
            break
        } catch {
            Write-Host "  Failed: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "`nScanner not found: $($scanner.Path)" -ForegroundColor Gray
    }
}

if (-not $scan_completed) {
    Write-Host "`n[ERROR] No scanner could complete the analysis" -ForegroundColor Red
    Write-Host "Please ensure at least one of the following is available:" -ForegroundColor Yellow
    Write-Host "  1. Node.js installed and check-bad-characters.js exists" -ForegroundColor Gray
    Write-Host "  2. PowerShell analyzer scripts are present" -ForegroundColor Gray
    Write-Host "  3. Rust CLI is built (cargo build --bin analyzer_cli)" -ForegroundColor Gray
    exit 1
}

# Quick summary
Write-Host "`n=== QUICK TIPS ===" -ForegroundColor Cyan
Write-Host "1. Most issues are in test files (intentional)" -ForegroundColor Gray
Write-Host "2. Archive folders contain old code with known issues" -ForegroundColor Gray
Write-Host "3. Focus on cleaning main source files only" -ForegroundColor Gray
Write-Host "4. Use -SaveReport to save results for later review" -ForegroundColor Gray

Write-Host "`nFighting for accessibility, one character at a time!" -ForegroundColor Cyan