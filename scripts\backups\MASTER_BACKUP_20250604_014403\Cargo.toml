[package]
name = "laptos-tauri"
version = "0.2.0"
edition = "2021"
default-run = "laptos-tauri"

# Main Tauri application binary
[[bin]]
name = "laptos-tauri"
path = "src/main.rs"

# CLI binary target
[[bin]]
name = "analyzer_cli"
path = "src/bin/analyzer_cli.rs"

[build-dependencies]
tauri-build = { version = "~2.2.0", features = [] }

[dependencies]
tauri = { version = "~2.5.1", features = ["devtools"] }
tauri-plugin-shell = "~2.2.0"
tauri-plugin-dialog = "~2.2.2"
tauri-plugin-fs = "~2.3.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Enhanced Unicode and text processing
unicode-normalization = "0.1"
unicode-segmentation = "1.10"
unicode-width = "0.1"
encoding_rs = "0.8"
regex = "1.10"

# Performance and async
tokio = { version = "1.0", features = ["full"] }
rayon = "1.7"

# File operations and data handling
csv = "1.3"
chrono = { version = "0.4", features = ["serde"] }
base64 = "0.22"
sha2 = "0.10"
anyhow = "1.0"

# Additional features
uuid = { version = "1.0", features = ["v4"] }
dirs = "5.0"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
