{"name": "laptos-tauri-v2", "version": "0.1.0", "private": true, "type": "module", "description": "A modern desktop application built with Tauri v2 and Leptos", "scripts": {"tauri": "tauri", "tauri:dev": "cargo tauri dev", "tauri:build": "cargo tauri build", "dev": "trunk serve --port 1420", "build": "trunk build --release", "check:tauri-version": "tauri info"}, "dependencies": {"@tauri-apps/api": "~2.5.0", "@tauri-apps/plugin-dialog": "~2.2.2", "@tauri-apps/plugin-fs": "~2.3.0", "@tauri-apps/plugin-shell": "~2.2.1"}, "devDependencies": {"@playwright/test": "^1.53.1", "@tauri-apps/cli": "~2.5.0", "@types/node": "^24.0.7", "tailwindcss": "^3.4.17"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/IBIYP/Rust_Test.git"}, "keywords": ["leptos", "tauri", "rust", "desktop", "wasm", "pwa"], "author": "", "license": "ISC"}