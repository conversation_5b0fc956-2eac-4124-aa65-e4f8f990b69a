<#
.SYNOPSIS
    Analyzes text for bad characters using the Bad Character Scanner
    
.DESCRIPTION
    This script provides a PowerShell interface to the Bad Character Scanner CLI
    
.PARAMETER Text
    The text to analyze
    
.PARAMETER FilePath
    Path to a file to analyze
    
.PARAMETER AsJson
    Return results as JSON
    
.EXAMPLE
    "Hello‌World" | .\analyze-text.ps1
    
.EXAMPLE
    .\analyze-text.ps1 -FilePath "document.txt" -AsJson
#>

param(
    [Parameter(ValueFromPipeline = $true)]
    [string]$Text,
    
    [string]$FilePath,
    
    [switch]$AsJson
)

$projectRoot = Split-Path -Parent $PSScriptRoot
$cliPath = Join-Path $projectRoot "src-tauri"

# Build the CLI if needed
if (-not (Test-Path (Join-Path $projectRoot "target\release\analyzer_cli.exe"))) {
    Write-Host "Building analyzer CLI..." -ForegroundColor Yellow
    Push-Location $cliPath
    cargo build --bin analyzer_cli --release
    Pop-Location
}

# Prepare command
$args = @("run", "--bin", "analyzer_cli", "--")

if ($FilePath) {
    $args += @("analyze", $FilePath)
} elseif ($Text) {
    # Save text to temp file
    $tempFile = [System.IO.Path]::GetTempFileName()
    $Text | Set-Content $tempFile -Encoding UTF8
    $args += @("analyze", $tempFile)
}

if ($AsJson) {
    $args += "--json"
}

# Run analyzer
Push-Location $cliPath
try {
    $output = & cargo $args 2>&1
    
    if ($AsJson) {
        $output | ConvertFrom-Json
    } else {
        $output
    }
} finally {
    Pop-Location
    
    # Clean up temp file
    if ($tempFile -and (Test-Path $tempFile)) {
        Remove-Item $tempFile -Force
    }
}