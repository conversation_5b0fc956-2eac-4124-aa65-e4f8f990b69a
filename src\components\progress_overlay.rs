use leptos::*;
use std::collections::VecDeque;

#[derive(Debug, <PERSON>lone)]
pub struct ProgressState {
    pub is_active: bool,
    pub current_step: usize,
    pub total_steps: usize,
    pub current_operation: String,
    pub current_file: Option<String>,
    pub files_processed: usize,
    pub files_failed: usize,
    pub error_messages: VecDeque<String>,
    pub start_time: Option<web_sys::js_sys::Date>,
    pub can_cancel: bool,
}

impl Default for ProgressState {
    fn default() -> Self {
        Self {
            is_active: false,
            current_step: 0,
            total_steps: 0,
            current_operation: String::new(),
            current_file: None,
            files_processed: 0,
            files_failed: 0,
            error_messages: VecDeque::new(),
            start_time: None,
            can_cancel: false,
        }
    }
}

#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct ErrorInfo {
    pub message: String,
    pub file_path: Option<String>,
    pub timestamp: web_sys::js_sys::Date,
    pub error_type: ErrorType,
}

#[derive(Debug, <PERSON>lone)]
#[allow(dead_code)]
pub enum ErrorType {
    FileAccess,
    Analysis,
    Network,
    Validation,
    System,
}

impl ErrorType {
    #[allow(dead_code)]
    fn icon(&self) -> &'static str {
        match self {
            ErrorType::FileAccess => "📁",
            ErrorType::Analysis => "🔍",
            ErrorType::Network => "🌐",
            ErrorType::Validation => "⚠️",
            ErrorType::System => "🔧",
        }
    }

    #[allow(dead_code)]
    fn color_class(&self) -> &'static str {
        match self {
            ErrorType::FileAccess => "text-orange-600",
            ErrorType::Analysis => "text-blue-600",
            ErrorType::Network => "text-purple-600",
            ErrorType::Validation => "text-yellow-600",
            ErrorType::System => "text-red-600",
        }
    }
}

#[component]
pub fn ProgressOverlay(
    progress_state: ReadSignal<ProgressState>,
    on_cancel: Option<Callback<()>>,
) -> impl IntoView {
    let (show_details, set_show_details) = create_signal(false);

    let progress_percentage = create_memo(move |_| {
        let state = progress_state.get();
        if state.total_steps == 0 {
            0.0
        } else {
            (state.current_step as f64 / state.total_steps as f64) * 100.0
        }
    });

    let elapsed_time = create_memo(move |_| {
        let state = progress_state.get();
        if let Some(start_time) = &state.start_time {
            let now = web_sys::js_sys::Date::new_0();
            let elapsed_ms = now.get_time() - start_time.get_time();
            let elapsed_seconds = (elapsed_ms / 1000.0) as u32;
            let minutes = elapsed_seconds / 60;
            let seconds = elapsed_seconds % 60;
            format!("{}:{:02}", minutes, seconds)
        } else {
            "00:00".to_string()
        }
    });

    let estimated_remaining = create_memo(move |_| {
        let state = progress_state.get();
        if let Some(start_time) = &state.start_time {
            if state.current_step > 0 && state.total_steps > state.current_step {
                let now = web_sys::js_sys::Date::new_0();
                let elapsed_ms = now.get_time() - start_time.get_time();
                let avg_time_per_step = elapsed_ms / state.current_step as f64;
                let remaining_steps = state.total_steps - state.current_step;
                let estimated_ms = avg_time_per_step * remaining_steps as f64;
                let estimated_seconds = (estimated_ms / 1000.0) as u32;
                let minutes = estimated_seconds / 60;
                let seconds = estimated_seconds % 60;
                format!("~{}:{:02}", minutes, seconds)
            } else {
                "calculating...".to_string()
            }
        } else {
            "unknown".to_string()
        }
    });

    let success_rate = create_memo(move |_| {
        let state = progress_state.get();
        let total_processed = state.files_processed + state.files_failed;
        if total_processed == 0 {
            100.0
        } else {
            (state.files_processed as f64 / total_processed as f64) * 100.0
        }
    });

    view! {
        <Show when=move || progress_state.get().is_active>
            // Overlay backdrop
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
                    // Header
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center gap-3">
                            <div class="animate-spin">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                {move || progress_state.get().current_operation}
                            </h3>
                        </div>
                        
                        <div class="flex items-center gap-2">
                            <button
                                class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                                on:click=move |_| set_show_details.update(|d| *d = !*d)
                                title="Toggle Details"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                            
                            <Show when=move || progress_state.get().can_cancel && on_cancel.is_some()>
                                <button
                                    class="px-4 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                    on:click=move |_| {
                                        if let Some(callback) = &on_cancel {
                                            callback.call(());
                                        }
                                    }
                                >
                                    "Cancel"
                                </button>
                            </Show>
                        </div>
                    </div>

                    // Progress bar
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {move || format!("Step {} of {}", progress_state.get().current_step, progress_state.get().total_steps)}
                            </span>
                            <span class="text-sm font-medium text-blue-600">
                                {move || format!("{:.1}%", progress_percentage.get())}
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                            <div 
                                class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                                style=move || format!("width: {}%", progress_percentage.get())
                            ></div>
                        </div>
                    </div>

                    // Current file
                    <Show when=move || progress_state.get().current_file.is_some()>
                        <div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">"Processing:"</p>
                            <p class="text-sm font-mono text-gray-800 dark:text-gray-200 break-all">
                                {move || progress_state.get().current_file.unwrap_or_default()}
                            </p>
                        </div>
                    </Show>

                    // Statistics
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                                {move || progress_state.get().files_processed}
                            </div>
                            <div class="text-xs text-green-600 dark:text-green-400">"Processed"</div>
                        </div>
                        
                        <div class="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                                {move || progress_state.get().files_failed}
                            </div>
                            <div class="text-xs text-red-600 dark:text-red-400">"Failed"</div>
                        </div>
                        
                        <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {move || format!("{:.1}%", success_rate.get())}
                            </div>
                            <div class="text-xs text-blue-600 dark:text-blue-400">"Success Rate"</div>
                        </div>
                        
                        <div class="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <div class="text-xl font-bold text-purple-600 dark:text-purple-400">
                                {elapsed_time}
                            </div>
                            <div class="text-xs text-purple-600 dark:text-purple-400">"Elapsed"</div>
                        </div>
                    </div>

                    // Estimated time remaining
                    <Show when=move || { progress_state.get().current_step > 0 }>
                        <div class="mb-4 text-center">
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                "Estimated remaining: " <span class="font-medium">{estimated_remaining}</span>
                            </p>
                        </div>
                    </Show>

                    // Details section
                    <Show when=move || show_details.get()>
                        <div class="border-t dark:border-gray-600 pt-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">"Recent Messages"</h4>
                            <div class="max-h-40 overflow-y-auto space-y-2">
                                {move || {
                                    let errors = progress_state.get().error_messages;
                                    if errors.is_empty() {
                                        view! {
                                            <p class="text-sm text-gray-500 dark:text-gray-400 italic">"No messages yet"</p>
                                        }.into_view()
                                    } else {
                                        errors.iter().rev().take(10).enumerate().map(|(_i, error)| {
                                            view! {
                                                <div class="flex items-start gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded text-sm">
                                                    <span class="text-red-500 mt-0.5">"⚠️"</span>
                                                    <span class="text-red-700 dark:text-red-300 flex-1">{error}</span>
                                                </div>
                                            }
                                        }).collect::<Vec<_>>().into_view()
                                    }
                                }}
                            </div>
                        </div>
                    </Show>
                </div>
            </div>
        </Show>
    }
}

// Hook for managing progress state
#[derive(Clone)]
pub struct ProgressManager {
    pub state: WriteSignal<ProgressState>,
    pub read_state: ReadSignal<ProgressState>,
}

impl ProgressManager {
    pub fn new() -> (ReadSignal<ProgressState>, Self) {
        let (state_read, state_write) = create_signal(ProgressState::default());
        (state_read, Self { state: state_write, read_state: state_read })
    }

    pub fn start_operation(&self, operation: &str, total_steps: usize, can_cancel: bool) {
        self.state.update(|state| {
            state.is_active = true;
            state.current_operation = operation.to_string();
            state.total_steps = total_steps;
            state.current_step = 0;
            state.files_processed = 0;
            state.files_failed = 0;
            state.error_messages.clear();
            state.start_time = Some(web_sys::js_sys::Date::new_0());
            state.can_cancel = can_cancel;
        });
    }

    pub fn update_progress(&self, step: usize, current_file: Option<String>) {
        self.state.update(|state| {
            state.current_step = step;
            state.current_file = current_file;
        });
    }

    pub fn file_processed(&self) {
        self.state.update(|state| {
            state.files_processed += 1;
        });
    }

    pub fn file_failed(&self, error_message: &str) {
        self.state.update(|state| {
            state.files_failed += 1;
            state.error_messages.push_back(error_message.to_string());
            
            // Keep only the last 50 error messages to prevent memory issues
            if state.error_messages.len() > 50 {
                state.error_messages.pop_front();
            }
        });
    }

    #[allow(dead_code)]
    pub fn add_error(&self, error_message: &str) {
        self.state.update(|state| {
            state.error_messages.push_back(error_message.to_string());
            
            // Keep only the last 50 error messages
            if state.error_messages.len() > 50 {
                state.error_messages.pop_front();
            }
        });
    }

    pub fn finish_operation(&self) {
        self.state.update(|state| {
            state.is_active = false;
            state.current_file = None;
        });
    }

    #[allow(dead_code)]
    pub fn is_active(&self) -> bool {
        self.read_state.get().is_active
    }
}
