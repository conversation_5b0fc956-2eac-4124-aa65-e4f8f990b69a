# Ticket: Tauri Command Argument Structure Panic

**Status:** OPEN
**Priority:** CRITICAL
**Assigned:** Agent
**Created:** 2025-06-28

## Summary

A runtime panic occurs when invoking the Tauri command `analyze_codebase` from the frontend. The error message is:

```
unexpected exception: JsValue("invalid args `request` for command `analyze_codebase`: command analyze_codebase missing required key request")
```

## Root Cause Analysis
- The backend expects the argument to be structured as `{ request: { path: ... } }`.
- The frontend is sending the wrong structure or not serializing the argument correctly.

## Steps to Reproduce
1. Run the app and trigger a codebase analysis from the UI.
2. Observe the panic and error message in the browser console and backend logs.

## Acceptance Criteria
- [ ] The frontend sends the correct argument structure to the Tauri command.
- [ ] The backend receives `{ request: { path: ... } }` for `analyze_codebase`.
- [ ] No runtime panic occurs and the analysis completes successfully.
- [ ] The error is handled gracefully in the UI if the backend returns an error.

## Next Steps
- Audit the frontend code that invokes `analyze_codebase`.
- Ensure the argument is wrapped in a `request` key and properly serialized.
- Test the integration to confirm the error is resolved.
- Add error handling to display backend errors in the GUI, not just the console.

---
# Ticket: Module Ambiguity Error (Historical)

**Status:** RESOLVED
**Priority:** HIGH
**Assigned:** Agent
**Created:** 2025-06-28
**Resolved:** 2025-06-28

## Summary
- Module ambiguity error due to both `src/components/app_layout.rs` and `src/components/app_layout/mod.rs` existing.
- Resolved by deleting the archived `app_layout.rs` file.
