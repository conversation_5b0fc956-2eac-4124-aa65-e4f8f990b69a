# Rust Analyzer configuration for Bad Character Scanner
# Exclude test directories that contain intentional malicious Unicode characters

[files]
excludeDirs = [
    "test_advanced_live",
    "test_files", 
    "test_data_secure",
    "test_results",
    "test_bash_interface",
    "test_cleaning_verification",
    "test_live_verification"
]

[check]
allTargets = false
command = "cargo"

[cargo]
allFeatures = false
