--- Image Usage Analysis ---

Found 52 potentially unused image files:
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\apple-touch-icon.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\bcs-logo.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS-v2.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS-v3.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS-v4.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS-v5.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS-v6.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\BCS.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\favicon-16x16.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\favicon-32x32.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\favicon.ico
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\icon.ico
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\image.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo.ico
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_128x128.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_16x16.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_192x192.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_256x256.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_32x32.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_48x48.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_512x512.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_64x64.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_128x128.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_16x16.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_192x192.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_256x256.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_32x32.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_48x48.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_512x512.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\logo_t_64x64.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\magnifying-glass.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\Shield_V3.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\all_ui_images\theme-toggle.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\icons\magnifying-glass.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\icons\theme-toggle.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\logos\bcs-logo.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_016.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_017.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_018.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_026.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_027.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_028.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_029.ico
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_030.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_031.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_032.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_033.png
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_034.svg
C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\assets\images\ui_images_40x40\uiimg_035.svg


--- CSS Sizing Rule Analysis ---


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\gui\enhanced_archived.css ---
Rule: body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--surface-light);
  }
Rule: h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--space-md);
  }


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\gui\simple_archived.css ---
Rule: body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}
Rule: .min-h-screen {
  min-height: 100vh;
}
Rule: .max-w-6xl {
  max-width: 72rem;
}
Rule: .text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
Rule: .text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
Rule: .border {
  border-width: 1px;
}
Rule: .border-b {
  border-bottom-width: 1px;
}
Rule: .border-b-2 {
  border-bottom-width: 2px;
}
Rule: .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
Rule: .w-full {
  width: 100%;
}
Rule: textarea {
  resize: vertical;
  min-height: 200px;
}
Rule: .text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
Rule: .text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
Rule: .text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\gui\style_complex.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\gui\style_complex_archived.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\gui\tailwind_archived.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\archive\style_simple.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\dist\style-8f9417c41bf2edb.css ---
Rule: .icon-xs{flex-shrink:0;width:12px!important;height:12px!important}
Rule: .icon-sm{flex-shrink:0;width:16px!important;height:16px!important}
Rule: .icon-md{flex-shrink:0;width:20px!important;height:20px!important}
Rule: .icon-lg{flex-shrink:0;width:24px!important;height:24px!important}
Rule: .icon-xl{flex-shrink:0;width:32px!important;height:32px!important}
Rule: svg[class*=icon-],.icon-40,.emoji,.nav-icon,.emoji-icon{object-fit:contain!important;vertical-align:middle!important;width:40px!important;height:40px!important;display:inline-block!important}
Rule: .emoji,.symbol{object-fit:contain!important;vertical-align:middle!important;width:40px!important;height:40px!important;font-size:40px!important;line-height:40px!important;display:inline-block!important}
Rule: .nav-icon{vertical-align:middle;margin-right:.5rem;line-height:1;display:inline-block;font-size:1.2em!important}
Rule: span[class*=icon],.nav-icon,.emoji-icon{object-fit:contain!important;vertical-align:middle!important;width:40px!important;height:40px!important;font-size:40px!important;line-height:40px!important;display:inline-block!important}
Rule: @media (width<=768px){.icon-xl{width:28px!important;height:28px!important}
Rule: .icon-lg{width:20px!important;height:20px!important}
Rule: .icon-md{width:18px!important;height:18px!important}
Rule: body{background-color:var(--bg-primary);color:var(--text-primary);min-height:100vh;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;line-height:1.6}
Rule: h1,h2,h3,h4,h5,h6{margin-bottom:var(--spacing-md);font-weight:600;line-height:1.2}
Rule: .app-container{background-color:var(--bg-primary);flex-direction:column;min-height:100vh;display:flex}
Rule: .header-content{max-width:1400px;padding:0 var(--spacing-lg);justify-content:space-between;align-items:center;margin:0 auto;display:flex}
Rule: .main-content{width:100%;max-width:1400px;padding:var(--spacing-2xl)var(--spacing-lg);flex:1;margin:0 auto}
Rule: .btn-icon{padding:var(--spacing-sm);width:2.5rem;height:2.5rem}
Rule: .textarea{resize:vertical;min-height:200px;font-family:Consolas,Monaco,Courier New,monospace}
Rule: .progress-bar{background-color:var(--bg-tertiary);border-radius:var(--radius-sm);width:100%;height:8px;overflow:hidden}
Rule: .progress{background-color:var(--purple-primary);height:100%;transition:width var(--transition-normal)}
Rule: .spinner{border:2px solid var(--purple-primary);border-right-color:#0000;border-radius:50%;width:1.25rem;height:1.25rem;animation:.75s linear infinite spin}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\recorder\assets\codeMirrorModule-C3UTv-Ge.css ---
Rule: .CodeMirror{font-family:monospace;height:300px;color:#000;direction:ltr}
Rule: .CodeMirror-linenumber{padding:0 3px 0 5px;min-width:20px;text-align:right;color:#999;white-space:nowrap}
Rule: .CodeMirror-cursor{border-left:1px solid black;border-right:none;width:0}
Rule: .CodeMirror-scroll{overflow:scroll!important;margin-bottom:-50px;margin-right:-50px;padding-bottom:50px;height:100%;outline:none;position:relative;z-index:0}
Rule: .CodeMirror-gutters{position:absolute;left:0;top:0;min-height:100%;z-index:3}
Rule: .CodeMirror-gutter{white-space:normal;height:100%;display:inline-block;vertical-align:top;margin-bottom:-50px}
Rule: .CodeMirror-lines{cursor:text;min-height:1px}
Rule: .CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;border-width:0;background:transparent;font-family:inherit;font-size:inherit;margin:0;white-space:pre;word-wrap:normal;line-height:inherit;color:inherit;z-index:2;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;-webkit-font-variant-ligatures:contextual;font-variant-ligatures:contextual}
Rule: .CodeMirror-measure{position:absolute;width:100%;height:0;overflow:hidden;visibility:hidden}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\recorder\assets\index-eHBmevrY.css ---
Rule: body{--transparent-blue: #2196F355;--light-pink: #ff69b460;--gray: #888888;--sidebar-width: 250px;--box-shadow: rgba(0, 0, 0, .133) 0px 1.6px 3.6px 0px, rgba(0, 0, 0, .11) 0px .3px .9px 0px}
Rule: html,body{width:100%;height:100%;padding:0;margin:0;overflow:hidden;display:flex;overscroll-behavior-x:none}
Rule: #root{width:100%;height:100%;display:flex}
Rule: dialog{border:none;padding:0;box-shadow:var(--box-shadow);line-height:28px;max-width:400px}
Rule: dialog .title{display:flex;align-items:center;margin:0;padding:0 5px;height:32px;background-color:var(--vscode-sideBar-background);max-width:400px}
Rule: .button{color:var(--vscode-button-foreground);background:var(--vscode-button-background);margin:10px;border:none;height:28px;min-width:40px;cursor:pointer;-webkit-user-select:none;user-select:none}
Rule: *{box-sizing:border-box;min-width:0;min-height:0}
Rule: .cm-wrapper{line-height:18px}
Rule: .cm-wrapper,.cm-wrapper>div{width:100%;height:100%}
Rule: .split-view.vertical>.split-view-resizer{left:0;right:0;height:12px;cursor:ns-resize}
Rule: .split-view.horizontal>.split-view-resizer{top:0;bottom:0;width:12px;cursor:ew-resize}
Rule: .tabbed-pane-tab{padding:2px 6px 0;cursor:pointer;display:flex;align-items:center;justify-content:center;-webkit-user-select:none;user-select:none;border-bottom:2px solid transparent;outline:none;height:100%}
Rule: .tabbed-pane-tab-label{max-width:250px;white-space:pre;overflow:hidden;text-overflow:ellipsis;display:inline-block}
Rule: .tabbed-pane-tab-counter{padding:0 4px;background:var(--vscode-menu-separatorBackground);border-radius:8px;height:16px;margin-left:4px;line-height:16px;min-width:18px;display:flex;align-items:center;justify-content:center}
Rule: .toolbar{position:relative;display:flex;color:var(--vscode-sideBarTitle-foreground);min-height:35px;align-items:center;flex:none;padding-right:4px}
Rule: .toolbar.no-min-height{min-height:0}
Rule: .toolbar input{padding:0 5px;line-height:24px;outline:none;margin:0 4px}
Rule: .toolbar-separator{flex:none;background-color:var(--vscode-menu-separatorBackground);width:1px;padding:0;margin:5px 4px;height:16px}
Rule: .call-log{display:flex;flex-direction:column;flex:auto;line-height:20px;white-space:pre;overflow:auto}
Rule: .recorder-chooser{border:none;background:none;outline:none;color:var(--vscode-sideBarTitle-foreground);min-width:100px}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\traceViewer\codeMirrorModule.C3UTv-Ge.css ---
Rule: .CodeMirror{font-family:monospace;height:300px;color:#000;direction:ltr}
Rule: .CodeMirror-linenumber{padding:0 3px 0 5px;min-width:20px;text-align:right;color:#999;white-space:nowrap}
Rule: .CodeMirror-cursor{border-left:1px solid black;border-right:none;width:0}
Rule: .CodeMirror-scroll{overflow:scroll!important;margin-bottom:-50px;margin-right:-50px;padding-bottom:50px;height:100%;outline:none;position:relative;z-index:0}
Rule: .CodeMirror-gutters{position:absolute;left:0;top:0;min-height:100%;z-index:3}
Rule: .CodeMirror-gutter{white-space:normal;height:100%;display:inline-block;vertical-align:top;margin-bottom:-50px}
Rule: .CodeMirror-lines{cursor:text;min-height:1px}
Rule: .CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;border-width:0;background:transparent;font-family:inherit;font-size:inherit;margin:0;white-space:pre;word-wrap:normal;line-height:inherit;color:inherit;z-index:2;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;-webkit-font-variant-ligatures:contextual;font-variant-ligatures:contextual}
Rule: .CodeMirror-measure{position:absolute;width:100%;height:0;overflow:hidden;visibility:hidden}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\traceViewer\defaultSettingsView.NYBT19Ch.css ---
Rule: body{--transparent-blue: #2196F355;--light-pink: #ff69b460;--gray: #888888;--sidebar-width: 250px;--box-shadow: rgba(0, 0, 0, .133) 0px 1.6px 3.6px 0px, rgba(0, 0, 0, .11) 0px .3px .9px 0px}
Rule: html,body{width:100%;height:100%;padding:0;margin:0;overflow:hidden;display:flex;overscroll-behavior-x:none}
Rule: #root{width:100%;height:100%;display:flex}
Rule: dialog{border:none;padding:0;box-shadow:var(--box-shadow);line-height:28px;max-width:400px}
Rule: dialog .title{display:flex;align-items:center;margin:0;padding:0 5px;height:32px;background-color:var(--vscode-sideBar-background);max-width:400px}
Rule: .button{color:var(--vscode-button-foreground);background:var(--vscode-button-background);margin:10px;border:none;height:28px;min-width:40px;cursor:pointer;-webkit-user-select:none;user-select:none}
Rule: *{box-sizing:border-box;min-width:0;min-height:0}
Rule: .split-view.vertical>.split-view-resizer{left:0;right:0;height:12px;cursor:ns-resize}
Rule: .split-view.horizontal>.split-view-resizer{top:0;bottom:0;width:12px;cursor:ew-resize}
Rule: .action-icons{flex:none;display:flex;flex-direction:row;cursor:pointer;height:20px;position:relative;top:1px;border-bottom:1px solid transparent}
Rule: .action-list-show-all{display:flex;cursor:pointer;height:28px;align-items:center}
Rule: .tree-view-entry{display:flex;flex:none;cursor:pointer;align-items:center;white-space:nowrap;line-height:28px;padding-left:5px}
Rule: .tree-view-indent{min-width:16px}
Rule: .toolbar-separator{flex:none;background-color:var(--vscode-menu-separatorBackground);width:1px;padding:0;margin:5px 4px;height:16px}
Rule: .call-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}
Rule: .call-section{padding-left:6px;padding-top:2px;margin-top:2px;font-weight:700;text-transform:uppercase;font-size:10px;color:var(--vscode-sideBarTitle-foreground);line-height:24px}
Rule: .call-line{padding:4px 0 4px 6px;display:flex;align-items:center;text-overflow:ellipsis;overflow:hidden;line-height:20px;white-space:nowrap}
Rule: .call-tab .error-message{padding:5px;line-height:17px}
Rule: .list-view-entry{display:flex;flex:none;cursor:pointer;align-items:center;white-space:nowrap;line-height:28px;padding-left:5px}
Rule: .list-view-indent{min-width:16px}
Rule: .log-list-item{text-wrap:wrap;-webkit-user-select:text;user-select:text;width:100%}
Rule: .attachments-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}
Rule: .attachments-section{padding-left:6px;font-weight:700;text-transform:uppercase;font-size:12px;color:var(--vscode-sideBarTitle-foreground);line-height:24px}
Rule: .attachment-item img{flex:none;min-width:200px;max-width:80%;box-shadow:0 12px 28px #0003,0 2px 4px #0000001a}
Rule: .cm-wrapper{line-height:18px}
Rule: .cm-wrapper,.cm-wrapper>div{width:100%;height:100%}
Rule: .expandable{flex:none;flex-direction:column;line-height:28px}
Rule: .source-tab-file-name{padding-left:8px;height:100%;display:flex;align-items:center;flex:1 1 auto}
Rule: .toolbar{position:relative;display:flex;color:var(--vscode-sideBarTitle-foreground);min-height:35px;align-items:center;flex:none;padding-right:4px}
Rule: .toolbar.no-min-height{min-height:0}
Rule: .toolbar input{padding:0 5px;line-height:24px;outline:none;margin:0 4px}
Rule: .console-time{float:left;min-width:50px;color:var(--vscode-editorCodeLens-foreground);-webkit-user-select:none;user-select:none}
Rule: .console-line .codicon.status-none:after,.console-line .codicon.status-error:after,.console-line .codicon.status-warning:after{display:inline-block;content:"a";color:transparent;border-radius:4px;width:8px;height:8px;position:relative;top:8px;left:-7px}
Rule: .console-repeat{display:inline-block;padding:0 2px;font-size:12px;line-height:18px;border-radius:6px;background-color:#8c959f;color:#fff;margin-right:10px;flex:none;font-weight:600}
Rule: .network-request-details-tab{width:100%;height:100%;-webkit-user-select:text;user-select:text;line-height:24px;margin-left:10px;overflow:auto}
Rule: .network-font-preview{font-family:font-preview;font-size:30px;line-height:40px;padding:16px 16px 16px 6px;overflow:hidden;text-overflow:ellipsis;text-align:center}
Rule: .tab-network .toolbar{min-height:30px!important;background-color:initial!important;border-bottom:1px solid var(--vscode-panel-border)}
Rule: .copy-request-dropdown .copy-request-dropdown-toggle{margin-right:14px;width:135px}
Rule: .copy-request-dropdown .copy-request-dropdown-menu{position:absolute;display:flex;flex-direction:column;width:135px;z-index:10;background-color:var(--vscode-list-dropBackground)}
Rule: .green-circle:before,.red-circle:before,.yellow-circle:before{content:"";display:inline-block;width:12px;height:12px;border-radius:6px;margin-right:2px;align-self:center}
Rule: .tabbed-pane-tab{padding:2px 6px 0;cursor:pointer;display:flex;align-items:center;justify-content:center;-webkit-user-select:none;user-select:none;border-bottom:2px solid transparent;outline:none;height:100%}
Rule: .tabbed-pane-tab-label{max-width:250px;white-space:pre;overflow:hidden;text-overflow:ellipsis;display:inline-block}
Rule: .tabbed-pane-tab-counter{padding:0 4px;background:var(--vscode-menu-separatorBackground);border-radius:8px;height:16px;margin-left:4px;line-height:16px;min-width:18px;display:flex;align-items:center;justify-content:center}
Rule: .network-filters{display:flex;gap:16px;background-color:var(--vscode-sideBar-background);padding:4px 8px;min-height:32px}
Rule: .snapshot-controls{flex:none;background-color:var(--vscode-sideBar-background);color:var(--vscode-sideBarTitle-foreground);display:flex;box-shadow:var(--box-shadow);height:32px;align-items:center;justify-content:center}
Rule: .snapshot-toggle{margin-top:4px;padding:4px 8px;cursor:pointer;border-radius:20px;margin-left:4px;width:60px;display:flex;align-items:center;justify-content:center}
Rule: .snapshot-wrapper{flex:auto;margin:1px;padding:10px;position:relative;--browser-frame-header-height: 40px}
Rule: .snapshot-switcher{width:100%;height:calc(100% - var(--browser-frame-header-height));position:relative}
Rule: iframe[name=snapshot]{position:absolute;top:0;left:0;width:100%;height:100%;border:none;background:#fff;visibility:hidden}
Rule: .snapshot-tab .cm-wrapper{line-height:23px;margin-right:4px}
Rule: .browser-frame-dot{border-radius:50%;display:inline-block;height:12px;margin-right:6px;margin-top:4px;width:12px}
Rule: .browser-frame-menu-bar{background-color:#aaa;display:block;height:3px;margin:3px 0;width:17px}
Rule: .browser-frame-header{align-items:center;background:#ebedf0;display:flex;padding:8px 16px;border-top-left-radius:6px;border-top-right-radius:6px;height:var(--browser-frame-header-height)}
Rule: .film-strip-lanes{flex:none;display:flex;flex-direction:column;position:relative;min-height:50px;max-height:200px;overflow-x:hidden;overflow-y:auto}
Rule: .timeline-divider{position:absolute;width:1px;top:0;bottom:0;background-color:var(--vscode-panel-border)}
Rule: .timeline-lane{pointer-events:none;overflow:hidden;flex:none;height:20px;position:relative}
Rule: .timeline-window-resizer{flex:none;width:10px;cursor:ew-resize;pointer-events:all;position:relative;background-color:var(--vscode-panel-border);border-left:1px solid var(--vscode-panel-border);border-right:1px solid var(--vscode-panel-border)}
Rule: .annotations-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\traceViewer\index.CFOW-Ezb.css ---
Rule: .drop-target{display:flex;align-items:center;justify-content:center;flex:auto;flex-direction:column;background-color:var(--vscode-editor-background);position:absolute;top:0;right:0;bottom:0;left:0;z-index:100;line-height:24px}
Rule: .progress{flex:none;width:100%;height:3px;margin-top:-3px;z-index:10}
Rule: .inner-progress{background-color:var(--vscode-progressBar-background);height:100%}
Rule: .header{display:flex;background-color:#000;flex:none;flex-basis:48px;line-height:48px;font-size:16px;color:#ccc}
Rule: .workbench-loader .logo img{height:32px;width:32px;pointer-events:none;flex:none}
Rule: html,body{min-width:550px;min-height:450px;overflow:auto}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\traceViewer\uiMode.BatfzHMG.css ---
Rule: .ui-mode .section-title{display:flex;flex:auto;flex-direction:row;align-items:center;font-size:11px;text-transform:uppercase;font-weight:700;text-overflow:ellipsis;overflow:hidden;padding:8px;height:30px}
Rule: .ui-mode-sidebar img{flex:none;margin-left:6px;width:24px;height:24px}
Rule: .ui-mode .disconnected{display:flex;align-items:center;justify-content:center;flex:auto;flex-direction:column;background-color:var(--vscode-editor-background);position:absolute;top:0;right:0;bottom:0;left:0;z-index:1000;line-height:24px}
Rule: .status-line{flex:auto;white-space:nowrap;line-height:22px;padding-left:10px;display:flex;flex-direction:row;align-items:center;height:30px}
Rule: .ui-mode-sidebar input[type=search]{flex:auto;padding:0 5px;line-height:24px;outline:none;margin:0 4px;border:none;color:var(--vscode-input-foreground);background-color:var(--vscode-input-background)}
Rule: .filter-summary{line-height:24px;margin-left:24px}
Rule: .filter-entry{line-height:24px}
Rule: .tag{display:inline-block;padding:0 8px;font-size:12px;font-weight:500;line-height:18px;border:1px solid transparent;border-radius:2em;background-color:#8c959f;color:#fff;margin:0 10px;flex:none;font-weight:600}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\playwright-core\lib\vite\traceViewer\xtermModule.Beg8tuEN.css ---
Rule: .xterm .xterm-helper-textarea{padding:0;border:0;margin:0;position:absolute;opacity:0;left:-9999em;top:0;width:0;height:0;z-index:-5;white-space:nowrap;overflow:hidden;resize:none}
Rule: .xterm-char-measure-element{display:inline-block;visibility:hidden;position:absolute;top:0;left:-9999em;line-height:normal}
Rule: .xterm .live-region{position:absolute;left:-9999px;width:1px;height:1px;overflow:hidden}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\lib\css\preflight.css ---
Rule: /*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme('borderColor.DEFAULT', currentColor); /* 2 */
}
Rule: /*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: theme('fontFamily.sans', ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"); /* 4 */
  font-feature-settings: theme('fontFamily.sans[1].fontFeatureSettings', normal); /* 5 */
  font-variation-settings: theme('fontFamily.sans[1].fontVariationSettings', normal); /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
Rule: /*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
Rule: /*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
Rule: /*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
Rule: /*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
Rule: /*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
Rule: /*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\src\css\preflight.css ---
Rule: /*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme('borderColor.DEFAULT', currentColor); /* 2 */
}
Rule: /*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: theme('fontFamily.sans', ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"); /* 4 */
  font-feature-settings: theme('fontFamily.sans[1].fontFeatureSettings', normal); /* 5 */
  font-variation-settings: theme('fontFamily.sans[1].fontVariationSettings', normal); /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
Rule: /*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
Rule: /*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
Rule: /*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
Rule: /*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
Rule: /*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
Rule: /*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\base.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\components.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\screens.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\tailwind.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\utilities.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\node_modules\tailwindcss\variants.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\target\debug\build\laptos-tauri-8691adccb9fe1a07\out\tauri-codegen-assets\aaad91c008eb73b9aa934a9626ba9d05f454af27f2ee8bb1d77bcb5f5bf90dcc.css ---


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\target\debug\build\laptos-tauri-8691adccb9fe1a07\out\tauri-codegen-assets\b7648b8180e8b6cbca6d94cbd5cc5358fba58836b5667c31c902cd37ddf5ecf9.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\target\debug\build\laptos-tauri-8691adccb9fe1a07\out\tauri-codegen-assets\bf137e842aae017ec98ee10d1dc4baaca3d8d214c8f1081084e0af3ded1cb286.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\tests\bash-interface\analyze_test\css\styles.css ---
No relevant sizing rules found in this file.


--- Analyzing: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS-0.3.1\style.css ---
Rule: /* Icon Sizing Standards - CRITICAL FOR PROPER DISPLAY */
.icon-xs {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}
Rule: .icon-sm {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}
Rule: .icon-md {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}
Rule: .icon-lg {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}
Rule: .icon-xl {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}
Rule: /* Strictly enforce 40x40px for all SVGs/icons */
svg[class*="icon-"], .icon-40, .emoji, .nav-icon, .emoji-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}
Rule: /* Prevent emoji and symbol oversizing */
.emoji, .symbol {
  width: 40px;
  height: 40px;
  font-size: 40px;
  line-height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}
Rule: /* Navigation icon sizing (for emojis in nav buttons) */
.nav-icon {
  font-size: 1.2em;
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.5rem;
  line-height: 1;
}
Rule: /* General emoji constraint for all contexts */
span[class*="icon"], .nav-icon, .emoji-icon {
  width: 40px;
  height: 40px;
  font-size: 40px;
  line-height: 40px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}
Rule: /* Responsive scaling for smaller screens */
@media (max-width: 768px) {
  .icon-xl { width: 28px; height: 28px; }
Rule: .icon-lg { width: 20px; height: 20px; }
Rule: .icon-md { width: 18px; height: 18px; }
Rule: body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}
Rule: /* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}
Rule: /* App Container */
.app-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
}
Rule: .header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
Rule: /* Main Content */
.main-content {
  flex: 1;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
}
Rule: .btn-icon {
  padding: var(--spacing-sm);
  width: 2.5rem;
  height: 2.5rem;
}
Rule: .input, .textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-fast);
}
Rule: .textarea {
  min-height: 200px;
  resize: vertical;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}
Rule: /* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}
Rule: .progress {
  height: 100%;
  background-color: var(--purple-primary);
  transition: width var(--transition-normal);
}
Rule: .spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--purple-primary);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 0.75s linear infinite;
}
