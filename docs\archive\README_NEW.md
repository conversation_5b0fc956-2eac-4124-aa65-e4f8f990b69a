# Laptos TauriV2 - Bad Character Scanner

A Progressive Web App (PWA) built with Tauri v2 and Leptos (Rust/WebAssembly), designed to evolve into a powerful Bad Character Scanner tool.

## Features

- **Modern UI**: Built with Leptos for fine-grained reactivity and optimal performance
- **Rust Frontend**: Type-safe frontend development with Rust and WebAssembly
- **Cross-Platform**: Runs on Windows, macOS, and Linux
- **Future-Ready**: Architecture designed to support advanced character scanning features

## Prerequisites

- [Rust](https://www.rust-lang.org/tools/install) (latest stable version)
- [Trunk](https://trunkrs.dev/) - Build tool for Rust/WASM applications
- [Tauri prerequisites](https://tauri.app/v1/guides/getting-started/prerequisites) for your platform

## Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/laptos-tauri-v2.git
   cd laptos-tauri-v2
   ```

2. **Install dependencies**
   ```bash
   # Install Rust WASM target
   rustup target add wasm32-unknown-unknown
   
   # Install Trunk
   cargo install trunk
   
   # Install Tauri CLI
   cargo install tauri-cli
   ```

3. **Start the development server**
   ```bash
   cargo tauri dev
   ```

## Project Structure

```
laptos-tauri-v2/
├── src/                  # Leptos frontend source (Rust/WebAssembly)
│   ├── components/       # Reusable UI components
│   ├── pages/           # Page components
│   ├── lib.rs           # Main application logic
│   └── main.rs          # Application entry point
├── src-tauri/           # Tauri backend (Rust)
│   ├── src/
│   │   └── main.rs     # Rust entry point
│   ├── Cargo.toml       # Rust dependencies
│   └── tauri.config.json # Tauri configuration
├── dist/                # Build output directory (generated by Trunk)
├── index.html           # HTML entry point for Leptos app
├── Cargo.toml           # Workspace configuration
└── README.md           # This file
```

## Available Scripts

- `cargo tauri dev` - Start Tauri in development mode (includes Leptos hot reload)
- `cargo tauri build` - Build the Tauri application for production
- `trunk serve` - Start only the Leptos frontend development server
- `trunk build` - Build only the Leptos frontend

## Technology Stack

- **Frontend**: [Leptos](https://leptos.dev/) - A full-stack, isomorphic Rust web framework
- **Build Tool**: [Trunk](https://trunkrs.dev/) - Build tool for Rust and WebAssembly
- **Desktop Framework**: [Tauri v2](https://tauri.app/) - Build cross-platform desktop apps
- **Language**: Rust for both frontend and backend

## Future Development

This is the initial Hello World version of the application. Future updates will include:

1. Text input validation with Leptos reactive forms
2. Character set analysis using Rust's Unicode libraries
3. Real-time character scanning with fine-grained reactivity
4. Pattern matching for suspicious characters
5. Export/import functionality with native file system access

## Contributing

Contributions are welcome! Please read our [contributing guidelines](CONTRIBUTING.md) before submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
