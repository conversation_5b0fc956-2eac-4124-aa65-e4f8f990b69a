use leptos::*;
use crate::components::codebase::types::BasicProgress;
use crate::components::codebase::ui::progress_bar::ProgressBar;

#[component]
pub fn ProgressDemo() -> impl IntoView {
    let (progress, set_progress) = create_signal::<Option<BasicProgress>>(None);
    let (is_running, set_is_running) = create_signal(false);

    let start_demo = move |_| {
        if is_running.get() {
            return;
        }
        
        set_is_running.set(true);
        set_progress.set(None);
        
        spawn_local(async move {
            // Simulate file analysis with realistic progress
            let files = vec![
                "src/main.rs",
                "src/lib.rs", 
                "src/components/mod.rs",
                "src/components/codebase/handlers.rs",
                "src/components/codebase/types.rs",
                "src/components/codebase/ui/progress_bar.rs",
                "src/components/text/analyzer.rs",
                "src/utils/unicode.rs",
                "Cargo.toml",
                "README.md",
            ];
            
            let total_files = files.len() as u32;
            
            // Initial progress
            set_progress.set(Some(BasicProgress {
                current: 0,
                total: total_files,
                percentage: 0.0,
                current_file: "Initializing analysis...".to_string(),
                status: "Starting".to_string(),
            }));
            
            gloo_timers::future::sleep(std::time::Duration::from_millis(200)).await;
            
            // Simulate analyzing each file
            for (index, file) in files.iter().enumerate() {
                let current = (index + 1) as u32;
                let percentage = (current as f32 / total_files as f32) * 100.0;
                
                set_progress.set(Some(BasicProgress {
                    current,
                    total: total_files,
                    percentage,
                    current_file: file.to_string(),
                    status: "Analyzing".to_string(),
                }));
                
                // Variable delay to simulate different file sizes
                let delay = match file {
                    f if f.contains("handlers.rs") => 800,  // Larger file
                    f if f.contains("progress_bar.rs") => 600,
                    f if f.contains("Cargo.toml") => 200,   // Small file
                    _ => 400,
                };
                
                gloo_timers::future::sleep(std::time::Duration::from_millis(delay)).await;
            }
            
            // Completion
            set_progress.set(Some(BasicProgress {
                current: total_files,
                total: total_files,
                percentage: 100.0,
                current_file: "Analysis complete!".to_string(),
                status: "Complete".to_string(),
            }));
            
            // Clear after a moment
            gloo_timers::future::sleep(std::time::Duration::from_millis(1000)).await;
            set_progress.set(None);
            set_is_running.set(false);
        });
    };

    let start_fast_demo = move |_| {
        if is_running.get() {
            return;
        }
        
        set_is_running.set(true);
        set_progress.set(None);
        
        spawn_local(async move {
            // Simulate very fast analysis (under 400ms)
            set_progress.set(Some(BasicProgress {
                current: 0,
                total: 3,
                percentage: 0.0,
                current_file: "Quick scan...".to_string(),
                status: "Starting".to_string(),
            }));
            
            gloo_timers::future::sleep(std::time::Duration::from_millis(100)).await;
            
            set_progress.set(Some(BasicProgress {
                current: 3,
                total: 3,
                percentage: 100.0,
                current_file: "Fast analysis complete!".to_string(),
                status: "Complete".to_string(),
            }));
            
            gloo_timers::future::sleep(std::time::Duration::from_millis(200)).await;
            set_progress.set(None);
            set_is_running.set(false);
        });
    };

    view! {
        <div class="space-y-6">
            <div class="card p-6">
                <h2 class="text-xl font-bold mb-4">"Progress Bar Demo"</h2>
                <p class="text-gray-600 mb-4">
                    "Test the lazy loading progress bar with different scenarios:"
                </p>
                
                <div class="flex gap-4 mb-6">
                    <button 
                        class="btn btn-primary"
                        on:click=start_demo
                        disabled=move || is_running.get()
                    >
                        {move || if is_running.get() { "Running..." } else { "Start Normal Demo (4+ seconds)" }}
                    </button>
                    
                    <button 
                        class="btn btn-secondary"
                        on:click=start_fast_demo
                        disabled=move || is_running.get()
                    >
                        {move || if is_running.get() { "Running..." } else { "Start Fast Demo (<400ms)" }}
                    </button>
                </div>
                
                <div class="text-sm text-gray-500 space-y-1">
                    <p>"• Normal demo: Shows progress bar after 400ms delay"</p>
                    <p>"• Fast demo: Completes before 400ms, no progress bar shown"</p>
                    <p>"• Progress bar includes file count, percentage, current file, and estimated time"</p>
                </div>
            </div>
            
            <ProgressBar progress=progress.into() />
            
            <div class="card p-4 bg-gray-50">
                <h3 class="font-semibold mb-2">"Current Progress State:"</h3>
                <pre class="text-xs text-gray-600 overflow-auto">
                    {move || {
                        match progress.get() {
                            Some(p) => format!("{:#?}", p),
                            None => "No progress data".to_string(),
                        }
                    }}
                </pre>
            </div>
        </div>
    }
}
