// Comprehensive Security Analysis Engine - Backend Core
// Advanced codebase analysis with threat detection - By <PERSON> - 2025
use serde::{Deserialize, Serialize};
// Removed unused HashMap import
use std::path::Path;

use super::{
    HomoglyphDetector, HomoglyphThreat,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>hr<PERSON>,
    SecurityScanner, SecurityThreat,
    RiskAssessor, RiskAssessment,
    UnicodeAnalyzer, UnicodeAnalysis,
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ComprehensiveAnalysisResult {
    pub analysis_metadata: AnalysisMetadata,
    pub file_analyses: Vec<FileAnalysisResult>,
    pub homoglyph_threats: Vec<HomoglyphThreat>,
    pub pattern_threats: Vec<PatternThreat>,
    pub security_threats: Vec<SecurityThreat>,
    pub unicode_analysis: UnicodeAnalysis,
    pub risk_assessment: RiskAssessment,
    pub executive_summary: ExecutiveSummary,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalysisMetadata {
    pub analysis_id: String,
    pub timestamp: String,
    pub total_files_scanned: usize,
    pub total_lines_analyzed: usize,
    pub total_characters_analyzed: usize,
    pub analysis_duration_ms: u64,
    pub analyzer_version: String,
    pub scan_depth: ScanDepth,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScanDepth {
    Basic,      // Just file counting and basic stats
    Standard,   // Homoglyph and pattern detection
    Deep,       // Full security analysis
    Forensic,   // Maximum detail for incident response
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileAnalysisResult {
    pub file_path: String,
    pub relative_path: String,
    pub file_type: String,
    pub file_size: u64,
    pub line_count: usize,
    pub character_count: usize,
    pub encoding: String,
    pub analysis_status: String,
    pub error_message: Option<String>,
    
    // Threat counts per file
    pub homoglyph_count: usize,
    pub pattern_threat_count: usize,
    pub security_threat_count: usize,
    
    // File-specific risk score
    pub risk_score: f32,
    pub risk_factors: Vec<String>,
    
    // Detailed findings
    pub file_homoglyphs: Vec<HomoglyphThreat>,
    pub file_patterns: Vec<PatternThreat>,
    pub file_security_threats: Vec<SecurityThreat>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutiveSummary {
    pub overall_security_posture: SecurityPosture,
    pub critical_findings: usize,
    pub high_risk_findings: usize,
    pub medium_risk_findings: usize,    // NEW: Medium severity issues
    pub low_risk_findings: usize,       // NEW: Low severity issues
    pub total_threats: usize,
    pub total_issues_detected: usize,   // NEW: All issues regardless of severity
    pub files_with_issues: usize,       // NEW: Count of files containing issues
    pub most_vulnerable_files: Vec<String>,
    pub recommended_actions: Vec<String>,
    pub compliance_status: String,
    pub business_impact: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityPosture {
    Critical,     // Immediate action required
    Poor,         // Significant vulnerabilities
    Fair,         // Some concerns
    Good,         // Minor issues only
    Excellent,    // No significant issues
}

pub struct CodebaseAnalyzer {
    homoglyph_detector: HomoglyphDetector,
    pattern_analyzer: PatternAnalyzer,
    security_scanner: SecurityScanner,
    unicode_analyzer: UnicodeAnalyzer,
    risk_assessor: RiskAssessor,
}

impl CodebaseAnalyzer {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(CodebaseAnalyzer {
            homoglyph_detector: HomoglyphDetector::new(),
            pattern_analyzer: PatternAnalyzer::new()?,
            security_scanner: SecurityScanner::new()?,
            unicode_analyzer: UnicodeAnalyzer::new(),
            risk_assessor: RiskAssessor::new(),
        })
    }

    pub fn analyze_codebase(
        &self,
        target_path: &str,
        scan_depth: ScanDepth,
    ) -> Result<ComprehensiveAnalysisResult, Box<dyn std::error::Error>> {
        self.analyze_codebase_with_progress(target_path, scan_depth, None::<fn(u32, u32, f32, &str, &str)>)
    }

    pub fn analyze_codebase_with_progress<F>(
        &self,
        target_path: &str,
        scan_depth: ScanDepth,
        progress_callback: Option<F>,
    ) -> Result<ComprehensiveAnalysisResult, Box<dyn std::error::Error>>
    where
        F: Fn(u32, u32, f32, &str, &str),
    {
        let start_time = std::time::Instant::now();
        let analysis_id = uuid::Uuid::new_v4().to_string();

        // Collect all files to analyze
        let file_paths = self.collect_files(target_path)?;
        let total_files = file_paths.len() as u32;

        // Emit initial progress if callback provided
        if let Some(ref callback) = progress_callback {
            callback(0, total_files, 0.0, "Initializing analysis...", "Starting");
        }

        let mut file_analyses = Vec::new();
        let mut all_homoglyph_threats = Vec::new();
        let mut all_pattern_threats = Vec::new();
        let mut all_security_threats = Vec::new();
        let mut combined_content = String::new();

        let mut total_lines = 0;
        let mut total_characters = 0;

        // Analyze each file with progress tracking
        for (index, file_path) in file_paths.iter().enumerate() {
            let current_file_num = (index + 1) as u32;
            let percentage = (current_file_num as f32 / total_files as f32) * 100.0;

            // Emit progress for current file
            if let Some(ref callback) = progress_callback {
                let file_name = std::path::Path::new(file_path)
                    .file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or(file_path);
                callback(current_file_num, total_files, percentage, file_name, "Analyzing");
            }
            match self.analyze_single_file(file_path, &scan_depth) {
                Ok(mut file_result) => {
                    total_lines += file_result.line_count;
                    total_characters += file_result.character_count;
                    
                    // Collect content for unicode analysis
                    if let Ok(content) = std::fs::read_to_string(file_path) {
                        combined_content.push_str(&content);
                        combined_content.push('\n');
                    }
                    
                    // Aggregate threats
                    all_homoglyph_threats.append(&mut file_result.file_homoglyphs);
                    all_pattern_threats.append(&mut file_result.file_patterns);
                    all_security_threats.append(&mut file_result.file_security_threats);
                    
                    file_analyses.push(file_result);
                }
                Err(e) => {
                    // Create error entry for failed file
                    file_analyses.push(FileAnalysisResult {
                        file_path: file_path.clone(),
                        relative_path: file_path.clone(),
                        file_type: self.get_file_extension(file_path),
                        file_size: 0,
                        line_count: 0,
                        character_count: 0,
                        encoding: "unknown".to_string(),
                        analysis_status: "error".to_string(),
                        error_message: Some(e.to_string()),
                        homoglyph_count: 0,
                        pattern_threat_count: 0,
                        security_threat_count: 0,
                        risk_score: 0.0,
                        risk_factors: vec!["Analysis failed".to_string()],
                        file_homoglyphs: Vec::new(),
                        file_patterns: Vec::new(),
                        file_security_threats: Vec::new(),
                    });
                }
            }
        }

        // Perform unicode analysis on combined content
        let unicode_analysis = if matches!(scan_depth, ScanDepth::Deep | ScanDepth::Forensic) {
            self.unicode_analyzer.analyze_text(&combined_content)
        } else {
            UnicodeAnalysis {
                invisible_chars: Vec::new(),
                bidirectional_overrides: Vec::new(),
                normalization_issues: Vec::new(),
                encoding_anomalies: Vec::new(),
                script_mixing: Vec::new(),
            }
        };

        // Generate risk assessment
        let file_paths_string: Vec<String> = file_paths;
        let risk_assessment = self.risk_assessor.assess_risks(
            &all_homoglyph_threats,
            &all_pattern_threats,
            &all_security_threats,
            &file_paths_string,
        );

        // Generate executive summary
        let executive_summary = self.generate_executive_summary(
            &all_homoglyph_threats,
            &all_pattern_threats,
            &all_security_threats,
            &risk_assessment,
            &file_analyses,
        );

        let analysis_duration = start_time.elapsed().as_millis() as u64;

        // Emit completion progress
        if let Some(ref callback) = progress_callback {
            callback(total_files, total_files, 100.0, "Analysis complete!", "Complete");
        }

        let analysis_metadata = AnalysisMetadata {
            analysis_id,
            timestamp: chrono::Utc::now().to_rfc3339(),
            total_files_scanned: file_analyses.len(),
            total_lines_analyzed: total_lines,
            total_characters_analyzed: total_characters,
            analysis_duration_ms: analysis_duration,
            analyzer_version: "2.0.0".to_string(),
            scan_depth,
        };

        Ok(ComprehensiveAnalysisResult {
            analysis_metadata,
            file_analyses,
            homoglyph_threats: all_homoglyph_threats,
            pattern_threats: all_pattern_threats,
            security_threats: all_security_threats,
            unicode_analysis,
            risk_assessment,
            executive_summary,
        })
    }

    fn analyze_single_file(
        &self,
        file_path: &str,
        scan_depth: &ScanDepth,
    ) -> Result<FileAnalysisResult, Box<dyn std::error::Error>> {
        let path = Path::new(file_path);
        let file_type = self.get_file_extension(file_path);
        
        // Read file content
        let content = std::fs::read_to_string(path)?;
        let file_size = content.len() as u64;
        let line_count = content.lines().count();
        let character_count = content.chars().count();

        let mut file_patterns = Vec::new();
        let mut file_security_threats = Vec::new();
        let mut risk_factors = Vec::new();

        // Always perform homoglyph detection (fast operation)
        let file_homoglyphs = self.homoglyph_detector.analyze_text(&content, &file_type);

        // Pattern analysis for Standard+ scans
        if matches!(scan_depth, ScanDepth::Standard | ScanDepth::Deep | ScanDepth::Forensic) {
            file_patterns = self.pattern_analyzer.analyze_content(&content, &file_type);
        }

        // Security scanning for Deep+ scans
        if matches!(scan_depth, ScanDepth::Deep | ScanDepth::Forensic) {
            file_security_threats = self.security_scanner.scan_content(&content, &file_type, file_path);
        }

        // Calculate file risk score
        let risk_score = self.calculate_file_risk_score(
            &file_homoglyphs,
            &file_patterns,
            &file_security_threats,
        );

        // Generate risk factors
        if !file_homoglyphs.is_empty() {
            risk_factors.push(format!("{} homoglyph threats detected", file_homoglyphs.len()));
        }
        if !file_patterns.is_empty() {
            risk_factors.push(format!("{} suspicious patterns found", file_patterns.len()));
        }
        if !file_security_threats.is_empty() {
            risk_factors.push(format!("{} security vulnerabilities identified", file_security_threats.len()));
        }

        Ok(FileAnalysisResult {
            file_path: file_path.to_string(),
            relative_path: file_path.to_string(), // TODO: Make relative to scan root
            file_type,
            file_size,
            line_count,
            character_count,
            encoding: "UTF-8".to_string(), // TODO: Detect actual encoding
            analysis_status: "success".to_string(),
            error_message: None,
            homoglyph_count: file_homoglyphs.len(),
            pattern_threat_count: file_patterns.len(),
            security_threat_count: file_security_threats.len(),
            risk_score,
            risk_factors,
            file_homoglyphs,
            file_patterns,
            file_security_threats,
        })
    }

    fn collect_files(&self, target_path: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        let mut files = Vec::new();
        let path = Path::new(target_path);

        if path.is_file() {
            files.push(target_path.to_string());
        } else if path.is_dir() {
            self.collect_files_recursive(path, &mut files)?;
        }

        Ok(files)
    }

    fn collect_files_recursive(
        &self,
        dir: &Path,
        files: &mut Vec<String>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        for entry in std::fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();

            // Skip hidden files and common non-source directories
            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                if name.starts_with('.') || 
                   name == "node_modules" || 
                   name == "target" || 
                   name == "dist" || 
                   name == "build" ||
                   name == ".git" {
                    continue;
                }
            }

            if path.is_dir() {
                self.collect_files_recursive(&path, files)?;
            } else if self.is_analyzable_file(&path) {
                files.push(path.to_string_lossy().to_string());
            }
        }

        Ok(())
    }

    fn is_analyzable_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            matches!(extension.to_lowercase().as_str(),
                "rs" | "js" | "ts" | "jsx" | "tsx" | "py" | "java" | "c" | "cpp" | "h" | "hpp" |
                "cs" | "php" | "rb" | "go" | "swift" | "kt" | "scala" | "clj" | "hs" |
                "html" | "htm" | "css" | "scss" | "sass" | "less" |
                "json" | "xml" | "yaml" | "yml" | "toml" | "ini" | "cfg" |
                "md" | "txt" | "sql" | "sh" | "bash" | "ps1" | "bat" | "cmd"
            )
        } else {
            false
        }
    }

    fn get_file_extension(&self, file_path: &str) -> String {
        Path::new(file_path)
            .extension()
            .and_then(|e| e.to_str())
            .unwrap_or("unknown")
            .to_lowercase()
    }

    fn calculate_file_risk_score(
        &self,
        homoglyphs: &[HomoglyphThreat],
        patterns: &[PatternThreat],
        security_threats: &[SecurityThreat],
    ) -> f32 {
        let mut score: f32 = 100.0;

        // Deduct for homoglyph threats
        for threat in homoglyphs {
            score -= match threat.risk_level {
                super::HomoglyphRiskLevel::Critical => 20.0,
                super::HomoglyphRiskLevel::High => 12.0,
                super::HomoglyphRiskLevel::Medium => 6.0,
                super::HomoglyphRiskLevel::Low => 2.0,
            };
        }

        // Deduct for pattern threats
        for threat in patterns {
            score -= match threat.risk_level {
                super::RiskLevel::Critical => 25.0,
                super::RiskLevel::High => 15.0,
                super::RiskLevel::Medium => 8.0,
                super::RiskLevel::Low => 3.0,
            };
        }

        // Deduct for security threats (highest impact)
        for threat in security_threats {
            score -= match threat.severity {
                super::ThreatSeverity::Critical => 40.0,
                super::ThreatSeverity::High => 25.0,
                super::ThreatSeverity::Medium => 12.0,
                super::ThreatSeverity::Low => 5.0,
            };
        }

        score.max(0.0f32)
    }

    fn generate_executive_summary(
        &self,
        homoglyphs: &[HomoglyphThreat],
        patterns: &[PatternThreat],
        security_threats: &[SecurityThreat],
        risk_assessment: &RiskAssessment,
        file_analyses: &[FileAnalysisResult],
    ) -> ExecutiveSummary {
        let critical_findings = risk_assessment.threat_breakdown.critical_issues;
        let high_risk_findings = risk_assessment.threat_breakdown.high_issues;
        let medium_risk_findings = risk_assessment.threat_breakdown.medium_issues;  // NEW
        let low_risk_findings = risk_assessment.threat_breakdown.low_issues;        // NEW
        let total_threats = homoglyphs.len() + patterns.len() + security_threats.len();
        
        // NEW: Calculate total issues detected and files with issues
        let total_issues_detected = critical_findings + high_risk_findings + medium_risk_findings + low_risk_findings;
        let files_with_issues = file_analyses.iter()
            .filter(|f| f.homoglyph_count > 0 || f.pattern_threat_count > 0 || f.security_threat_count > 0)
            .count();

        let overall_security_posture = if critical_findings > 0 {
            SecurityPosture::Critical
        } else if high_risk_findings > 5 {
            SecurityPosture::Poor
        } else if high_risk_findings > 0 || total_threats > 10 {
            SecurityPosture::Fair
        } else if total_threats > 0 {
            SecurityPosture::Good
        } else {
            SecurityPosture::Excellent
        };

        let mut most_vulnerable_files: Vec<_> = file_analyses
            .iter()
            .filter(|f| f.risk_score < 80.0)
            .map(|f| f.file_path.clone())
            .take(5)
            .collect();
        most_vulnerable_files.sort_by(|a, b| {
            let score_a = file_analyses.iter().find(|f| f.file_path == *a).map(|f| f.risk_score).unwrap_or(100.0);
            let score_b = file_analyses.iter().find(|f| f.file_path == *b).map(|f| f.risk_score).unwrap_or(100.0);
            score_a.partial_cmp(&score_b).unwrap_or(std::cmp::Ordering::Equal)
        });

        let recommended_actions = if critical_findings > 0 {
            vec![
                "IMMEDIATE ACTION REQUIRED: Critical security vulnerabilities detected".to_string(),
                "Patch or mitigate all critical security issues within 24 hours".to_string(),
                "Conduct emergency security review of affected systems".to_string(),
                "Implement monitoring for exploitation attempts".to_string(),
            ]
        } else if high_risk_findings > 0 {
            vec![
                "Address high-risk security issues within 1 week".to_string(),
                "Implement additional security controls".to_string(),
                "Review and update security practices".to_string(),
            ]
        } else {
            vec![
                "Continue regular security monitoring".to_string(),
                "Address remaining medium and low risk issues".to_string(),
            ]
        };

        let compliance_status = if critical_findings > 0 {
            "NON-COMPLIANT - Critical issues require immediate attention".to_string()
        } else if high_risk_findings > 0 {
            "PARTIALLY COMPLIANT - High risk issues identified".to_string()
        } else {
            "COMPLIANT - No critical security issues detected".to_string()
        };

        let business_impact = match overall_security_posture {
            SecurityPosture::Critical => "HIGH BUSINESS RISK - Potential for data breach, system compromise, or service disruption".to_string(),
            SecurityPosture::Poor => "MEDIUM BUSINESS RISK - Vulnerabilities could impact operations or reputation".to_string(),
            SecurityPosture::Fair => "LOW-MEDIUM BUSINESS RISK - Some security concerns that should be addressed".to_string(),
            SecurityPosture::Good => "LOW BUSINESS RISK - Minor security issues with minimal impact".to_string(),
            SecurityPosture::Excellent => "MINIMAL BUSINESS RISK - Strong security posture maintained".to_string(),
        };

        ExecutiveSummary {
            overall_security_posture,
            critical_findings,
            high_risk_findings,
            medium_risk_findings,      // NEW
            low_risk_findings,         // NEW
            total_threats,
            total_issues_detected,     // NEW
            files_with_issues,         // NEW
            most_vulnerable_files,
            recommended_actions,
            compliance_status,
            business_impact,
        }
    }
}
