#!/usr/bin/env powershell
# Fix BOM (Byte Order Mark) issues in JavaScript files

$files = @(
    "scripts\check-bad-characters.js",
    "scripts\check-tauri-version.js"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing BOM in: $file" -ForegroundColor Yellow
        
        # Read content as bytes to detect BOM
        $bytes = [System.IO.File]::ReadAllBytes($file)
        
        # Check for UTF-8 BOM (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "  Found UTF-8 BOM, removing..." -ForegroundColor Red
            
            # Remove BOM bytes
            $newBytes = $bytes[3..($bytes.Length - 1)]
            [System.IO.File]::WriteAllBytes($file, $newBytes)
            
            Write-Host "  BOM removed successfully!" -ForegroundColor Green
        } else {
            Write-Host "  No BOM found" -ForegroundColor Green
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "`nBOM fix complete!" -ForegroundColor Cyan
