# Advanced Security Analysis Bug Tracer
# By <PERSON> 2025
# Comprehensive debugging script that traces the JSON parsing bug across the entire codebase

Write-Host "🔍 Advanced Security Analysis Bug Tracer v1.0" -ForegroundColor Cyan
Write-Host "By <PERSON>ho<PERSON> - 2025" -ForegroundColor Green
Write-Host "Tracing the root cause of the '0 Total Threats' display bug..." -ForegroundColor Yellow
Write-Host ""

$ErrorActionPreference = "Continue"
$script:FoundIssues = @()
$script:Recommendations = @()

function Add-Issue {
    param($severity, $component, $description)
    $script:FoundIssues += @{
        Severity = $severity
        Component = $component  
        Description = $description
    }
}

function Add-Recommendation {
    param($action)
    $script:Recommendations += $action
}

function Test-FileExists {
    param($path, $description)
    if (Test-Path $path) {
        Write-Host "✅ $description exists: $path" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $description missing: $path" -ForegroundColor Red
        Add-Issue "HIGH" "File System" "Missing critical file: $path"
        return $false
    }
}

Write-Host "=== 🎯 STEP 1: CRITICAL FILE VERIFICATION ===" -ForegroundColor Magenta

# Check all files in the bug chain
$criticalFiles = @(
    @{Path="src\components\codebase_old.rs"; Desc="Frontend caller"},
    @{Path="src-tauri\src\main_module.rs"; Desc="Backend command handler"},
    @{Path="src-tauri\src\analysis\codebase_analyzer.rs"; Desc="Analysis engine"},
    @{Path="src\components\codebase\ui\results.rs"; Desc="Frontend JSON parser (BUG LOCATION)"},
    @{Path="src\components\codebase\types.rs"; Desc="Type definitions"}
)

foreach ($file in $criticalFiles) {
    Test-FileExists $file.Path $file.Desc
}

Write-Host ""
Write-Host "=== 🔍 STEP 2: ANALYZE FRONTEND JSON PARSING ===" -ForegroundColor Magenta

$resultsFile = "src\components\codebase\ui\results.rs"
if (Test-Path $resultsFile) {
    $content = Get-Content $resultsFile -Raw
    
    Write-Host "🔍 Checking JSON field access patterns..." -ForegroundColor Cyan
      # Check for incorrect field access patterns
    $incorrectPatterns = @(
        @{Pattern="json\.get\(`"total_files`""; Expected="analysis_metadata.total_files_scanned"},
        @{Pattern="json\.get\(`"files_analyzed`""; Expected="analysis_metadata.total_files_scanned"},
        @{Pattern="json\.get\(`"overall_risk_score`""; Expected="risk_assessment.overall_risk_score"},
        @{Pattern="json\.get\(`"homoglyph_threats`""; Expected="homoglyph_threats array"}
    )
    
    foreach ($pattern in $incorrectPatterns) {
        if ($content -match $pattern.Pattern) {
            Write-Host "❌ FOUND BUG: Incorrect JSON field access" -ForegroundColor Red
            Write-Host "   Pattern: $($pattern.Pattern)" -ForegroundColor Yellow
            Write-Host "   Should be: $($pattern.Expected)" -ForegroundColor Green
            Add-Issue "CRITICAL" "JSON Parsing" "Frontend uses wrong JSON field paths in $resultsFile"
        }
    }
    
    # Check if the fix was already applied
    if ($content -match "analysis_metadata" -and $content -match "risk_assessment") {
        Write-Host "✅ JSON parsing appears to be fixed (found correct field paths)" -ForegroundColor Green
    } else {
        Write-Host "❌ JSON parsing still uses old field paths" -ForegroundColor Red
        Add-Issue "CRITICAL" "JSON Parsing" "Frontend still uses flat JSON structure instead of nested ComprehensiveAnalysisResult"
        Add-Recommendation "Update $resultsFile to use nested JSON field access"
    }
} else {
    Add-Issue "HIGH" "File System" "Cannot analyze frontend JSON parsing - file missing"
}

Write-Host ""
Write-Host "=== 🏗️ STEP 3: VERIFY BACKEND JSON STRUCTURE ===" -ForegroundColor Magenta

$backendFile = "src-tauri\src\analysis\codebase_analyzer.rs"
if (Test-Path $backendFile) {
    $backendContent = Get-Content $backendFile -Raw
    
    Write-Host "🔍 Checking ComprehensiveAnalysisResult structure..." -ForegroundColor Cyan
    
    $expectedFields = @(
        "analysis_metadata",
        "file_analyses", 
        "homoglyph_threats",
        "pattern_threats",
        "security_threats",
        "risk_assessment",
        "executive_summary"
    )
      $missingFields = @()
    foreach ($field in $expectedFields) {
        if ($backendContent -match "pub ${field}:") {
            Write-Host "✅ Found expected field: $field" -ForegroundColor Green
        } else {
            Write-Host "❌ Missing expected field: $field" -ForegroundColor Red
            $missingFields += $field
        }
    }
    
    if ($missingFields.Count -eq 0) {
        Write-Host "✅ Backend JSON structure looks correct" -ForegroundColor Green
    } else {
        Add-Issue "HIGH" "Backend Structure" "Missing fields in ComprehensiveAnalysisResult: $($missingFields -join ', ')"
    }
} else {
    Add-Issue "HIGH" "File System" "Cannot verify backend structure - file missing"
}

Write-Host ""
Write-Host "=== 🔄 STEP 4: TRACE COMMAND INVOCATION ===" -ForegroundColor Magenta

$frontendFile = "src\components\codebase_old.rs"
if (Test-Path $frontendFile) {
    $frontendContent = Get-Content $frontendFile -Raw
    
    Write-Host "🔍 Checking which Tauri command is being called..." -ForegroundColor Cyan
    
    if ($frontendContent -match "analyze_codebase_advanced") {
        Write-Host "✅ Frontend calls correct command: analyze_codebase_advanced" -ForegroundColor Green
    } elseif ($frontendContent -match "analyze_codebase") {
        Write-Host "⚠️ Frontend calls old command: analyze_codebase (may work but different structure)" -ForegroundColor Yellow
        Add-Issue "MEDIUM" "Command Invocation" "Frontend may be using older command with different JSON structure"
    } else {
        Write-Host "❌ Cannot determine which Tauri command is being called" -ForegroundColor Red
        Add-Issue "HIGH" "Command Invocation" "Cannot trace which backend command frontend is calling"
    }
} else {
    Add-Issue "HIGH" "File System" "Cannot trace command invocation - frontend file missing"
}

Write-Host ""
Write-Host "=== 🧪 STEP 5: TEST WITH SAMPLE DATA ===" -ForegroundColor Magenta

# Look for recent analysis results to test parsing
$testFiles = @(
    "analysis_results.json",
    "analysis_results_utf8.json", 
    "test_analysis.json"
)

$foundTestFile = $null
foreach ($testFile in $testFiles) {
    if (Test-Path $testFile) {
        $foundTestFile = $testFile
        break
    }
}

if ($foundTestFile) {
    Write-Host "🔍 Testing JSON structure with: $foundTestFile" -ForegroundColor Cyan
    
    try {
        $testData = Get-Content $foundTestFile -Raw | ConvertFrom-Json
        
        # Check for expected structure
        if ($testData.analysis_metadata) {
            Write-Host "✅ Found analysis_metadata in test data" -ForegroundColor Green
            if ($testData.analysis_metadata.total_files_scanned) {
                Write-Host "✅ Found total_files_scanned: $($testData.analysis_metadata.total_files_scanned)" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ Missing analysis_metadata in test data" -ForegroundColor Red
        }
        
        if ($testData.risk_assessment) {
            Write-Host "✅ Found risk_assessment in test data" -ForegroundColor Green  
            if ($testData.risk_assessment.overall_risk_score) {
                Write-Host "✅ Found overall_risk_score: $($testData.risk_assessment.overall_risk_score)" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ Missing risk_assessment in test data" -ForegroundColor Red
        }
        
        if ($testData.executive_summary) {
            Write-Host "✅ Found executive_summary in test data" -ForegroundColor Green
            if ($testData.executive_summary.total_threats) {
                Write-Host "✅ Found total_threats: $($testData.executive_summary.total_threats)" -ForegroundColor Green
                if ($testData.executive_summary.total_threats -gt 0) {
                    Write-Host "🚨 CRITICAL: Test data shows $($testData.executive_summary.total_threats) threats but UI likely shows 0!" -ForegroundColor Red
                    Add-Issue "CRITICAL" "Data Mismatch" "Test data contains threats but UI displays zero - confirms parsing bug"
                }
            }
        }
        
        # Check threat arrays
        $threatTypes = @("homoglyph_threats", "pattern_threats", "security_threats")
        foreach ($threatType in $threatTypes) {
            if ($testData.$threatType) {
                $count = $testData.$threatType.Count
                Write-Host "✅ Found $threatType`: $count items" -ForegroundColor Green
            }
        }
        
    } catch {
        Write-Host "❌ Error parsing test JSON: $($_.Exception.Message)" -ForegroundColor Red
        Add-Issue "MEDIUM" "Test Data" "Cannot parse test JSON file for validation"
    }
} else {
    Write-Host "⚠️ No test JSON files found for validation" -ForegroundColor Yellow
    Add-Recommendation "Run an analysis to generate test data for debugging"
}

Write-Host ""
Write-Host "=== 📊 STEP 6: COMPILATION STATUS CHECK ===" -ForegroundColor Magenta

Write-Host "🔍 Checking if project compiles..." -ForegroundColor Cyan
try {
    $compileResult = & cargo check 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Project compiles successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Compilation errors found" -ForegroundColor Red
        Add-Issue "HIGH" "Compilation" "Project has compilation errors that prevent testing the fix"
        Add-Recommendation "Fix compilation errors before testing Advanced Security Analysis"
        
        # Show some compilation errors
        $errorLines = $compileResult | Select-Object -First 10
        foreach ($line in $errorLines) {
            Write-Host "   $line" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "⚠️ Could not run cargo check: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 📋 FINAL ANALYSIS REPORT ===" -ForegroundColor Magenta

Write-Host ""
Write-Host "🚨 ISSUES FOUND:" -ForegroundColor Red
if ($script:FoundIssues.Count -eq 0) {
    Write-Host "   ✅ No major issues detected!" -ForegroundColor Green
} else {
    foreach ($issue in $script:FoundIssues) {
        $color = switch ($issue.Severity) {
            "CRITICAL" { "Red" }
            "HIGH" { "Yellow" }
            "MEDIUM" { "Cyan" }
            default { "White" }
        }
        Write-Host "   [$($issue.Severity)] $($issue.Component): $($issue.Description)" -ForegroundColor $color
    }
}

Write-Host ""
Write-Host "💡 RECOMMENDATIONS:" -ForegroundColor Green
if ($script:Recommendations.Count -eq 0) {
    Write-Host "   ✅ No immediate actions needed!" -ForegroundColor Green
} else {
    foreach ($rec in $script:Recommendations) {
        Write-Host "   → $rec" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "=== 🎯 DEBUGGING SUMMARY ===" -ForegroundColor Magenta
Write-Host ""
Write-Host "🔍 What to check next:" -ForegroundColor Cyan
Write-Host "1. Open browser dev tools and run Advanced Security Analysis" -ForegroundColor White
Write-Host "2. Look at Network tab for 'analyze_codebase_advanced' response" -ForegroundColor White  
Write-Host "3. Verify the JSON structure matches ComprehensiveAnalysisResult" -ForegroundColor White
Write-Host "4. Check that UI parsing in results.rs uses correct field paths" -ForegroundColor White
Write-Host ""
Write-Host "🚨 Critical test: Run analysis on file with known bad characters" -ForegroundColor Red
Write-Host "   - UI should show NON-ZERO threat count" -ForegroundColor Red
Write-Host "   - Risk level should NOT be 'Minimal'" -ForegroundColor Red
Write-Host "   - Analysis ID should NOT be 'Unknown'" -ForegroundColor Red
Write-Host ""
Write-Host "📞 Emergency contacts:" -ForegroundColor Yellow
Write-Host "   - Check docs/CRITICAL_BUG_FIXES.md for the fix details" -ForegroundColor White
Write-Host "   - Run .\emergency_diagnostic.ps1 for system health" -ForegroundColor White
Write-Host "   - Review docs/CTO_HOLISTIC_BUG_ANALYSIS.md for complete analysis" -ForegroundColor White

Write-Host ""
Write-Host "Bug tracer complete! By John Shoy - 2025" -ForegroundColor Green
