#!/usr/bin/env powershell
# Doctor script - Comprehensive health check for Bad Character Scanner project
# Checks dependencies, build status, configuration, and common issues

param(
    [switch]$Fix,        # Attempt to fix issues automatically
    [switch]$Verbose,    # Show detailed output
    [switch]$Quick       # Skip time-consuming checks
)

Write-Host "`n🏥 BAD CHARACTER SCANNER - DOCTOR" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "Running comprehensive health check..." -ForegroundColor Gray

$script:issues = @()
$script:warnings = @()
$script:fixed = @()
$project_root = $PSScriptRoot | Split-Path -Parent

# Helper functions
function Test-Check {
    param(
        [string]$Name,
        [scriptblock]$Check,
        [scriptblock]$Fix = $null,
        [string]$Category = "General"
    )
    
    Write-Host "`n[$Category] $Name" -ForegroundColor Yellow -NoNewline
    
    try {
        $result = & $Check
        if ($result -eq $true) {
            Write-Host " ✓" -ForegroundColor Green
            return $true
        } else {
            Write-Host " ✗" -ForegroundColor Red
            $issue = @{
                Category = $Category
                Name = $Name
                Details = if ($result -is [string]) { $result } else { "Check failed" }
                CanFix = $null -ne $Fix
            }
            $script:issues += $issue
            
            if ($Fix -and $script:Fix) {
                Write-Host "  Attempting fix..." -ForegroundColor Yellow
                try {
                    & $Fix
                    Write-Host "  Fixed!" -ForegroundColor Green
                    $script:fixed += $Name
                    return $true
                } catch {
                    Write-Host "  Fix failed: $_" -ForegroundColor Red
                }
            }
            return $false
        }
    } catch {
        Write-Host " ⚠" -ForegroundColor Yellow
        $script:warnings += @{
            Category = $Category
            Name = $Name
            Details = $_.ToString()
        }
        return $false
    }
}

# 1. System Requirements
Write-Host "`n🔧 CHECKING SYSTEM REQUIREMENTS" -ForegroundColor Cyan

Test-Check "Rust Installation" {
    $rust = Get-Command cargo -ErrorAction SilentlyContinue
    if ($rust) {
        $version = cargo --version
        if ($Verbose) { Write-Host "`n    $version" -ForegroundColor Gray }
        return $true
    }
    return "Rust/Cargo not found"
} -Category "System" -Fix {
    Write-Host "Please install Rust from https://rustup.rs" -ForegroundColor Yellow
}

Test-Check "Node.js Installation" {
    $node = Get-Command node -ErrorAction SilentlyContinue
    if ($node) {
        $version = node --version
        if ($Verbose) { Write-Host "`n    Node $version" -ForegroundColor Gray }
        # Check minimum version (14.0.0)
        $versionNum = [version]($version -replace 'v', '')
        if ($versionNum -ge [version]"14.0.0") {
            return $true
        }
        return "Node.js version too old (need 14.0.0+)"
    }
    return "Node.js not found"
} -Category "System"

Test-Check "Tauri CLI" {
    $result = cargo tauri --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        if ($Verbose) { Write-Host "`n    $result" -ForegroundColor Gray }
        return $true
    }
    return "Tauri CLI not installed"
} -Category "System" -Fix {
    cargo install tauri-cli
}

Test-Check "WASM Target" {
    $targets = rustup target list --installed
    if ($targets -match "wasm32-unknown-unknown") {
        return $true
    }
    return "WASM target not installed"
} -Category "System" -Fix {
    rustup target add wasm32-unknown-unknown
}

Test-Check "Trunk Installation" {
    $trunk = Get-Command trunk -ErrorAction SilentlyContinue
    if ($trunk) {
        return $true
    }
    return "Trunk not installed"
} -Category "System" -Fix {
    cargo install trunk
}

# 2. Project Structure
Write-Host "`n📁 CHECKING PROJECT STRUCTURE" -ForegroundColor Cyan

Test-Check "Project Root Files" {
    $required = @("Cargo.toml", "index.html", "Trunk.toml", "package.json")
    $missing = $required | Where-Object { -not (Test-Path (Join-Path $project_root $_)) }
    if ($missing.Count -eq 0) {
        return $true
    }
    return "Missing files: $($missing -join ', ')"
} -Category "Structure"

Test-Check "Source Directories" {
    $required = @("src", "src-tauri", "scripts", "docs", "assets")
    $missing = $required | Where-Object { -not (Test-Path (Join-Path $project_root $_)) }
    if ($missing.Count -eq 0) {
        return $true
    }
    return "Missing directories: $($missing -join ', ')"
} -Category "Structure"

Test-Check "Backend Modules" {
    $backend_path = Join-Path $project_root "src-tauri\src"
    $required = @("main.rs", "lib.rs", "error.rs", "logging.rs")
    $missing = $required | Where-Object { -not (Test-Path (Join-Path $backend_path $_)) }
    if ($missing.Count -eq 0) {
        return $true
    }
    return "Missing backend files: $($missing -join ', ')"
} -Category "Structure"

# 3. Dependencies
Write-Host "`n📦 CHECKING DEPENDENCIES" -ForegroundColor Cyan

Test-Check "NPM Dependencies" {
    $node_modules = Join-Path $project_root "node_modules"
    if (Test-Path $node_modules) {
        return $true
    }
    return "node_modules not found"
} -Category "Dependencies" -Fix {
    Push-Location $project_root
    npm install
    Pop-Location
}

Test-Check "Cargo Dependencies" {
    Push-Location (Join-Path $project_root "src-tauri")
    $result = cargo check --quiet 2>&1
    $success = $LASTEXITCODE -eq 0
    Pop-Location
    if ($success) {
        return $true
    }
    return "Cargo dependency issues"
} -Category "Dependencies" -Fix {
    Push-Location (Join-Path $project_root "src-tauri")
    cargo update
    Pop-Location
}

# 4. Build Status
if (-not $Quick) {
    Write-Host "`n🔨 CHECKING BUILD STATUS" -ForegroundColor Cyan
    
    Test-Check "Frontend Build" {
        Push-Location $project_root
        $result = trunk build 2>&1 | Select-String -Pattern "error" -SimpleMatch
        Pop-Location
        if (-not $result) {
            return $true
        }
        return "Frontend build errors found"
    } -Category "Build"
    
    Test-Check "Backend Build" {
        Push-Location (Join-Path $project_root "src-tauri")
        $result = cargo build --quiet 2>&1
        $success = $LASTEXITCODE -eq 0
        Pop-Location
        if ($success) {
            return $true
        }
        return "Backend build failed"
    } -Category "Build"
}

# 5. Configuration
Write-Host "`n⚙️ CHECKING CONFIGURATION" -ForegroundColor Cyan

Test-Check "Tauri Configuration" {
    $config_path = Join-Path $project_root "src-tauri\tauri.conf.json"
    if (Test-Path $config_path) {
        try {
            $config = Get-Content $config_path -Raw | ConvertFrom-Json
            if ($config.package -and $config.tauri) {
                return $true
            }
            return "Invalid configuration structure"
        } catch {
            return "Invalid JSON in tauri.conf.json"
        }
    }
    return "tauri.conf.json not found"
} -Category "Config"

Test-Check "Package.json Validity" {
    $package_path = Join-Path $project_root "package.json"
    try {
        $package = Get-Content $package_path -Raw | ConvertFrom-Json
        if ($package.name -and $package.scripts) {
            return $true
        }
        return "Missing required fields"
    } catch {
        return "Invalid JSON"
    }
} -Category "Config"

# 6. Common Issues
Write-Host "`n🐛 CHECKING COMMON ISSUES" -ForegroundColor Cyan

Test-Check "Git Repository" {
    $git_dir = Join-Path $project_root ".git"
    if (Test-Path $git_dir) {
        return $true
    }
    return "Not a git repository"
} -Category "Git"

Test-Check "TypeScript Errors" {
    $ts_errors = Get-ChildItem $project_root -Filter "*.ts" -Recurse -ErrorAction SilentlyContinue |
        Select-String -Pattern "// @ts-ignore|@ts-nocheck" -SimpleMatch
    if ($ts_errors.Count -eq 0) {
        return $true
    }
    return "$($ts_errors.Count) TypeScript ignores found"
} -Category "Code Quality"

Test-Check "Logging System" {
    $log_module = Join-Path $project_root "src-tauri\src\logging.rs"
    $error_module = Join-Path $project_root "src-tauri\src\error.rs"
    if ((Test-Path $log_module) -and (Test-Path $error_module)) {
        return $true
    }
    return "Security logging modules missing"
} -Category "Security"

# 7. Bad Character Self-Check
Write-Host "`n🦸 CHECKING FOR BAD CHARACTERS" -ForegroundColor Cyan

Test-Check "Self-Scan" {
    $scanner = Join-Path $project_root "scripts\check-bad-characters.js"
    if (Test-Path $scanner) {
        if (Get-Command node -ErrorAction SilentlyContinue) {
            $result = node $scanner $project_root 2>&1 | Select-String -Pattern "Total issues found: (\d+)"
            if ($result -match "Total issues found: (\d+)") {
                $count = [int]$matches[1]
                if ($count -eq 0) {
                    return $true
                }
                $script:warnings += @{
                    Category = "Bad Characters"
                    Name = "Found Issues"
                    Details = "$count bad characters found (mostly in test files)"
                }
                return $true  # Not a failure, just a warning
            }
        }
    }
    return $true  # Don't fail if scanner not available
} -Category "Accessibility"

# 8. Performance Checks
Write-Host "`n⚡ CHECKING PERFORMANCE" -ForegroundColor Cyan

Test-Check "Bundle Size" {
    $dist_dir = Join-Path $project_root "dist"
    if (Test-Path $dist_dir) {
        $size = (Get-ChildItem $dist_dir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        if ($size -lt 50) {
            return $true
        }
        return "Bundle size too large: ${size}MB"
    }
    return $true  # Not built yet
} -Category "Performance"

# Summary Report
Write-Host "`n" + ("=" * 50) -ForegroundColor Cyan
Write-Host "📊 DIAGNOSTIC SUMMARY" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Cyan

$total_checks = $script:issues.Count + $script:warnings.Count + $script:fixed.Count
$healthy_checks = $total_checks - $script:issues.Count

Write-Host "`nHealth Score: " -NoNewline
$health_percent = if ($total_checks -gt 0) { [int](($healthy_checks / $total_checks) * 100) } else { 100 }
$health_color = if ($health_percent -ge 80) { "Green" } elseif ($health_percent -ge 60) { "Yellow" } else { "Red" }
Write-Host "$health_percent%" -ForegroundColor $health_color

if ($script:issues.Count -gt 0) {
    Write-Host "`n❌ ISSUES FOUND ($($script:issues.Count)):" -ForegroundColor Red
    $script:issues | Group-Object Category | ForEach-Object {
        Write-Host "`n  $($_.Name):" -ForegroundColor Yellow
        $_.Group | ForEach-Object {
            Write-Host "    - $($_.Name): $($_.Details)" -ForegroundColor Red
            if ($_.CanFix -and -not $Fix) {
                Write-Host "      (Run with -Fix to attempt repair)" -ForegroundColor Gray
            }
        }
    }
}

if ($script:warnings.Count -gt 0) {
    Write-Host "`n⚠️  WARNINGS ($($script:warnings.Count)):" -ForegroundColor Yellow
    $script:warnings | ForEach-Object {
        Write-Host "  - [$($_.Category)] $($_.Name): $($_.Details)" -ForegroundColor Yellow
    }
}

if ($script:fixed.Count -gt 0) {
    Write-Host "`n✅ FIXED ISSUES ($($script:fixed.Count)):" -ForegroundColor Green
    $script:fixed | ForEach-Object {
        Write-Host "  - $_" -ForegroundColor Green
    }
}

# Recommendations
Write-Host "`n💡 RECOMMENDATIONS:" -ForegroundColor Cyan

if ($script:issues.Count -eq 0) {
    Write-Host "  ✨ Your project is healthy! You can run:" -ForegroundColor Green
    Write-Host "     cargo tauri dev" -ForegroundColor White
    Write-Host "  to start development." -ForegroundColor Gray
} else {
    Write-Host "  1. Fix the issues listed above" -ForegroundColor Yellow
    if ($script:issues | Where-Object { $_.CanFix }) {
        Write-Host "  2. Run 'doctor.ps1 -Fix' to auto-fix some issues" -ForegroundColor Yellow
    }
    Write-Host "  3. Check documentation in /docs for help" -ForegroundColor Yellow
}

Write-Host "`n🏥 Doctor's Orders: Keep coding for accessibility!" -ForegroundColor Cyan

# Exit with appropriate code
exit $(if ($script:issues.Count -eq 0) { 0 } else { 1 })