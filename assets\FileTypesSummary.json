{"$schema": "./file-types-schema.json", "version": "2.4.0", "lastUpdated": "2025-05-07T09:09:50Z", "categories": {"System Files": [".dat", ".sam", ".dmp", ".evtx", ".etl", ".vhd", ".vhdx", ".bak"], "Virtualization Formats": [".vmdk", ".vhdx", ".qcow2", ".vmx", ".vbox", ".vmsn", ".vmss", ".vdi"], "Image Formats": [".jpeg", ".png", ".webp", ".svg", ".eps", ".gif", ".tiff", ".raw", ".ico", ".psd", ".xcf", ".kra"], "Configuration Files": [".ini", ".reg", ".pol", ".yml", ".yaml", ".json", ".env", ".tf", ".cfg", ".toml", ".conf"], "Log Files": [".evtx", ".audit", ".etl", ".log", ".csv", ".cef"], "Document Formats": [".abw", ".accdb", ".accde", ".accdr", ".accdt", ".acl", ".afpub", ".ai", ".ait", ".ans", ".apkg", ".apt", ".asd", ".asp", ".aw", ".aww", ".azw", ".azw3", ".azw4", ".b", ".bbs", ".bib", ".bna", ".bok", ".btd", ".bzip", ".bzip2", ".cbr", ".cbt", ".cbz", ".cdb", ".cdw", ".ceb", ".chm", ".cnm", ".cover", ".cpt", ".csv", ".cwk", ".cws", ".dat", ".db", ".dbf", ".dcr", ".ddf", ".djvu", ".dmp", ".doc", ".docb", ".docm", ".docx", ".docxml", ".dot", ".dotm", ".dotx", ".dox", ".dpt", ".dsm", ".dss", ".dst", ".dta", ".dvi", ".dwf", ".dwfx", ".dxf", ".dxr", ".ebd", ".edb", ".edk", ".edn", ".eml", ".emlx", ".emlxpart", ".epub", ".et", ".ett", ".fax", ".fb2", ".fbl", ".fdb", ".fdf", ".fdt", ".fdx", ".fds", ".fews", ".fft", ".fkey", ".flp", ".fodg", ".fodp", ".fods", ".fodt", ".fopd", ".fopt", ".fpd", ".fpt", ".frx", ".ftm", ".fwtemplate", ".gdoc", ".gform", ".giff", ".gnumeric", ".gp", ".gpx", ".gsheet", ".gslides", ".gz", ".hwp", ".hwt", ".ibooks", ".ics", ".idx", ".ifo", ".imd", ".imp", ".indd", ".indl", ".indt", ".info", ".inx", ".ipe", ".ipynb", ".jmd", ".jnt", ".joboptions", ".kdb", ".kdbx", ".kfn", ".key", ".keynote", ".kfo", ".kml", ".kmz", ".kon", ".kth", ".kwd", ".latex", ".ldb", ".lgp", ".lit", ".log", ".lrf", ".lst", ".ltx", ".lue", ".lwp", ".lyx", ".m3u", ".m3u8", ".man", ".maud", ".mcw", ".mdb", ".mdf", ".md", ".mdi", ".mdl", ".me", ".mht", ".mhtml", ".mid", ".mif", ".mmap", ".mobi", ".mpp", ".mpt", ".mpx", ".msg", ".mso", ".mwb", ".mw", ".mwd", ".nb", ".ndf", ".nds", ".nfo", ".nlogo", ".notebook", ".numbers", ".nwp", ".oab", ".odb", ".odc", ".odf", ".odg", ".odi", ".odm", ".odp", ".ods", ".odt", ".oft", ".one", ".onepkg", ".onetoc2", ".opd", ".opf", ".opml", ".oqy", ".ora", ".org", ".ott", ".ova", ".ovf", ".pab", ".pages", ".p7s", ".pbd", ".pcap", ".pcapng", ".pdax", ".pdb", ".pdf", ".pdfa", ".pdfx", ".pem", ".pfs", ".pfx", ".pka", ".pkg", ".pl", ".pml", ".pnd", ".pns", ".pobj", ".pot", ".potm", ".potx", ".ppa", ".ppam", ".ppd", ".ppm", ".pps", ".ppsm", ".ppsx", ".ppt", ".pptm", ".pptx", ".prc", ".prf", ".prn", ".pro", ".ps", ".ps1", ".psd", ".pst", ".ptx", ".pub", ".puz", ".pwi", ".pws", ".qbb", ".qbw", ".qdf", ".qf", ".qgs", ".qgz", ".qpf", ".qrp", ".qxp", ".qxd", ".ris", ".rft", ".rge", ".rgo", ".rmd", ".rpt", ".rsd", ".rsrc", ".rtf", ".rtfd", ".rtx", ".run", ".sam", ".sav", ".scd", ".sch", ".scm", ".scp", ".scriv", ".scrivx", ".sct", ".scw", ".sda", ".sdc", ".sdd", ".sdm", ".sdn", ".sdo", ".sdp", ".sds", ".sdw", ".sgm", ".sgml", ".shs", ".shw", ".sig", ".skd", ".skp", ".sla", ".slagz", ".slk", ".snb", ".snx", ".spd", ".spv", ".spw", ".sql", ".sqlite", ".sqlite3", ".srq", ".srt", ".ssa", ".stc", ".std", ".stw", ".sty", ".sub", ".sxc", ".sxd", ".sxg", ".sxi", ".sxm", ".sxw", ".t", ".tab", ".tar.gz", ".tax", ".tbk", ".tcb", ".tcr", ".tex", ".texi", ".texinfo", ".text", ".textClipping", ".tfr", ".tfrd", ".tgz", ".thm", ".thmx", ".tlb", ".tlp", ".tmp", ".tnc", ".tnf", ".tns", ".tpl", ".tpo", ".tpt", ".trc", ".tsv", ".ttc", ".tvl", ".twd", ".txt", ".uof", ".uop", ".uos", ".uot", ".upd", ".url", ".usertheme", ".vcf", ".vcs", ".vct", ".vdx", ".vmd", ".vmg", ".vmm", ".vmsd", ".vmx", ".vob", ".vsd", ".vsdm", ".vsdx", ".vss", ".vssm", ".vssx", ".vst", ".vstm", ".vstx", ".vsw", ".vtf", ".vtx", ".vxd", ".wbk", ".wbm", ".wbmp", ".wcf", ".wcm", ".wdb", ".wdp", ".webarchive", ".webbookmark", ".webdoc", ".wgz", ".wiz", ".wkb", ".wks", ".wk1", ".wk3", ".wk4", ".wll", ".wmf", ".wml", ".wmp", ".wms", ".wp", ".wpa", ".wpb", ".wpd", ".wpg", ".wpl", ".wps", ".wpt", ".wq1", ".wq2", ".wri", ".wsd", ".wsf", ".wsh", ".wsp", ".wtd", ".wwl", ".xar", ".xbk", ".xbrl", ".xd", ".xdd", ".xdf", ".xdp", ".xer", ".xfdl", ".xfd", ".xht", ".xhtml", ".xl", ".xla", ".xlam", ".xlb", ".xlc", ".xld", ".xlk", ".xll", ".xlm", ".xlr", ".xls", ".xlsb", ".xlsm", ".xlsx", ".xlt", ".xltm", ".xltx", ".xlv", ".xlw", ".xml", ".xps", ".xsd", ".xsl", ".xslt", ".xsn", ".xtp", ".xul", ".xy", ".xy3", ".xyp", ".xyw", ".zabw", ".zfo", ".zip", ".zot"], "Archive Formats": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz", ".lz", ".z"], "Executable Formats": [".exe", ".dll", ".msi", ".apk", ".deb", ".rpm", ".app", ".jar", ".bat", ".sh", ".py", ".js"], "Database Formats": [".sql", ".mdb", ".accdb", ".sqlite", ".dbf", ".db", ".frm", ".ibd", ".myd", ".myi"], "Network Formats": [".pcap", ".har", ".netflow", ".cap", ".saz"], "Audio Formats": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".midi", ".wma", ".m4a", ".opus"], "Video Formats": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"], "Email Formats": [".eml", ".pst", ".mbox", ".msg", ".ost", ".nsf", ".olm"], "3D Models": [".stl", ".obj", ".fbx", ".blend", ".3ds", ".dae", ".ply", ".x3d"], "Scientific Data": [".fits", ".silo", ".spc", ".root", ".hdf5", ".cdf", ".nc"], "GIS Formats": [".shp", ".kml", ".geo<PERSON><PERSON>", ".gpx", ".tiff", ".dem", ".ov2"], "Font Formats": [".ttf", ".otf", ".woff", ".woff2", ".eot", ".pfb", ".sfd"], "CAD Formats": [".dwg", ".dxf", ".skp", ".step", ".iges", ".sat", ".prt"], "Virtual Machines": [".ova", ".ovf", ".vmdk", ".nvram", ".vmem", ".vsv", ".vud"], "Game Formats": [".osz", ".osk", ".mcpack", ".unitypackage", ".blend", ".umap", ".uasset"], "E-Book Formats": [".epub", ".mobi", ".azw3", ".ibooks", ".cbz", ".cbr", ".djvu"], "BIOS/UEFI Formats": [".rom", ".bin", ".cap", ".fd", ".efi"], "Forensic Formats": [".dd", ".e01", ".aff", ".img", ".vhd"], "Container Formats": [".iso", ".dmg", ".vcd", ".nrg", ".bin"], "Project Files": [".sln", ".c<PERSON><PERSON>j", ".vcxproj", ".xcodeproj", ".pbxproj"], "Programming Source Code": {"C Family": [".c", ".h", ".cpp", ".hpp", ".cc", ".cxx", ".ino"], "Java/JVM": [".java", ".kt", ".scala", ".clj", ".cljs"], "Python": [".py", ".pyw", ".pyi"], "JavaScript/TypeScript": [".js", ".ts", ".jsx", ".tsx", ".d.ts"], "Other": [".cs", ".csx", ".vb", ".fs", ".fsi", ".fsx", ".php", ".phtml", ".phps", ".inc", ".swift", ".m", ".mm", ".go", ".rs", ".rlib", ".sh", ".bash", ".zsh", ".ksh", ".csh", ".tcsh", ".pl", ".pm", ".t", ".pod", ".lua", ".rb", ".erb", ".rake", ".gemspec", ".ru", ".hs", ".lhs", ".dart", ".elm", ".ex", ".exs", ".sql", ".psql", ".pgsql", ".mysql", ".sqlite", ".dump", ".json", ".jsonc", ".yaml", ".yml", ".xml", ".toml", ".ini", ".cfg", ".conf", ".vue", ".svelte", ".twig", ".hbs", ".handlebars", ".ejs", ".njk", ".coffee", ".re", ".res", ".resi", ".asm", ".s", ".S", ".nasm", ".f", ".for", ".f90", ".f95", ".f03", ".f08", ".adb", ".ads", ".tcl", ".itcl", ".itk", ".ps1", ".psm1", ".psd1", ".wat", ".sass", ".scss", ".less", ".styl", ".nix", ".sol", ".vhdl", ".vhd", ".vh", ".sv", ".svh", ".cr", ".nim", ".jl", ".ml", ".mli", ".qml", ".r", ".hx", ".4th", ".a", ".actionscript", ".ada", ".adb", ".ado", ".adoc", ".ads", ".ahk", ".al", ".als", ".am", ".aml", ".apl", ".applescript", ".as", ".ascx", ".asd", ".asax", ".ashx", ".asi", ".asm", ".asp", ".aspx", ".as", ".ass", ".au3", ".avsc", ".awk", ".axd", ".b", ".babelrc", ".bas", ".bash", ".bash_history", ".bash_logout", ".bash_profile", ".bashrc", ".bat", ".bats", ".bazel", ".bdy", ".bib", ".bicep", ".blade.php", ".bmx", ".bones", ".boot", ".brs", ".bs", ".build", ".bzl", ".c", ".C", ".c++", ".cabal", ".cake", ".capnp", ".cats", ".cb", ".cbl", ".cc", ".cdf", ".cds", ".cfm", ".cfml", ".cfc", ".cfg", ".cgi", ".cg", ".ch", ".chai", ".checkbox", ".chpl", ".chs", ".cjs", ".cl", ".class", ".cljc", ".cljs", ".clj", ".cljx", ".clp", ".cls", ".cmake", ".cmake.in", ".cmd", ".cmp", ".cnf", ".cob", ".cobol", ".code-workspace", ".coffee", ".<PERSON><PERSON><PERSON><PERSON>", ".com", ".command", ".comp", ".component", ".conf", ".config", ".coq", ".cp", ".cpp", ".CPP", ".cpt", ".cpy", ".cr", ".craft", ".cs", ".csh", ".cshtml", ".cson", ".c<PERSON><PERSON>j", ".css", ".csv", ".csx", ".ctl", ".cu", ".cue", ".cuh", ".cur", ".cxx", ".cy", ".cypher", ".d", ".d.ts", ".dart", ".data", ".db", ".dbml", ".dbs", ".ddl", ".deca", ".decl", ".def", ".defs", ".deps.json", ".desktop", ".desktop.in", ".dfm", ".dfy", ".di", ".diff", ".dita", ".ditamap", ".djt", ".dj", ".dll", ".dlm", ".dmap", ".dmd", ".dms", ".do", ".doc", ".docker<PERSON>le", ".dockerignore", ".dox", ".doxyfile", ".dpk", ".dpr", ".druby", ".ds", ".dsc", ".dsd", ".dsf", ".dsk", ".dsp", ".dsq", ".dtd", ".dtx", ".dump", ".d<PERSON>n", ".e", ".ebuild", ".ec", ".ecl", ".editorconfig", ".edn", ".eex", ".egg", ".ejs", ".el", ".elm", ".em", ".emacs", ".eml", ".ent", ".env", ".epp", ".erb", ".erl", ".es", ".es6", ".esc", ".ex", ".exs", ".exe", ".exp", ".eye", ".f", ".f03", ".f08", ".f77", ".f90", ".f95", ".factor", ".fan", ".fas", ".feature", ".fdoc", ".fetch", ".ff", ".ffi", ".fig", ".fish", ".filters", ".fnc", ".fnt", ".for", ".forth", ".fos", ".fountain", ".fp", ".fpp", ".frag", ".fr", ".frg", ".frm", ".frt", ".frx", ".fs", ".fsh", ".fsi", ".fsproj", ".fst", ".fsx", ".fth", ".ftl", ".fun", ".fx", ".fxh", ".g", ".g4", ".gaml", ".gawk", ".gbs", ".gc", ".gcode", ".gd", ".gdb", ".gdbinit", ".gdl", ".gemspec", ".geom", ".geo<PERSON><PERSON>", ".g<PERSON>kin", ".gi", ".gitattributes", ".gitconfig", ".giti<PERSON>re", ".gitkeep", ".git<PERSON><PERSON><PERSON>", ".glade", ".gld", ".glf", ".gls", ".glsl", ".glslv", ".gltf", ".gml", ".gmx", ".gn", ".gni", ".gnu", ".gnuplot", ".go", ".god", ".golo", ".gp", ".gpb", ".gph", ".gpi", ".gpl", ".gpr", ".gradle", ".gradle.kts", ".graphql", ".graphqls", ".groovy", ".gr", ".grc", ".grf", ".grm", ".grn", ".grt", ".gs", ".gsd", ".gsh", ".gsp", ".gst", ".gsx", ".gtkbuilder", ".gv", ".gvy", ".gy", ".gyp", ".gypi", ".h", ".H", ".h++", ".haml", ".handlebars", ".hats", ".hb", ".hbs", ".hcl", ".hh", ".hhc", ".hhh", ".hlsl", ".hlslh", ".hlsli", ".ho<PERSON>", ".hp", ".hpp", ".HPP", ".hql", ".hrl", ".hs", ".hsc", ".hsh", ".hsl", ".hs-boot", ".hta", ".htaccess", ".htd", ".htm", ".html", ".html.erb", ".htpasswd", ".htpl", ".http", ".hx", ".hxml", ".hxx", ".hydra", ".hy", ".i", ".iced", ".icl", ".icon", ".idc", ".idl", ".idr", ".idx", ".iec", ".iex", ".if", ".ifc", ".ig", ".ijs", ".ik", ".ily", ".iml", ".inc", ".inf", ".info", ".ini", ".ink", ".inl", ".ino", ".inp", ".ins", ".int", ".io", ".ipf", ".ipp", ".ipynb", ".ips", ".ipr", ".ipsw", ".ir", ".irc", ".irep", ".is", ".isc", ".ism", ".it", ".itcl", ".itk", ".itp", ".j", ".j2", ".jac", ".jade", ".jag", ".jake", ".jam", ".jape", ".jar", ".java", ".jav", ".jbuilder", ".jc", ".jcl", ".jcm", ".jcs", ".jcw", ".jd", ".jdc", ".jdf", ".jdi", ".jdl", ".jdp", ".jelly", ".jflex", ".jinja", ".jinja2", ".jl", ".jks", ".jnlp", ".jolie", ".jq", ".js", ".jsb", ".jsc", ".jsd", ".jsf", ".jsfl", ".jsfx", ".jsh", ".jsm", ".json", ".json5", ".jsonc", ".j<PERSON>l", ".j<PERSON><PERSON>", ".jsp", ".jspf", ".jspx", ".jsx", ".js", ".jst", ".jsxinc", ".k", ".k2", ".ka", ".kcl", ".kd", ".key", ".kicad_pcb", ".kid", ".kif", ".kit", ".kix", ".kjs", ".kk", ".kl", ".kml", ".kmt", ".ko", ".kodu", ".kojo", ".krl", ".ks", ".ksc", ".ksh", ".ksy", ".kt", ".ktm", ".kts", ".kv", ".l", ".lagda", ".las", ".lasso", ".lassoapp", ".latte", ".launch", ".lb", ".lbi", ".lbs", ".ld", ".ldif", ".lds", ".leaf", ".lean", ".led", ".less", ".lex", ".lfe", ".lg", ".lgt", ".lhs", ".lid", ".lidr", ".ligo", ".linc", ".link", ".liquid", ".lisp", ".list", ".litcoffee", ".livemd", ".lk", ".ll", ".lmi", ".lmo", ".lng", ".lnp", ".loc", ".log", ".lol", ".lookml", ".lpr", ".lproj", ".lrf", ".lrs", ".lrv", ".ls", ".lsl", ".lslp", ".lsp", ".lst", ".ltx", ".lua", ".luadoc", ".lvclass", ".lvlib", ".lvproj", ".lvtest", ".ly", ".m", ".m2", ".m3", ".m4", ".ma", ".mac", ".mab", ".mak", ".make", ".makefile", ".mako", ".man", ".map", ".markdown", ".mask", ".master", ".mat", ".mata", ".matah", ".mawk", ".max", ".maxhelp", ".maxpat", ".maxproj", ".mb", ".mc", ".mca", ".mcd", ".mcf", ".mcfunction", ".mcmeta", ".mcr", ".md", ".mdc", ".mdd", ".mdpolicy", ".me", ".metal", ".mex", ".mf", ".mfa", ".mfl", ".mg", ".mia", ".mib", ".mid", ".midi", ".mig", ".milk", ".min", ".minid", ".mint", ".mir", ".mjml", ".mjs", ".mk", ".mkb", ".mkd", ".mkdn", ".mkfile", ".mki", ".mkl", ".ml", ".mli", ".mligo", ".mll", ".mlmodel", ".mlo", ".mlp", ".mlt", ".mly", ".mm", ".mms", ".mmk", ".mnd", ".mne", ".mng", ".mo", ".moc", ".mod", ".model", ".module", ".mon", ".monkey", ".moo", ".mos", ".motoko", ".mp", ".mpc", ".mpd", ".mpf", ".mpg", ".mpl", ".mps", ".mpx", ".mq4", ".mq5", ".mqh", ".mrc", ".mr", ".ms", ".msd", ".msg", ".mspec", ".mss", ".mst", ".msu", ".msvc", ".mt", ".mtl", ".mtml", ".mts", ".mtx", ".mu", ".mud", ".mustache", ".mv", ".mva", ".mvi", ".mxml", ".my", ".mypy", ".mysql", ".n", ".nadesiko", ".nako", ".nanorc", ".nasm", ".nb", ".nbp", ".nc", ".ncl", ".ncs", ".nd", ".ndf", ".nd<PERSON><PERSON>", ".ne", ".nemerle", ".neon", ".nes", ".net", ".neta", ".netlinx", ".netlogo", ".new", ".nextflow", ".nf", ".nginx", ".ng", ".ngp", ".ni", ".nim", ".nim.cfg", ".nimble", ".nims", ".nimsp", ".<PERSON>", ".njk", ".njs", ".nix", ".nka", ".nkb", ".nkey", ".nl", ".nlogo", ".nmake", ".nml", ".nno", ".no", ".nomad", ".noon", ".nq", ".nr", ".nroff", ".nse", ".nsh", ".nsi", ".nsis", ".nsp", ".nsr", ".nt", ".ntl", ".nut", ".nvmrc", ".nxc", ".nxd", ".nxg", ".nxh", ".nxj", ".nxm", ".nxp", ".nxs", ".nxt", ".nxy", ".o", ".obj", ".objf", ".objm", ".objx", ".occ", ".ocl", ".oct", ".octave", ".odin", ".odl", ".om", ".oml", ".ooc", ".opa", ".opal", ".opencl", ".openedge", ".option", ".opts", ".orc", ".ore", ".org", ".os", ".os2", ".osc", ".otd", ".otl", ".out", ".owl", ".ox", ".oxh", ".oxo", ".oxygene", ".oz", ".p", ".p4", ".p6", ".p6l", ".p6m", ".pac", ".pan", ".pap", ".parrot", ".pas", ".pascal", ".patch", ".pat", ".pax", ".pb", ".pbi", ".pbl", ".pbp", ".pbs", ".pbt", ".pbxproj", ".pc", ".pcb", ".pcd", ".pce", ".pch", ".pcl", ".pcm", ".pcs", ".pcss", ".pd", ".pddl", ".pde", ".pd_lua", ".pdl", ".pdm", ".pdp", ".pdr", ".pds", ".peg", ".pegjs", ".pem", ".perf", ".perl", ".pf", ".pfa", ".pfb", ".pfc", ".pfd", ".pfe", ".pfg", ".pfi", ".pfl", ".pfr", ".pfs", ".pft", ".pg", ".pgc", ".pgm", ".pgs", ".pgsql", ".ph", ".phar", ".php", ".php_cs", ".php_cs.dist", ".php3", ".php4", ".php5", ".php7", ".phpt", ".phps", ".phtml", ".pick", ".pickle", ".pike", ".pika", ".pir", ".pit", ".piv", ".pjs", ".pkb", ".pkc", ".pkg", ".pkh", ".pki", ".pkk", ".pkl", ".pkm", ".pkp", ".pkr", ".pks", ".pkt", ".pl", ".PL", ".pl1", ".pla", ".plan", ".plantuml", ".play", ".playlist", ".plb", ".plc", ".pledge", ".plg", ".plh", ".pli", ".plm", ".plot", ".pls", ".plsql", ".plt", ".plx", ".ply", ".plz", ".pm", ".pm6", ".pmc", ".pmd", ".pme", ".pmk", ".pml", ".pmod", ".pmp", ".pmw", ".pnb", ".pnc", ".pnd", ".pne", ".pnf", ".png", ".pni", ".pnk", ".pnl", ".pnm", ".pno", ".pnp", ".pnq", ".pnr", ".pns", ".pnt", ".pnu", ".pnv", ".pnw", ".pnx", ".pny", ".pnz", ".po", ".pod", ".pod6", ".pogo", ".pol", ".pony", ".pom", ".pom.xml", ".por", ".postcss", ".pov", ".pp", ".ppc", ".ppd", ".ppg", ".pph", ".ppi", ".ppj", ".ppk", ".ppl", ".ppm", ".ppo", ".ppp", ".pps", ".ppt", ".ppu", ".ppv", ".ppw", ".ppx", ".ppy", ".ppz", ".prc", ".pre", ".prf", ".prg", ".pri", ".prim", ".print", ".priv", ".prm", ".pro", ".proc", ".profile", ".proj", ".project", ".prom", ".properties", ".proto", ".protobuf", ".prt", ".prw", ".ps", ".ps1", ".ps1xml", ".ps2", ".ps3", ".psa", ".psb", ".psc", ".psd", ".psd1", ".psf", ".psg", ".psh", ".psi", ".psj", ".psk", ".psl", ".psm", ".psm1", ".psp", ".psq", ".psr", ".pss", ".pst", ".psw", ".psx", ".psy", ".pt", ".ptl", ".ptls", ".ptx", ".pub", ".pug", ".purs", ".puz", ".pvs", ".pwn", ".pws", ".px", ".pxd", ".pxi", ".pxm", ".pxr", ".py", ".py3", ".pya", ".pyc", ".pyd", ".pyde", ".pyi", ".pyj", ".pyl", ".pyl<PERSON><PERSON>", ".pym", ".pyo", ".pyp", ".pyp<PERSON>j", ".pyr", ".pyt", ".pyw", ".pyx", ".q", ".qasm", ".qb", ".qbs", ".qf", ".qic", ".ql", ".qll", ".qlo", ".qls", ".qlt", ".qlx", ".qml", ".qmlproject", ".qmltypes", ".qpb", ".qpl", ".qpr", ".qpt", ".qs", ".qsf", ".qsp", ".qt", ".que", ".qy", ".r", ".R", ".r2", ".r3", ".ra", ".rabl", ".rake", ".rakefile", ".raml", ".ras", ".rat", ".raw", ".ray", ".razor", ".rb", ".rbbas", ".rbfrm", ".rbi", ".rbm", ".rbp", ".rbs", ".rbt", ".rbvcp", ".rbw", ".rbx", ".rc", ".rc2", ".rcp", ".rd", ".rdb", ".rdf", ".rdl", ".rdoc", ".rdp", ".rds", ".re", ".reac", ".reb", ".rebol", ".red", ".reds", ".reg", ".rego", ".rel", ".rem", ".rep", ".req", ".res", ".resx", ".rest", ".rexx", ".rf", ".rft", ".rg", ".rhtml", ".rjs", ".rkt", ".rktl", ".rl", ".rlib", ".rli", ".rm", ".rmd", ".rml", ".rms", ".rnh", ".rno", ".rob", ".robot", ".robotframework", ".rockspec", ".ron", ".roff", ".ros", ".rp", ".rpc", ".rpd", ".rpg", ".rpgle", ".rpm", ".rpp", ".rprofile", ".rproj", ".rpy", ".rq", ".rql", ".rrd", ".rsc", ".rsd", ".rsh", ".rspec", ".rst", ".rsw", ".rt", ".rtf", ".rtfd", ".rtex", ".rtx", ".ru", ".rub", ".ruby", ".rules", ".run", ".rvb", ".rws", ".rxml", ".s", ".S", ".s2", ".s3m", ".sa", ".sac", ".sage", ".sagews", ".saif", ".sal", ".san", ".sas", ".sash", ".sass", ".sas7bdat", ".saty", ".satysfi", ".sb", ".sb2", ".sb3", ".sbt", ".sc", ".sca", ".scad", ".scala", ".scaml", ".scar", ".sce", ".scf", ".sch", ".sci", ".scm", ".scpt", ".scptd", ".scr", ".script", ".scs", ".scss", ".scxml", ".sd", ".sda", ".sdb", ".sdc", ".sdd", ".sdef", ".sdi", ".sdl", ".sdm", ".sdoc", ".sds", ".sdt", ".sdv", ".sdw", ".sdx", ".sdy", ".sed", ".seed", ".sej", ".selenium", ".sem", ".sensu.json", ".ser", ".service", ".set", ".settings", ".sexp", ".sf", ".sfd", ".sfk", ".sfl", ".sfs", ".sft", ".sfv", ".sfw", ".sfx", ".sg", ".sgi", ".sgm", ".sgml", ".sh", ".sh-session", ".shader", ".shb", ".shd", ".shex", ".shl", ".shn", ".shp", ".shr", ".sht", ".shtml", ".si", ".sid", ".sig", ".sim", ".siv", ".sj", ".sjs", ".sk", ".skb", ".skd", ".skel", ".skin", ".skk", ".skl", ".skm", ".sko", ".skp", ".skr", ".sks", ".skt", ".sku", ".skv", ".skw", ".skx", ".sky", ".sl", ".slag", ".sld", ".slim", ".sln", ".sls", ".slt", ".sm", ".sma", ".smali", ".smc", ".smd", ".smf", ".smil", ".sml", ".smk", ".smm", ".smp", ".smps", ".sms", ".smt", ".smt2", ".smv", ".sn", ".snap", ".snip", ".snk", ".snm", ".sno", ".snp", ".snup", ".so", ".sobj", ".sol", ".sop", ".soup", ".sp", ".spa", ".space", ".sparql", ".spec", ".spg", ".sph", ".spiff", ".spin", ".spl", ".splus", ".spn", ".spp", ".spr", ".sps", ".spthy", ".spt", ".spx", ".spy", ".sql", ".sqlite", ".sqlite2", ".sqlite3", ".sqm", ".sqr", ".sqs", ".sqt", ".squ", ".squery", ".squirrel", ".sr", ".src", ".srdf", ".srf", ".srli", ".srs", ".srt", ".ss", ".ssa", ".ssc", ".ssd", ".ssjs", ".ssk", ".ssl", ".ssm", ".sss", ".sst", ".ssv", ".st", ".stan", ".star", ".stTheme", ".sty", ".styl", ".stylus", ".sub", ".sublime-build", ".sublime-commands", ".sublime-completions", ".sublime-keymap", ".sublime-macro", ".sublime-menu", ".sublime-mousemap", ".sublime-project", ".sublime-settings", ".sublime-snippet", ".sublime-syntax", ".sublime-text", ".sublime-theme", ".sublime-workspace", ".sudo", ".suite", ".sum", ".sup", ".sur", ".sv", ".svc", ".svd", ".svg", ".svh", ".svr", ".svs", ".svelte", ".sw", ".swc", ".swd", ".swf", ".swg", ".swi", ".swift", ".swiftpm", ".swm", ".swo", ".swp", ".sws", ".swt", ".sxc", ".sxd", ".sxg", ".sxi", ".sxm", ".sxw", ".sy", ".syl", ".sym", ".sys", ".syx", ".t", ".t2t", ".t2x", ".tab", ".tac", ".tag", ".tal", ".tap", ".tar", ".task", ".tasp", ".taz", ".tb", ".tbc", ".tbd", ".tbf", ".tbi", ".tbk", ".tbl", ".tbm", ".tbp", ".tbs", ".tbt", ".tbv", ".tbw", ".tbz", ".tbz2", ".tc", ".tcc", ".tcf", ".tcl", ".tcl.en", ".tcsh", ".tcs", ".tct", ".td", ".tdf", ".tdl", ".tds", ".te", ".tea", ".tef", ".tei", ".tel", ".temp", ".template", ".ter", ".term", ".tesc", ".tese", ".test", ".tex", ".texi", ".texinfo", ".text", ".textile", ".tf", ".tfdoc", ".tfi", ".tfm", ".tfs", ".tfstate", ".tfvars", ".tfw", ".tfx", ".tg", ".tga", ".tgz", ".th", ".them", ".theme", ".themes", ".things", ".thor", ".thrift", ".thtml", ".thy", ".ti", ".tif", ".tiff", ".tikz", ".tim", ".tip", ".tit", ".tix", ".tk", ".tla", ".tlc", ".tld", ".tle", ".tli", ".tlp", ".tm", ".tm_properties", ".tm<PERSON><PERSON>mand", ".tmLanguage", ".tmPreferences", ".tmSnippet", ".tmTheme", ".tml", ".tmlanguage", ".tmp", ".tmpl", ".tmproj", ".tms", ".tns", ".toc", ".todo", ".toml", ".tool", ".top", ".tpl", ".tpp", ".tpr", ".tps", ".tpt", ".tr", ".trace", ".tres", ".trg", ".tri", ".trig", ".trm", ".trn", ".troff", ".trs", ".ts", ".tscn", ".tsa", ".tsc", ".tse", ".tsi", ".tsk", ".tsm", ".tsq", ".tsql", ".tss", ".tst", ".tsv", ".tsx", ".tt", ".tt2", ".ttc", ".ttf", ".ttkgp", ".ttm", ".ttml", ".tts", ".ttl", ".tu", ".twig", ".tx", ".txi", ".txl", ".txt", ".ty", ".tyaml", ".type", ".typ", ".tys", ".tz", ".uc", ".ud", ".udeb", ".udf", ".udt", ".ui", ".uil", ".uistate", ".ul", ".uml", ".un", ".uno", ".unx", ".upr", ".ups", ".upstart", ".url", ".user", ".usr", ".usx", ".ut", ".utl", ".uts", ".utz", ".uw", ".ux", ".v", ".v3d", ".va", ".vagrant<PERSON>le", ".val", ".vala", ".valm", ".vapi", ".var", ".vas", ".vb", ".vba", ".vbe", ".vbg", ".vbp", ".vbs", ".vbscript", ".vbproj", ".vbhtml", ".vbx", ".vc", ".vca", ".vcd", ".vcf", ".vch", ".vcl", ".vcls", ".vcm", ".vcp", ".vcproj", ".vcs", ".vct", ".vcw", ".vcx", ".vcxproj", ".vcproj.filters", ".vd", ".vda", ".vdb", ".vdc", ".vdd", ".vdi", ".vdk", ".vdm", ".vdp", ".vds", ".vdt", ".vdw", ".vdx", ".ve", ".veo", ".ver", ".vert", ".vfb", ".vfd", ".vfp", ".vfu", ".vfw", ".vg", ".vgc", ".vgr", ".vgs", ".vgx", ".vh", ".vhd", ".vhdl", ".vhi", ".vho", ".vhp", ".vhs", ".vht", ".vi", ".vic", ".view", ".vim", ".viminfo", ".vimrc", ".vmg", ".vml", ".vms", ".volt", ".vot", ".vox", ".vp", ".vpc", ".vpg", ".vpi", ".vpl", ".vpm", ".vpr", ".vps", ".vpt", ".vpw", ".vpx", ".vq", ".vr", ".vrb", ".vrc", ".vrm", ".vrml", ".vrp", ".vrs", ".vrt", ".vrw", ".vs", ".vsc", ".vsd", ".vsh", ".vsi", ".vsl", ".vsm", ".vsp", ".vsprops", ".vspscc", ".vss", ".vssscc", ".vst", ".vstemplate", ".vsto", ".vstw", ".vsu", ".vsz", ".vtml", ".vtp", ".vts", ".vue", ".vv", ".vvv", ".vw", ".vwm", ".vxml", ".vy", ".w", ".w3c", ".wacc", ".wad", ".wall", ".was", ".wasm", ".wast", ".wat", ".watchr", ".watte", ".wb", ".wbs", ".wbxml", ".wd", ".wdgt", ".wdi", ".wdl", ".wdp", ".wdw", ".web", ".webapp", ".webc", ".webidl", ".webmanifest", ".webloc", ".webpack.js", ".wep", ".wex", ".wf", ".wfm", ".wfs", ".wgsl", ".whl", ".wick", ".wift", ".wiki", ".wikitext", ".wiko", ".wim", ".wiml", ".win", ".wip", ".wisp", ".wix", ".wixlib", ".wixobj", ".wixout", ".wixpdb", ".wixp<PERSON>j", ".wiz", ".wl", ".wls", ".wlt", ".wlua", ".wm", ".wma", ".wmc", ".wmd", ".wmf", ".wml", ".wmls", ".wmp", ".wmv", ".wmx", ".wn", ".wo", ".woff", ".woff2", ".wof", ".wog", ".wokka", ".wollok", ".word", ".work", ".workspace", ".wot", ".wow", ".wp", ".wpd", ".wpg", ".wpl", ".wps", ".wpt", ".wpy", ".wql", ".wr", ".wren", ".wrl", ".ws", ".wsc", ".wsdl", ".wsdd", ".wsf", ".wsh", ".wsp", ".wsr", ".wta", ".wtf", ".wud", ".wurst", ".wxs", ".wy", ".wyc", ".wys", ".wz", ".x", ".x3d", ".xacro", ".xad", ".xaf", ".xag", ".xaiml", ".xaml", ".xaml.cs", ".xaml.vb", ".xar", ".xav", ".xbap", ".xbd", ".xbel", ".xbf", ".xbl", ".xbm", ".xbm", ".xbrl", ".xc", ".xcat", ".xcdatamodel", ".xcdatamodeld", ".xce", ".xcer", ".xcfg", ".xci", ".xcl", ".xclass", ".xcodeml", ".xconf", ".xcon", ".xcp", ".xcs", ".xcu", ".xct", ".xd", ".xda", ".xdb", ".xdc", ".xdd", ".xdf", ".xdl", ".xdm", ".xdml", ".xdo", ".xdp", ".xdr", ".xds", ".xdt", ".xdw", ".xdx", ".xdy", ".xe", ".xeb", ".xed", ".xef", ".xej", ".xem", ".xer", ".xes", ".xet", ".xev", ".xey", ".xf", ".xfd", ".xff", ".xfm", ".xform", ".xfp", ".xfr", ".xfs", ".xft", ".xfx", ".xg", ".xht", ".xhtml", ".xib", ".xi", ".xic", ".xif", ".xig", ".xil", ".xim", ".xin", ".xip", ".xis", ".xit", ".xix", ".xl", ".xla", ".xlam", ".xlb", ".xlc", ".xld", ".xlex", ".xlf", ".xliff", ".xlg", ".xli", ".xlk", ".xll", ".xlm", ".xln", ".xlo", ".xlp", ".xlr", ".xls", ".xlsb", ".xlsm", ".xlsx", ".xlt", ".xltm", ".xltx", ".xlv", ".xlw", ".xlx", ".xly", ".xm", ".xmap", ".xmf", ".xmi", ".xml", ".xml.dist", ".xmldb", ".xmls", ".xmo", ".xmp", ".xms", ".xmt", ".xmt_txt", ".xmz", ".xn", ".xnd", ".xnf", ".xns", ".xnt", ".xo", ".xodp", ".xoj", ".xoml", ".xop", ".xot", ".xpl", ".xpm", ".xpn", ".xpo", ".xpp", ".xpr", ".xps", ".xpt", ".xpy", ".xq", ".xql", ".xqm", ".x<PERSON>y", ".xqy", ".xr", ".xrc", ".xrd", ".xre", ".xrf", ".xrg", ".xrj", ".xrl", ".xrm", ".xrn", ".xrp", ".xrs", ".xrt", ".xru", ".xrw", ".xrx", ".xs", ".xsc", ".xsd", ".xsem", ".xsf", ".xsh", ".xsi", ".xsp", ".xsql", ".xsr", ".xss", ".xsl", ".xslt", ".xsm", ".xsp-config", ".xst", ".xsw", ".xt", ".xtend", ".xtm", ".xtn", ".xtp", ".xtq", ".xtr", ".xts", ".xtt", ".xu", ".xul", ".xur", ".xvc", ".xvm", ".xvml", ".xwd", ".xwf", ".xwg", ".xws", ".xwt", ".xwv", ".xww", ".xx", ".xxe", ".xxl", ".xy", ".xyz", ".xz", ".y", ".yab", ".yacc", ".yad", ".yaff", ".yak", ".yal", ".yaml", ".yaml-tmlanguage", ".yaml.dist", ".yaml.sed", ".yang", ".yap", ".yara", ".yard", ".yar", ".yas", ".yate", ".yb", ".yc", ".yd", ".yeg", ".yel", ".yep", ".yes", ".yh", ".yhx", ".yi", ".yif", ".yin", ".yipe", ".yj", ".yk", ".yl", ".ym", ".yml", ".yml.dist", ".ymp", ".yms", ".yn", ".yo", ".yok", ".yoml", ".yos", ".you", ".yp", ".ypr", ".yps", ".yr", ".ys", ".yt", ".yul", ".yuml", ".yw", ".yx", ".yy", ".yyg", ".yyp", ".yz", ".z", ".z80", ".zab", ".zap", ".zasm", ".zbr", ".zc", ".zcl", ".zcn", ".zd", ".zdb", ".zdc", ".zdl", ".zds", ".zep", ".zes", ".zeus", ".zfs", ".zg", ".zh", ".zif", ".zig", ".zim", ".zimpl", ".zix", ".zjs", ".zk", ".zl", ".zln", ".zlo", ".zlr", ".zls", ".zlt", ".zm", ".zmap", ".zmax", ".zmc", ".zmd", ".zml", ".zmp", ".zmpl", ".zms", ".zmu", ".zmx", ".zn", ".znn", ".zo", ".zod", ".zof", ".zog", ".zom", ".zon", ".zoo", ".zop", ".zor", ".zot", ".zpl", ".zpr", ".zps", ".zpt", ".zpy", ".zrb", ".zrx", ".zs", ".zsc", ".zsd", ".zsh", ".zsh-theme", ".zshrc", ".zsh_history", ".zsh_profile", ".zsm", ".zso", ".zsp", ".zst", ".ztr", ".zu", ".zul", ".zus", ".zuz", ".zv", ".zvr", ".zvs", ".zxp", ".zxt", ".zy", ".zz", "AUTHORS", "Berksfile", "Berksfile.lock", "Brewfile", "BUILD", "BUILD.bazel", "Capfile", "Cargo.lock", "Cargo.toml", "Cartfile", "Cartfile.resolved", "CHANGELOG", "CMakeLists.txt", "CODEOWNERS", "composer.json", "composer.lock", "Containerfile", "CONTRIBUTING.md", "crontab", "cypress.json", "DESCRIPTION", "Dockerfile", "docker-compose.yml", "docker-<PERSON>.yaml", "dub.json", "dub.sdl", "<PERSON><PERSON><PERSON><PERSON>", "elm.json", "Fastfile", "firebase.json", "Gem<PERSON>le", "Gemfile.lock", "GNUmakefile", "go.mod", "go.sum", "Gruntfile.coffee", "Gruntfile.js", "Gulpfile.coffee", "Gulpfile.js", "haproxy.cfg", "HOSTS", "hosts", "httpd.conf", "Jenkins<PERSON><PERSON>", "jest.config.js", "jsconfig.json", "Justfile", "justfile", "karma.conf.coffee", "karma.conf.js", "LICENSE", "lerna.json", "<PERSON><PERSON><PERSON>", "makefile", "MANIFEST", "MANIFEST.in", "meson.build", "meson_options.txt", "metadata.rb", "mix.exs", "mix.lock", "NAMESPACE", "netlify.toml", "NEWS", "nginx.conf", "now.json", "package.json", "package-lock.json", "pdm.lock", "Pipfile", "Pipfile.lock", "PKGBUILD", "playbook.yml", "pnpm-lock.yaml", "pnpm-workspace.yaml", "Podfile", "Podfile.lock", "poetry.lock", "Procfile", "protractor.conf.js", "pyproject.toml", "Rakefile", "README", "README.md", "rebar.config", "rebar.lock", "requirements.txt", "requirements-dev.txt", "robots.txt", "rollup.config.js", "SConstruct", "SConscript", "serverless.json", "serverless.ts", "serverless.yaml", "serverless.yml", "setup.cfg", "setup.py", "shard.lock", "shard.yml", "sitemap.xml", "supervisord.conf", "Tiltfile", "tox.ini", "tsconfig.app.json", "tsconfig.base.json", "tsconfig.build.json", "tsconfig.json", "tslint.json", "Vagrantfile", "vercel.json", "vite.config.js", "vite.config.ts", "webpack.config.babel.js", "webpack.config.coffee", "webpack.config.js", "WORKSPACE", "wrangler.toml", "yarn.lock"]}}}