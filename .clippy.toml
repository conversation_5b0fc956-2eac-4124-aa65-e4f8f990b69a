# Clippy Configuration for Leptos + Tauri v2 Bad Character Scanner
# Documentation: https://rust-lang.github.io/rust-clippy/master/

# Complexity thresholds
cognitive-complexity-threshold = 30
too-many-arguments-threshold = 8
too-many-lines-threshold = 150
type-complexity-threshold = 250

# Size thresholds
large-error-threshold = 128
trivial-copy-size-limit = 128
pass-by-value-size-limit = 256

# Allow some flexibility during development
# These can be tightened for production builds
allow-expect-in-tests = true
allow-unwrap-in-tests = true
allow-panic-in-tests = true
allow-print-in-tests = true

# MSRV compatibility
msrv = "1.70.0"

# Suppress some pedantic lints that can be noisy during development
# Remove these comments and adjust as needed for production
