# TROUBLESHOOTING GUIDE: Rust Analyzer Macro Issues

## Quick Diagnostic Checklist

### ✅ **Verify Current Status**
- [ ] Does the project build successfully with `cargo build` or `trunk build`?
- [ ] Does the application run correctly?
- [ ] Are only IDE/analyzer features affected?

### 🔄 **Quick Fixes (Try in Order)**

#### 1. Restart Rust Analyzer
```
VS Code: Ctrl+Shift+P → "Rust Analyzer: Restart"
```

#### 2. Reload VS Code Window
```
VS Code: Ctrl+Shift+P → "Developer: Reload Window"
```

#### 3. Check Rust Analyzer Status
```
VS Code: View → Output → Select "Rust Analyzer Language Server"
```

#### 4. Rebuild Project Dependencies
```powershell
cargo build --release
# Then restart Rust Analyzer
```

### 🛠️ **Advanced Solutions**

#### Option A: Targeted Cache Clear
```powershell
# Clear only analyzer cache
rm -r target/debug/deps/*macro*
cargo check
```

#### Option B: Full Clean Rebuild
```powershell
cargo clean
cargo build
# Restart IDE
```

#### Option C: Check Build Configuration
```toml
# In Cargo.toml - ensure proper proc-macro settings
[dependencies]
wasm-bindgen = { version = "0.2", features = ["serde-serialize"] }
leptos = { version = "0.6", features = ["csr"] }
```

### 📊 **Common Patterns**

#### When This Happens
- After `cargo clean`
- During active development with frequent builds
- When switching between build targets (debug/release)
- After dependency updates

#### Warning Signs
- Successful builds but IDE errors
- Missing IntelliSense/completion
- Macro expansion failures
- "Cannot create expander" messages

### 🔍 **Investigation Steps**

#### Check Build Artifacts
```powershell
ls target/debug/deps/ | findstr macro
```

#### Verify Rust Toolchain
```powershell
rustc --version
cargo --version
```

#### Check VS Code Extensions
- Ensure rust-analyzer extension is updated
- Disable/re-enable if necessary

### 📝 **Prevention Tips**

1. **Avoid concurrent builds** - Don't run `cargo build` while IDE is active
2. **Use incremental builds** - Let cargo optimize build caching
3. **Regular analyzer restarts** - Restart analyzer after major changes
4. **Keep toolchain updated** - Update rust-analyzer extension regularly

### 🆘 **When to Escalate**

Contact maintainers if:
- Quick fixes don't resolve the issue
- Problem persists across multiple sessions
- Build process itself starts failing
- Multiple team members experience same issue

---
**Last Updated**: 2025-06-16  
**Version**: 1.0  
