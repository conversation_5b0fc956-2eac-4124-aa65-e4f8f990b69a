# Test Markdown with Suspicious Characters

This is a test markdown file with various **suspicious characters** embedded for testing the Bad Character Scanner.

## Section with Hidden Characters

- Normal bullet point
- Bullet with zero-width space: item​with​hidden​spaces
- Bullet with non-breaking space: item with nbspace

## Code Block Section

```python
# Python code with suspicious characters
def process_data():
    # Contains bidirectional override
    filename = "data‮.py.txt"
    
    # Contains soft hyphen
    variable­name = "test"
    
    # Normal code
    return True
```

## Links and Text

[Normal Link](https://example.com)
[Link with hidden chars](https://exam​ple.com)

Some **bold text** with normal formatting.
Some **bold​text** with zero-width space.

---

*Note: This file intentionally contains suspicious Unicode characters for testing purposes.*
