# Test Script for Bash Interface - Bad Character Scanner
# This script thoroughly tests the CLI analyzer and bash script interface
# with verbose output and comprehensive error handling

param(
    [switch]$Verbose = $false,
    [switch]$CleanOnly = $false,
    [string]$TestDir = "test_files"
)

# PowerShell equivalent of bash colors
$RED = "`e[31m"
$GREEN = "`e[32m"
$YELLOW = "`e[33m"
$BLUE = "`e[34m"
$PURPLE = "`e[35m"
$CYAN = "`e[36m"
$NC = "`e[0m"

# Simple text markers instead of emoji
$CHECKMARK = "[PASS]"
$CROSS = "[FAIL]"
$WARNING = "[WARN]"
$INFO = "[INFO]"
$GEAR = "[DEBUG]"
$ROCKET = "[START]"

$SCRIPT_DIR = $PSScriptRoot
$PROJECT_ROOT = $SCRIPT_DIR
$CLI_BINARY = "$PROJECT_ROOT\target\debug\analyzer_cli.exe"
$BASH_SCRIPT = "$PROJECT_ROOT\scripts\codebase_analyzer.sh"
$TEST_DIR_PATH = "$PROJECT_ROOT\$TestDir"
$RESULTS_DIR = "$PROJECT_ROOT\test_results"

# Test counters
$script:TestsPassed = 0
$script:TestsFailed = 0
$script:TestsTotal = 0

function Write-Log {
    param([string]$Level, [string]$Message, [switch]$Force)
    
    if ($Verbose -or $Force) {
        switch ($Level) {
            "INFO" { Write-Host "$INFO $Message" -ForegroundColor Blue }
            "SUCCESS" { Write-Host "$CHECKMARK $Message" -ForegroundColor Green }
            "WARNING" { Write-Host "$WARNING $Message" -ForegroundColor Yellow }
            "ERROR" { Write-Host "$CROSS $Message" -ForegroundColor Red }
            "DEBUG" { if ($Verbose) { Write-Host "$GEAR $Message" -ForegroundColor Magenta } }
            default { Write-Host "$Message" }
        }
    }
}

function Test-Function {
    param(
        [string]$TestName,
        [scriptblock]$TestBlock,
        [string]$Description = ""
    )
    
    $script:TestsTotal++
    Write-Log "INFO" "Running Test: $TestName" -Force
    if ($Description) {
        Write-Log "INFO" "   Description: $Description"
    }
    
    try {
        $result = & $TestBlock
        if ($result) {
            $script:TestsPassed++
            Write-Log "SUCCESS" "$TestName PASSED" -Force
            return $true
        } else {
            $script:TestsFailed++
            Write-Log "ERROR" "$TestName FAILED" -Force
            return $false
        }
    } catch {
        $script:TestsFailed++
        Write-Log "ERROR" "$TestName FAILED with exception: $($_.Exception.Message)" -Force
        return $false
    }
}

function Setup-TestEnvironment {
    Write-Log "INFO" "Setting up test environment..." -Force
    
    # Create test directories
    @($TEST_DIR_PATH, $RESULTS_DIR) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
            Write-Log "INFO" "Created directory: $_"
        }
    }
    
    # Create test files with various problematic characters
    $testFiles = @{
        "clean_file.txt" = "This is a clean file with no bad characters.`nJust normal text here.`nNothing suspicious at all!"
        
        "bad_chars_simple.txt" = @"
This file has some bad characters:
Zero-width space: Test word (U+200B)
Non-breaking space: word word (U+00A0)
Replacement char: � (U+FFFD)
"@
        
        "bad_chars_complex.txt" = @"
This file simulates a document with various problematic Unicode characters.
It includes a Zero Width Space here -> <- (U+200B) which is invisible.
Sometimes, text might contain a No-Break Space like this: word word (U+00A0) instead of a regular space.
Be careful with directional overrides! This text ->backwards text<- (U+202E) is an example.

Soft hyphens (U+00AD) are tricky: hyphenation. They should not always be visible.
A Zero Width Non-Joiner (U+200C) can affect ligatures, like in five (fi).
If data gets corrupted, you might see a Replacement Character: � (U+FFFD).

This file also contains a few control characters that might be problematic:
A null character: -> <- (U+0000).
And a Form Feed character here -> <- (U+000C) which might cause a page break.
Finally, an Ideographic Space -> <- (U+3000) which is wider than a normal space.
End of mixed severity test.
"@
        
        "test_script.js" = @"
// JavaScript file with bad characters
function testFunction() {
    let badString = "testinvisible"; // Contains zero-width space
    console.log("Processing: " + badString);
    return badString.replace(/\s/g, "_");
}
"@
        
        "test_config.json" = @"
{
    "name": "testconfig",
    "settings": {
        "enable_feature": true,
        "timeout": 5000
    }
}
"@
    }
    
    foreach ($file in $testFiles.GetEnumerator()) {
        $filePath = Join-Path $TEST_DIR_PATH $file.Key
        $file.Value | Out-File -FilePath $filePath -Encoding UTF8 -Force
        Write-Log "DEBUG" "Created test file: $filePath"
    }
    
    Write-Log "SUCCESS" "Test environment setup complete!" -Force
}

function Test-CLIBinaryExists {
    if (Test-Path $CLI_BINARY) {
        Write-Log "SUCCESS" "CLI binary found at: $CLI_BINARY"
        return $true
    } else {
        Write-Log "ERROR" "CLI binary not found at: $CLI_BINARY"
        Write-Log "INFO" "Attempting to build CLI binary..."
        
        Push-Location "$PROJECT_ROOT\src-tauri"
        try {
            $buildResult = & cargo build --bin analyzer_cli 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Log "SUCCESS" "CLI binary built successfully"
                return Test-Path $CLI_BINARY
            } else {
                Write-Log "ERROR" "Failed to build CLI binary: $buildResult"
                return $false
            }
        } finally {
            Pop-Location
        }
    }
}

function Test-CLIBasicUsage {
    Write-Log "INFO" "Testing CLI basic usage and help..."
    
    try {
        $helpOutput = & $CLI_BINARY 2>&1
        Write-Log "DEBUG" "CLI help output: $helpOutput"
        
        # Should show usage information and exit with code 1
        if ($LASTEXITCODE -eq 1 -and $helpOutput -match "Usage:") {
            return $true
        } else {
            Write-Log "ERROR" "CLI help output unexpected. Exit code: $LASTEXITCODE"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Failed to run CLI binary: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIAnalyzeCleanFile {
    $testFile = Join-Path $TEST_DIR_PATH "clean_file.txt"
    Write-Log "INFO" "Testing CLI analysis of clean file: $testFile"
    
    try {
        $output = & $CLI_BINARY analyze $testFile json 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "DEBUG" "CLI output for clean file: $output"
            
            # Parse JSON to verify structure
            try {
                $jsonResult = $output | ConvertFrom-Json
                if ($jsonResult.total_files -eq 1 -and $jsonResult.files_with_issues -eq 0) {
                    Write-Log "SUCCESS" "Clean file correctly identified as clean"
                    return $true
                } else {
                    Write-Log "ERROR" "Clean file analysis results unexpected"
                    return $false
                }
            } catch {
                Write-Log "ERROR" "Failed to parse JSON output: $($_.Exception.Message)"
                return $false
            }
        } else {
            Write-Log "ERROR" "CLI failed with exit code: $LASTEXITCODE, Output: $output"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Exception during CLI test: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIAnalyzeBadFile {
    $testFile = Join-Path $TEST_DIR_PATH "bad_chars_complex.txt"
    Write-Log "INFO" "Testing CLI analysis of file with bad characters: $testFile"
    
    try {
        $output = & $CLI_BINARY analyze $testFile json 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "DEBUG" "CLI output for bad file: $output"
            
            try {
                $jsonResult = $output | ConvertFrom-Json
                if ($jsonResult.total_files -eq 1 -and $jsonResult.files_with_issues -ge 0 -and $jsonResult.total_suspicious_chars -ge 0) {
                    Write-Log "SUCCESS" "File analyzed successfully. Found $($jsonResult.total_suspicious_chars) suspicious characters"
                    if ($jsonResult.file_details -and $jsonResult.file_details[0].issues) {
                        Write-Log "INFO" "Issues found: $($jsonResult.file_details[0].issues -join ', ')"
                    }
                    return $true
                } else {
                    Write-Log "ERROR" "File analysis results unexpected. Files with issues: $($jsonResult.files_with_issues), Suspicious chars: $($jsonResult.total_suspicious_chars)"
                    return $false
                }
            } catch {
                Write-Log "ERROR" "Failed to parse JSON output: $($_.Exception.Message)"
                return $false
            }
        } else {
            Write-Log "ERROR" "CLI failed with exit code: $LASTEXITCODE, Output: $output"
            return $false
        }
    } catch {
        Write-Log "ERROR" "Exception during CLI test: $($_.Exception.Message)"
        return $false
    }
}

function Test-CLIOutputFormats {
    $testFile = Join-Path $TEST_DIR_PATH "bad_chars_simple.txt"
    $formats = @("json", "text", "markdown")
    
    foreach ($format in $formats) {
        Write-Log "INFO" "Testing CLI output format: $format"
        
        try {
            $output = & $CLI_BINARY analyze $testFile $format 2>&1
            if ($LASTEXITCODE -eq 0 -and $output.Length -gt 0) {
                Write-Log "SUCCESS" "Format $format produced output"
                Write-Log "DEBUG" "Sample output ($format): $($output[0..2] -join '; ')..."
            } else {
                Write-Log "ERROR" "Format $format failed or produced no output"
                return $false
            }
        } catch {
            Write-Log "ERROR" "Exception testing format $format : $($_.Exception.Message)"
            return $false
        }
    }
    
    return $true
}

function Test-CLIErrorHandling {
    Write-Log "INFO" "Testing CLI error handling..."
    
    # Test non-existent file
    $nonExistentFile = Join-Path $TEST_DIR_PATH "does_not_exist.txt"
    try {
        $output = & $CLI_BINARY analyze $nonExistentFile json 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "SUCCESS" "CLI correctly handled non-existent file with exit code: $LASTEXITCODE"
        } else {
            Write-Log "ERROR" "CLI should have failed for non-existent file"
            return $false
        }
    } catch {
        Write-Log "SUCCESS" "CLI correctly threw exception for non-existent file"
    }
    
    # Test invalid format
    $testFile = Join-Path $TEST_DIR_PATH "clean_file.txt"
    try {
        $output = & $CLI_BINARY analyze $testFile invalid_format 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "SUCCESS" "CLI correctly handled invalid format with exit code: $LASTEXITCODE"
        } else {
            Write-Log "ERROR" "CLI should have failed for invalid format"
            return $false
        }
    } catch {
        Write-Log "SUCCESS" "CLI correctly threw exception for invalid format"
    }
    
    return $true
}

function Show-TestSummary {
    Write-Log "INFO" "$ROCKET TEST SUMMARY" -Force
    Write-Log "INFO" "=================" -Force
    Write-Log "SUCCESS" "Tests Passed: $script:TestsPassed" -Force
    Write-Log "ERROR" "Tests Failed: $script:TestsFailed" -Force
    Write-Log "INFO" "Total Tests: $script:TestsTotal" -Force
    
    $successRate = if ($script:TestsTotal -gt 0) { ($script:TestsPassed / $script:TestsTotal) * 100 } else { 0 }
    Write-Log "INFO" "Success Rate: $([math]::Round($successRate, 2))%" -Force
    
    if ($script:TestsFailed -eq 0) {
        Write-Log "SUCCESS" "$CHECKMARK All tests passed! Bash Script Interface is working correctly." -Force
    } else {
        Write-Log "WARNING" "$WARNING Some tests failed. Review the output above for details." -Force
    }
}

function Cleanup-TestEnvironment {
    if ($CleanOnly) {
        Write-Log "INFO" "Cleaning up test environment..." -Force
        
        @($TEST_DIR_PATH, $RESULTS_DIR) | ForEach-Object {
            if (Test-Path $_) {
                Remove-Item -Path $_ -Recurse -Force
                Write-Log "SUCCESS" "Removed: $_" -Force
            }
        }
        
        Write-Log "SUCCESS" "Cleanup complete!" -Force
        return
    }
}

# Main execution
Write-Host "$ROCKET Bad Character Scanner - Bash Interface Test Suite" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan

if ($CleanOnly) {
    Cleanup-TestEnvironment
    exit 0
}

Write-Log "INFO" "Starting comprehensive test suite..." -Force
Write-Log "INFO" "Verbose mode: $Verbose" -Force

# Setup
Setup-TestEnvironment

# Run all tests
Test-Function "CLI Binary Exists" { Test-CLIBinaryExists } "Check if the CLI binary exists or can be built"
Test-Function "CLI Basic Usage" { Test-CLIBasicUsage } "Test CLI help and basic command structure"
Test-Function "CLI Analyze Clean File" { Test-CLIAnalyzeCleanFile } "Test analysis of a file without bad characters"
Test-Function "CLI Analyze Bad File" { Test-CLIAnalyzeBadFile } "Test analysis of a file with problematic characters"
Test-Function "CLI Output Formats" { Test-CLIOutputFormats } "Test all supported output formats (JSON, text, markdown)"
Test-Function "CLI Error Handling" { Test-CLIErrorHandling } "Test error handling for invalid inputs"

# Show summary
Show-TestSummary

# Exit with appropriate code
exit $script:TestsFailed
