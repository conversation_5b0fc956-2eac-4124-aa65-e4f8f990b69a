#!/bin/bash

# Codebase Analyzer - Bash Interface
# A command-line interface for the Laptos TauriV2 Bad Character Scanner
# This script provides direct access to analysis and export functionality
# 
# Features:
# - Analyze directories for suspicious Unicode characters
# - Export analysis results in multiple formats (JSON, Markdown, Text)
# - Scan individual files for character issues
# - Built-in testing and validation
# - Comprehensive error handling and logging
# - Verbose output modes for debugging

# Enable strict error handling
set -euo pipefail

# Configuration and Environment Setup
# These variables define the working environment and temporary storage locations
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEMP_DIR="/tmp/laptos_analyzer_$$"
LOG_FILE="$TEMP_DIR/analyzer.log"

# Color Definitions for Enhanced Terminal Output
# These ANSI color codes provide visual feedback and improved user experience
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m' # Corrected from YIGHLLOW
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Unicode Emoji Characters for Better UX
# These symbols provide visual cues for different types of messages and operations
CHECKMARK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
ROCKET="🚀"
FOLDER="📁"
FILE="📄"
EXPORT="📤"

# Default Configuration Values
# These settings control the default behavior of the script and can be overridden
DEFAULT_OUTPUT_FORMAT="json"
DEFAULT_OUTPUT_DIR="./reports"
VERBOSE=false
DRY_RUN=false

# Usage information
show_usage() {
    cat << EOF
${BLUE}${ROCKET} Laptos TauriV2 Bad Character Scanner - Bash Interface${NC}

${CYAN}USAGE:${NC}
    $0 [OPTIONS] <COMMAND> [ARGS...]

${CYAN}COMMANDS:${NC}
    ${GREEN}analyze${NC} <directory>     - Analyze a directory for suspicious characters
    ${GREEN}export${NC} <analysis_file>  - Export analysis results to different formats
    ${GREEN}scan${NC} <file>            - Scan a single file for suspicious characters
    ${GREEN}test${NC}                   - Run built-in tests and validation
    ${GREEN}demo${NC}                   - Run demonstration with sample data
    ${GREEN}health${NC}                 - Check system health and dependencies

${CYAN}OPTIONS:${NC}
    ${YELLOW}-f, --format FORMAT${NC}      Export format: json, markdown, text (default: json)
    ${YELLOW}-o, --output DIR${NC}         Output directory (default: ./reports)
    ${YELLOW}-v, --verbose${NC}            Enable verbose logging
    ${YELLOW}-d, --dry-run${NC}            Show what would be done without executing
    ${YELLOW}-q, --quiet${NC}              Suppress non-error output
    ${YELLOW}-h, --help${NC}               Show this help message

${CYAN}EXAMPLES:${NC}
    ${GREEN}# Analyze a directory and export as JSON${NC}
    $0 analyze /path/to/codebase

    ${GREEN}# Analyze and export as Markdown${NC}
    $0 --format markdown analyze /path/to/codebase

    ${GREEN}# Export existing analysis to text format${NC}
    $0 --format text export analysis_results.json

    ${GREEN}# Scan a single file${NC}
    $0 scan suspicious_file.js

    ${GREEN}# Run tests${NC}
    $0 test

    ${GREEN}# Run demo with verbose output${NC}
    $0 --verbose demo

${CYAN}EXIT CODES:${NC}
    0  - Success
    1  - General error
    2  - Invalid arguments
    3  - File/directory not found
    4  - Analysis failed
    5  - Export failed
    6  - Dependency missing

EOF
}

# Logging Functions
# These functions provide consistent, colored output with different severity levels
# All log output goes to stderr to avoid interfering with program output

# Log informational messages (only shown in verbose mode unless forced)
log_info() {
    if [[ "$VERBOSE" == "true" ]] || [[ "${1:-}" == "--force" ]]; then
        echo -e "${INFO} ${BLUE}[INFO]${NC} $2" >&2
    fi
}

# Log success messages (always shown)
log_success() {
    echo -e "${CHECKMARK} ${GREEN}[SUCCESS]${NC} $1" >&2
}

# Log warning messages (always shown)
log_warning() {
    echo -e "${WARNING} ${YELLOW}[WARNING]${NC} $1" >&2
}

# Log error messages (always shown)
log_error() {
    echo -e "${CROSS} ${RED}[ERROR]${NC} $1" >&2
}

# Log debug messages (only shown in verbose mode)
log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${GEAR} ${PURPLE}[DEBUG]${NC} $1" >&2
    fi
}

# Setup temporary directory
setup_temp_dir() {
    mkdir -p "$TEMP_DIR"
    echo "# Laptos Analyzer Log - $(date)" > "$LOG_FILE"
    log_debug "Temporary directory: $TEMP_DIR"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [[ -d "$TEMP_DIR" ]]; then
        log_debug "Cleaning up temporary directory: $TEMP_DIR"
        rm -rf "$TEMP_DIR"
    fi
    exit $exit_code
}

# Check if required dependencies are available
check_dependencies() {
    local missing_deps=()
    
    # Check for required tools
    if ! command -v cargo &> /dev/null; then
        missing_deps+=("cargo (Rust toolchain)")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq (JSON processor)")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "Missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo -e "  ${CROSS} $dep"
        done
        echo -e "\n${INFO} Please install missing dependencies and try again."
        exit 6
    fi
    
    log_debug "All dependencies found"
}

# Check if we're in the project root
check_project_structure() {
    if [[ ! -f "$PROJECT_ROOT/Cargo.toml" ]] || [[ ! -d "$PROJECT_ROOT/src-tauri" ]]; then
        log_error "Not in Laptos project root. Please run from project directory."
        echo -e "${INFO} Expected structure: Cargo.toml and src-tauri/ directory"
        exit 3
    fi
    log_debug "Project structure validated"
}

# Build the Rust analyzer if needed
build_analyzer() {
    local build_needed=false
    local binary_path="$PROJECT_ROOT/target/release/analyzer_cli"
    
    # Check if binary exists and is newer than source
    if [[ ! -f "$binary_path" ]]; then
        build_needed=true
        log_info --force "Analyzer binary not found, building..."
    else
        # Check if source is newer than binary
        if find "$PROJECT_ROOT/src-tauri/src" -name "*.rs" -newer "$binary_path" | grep -q .; then
            build_needed=true
            log_info --force "Source files updated, rebuilding..."
        fi
    fi
    
    if [[ "$build_needed" == "true" ]]; then
        log_info --force "Building Rust analyzer..."
        if [[ "$DRY_RUN" == "true" ]]; then
            echo "[DRY RUN] Would build: cargo build --release --package laptos-tauri --bin analyzer_cli"
            return 0
        fi
        
        cd "$PROJECT_ROOT"
        if ! cargo build --release --package laptos-tauri --bin analyzer_cli >> "$LOG_FILE" 2>&1; then
            log_error "Failed to build analyzer. Check log: $LOG_FILE"
            cat "$LOG_FILE"
            exit 4
        fi
        log_success "Analyzer built successfully"
    else
        log_debug "Using existing analyzer binary"
    fi
}

# Create a simple Rust CLI binary for analysis
create_analyzer_binary() {
    local cli_source_dir="$PROJECT_ROOT/src-tauri/src/bin"
    local cli_source="$cli_source_dir/analyzer_cli.rs"
    
    log_info --force "Ensuring CLI analyzer binary source at $cli_source is up-to-date (will overwrite)..."
    
    mkdir -p "$cli_source_dir" # Ensure the bin directory exists
    # Always overwrite the file
    # Using unquoted EOF delimiter for cat. Expansions will be enabled in here-doc,
    # but Rust code should be safe.
    cat > "$cli_source" << EOF
use std::error::Error;
use std::path::Path;
use std::env; // Ensure env is imported for argument parsing
use tokio::runtime::Runtime;

// Assuming these are the correct paths after previous fixes
use laptos_tauri::main_module::analyze_codebase;
use laptos_tauri::modules::data_structures::CodeBaseAnalysisResult; // Keep for now, even if analyzer_cli itself doesn't directly use the type

// This function performs the core analysis and returns JSON string
async fn perform_core_analysis(target_path_str: &str) -> Result<String, Box<dyn Error>> {
    eprintln!("[DEBUG] analyzer_cli: Calling analyze_codebase for path: {}", target_path_str); // Use eprintln!
    let analysis_result = analyze_codebase(target_path_str.to_string()).await?;
    let json_output = serde_json::to_string_pretty(&analysis_result)?;
    Ok(json_output)
}

// Wrapper for the 'analyze' command
async fn handle_analyze_command(path_arg: String, _format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'analyze' command received for path: {}", path_arg); // Use eprintln!
    // perform_core_analysis expects &str, path_arg is String
    let analysis_json = perform_core_analysis(&path_arg).await?;
    // The output is printed to stdout. The calling bash script will capture it.
    println!("{}", analysis_json); // This remains println! for the actual JSON output
    Ok(())
}

// Placeholder for 'export' command handling
fn handle_export_command(path_arg: String, format_arg: String) -> Result<(), Box<dyn Error>> {
    eprintln!("[INFO] analyzer_cli: 'export' command (stub) for path: {}, format: {}", path_arg, format_arg); // Use eprintln!
    if !Path::new(&path_arg).exists() {
        return Err(format!("File not found for export: {}", path_arg).into());
    }
    eprintln!("[WARN] Export functionality is a stub in analyzer_cli.rs."); // Use eprintln!
    // In a real scenario, this would read the analysis_json and convert to the specified format.
    Ok(())
}

fn main() -> Result<(), Box<dyn Error>> {
    let mut args = std::env::args().skip(1); // Skip executable name

    let command = args.next().ok_or_else(|| {
        "No command provided. Usage: analyzer_cli <analyze|export> <path> [format]".to_string()
    })?;

    let path_arg = args.next().ok_or_else(|| {
        format!("Missing path argument for command '{}'", command)
    })?;

    // Format argument is optional, defaults to "json"
    let format_arg = args.next().unwrap_or_else(|| "json".to_string());

    // Tokio runtime for async operations
    let rt = Runtime::new()?;

    match command.as_str() {
        "analyze" => {
            // path_arg is String, handle_analyze_command expects String
            rt.block_on(handle_analyze_command(path_arg, format_arg))?;
        }
        "export" => {
            // path_arg is String, handle_export_command expects String
            handle_export_command(path_arg, format_arg)?;
        }
        _ => {
            eprintln!("Unknown command: '{}'. Supported commands: analyze, export.", command);
            // Return an error that can be converted to Box<dyn Error>
            return Err(format!("Unknown command: {}", command).into());
        }
    }
    Ok(())
}
EOF
    
    log_success "CLI analyzer source (over)written successfully at $cli_source"
}

# Analyze a directory
analyze_directory() {
    local target_dir_arg="$1" # Store the original argument
    local output_format="${2:-$DEFAULT_OUTPUT_FORMAT}"
    local resolved_target_dir

    # Resolve the target directory path
    # If target_dir_arg is already an absolute path, use it.
    # Otherwise, assume it's relative to PROJECT_ROOT.
    if [[ "$target_dir_arg" == /* ]] || [[ "$target_dir_arg" == ~* ]] || [[ "$target_dir_arg" =~ ^[A-Za-z]: ]]; then
        resolved_target_dir="$target_dir_arg"
    else
        # Construct path relative to PROJECT_ROOT if it's a relative path like '../cli_test_sandbox'
        # when the script is in SCRIPT_DIR ($PROJECT_ROOT/scripts)
        # So, ../cli_test_sandbox from SCRIPT_DIR becomes $PROJECT_ROOT/cli_test_sandbox
        if [[ "$target_dir_arg" == ../* ]]; then
             # Handle cases like ../folder by going up one from SCRIPT_DIR then adding the rest
             local base_path=$(dirname "$SCRIPT_DIR") # This is PROJECT_ROOT
             local relative_part="${target_dir_arg#../}"
             resolved_target_dir="$base_path/$relative_part"
        else
            # For paths like 'myfolder' or './myfolder', assume relative to current execution dir,
            # but it's safer to make it relative to PROJECT_ROOT if that's the convention.
            # For now, let's try making it relative to where the script is run from,
            # but this might need adjustment if CWD changes.
            # A more robust way for general relative paths:
            resolved_target_dir="$(cd "$(pwd)" && cd "$target_dir_arg" && pwd)"
            if [[ ! -d "$resolved_target_dir" ]]; then # Fallback for simple relative paths from project root
                 resolved_target_dir="$PROJECT_ROOT/$target_dir_arg"
            fi
        fi
    fi

    log_debug "analyze_directory: Original target_dir_arg: '$target_dir_arg'"
    log_debug "analyze_directory: SCRIPT_DIR: '$SCRIPT_DIR'"
    log_debug "analyze_directory: PROJECT_ROOT: '$PROJECT_ROOT'"
    log_debug "analyze_directory: Resolved target_dir: '$resolved_target_dir'"
    
    if [[ ! -d "$resolved_target_dir" ]]; then
        log_error "Directory not found: $target_dir_arg (resolved to: $resolved_target_dir)"
        exit 3
    fi
    
    log_info --force "Analyzing directory: $resolved_target_dir"
    log_info --force "Output format: $output_format"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would analyze: $target_dir"
        echo "[DRY RUN] Would output format: $output_format"
        return 0
    fi
    
    # Create output directory
    mkdir -p "$DEFAULT_OUTPUT_DIR"
    
    # Generate output filename with timestamp
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local safe_dirname=$(basename "$resolved_target_dir" | tr ' ' '_' | tr -cd '[:alnum:]_-') # Use resolved_target_dir
    local output_file="$DEFAULT_OUTPUT_DIR/analysis_${safe_dirname}_${timestamp}.${output_format}"
    
    # Run analysis
    local binary_path="$PROJECT_ROOT/target/release/analyzer_cli"
    if ! "$binary_path" analyze "$resolved_target_dir" "$output_format" > "$output_file" 2>> "$LOG_FILE"; then
        log_error "Analysis failed for $resolved_target_dir. Check log: $LOG_FILE"
        return 4
    fi
    
    log_success "Analysis completed: $output_file"
    
    # Show summary
    if [[ "$output_format" == "json" ]] && command -v jq &> /dev/null; then
        local total_files=$(jq -r '.codebase_analysis.total_files // .total_files' "$output_file")
        local files_with_issues=$(jq -r '.codebase_analysis.files_with_issues // .files_with_issues' "$output_file")
        local health_score=$(jq -r '.codebase_analysis.health_score // .health_score' "$output_file")
        
        echo -e "\n${INFO} ${CYAN}Analysis Summary:${NC}"
        echo -e "  ${FOLDER} Total Files: $total_files"
        echo -e "  ${WARNING} Files with Issues: $files_with_issues"
        echo -e "  ${CHECKMARK} Health Score: ${health_score}%"
    fi
    
    return 0
}

# Export existing analysis
export_analysis() {
    local analysis_file="$1"
    local output_format="${2:-$DEFAULT_OUTPUT_FORMAT}"
    
    if [[ ! -f "$analysis_file" ]]; then
        log_error "Analysis file not found: $analysis_file"
        exit 3
    fi
    
    log_info --force "Exporting analysis: $analysis_file"
    log_info --force "Output format: $output_format"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would export: $analysis_file to $output_format"
        return 0
    fi
    
    # Create output directory
    mkdir -p "$DEFAULT_OUTPUT_DIR"
    
    # Generate output filename
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local basename_no_ext=$(basename "$analysis_file" | sed 's/\.[^.]*$//')
    local output_file="$DEFAULT_OUTPUT_DIR/${basename_no_ext}_export_${timestamp}.${output_format}"
    
    # Run export
    local binary_path="$PROJECT_ROOT/target/release/analyzer_cli"
    if ! "$binary_path" export "$analysis_file" "$output_format" > "$output_file" 2>> "$LOG_FILE"; then
        log_error "Export failed. Check log: $LOG_FILE"
        return 5
    fi
    
    log_success "Export completed: $output_file"
    return 0
}

# Scan a single file
scan_file() {
    local target_file="$1"
    
    if [[ ! -f "$target_file" ]]; then
        log_error "File not found: $target_file"
        exit 3
    fi
    
    log_info --force "Scanning file: $target_file"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would scan: $target_file"
        return 0
    fi
    
    # Simple character analysis using built-in tools
    local file_size=$(stat -f%z "$target_file" 2>/dev/null || stat -c%s "$target_file" 2>/dev/null || echo "unknown")
    local line_count=$(wc -l < "$target_file" 2>/dev/null || echo "unknown")
    
    echo -e "\n${FILE} ${CYAN}File Analysis: $(basename "$target_file")${NC}"
    echo -e "  Path: $target_file"
    echo -e "  Size: $file_size bytes"
    echo -e "  Lines: $line_count"
    
    # Check for suspicious Unicode characters
    local suspicious_chars=()
    
    # Check for zero-width characters
    if grep -q $'\u200B\|\u200C\|\u200D\|\uFEFF' "$target_file" 2>/dev/null; then
        suspicious_chars+=("Zero-width characters detected")
    fi
    
    # Check for bidirectional override characters
    if grep -q $'\u202E\|\u202D' "$target_file" 2>/dev/null; then
        suspicious_chars+=("Bidirectional override characters detected")
    fi
    
    # Check for unusual Unicode ranges
    if grep -qP '[\x{2000}-\x{206F}]|\x{FFF0}-\x{FFFF}]' "$target_file" 2>/dev/null; then
        suspicious_chars+=("Unusual Unicode characters detected")
    fi
    
    if [[ ${#suspicious_chars[@]} -eq 0 ]]; then
        echo -e "  ${CHECKMARK} ${GREEN}Status: Clean${NC}"
    else
        echo -e "  ${WARNING} ${YELLOW}Status: Issues Found${NC}"
        for issue in "${suspicious_chars[@]}"; do
            echo -e "    ${CROSS} $issue"
        done
    fi
    
    return 0
}

# Run built-in tests
run_tests() {
    log_info --force "Running built-in tests..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would run tests"
        return 0
    fi
    
    local test_passed=0
    local test_failed=0
    
    # Test 1: Check project structure
    echo -e "\n${GEAR} ${CYAN}Test 1: Project Structure${NC}"
    if check_project_structure 2>/dev/null; then
        echo -e "  ${CHECKMARK} Project structure valid"
        ((test_passed++))
    else
        echo -e "  ${CROSS} Project structure invalid"
        ((test_failed++))
    fi
    
    # Test 2: Check dependencies
    echo -e "\n${GEAR} ${CYAN}Test 2: Dependencies${NC}"
    if check_dependencies 2>/dev/null; then
        echo -e "  ${CHECKMARK} All dependencies available"
        ((test_passed++))
    else
        echo -e "  ${CROSS} Missing dependencies"
        ((test_failed++))
    fi
    
    # Test 3: Build analyzer
    echo -e "\n${GEAR} ${CYAN}Test 3: Analyzer Build${NC}"
    if build_analyzer 2>/dev/null; then
        echo -e "  ${CHECKMARK} Analyzer builds successfully"
        ((test_passed++))
    else
        echo -e "  ${CROSS} Analyzer build failed"
        ((test_failed++))
    fi
    
    # Test 4: Rust tests
    echo -e "\n${GEAR} ${CYAN}Test 4: Rust Unit Tests${NC}"
    cd "$PROJECT_ROOT"
    if cargo test --package laptos-tauri >> "$LOG_FILE" 2>&1; then
        echo -e "  ${CHECKMARK} Rust tests pass"
        ((test_passed++))
    else
        echo -e "  ${CROSS} Rust tests failed"
        ((test_failed++))
    fi
    
    # Test summary
    echo -e "\n${INFO} ${CYAN}Test Summary:${NC}"
    echo -e "  ${GREEN}Passed: $test_passed${NC}"
    echo -e "  ${RED}Failed: $test_failed${NC}"
    
    if [[ $test_failed -eq 0 ]]; then
        log_success "All tests passed!"
        return 0
    else
        log_error "$test_failed test(s) failed"
        return 1
    fi
}

# Run demonstration
run_demo() {
    log_info --force "Running demonstration..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would run demo"
        return 0
    fi
    
    # Create demo directory with test files
    local demo_dir="$TEMP_DIR/demo_codebase"
    mkdir -p "$demo_dir"
    
    # Create sample files with various issues
    cat > "$demo_dir/clean_file.js" << 'EOF'
// This is a clean JavaScript file
function greetUser(name) {
    console.log("Hello, " + name + "!");
}

greetUser("World");
EOF
    
    cat > "$demo_dir/suspicious_file.js" << 'EOF'
// This file contains suspicious characters
function greetUser(name) {
    console.log("Hello, " + name + "!");​
    // Note: There's a zero-width space after the exclamation mark above
}

// Hidden bidirectional override: ‮
const config = {
    apiKey: "secret-key"
};
EOF
    
    cat > "$demo_dir/README.md" << 'EOF'
# Demo Project

This is a demonstration project for the Laptos analyzer.

## Files

- `clean_file.js` - A normal JavaScript file
- `suspicious_file.js` - Contains suspicious Unicode characters
EOF
    
    echo -e "\n${ROCKET} ${CYAN}Demo: Analyzing sample codebase${NC}"
    echo -e "Demo directory: $demo_dir"
    
    # Analyze the demo directory
    analyze_directory "$demo_dir" "markdown"
    
    log_success "Demo completed! Check the reports directory for results."
    return 0
}

# Check system health
check_health() {
    log_info --force "Checking system health..."
    
    echo -e "\n${GEAR} ${CYAN}System Health Check${NC}"
    
    # Check Rust installation
    if command -v rustc &> /dev/null; then
        local rust_version=$(rustc --version)
        echo -e "  ${CHECKMARK} Rust: $rust_version"
    else
        echo -e "  ${CROSS} Rust: Not installed"
    fi
    
    # Check Cargo
    if command -v cargo &> /dev/null; then
        local cargo_version=$(cargo --version)
        echo -e "  ${CHECKMARK} Cargo: $cargo_version"
    else
        echo -e "  ${CROSS} Cargo: Not installed"
    fi
    
    # Check project
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]]; then
        echo -e "  ${CHECKMARK} Project: Found"
    else
        echo -e "  ${CROSS} Project: Not found"
    fi
    
    # Check disk space
    local available_space=$(df -h . | awk 'NR==2 {print $4}')
    echo -e "  ${INFO} Available space: $available_space"
    
    # Check memory
    if command -v free &> /dev/null; then
        local available_memory=$(free -h | awk 'NR==2 {print $7}')
        echo -e "  ${INFO} Available memory: $available_memory"
    fi
    
    log_success "Health check completed"
    return 0
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--format)
                DEFAULT_OUTPUT_FORMAT="$2"
                shift 2
                ;;
            -o|--output)
                DEFAULT_OUTPUT_DIR="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -q|--quiet)
                VERBOSE=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            analyze)
                if [[ -z "${2:-}" ]]; then
                    log_error "analyze command requires a directory argument"
                    exit 2
                fi
                COMMAND="analyze"
                COMMAND_ARG="$2"
                shift 2
                ;;
            export)
                if [[ -z "${2:-}" ]]; then
                    log_error "export command requires a file argument"
                    exit 2
                fi
                COMMAND="export"
                COMMAND_ARG="$2"
                shift 2
                ;;
            scan)
                if [[ -z "${2:-}" ]]; then
                    log_error "scan command requires a file argument"
                    exit 2
                fi
                COMMAND="scan"
                COMMAND_ARG="$2"
                shift 2
                ;;
            test)
                COMMAND="test"
                shift
                ;;
            demo)
                COMMAND="demo"
                shift
                ;;
            health)
                COMMAND="health"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 2
                ;;
        esac
    done
}

# Main function
main() {
    # Set up trap for cleanup
    trap cleanup EXIT INT TERM
    
    # Initialize
    setup_temp_dir
    
    # Parse arguments
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 0
    fi
    
    parse_args "$@"
    
    # Validate format
    if [[ ! "$DEFAULT_OUTPUT_FORMAT" =~ ^(json|markdown|text)$ ]]; then
        log_error "Invalid format: $DEFAULT_OUTPUT_FORMAT"
        echo "Supported formats: json, markdown, text"
        exit 2
    fi
    
    # Check project structure and dependencies (unless it's a health check)
    if [[ "${COMMAND:-}" != "health" ]]; then
        check_project_structure
        check_dependencies
    fi
    
    # Create analyzer binary if needed
    if [[ "${COMMAND:-}" =~ ^(analyze|export)$ ]]; then
        create_analyzer_binary
        build_analyzer
    fi
    
    # Execute command
    case "${COMMAND:-}" in
        analyze)
            analyze_directory "$COMMAND_ARG" "$DEFAULT_OUTPUT_FORMAT"
            ;;
        export)
            export_analysis "$COMMAND_ARG" "$DEFAULT_OUTPUT_FORMAT"
            ;;
        scan)
            scan_file "$COMMAND_ARG"
            ;;
        test)
            run_tests
            ;;
        demo)
            run_demo
            ;;
        health)
            check_health
            ;;
        *)
            log_error "No command specified"
            show_usage
            exit 2
            ;;
    esac
}

# Run main function with all arguments
main "$@"
