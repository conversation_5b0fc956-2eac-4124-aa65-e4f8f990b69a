# UPGRADE-1 - Update Tauri v2+ Components to Latest Versions

**Status:** 🟡 In Progress  
**Priority:** P1 (High)  
**Type:** 🔧 Enhancement  
**Created:** 2024-12-29  
**Updated:** 2025-06-17  
**Assigned To:** @developer  
**Complexity:** Medium  
**Story Points:** 3

## 📋 Description

Update all outdated Tauri v2+ components to their latest versions to ensure compatibility, security, and access to latest features. Scanner detected 5 outdated components that need updating.

## 🎯 Objectives

- Update tauri-plugin-shell from ~2.2.0 to 2.2.1
- Update @tauri-apps/plugin-fs from ~2.2.0 to 2.3.0
- Remove or fix tauri-sys dependency issue
- Ensure all Tauri components are using latest stable versions
- Verify AI detection functionality works with updated dependencies

## ✅ Acceptance Criteria

- [ ] tauri-plugin-shell updated to 2.2.1 in src-tauri/Cargo.toml
- [ ] @tauri-apps/plugin-fs updated to 2.3.0 in package.json
- [ ] tauri-sys dependency issue resolved
- [ ] All dependencies compile successfully
- [ ] AI detection functionality tested with new versions
- [ ] No regression in existing functionality

## 🛠 Technical Requirements

### Components to Update
- **Backend (Cargo.toml):**
  - tauri-plugin-shell: ~2.2.0 → 2.2.1

- **Frontend (package.json):**
  - @tauri-apps/plugin-fs: ~2.2.0 → 2.3.0

- **Root Cargo.toml:**
  - tauri-sys: 0.4 → resolve or remove

### Files to Modify
- `src-tauri/Cargo.toml`
- `package.json` 
- `Cargo.toml` (root)

## 🔗 Related Items

- **Follows:** AI Detection Implementation (BACKEND-AI-1)
- **Ensures:** Latest Tauri v2+ compatibility

## 📝 Implementation Notes

Scanner output shows:
```
[OUTDATED] tauri-plugin-shell@~2.2.0 → Latest: 2.2.1
[OUTDATED] @tauri-apps/plugin-fs@~2.2.0 → Latest: 2.3.0
[OUTDATED] tauri-sys@0.4 → Latest: unknown (could not fetch)
```

## ✅ Definition of Done

- [ ] All outdated components updated
- [ ] Project compiles successfully
- [ ] Tests pass
- [ ] AI detection functionality verified
