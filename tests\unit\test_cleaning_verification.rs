use std::fs;
use std::path::Path;

// Test cleaning functionality to verify CODEBASE-6 fixes
fn main() {
    println!("🧹 TESTING CLEANING FUNCTIONALITY (CODEBASE-6 Fix Verification)");
    
    // Create test directory and files with suspicious characters
    let test_dir = "test_cleaning_verification";
    let test_file = format!("{}/test_file.rs", test_dir);
    
    // Create test directory
    if let Err(e) = fs::create_dir_all(test_dir) {
        eprintln!("Failed to create test directory: {}", e);
        return;
    }
    
    // Create test content with known suspicious characters
    let test_content = format!(
        "// Test file with suspicious characters\n{}fn main() {{\n{}    println!(\"Hello{}World\");\n}}",
        '\u{200B}', // Zero Width Space - should be removed
        '\u{202E}', // Right-to-Left Override - should be removed  
        '\u{200C}'  // Zero Width Non-Joiner - should be removed
    );
    
    println!("📝 Original test content (with suspicious chars):");
    println!("  Length: {} characters", test_content.chars().count());
    println!("  Contains ZWSP: {}", test_content.contains('\u{200B}'));
    println!("  Contains RLO: {}", test_content.contains('\u{202E}'));
    println!("  Contains ZWNJ: {}", test_content.contains('\u{200C}'));
    
    // Write test file
    if let Err(e) = fs::write(&test_file, &test_content) {
        eprintln!("Failed to write test file: {}", e);
        return;
    }
    
    println!("\n✅ Test file created: {}", test_file);
    println!("📁 To test the fix:");
    println!("  1. Start the application: cargo tauri dev");
    println!("  2. Navigate to Code Base Analysis");
    println!("  3. Select folder: {}", fs::canonicalize(test_dir).unwrap_or_else(|_| Path::new(test_dir).to_path_buf()).display());
    println!("  4. Click 'Analyze Files' - should detect 3 suspicious characters");
    println!("  5. Click 'Create Cleaned Copy' - should show progress and create cleaned version");
    println!("  6. Check the _cleaned folder - suspicious characters should be removed");
    
    println!("\n🎯 Expected results:");
    println!("  - Progress bar should appear during cleaning");
    println!("  - Cleaned file should have 3 fewer characters");
    println!("  - Cleaned file should contain NO suspicious characters");
    
    // Show what the cleaned content should look like
    let expected_clean = test_content
        .chars()
        .filter(|&c| !matches!(c, '\u{200B}' | '\u{202E}' | '\u{200C}'))
        .collect::<String>();
    
    println!("\n📋 Expected cleaned content:");
    println!("  Length: {} characters (reduced by {})", 
             expected_clean.chars().count(), 
             test_content.chars().count() - expected_clean.chars().count());
    println!("  Content preview: {:?}", expected_clean.lines().take(3).collect::<Vec<_>>());
}
