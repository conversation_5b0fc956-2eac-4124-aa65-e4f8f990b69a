use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use fancy_regex::Regex;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HomoglyphThreat {
    pub position: usize,
    pub suspicious_char: char,
    pub unicode_point: String,
    pub similar_chars: Vec<String>,
    pub risk_level: HomoglyphRiskLevel,
    pub context: String,
    pub threat_description: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum HomoglyphRiskLevel {
    Critical,  // Identical appearance, high abuse potential
    High,      // Very similar, commonly used in attacks
    Medium,    // Similar, potential for confusion
    Low,       // Slight similarity
}

pub struct HomoglyphDetector {
    // Comprehensive homoglyph mappings
    dangerous_mappings: HashMap<char, Vec<HomoglyphMapping>>,
    context_patterns: Vec<ContextPattern>,
}

#[derive(Debug, Clone)]
struct HomoglyphMapping {
    target_char: char,
    risk_level: HomoglyphRiskLevel,
    description: String,
    abuse_examples: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>)]
struct ContextPattern {
    pattern: Regex,
    context_type: ContextType,
    risk_multiplier: f32,
}

#[derive(Debug, <PERSON>lone)]
enum ContextType {
    JavaScriptCode,
    PythonCode,
    HTMLAttribute,
    URLPath,
    DomainName,
    EmailAddress,
    DatabaseQuery,
    ConfigFile,
    AuthToken,
}

impl HomoglyphDetector {
    pub fn new() -> Self {
        let mut detector = HomoglyphDetector {
            dangerous_mappings: HashMap::new(),
            context_patterns: Vec::new(),
        };
        
        detector.initialize_dangerous_mappings();
        detector.initialize_context_patterns();
        detector
    }

    fn initialize_dangerous_mappings(&mut self) {
        // Critical homoglyphs - identical appearance
        self.add_mapping('а', 'a', HomoglyphRiskLevel::Critical, 
            "Cyrillic 'а' identical to Latin 'a'", 
            vec!["Variable substitution in code".to_string(), "Domain spoofing".to_string()]);
            
        self.add_mapping('о', 'o', HomoglyphRiskLevel::Critical, 
            "Cyrillic 'о' identical to Latin 'o'", 
            vec!["Function name spoofing".to_string(), "URL manipulation".to_string()]);
            
        self.add_mapping('р', 'p', HomoglyphRiskLevel::Critical, 
            "Cyrillic 'р' identical to Latin 'p'", 
            vec!["API endpoint spoofing".to_string(), "Command injection".to_string()]);
            
        self.add_mapping('с', 'c', HomoglyphRiskLevel::Critical, 
            "Cyrillic 'с' identical to Latin 'c'", 
            vec!["Class name manipulation".to_string(), "Config key spoofing".to_string()]);
            
        self.add_mapping('е', 'e', HomoglyphRiskLevel::Critical, 
            "Cyrillic 'е' identical to Latin 'e'", 
            vec!["Variable spoofing".to_string(), "Email domain spoofing".to_string()]);

        // High-risk homoglyphs
        self.add_mapping('ı', 'i', HomoglyphRiskLevel::High, 
            "Turkish dotless 'ı' vs Latin 'i'", 
            vec!["Loop variable manipulation".to_string(), "Index spoofing".to_string()]);
            
        self.add_mapping('ǃ', '!', HomoglyphRiskLevel::High, 
            "Alveolar click 'ǃ' vs exclamation mark", 
            vec!["Negation operator spoofing".to_string(), "Shell command injection".to_string()]);
            
        self.add_mapping('Α', 'A', HomoglyphRiskLevel::High, 
            "Greek Alpha 'Α' vs Latin 'A'", 
            vec!["Constant name spoofing".to_string(), "Class spoofing".to_string()]);
            
        self.add_mapping('Β', 'B', HomoglyphRiskLevel::High, 
            "Greek Beta 'Β' vs Latin 'B'", 
            vec!["Boolean manipulation".to_string(), "Binary flag spoofing".to_string()]);

        // Medium-risk homoglyphs  
        self.add_mapping('０', '0', HomoglyphRiskLevel::Medium, 
            "Fullwidth digit zero vs ASCII zero", 
            vec!["Numeric manipulation".to_string(), "Array index spoofing".to_string()]);
            
        self.add_mapping('１', '1', HomoglyphRiskLevel::Medium, 
            "Fullwidth digit one vs ASCII one", 
            vec!["Boolean value spoofing".to_string(), "Counter manipulation".to_string()]);

        // Unicode space and invisible characters
        self.add_mapping('\u{2000}', ' ', HomoglyphRiskLevel::Critical, 
            "En Quad space vs regular space", 
            vec!["Code injection".to_string(), "Parser confusion".to_string()]);
            
        self.add_mapping('\u{200B}', '\u{0000}', HomoglyphRiskLevel::Critical, 
            "Zero Width Space - invisible character", 
            vec!["Hidden code injection".to_string(), "Steganographic attacks".to_string()]);
            
        self.add_mapping('\u{FEFF}', '\u{0000}', HomoglyphRiskLevel::Critical, 
            "Zero Width No-Break Space (BOM)", 
            vec!["BOM injection attacks".to_string(), "Encoding manipulation".to_string()]);
    }

    fn add_mapping(&mut self, suspicious: char, normal: char, risk: HomoglyphRiskLevel, desc: &str, examples: Vec<String>) {
        let mapping = HomoglyphMapping {
            target_char: normal,
            risk_level: risk,
            description: desc.to_string(),
            abuse_examples: examples,
        };
        
        self.dangerous_mappings.entry(suspicious)
            .or_insert_with(Vec::new)
            .push(mapping);
    }

    fn initialize_context_patterns(&mut self) {
        // JavaScript/TypeScript patterns
        if let Ok(regex) = Regex::new(r"(function|const|let|var|class|import|export)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::JavaScriptCode,
                risk_multiplier: 2.0,
            });
        }

        // Python patterns
        if let Ok(regex) = Regex::new(r"(def|class|import|from|lambda|if|else|elif|for|while)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::PythonCode,
                risk_multiplier: 2.0,
            });
        }

        // HTML attribute patterns
        if let Ok(regex) = Regex::new(r"(href=|src=|onclick=|onload=|style=|class=|id=)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::HTMLAttribute,
                risk_multiplier: 2.2,
            });
        }

        // URL path patterns
        if let Ok(regex) = Regex::new(r"(/api/|/admin/|/login|/auth|/upload|\.php|\.asp)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::URLPath,
                risk_multiplier: 2.8,
            });
        }

        // URL/Domain patterns
        if let Ok(regex) = Regex::new(r"(https?://|www\.|\.com|\.org|\.net)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::DomainName,
                risk_multiplier: 3.0,
            });
        }

        // Email address patterns
        if let Ok(regex) = Regex::new(r"(@gmail\.|@yahoo\.|@outlook\.|@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::EmailAddress,
                risk_multiplier: 2.4,
            });
        }

        // Database query patterns
        if let Ok(regex) = Regex::new(r"(SELECT|INSERT|UPDATE|DELETE|WHERE|FROM|JOIN)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::DatabaseQuery,
                risk_multiplier: 2.5,
            });
        }

        // Configuration file patterns
        if let Ok(regex) = Regex::new(r"(password=|secret=|key=|token=|api_key=|database_url=)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::ConfigFile,
                risk_multiplier: 3.2,
            });
        }

        // Authentication token patterns
        if let Ok(regex) = Regex::new(r"(Bearer |JWT |oauth|access_token|refresh_token|session_id)") {
            self.context_patterns.push(ContextPattern {
                pattern: regex,
                context_type: ContextType::AuthToken,
                risk_multiplier: 3.5,
            });
        }
    }

    pub fn analyze_text(&self, content: &str, file_type: &str) -> Vec<HomoglyphThreat> {
        let mut threats = Vec::new();
        
        for (pos, ch) in content.char_indices() {
            if let Some(mappings) = self.dangerous_mappings.get(&ch) {
                for mapping in mappings {
                    let context = self.extract_context(content, pos, 50);
                    let risk_level = self.assess_contextual_risk(&mapping.risk_level, &context, file_type);
                    
                    threats.push(HomoglyphThreat {
                        position: pos,
                        suspicious_char: ch,
                        unicode_point: format!("U+{:04X}", ch as u32),
                        similar_chars: vec![mapping.target_char.to_string()],
                        risk_level,
                        context: context.clone(),
                        threat_description: self.generate_threat_description(ch, mapping, &context, file_type),
                    });
                }
            }
        }
        
        threats
    }

    fn extract_context(&self, content: &str, position: usize, window: usize) -> String {
        let start = position.saturating_sub(window);
        let end = (position + window).min(content.len());
        
        content.chars()
            .skip(start)
            .take(end - start)
            .collect()
    }

    fn assess_contextual_risk(&self, base_risk: &HomoglyphRiskLevel, context: &str, file_type: &str) -> HomoglyphRiskLevel {
        let mut risk_score = match base_risk {
            HomoglyphRiskLevel::Critical => 4.0,
            HomoglyphRiskLevel::High => 3.0,
            HomoglyphRiskLevel::Medium => 2.0,
            HomoglyphRiskLevel::Low => 1.0,
        };

        // Increase risk based on file type
        match file_type {
            "js" | "ts" | "jsx" | "tsx" => risk_score *= 1.5,
            "py" | "pyw" => risk_score *= 1.4,
            "html" | "htm" => risk_score *= 1.3,
            "sql" => risk_score *= 1.8,
            "sh" | "bash" | "ps1" => risk_score *= 1.6,
            _ => {}
        }

        // Check context patterns with context-type specific logic
        for pattern in &self.context_patterns {
            if pattern.pattern.is_match(context).expect("Failed to check regex match") {
                // Apply base risk multiplier
                risk_score *= pattern.risk_multiplier;

                // Apply additional context-type specific risk adjustments
                match pattern.context_type {
                    ContextType::AuthToken | ContextType::ConfigFile => {
                        // Authentication and config contexts are extremely sensitive
                        risk_score *= 1.2;
                    },
                    ContextType::DatabaseQuery => {
                        // SQL injection risks are high
                        risk_score *= 1.15;
                    },
                    ContextType::JavaScriptCode | ContextType::PythonCode => {
                        // Code contexts allow for sophisticated attacks
                        risk_score *= 1.1;
                    },
                    ContextType::URLPath | ContextType::DomainName => {
                        // URL manipulation attacks
                        risk_score *= 1.05;
                    },
                    _ => {
                        // Other contexts get standard treatment
                    }
                }
            }
        }

        // Convert back to risk level
        match risk_score as i32 {
            7.. => HomoglyphRiskLevel::Critical,
            5..=6 => HomoglyphRiskLevel::High,
            3..=4 => HomoglyphRiskLevel::Medium,
            _ => HomoglyphRiskLevel::Low,
        }
    }

    fn generate_threat_description(&self, ch: char, mapping: &HomoglyphMapping, _context: &str, file_type: &str) -> String {
        let unicode_name = self.get_unicode_name(ch);
        
        format!(
            "HOMOGLYPH ATTACK: Character '{}' (U+{:04X}, {}) masquerading as '{}'. {} Detected in {} context. Potential attacks: {}",
            ch,
            ch as u32,
            unicode_name,
            mapping.target_char,
            mapping.description,
            file_type.to_uppercase(),
            mapping.abuse_examples.join(", ")
        )
    }

    fn get_unicode_name(&self, ch: char) -> String {
        // This would ideally use a Unicode database, but for now we'll use basic names
        match ch {
            'а' => "CYRILLIC SMALL LETTER A".to_string(),
            'о' => "CYRILLIC SMALL LETTER O".to_string(),
            'р' => "CYRILLIC SMALL LETTER ER".to_string(),
            'с' => "CYRILLIC SMALL LETTER ES".to_string(),
            'е' => "CYRILLIC SMALL LETTER IE".to_string(),
            'ı' => "LATIN SMALL LETTER DOTLESS I".to_string(),
            'ǃ' => "LATIN LETTER RETROFLEX CLICK".to_string(),
            'Α' => "GREEK CAPITAL LETTER ALPHA".to_string(),
            'Β' => "GREEK CAPITAL LETTER BETA".to_string(),
            '\u{200B}' => "ZERO WIDTH SPACE".to_string(),
            '\u{FEFF}' => "ZERO WIDTH NO-BREAK SPACE".to_string(),
            _ => format!("UNICODE CHARACTER U+{:04X}", ch as u32),
        }
    }
}
