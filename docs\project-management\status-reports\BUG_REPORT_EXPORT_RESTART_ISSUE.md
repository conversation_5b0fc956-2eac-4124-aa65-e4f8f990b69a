# Bug Report: Export Button Causes Application Restart

**Report ID**: BUG-2025-06-15-001  
**Date**: June 15, 2025  
**Severity**: High  
**Priority**: P1  
**Reporter**: Development Team  
**Status**: Confirmed  

## Issue Summary

When users click the Export button in the Bad Character Scanner GUI, the export operation completes successfully but the application immediately restarts/closes, giving the impression that the program is crashing or closing unexpectedly.

## Problem Description

### What Users Experience:
1. User performs text analysis in the GUI
2. User clicks "Export" button (JSON/HTML/CSV format)
3. Export appears to work (file is created)
4. **Application suddenly closes/restarts**
5. User loses their current session and analysis results

### Actual Root Cause:
The application is not crashing - it's being automatically restarted by the Tauri development file watcher system.

## Technical Analysis

### Evidence from Console Output:
```
✅ JSON export saved to: C:\Users\<USER>\Documents\Software\Leptos_TaurieV2_BCS\src-tauri\reports\analysis_export_2025-06-16T04-03-04-872Z.json
Info File src-tauri\reports\analysis_export_2025-06-16T04-03-04-872Z.json changed. Rebuilding application...
Running DevCommand (`cargo  run --no-default-features --color always --`)
```

### Root Cause Analysis:

1. **Export Function Works Correctly**: 
   - Files are successfully created in `src-tauri\reports\` directory
   - Export data is properly formatted and saved

2. **File Watcher Interference**:
   - Tauri's development file watcher monitors the entire project directory
   - When export creates a new file in `src-tauri\reports\`, the watcher detects it as a "source change"
   - This triggers an automatic rebuild and restart of the application

3. **Directory Location Issue**:
   - Export files are being saved to `src-tauri\reports\` (inside source directory)
   - This location is being monitored by the development file watcher
   - Any file creation here triggers a rebuild

## Impact Assessment

### User Experience Impact:
- **Critical**: Users lose their current analysis session
- **High**: Appears as application crash/malfunction
- **Medium**: Users must re-perform analysis after each export
- **Low**: Files are still successfully exported

### Development Impact:
- **High**: Major usability issue in development mode
- **Medium**: May affect production builds differently
- **Low**: Core export functionality works correctly

## Proposed Solutions

### Solution 1: Change Export Directory (Recommended)
**Priority**: P1 - Immediate Fix
- Move export directory outside the source tree
- Use user's Documents folder or system temp directory
- Update export path in `main_module.rs`

**Implementation**:
```rust
// Current problematic path
let reports_dir = project_root.join("src-tauri").join("reports");

// Proposed fix - use user directory
let reports_dir = dirs::document_dir()
    .unwrap_or_else(|| std::env::temp_dir())
    .join("BadCharacterScanner")
    .join("exports");
```

### Solution 2: Configure File Watcher Exclusions
**Priority**: P2 - Development Configuration
- Update Tauri configuration to exclude reports directory
- Modify `tauri.config.json` or build configuration
- Add file watcher ignore patterns

### Solution 3: Development vs Production Logic
**Priority**: P3 - Environmental Logic
- Detect development vs production mode
- Use different export paths for each environment
- Implement conditional export directory logic

## Reproduction Steps

1. Start the application in development mode (`cargo tauri dev`)
2. Perform any text analysis (paste text and click Analyze)
3. Click any Export button (JSON, HTML, or CSV)
4. Observe: File is created successfully but application restarts immediately

## Expected vs Actual Behavior

### Expected:
- Export completes successfully
- Success message displays
- Application continues running
- User can perform additional operations

### Actual:
- Export completes successfully ✅
- **Application restarts immediately** ❌
- User session is lost ❌
- GUI appears to have "crashed" ❌

## Workaround

**For Users**:
- Files are still successfully exported to the reports directory
- Re-open application after export to continue working
- Perform all exports at the end of analysis session

**For Developers**:
- Test export functionality in production build mode
- Use `cargo tauri build` for testing export without file watcher interference

## Files Affected

### Primary Files:
- `src-tauri/src/main_module.rs` - Export directory logic (lines 340-350)
- `tauri.config.json` - File watcher configuration
- `src/components/export.rs` - Frontend export component

### Configuration Files:
- `Cargo.toml` - Dependencies for directory handling
- `tauri.conf.json` - Alternative config file

## Testing Requirements

### Verification Steps:
1. **Fix Implementation**: Apply directory change solution
2. **Development Testing**: Verify no restart occurs after export
3. **Production Testing**: Confirm exports work in built application
4. **Path Testing**: Verify export files are accessible to users
5. **Permission Testing**: Ensure write permissions to new directory

### Test Cases:
- [ ] Export JSON format without restart
- [ ] Export HTML format without restart  
- [ ] Export CSV format without restart
- [ ] Verify files are created in correct location
- [ ] Confirm files contain correct analysis data
- [ ] Test with various analysis result sizes
- [ ] Test export permissions on different systems

## Related Issues

- **Similar Pattern**: Any file creation in `src-tauri` directory may trigger this
- **Development Mode**: Issue likely only affects `cargo tauri dev` mode
- **File Watchers**: Other development tools may have similar issues

## Recommended Action Plan

### Immediate (This Sprint):
1. **Implement Solution 1**: Change export directory to user Documents
2. **Test thoroughly**: Verify fix works in both dev and production
3. **Update documentation**: Reflect new export file location

### Short Term (Next Sprint):
1. **Configure file watchers**: Add appropriate exclusions
2. **Improve user feedback**: Better export success messaging
3. **Path customization**: Allow users to choose export location

### Long Term (Future):
1. **Export management**: Built-in file browser for exports
2. **Session persistence**: Remember analysis state across restarts
3. **Advanced export options**: More formats and customization

---

**Next Steps**: Assign to backend team for immediate fix implementation using Solution 1.
