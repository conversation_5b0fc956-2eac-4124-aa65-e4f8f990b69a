# Notice to Collaborators: ShoyDev Branch

The `ShoyDev` branch was created to isolate ongoing refactoring, bugfixes, and experimental changes. This allows the main branch to remain stable for other developers.

**Why this was done:**
- The main branch had breaking changes and in-progress refactors that could block or disrupt other developers.
- All current work-in-progress, including modularization, Leptos/Tauri fixes, and ticketing, is now on `ShoyDev`.
- Please continue your work on the main branch as needed. If you need any of the new features or fixes, you can merge or cherry-pick from `ShoyDev`.

**Contact Shoy for details or if you need to coordinate merges.**

---

## Next Steps
- All new experimental or risky changes should be pushed to `ShoyDev` until they are stable.
- The main branch is now free for production or collaborative work.

---

*This notice was auto-generated to inform the team of the branch split and workflow.*
