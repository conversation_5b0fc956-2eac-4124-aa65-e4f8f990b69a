import { test, expect } from '@playwright/test';

test.describe('Bad Character Scanner - Basic Functionality', () => {
  test('should load the application successfully', async ({ page }) => {
    await page.goto('http://localhost:1420');
    
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
    
    // Test main header elements
    await expect(page.getByText('Bad Character Scanner')).toBeVisible();
    await expect(page.getByText('Advanced Unicode Analysis & Security Scanner')).toBeVisible();
  });

  test('should display both analysis modes', async ({ page }) => {
    await page.goto('http://localhost:1420');
    await page.waitForLoadState('networkidle');
    
    // Test mode switcher buttons exist
    const textAnalysisTab = page.getByRole('button', { name: /Text Analysis & Cleaning/i });
    const codebaseAnalysisTab = page.getByRole('button', { name: /Code Base Analysis & Cleaning/i });
    
    await expect(textAnalysisTab).toBeVisible();
    await expect(codebaseAnalysisTab).toBeVisible();
  });

  test('should switch to codebase analysis mode', async ({ page }) => {
    await page.goto('http://localhost:1420');
    await page.waitForLoadState('networkidle');
    
    // Switch to codebase analysis mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    // Test folder selection interface
    await expect(page.getByText('Code Base Analysis & Cleaning')).toBeVisible();
    await expect(page.getByPlaceholder('Enter folder path or drag and drop')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Browse...' })).toBeVisible();
  });

  test('should handle folder path input', async ({ page }) => {
    await page.goto('http://localhost:1420');
    await page.waitForLoadState('networkidle');
    
    // Switch to codebase analysis mode
    await page.getByRole('button', { name: /Code Base Analysis & Cleaning/i }).click();
    
    const pathInput = page.getByPlaceholder('Enter folder path or drag and drop');
    await pathInput.fill('C:\\\\test\\\\folder');
    await expect(pathInput).toHaveValue('C:\\\\test\\\\folder');
  });

  test('should display settings panel', async ({ page }) => {
    await page.goto('http://localhost:1420');
    await page.waitForLoadState('networkidle');
    
    const settingsButton = page.getByRole('button', { name: /Settings/i });
    await expect(settingsButton).toBeVisible();
    
    // Click to open settings
    await settingsButton.click();
    await expect(page.getByText('Application Settings')).toBeVisible();
  });
});
