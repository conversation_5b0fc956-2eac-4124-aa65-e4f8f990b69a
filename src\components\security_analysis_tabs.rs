use leptos::*;
use serde_json::Value;

// Security Analysis Tabbed Interface Component
// By <PERSON> - 2025
// Provides comprehensive security analysis results in a tabbed interface

#[derive(<PERSON>lone, Debug, PartialEq)]
pub enum SecurityTab {
    Overview,
    BadCharacters,
    HomoglyphAttacks,
    VisualSpoofing,
}

#[component]
pub fn SecurityAnalysisTabs(analysis_result: String) -> impl IntoView {
    let (active_tab, set_active_tab) = create_signal(SecurityTab::Overview);
    
    // Parse the analysis result JSON to extract relevant data
    let parsed_data = create_memo(move |_| {
        serde_json::from_str::<Value>(&analysis_result).unwrap_or_else(|_| {
            serde_json::json!({
                "suspicious_characters": [],
                "security_analysis": {
                    "risk_level": "Low",
                    "homograph_attacks": [],
                    "phishing_indicators": []
                },
                "total_characters": 0
            })
        })
    });

    // Calculate security score (never reaches 100%)
    let security_score = create_memo(move |_| {
        let data = parsed_data.get();
        let suspicious_count = data.get("suspicious_characters")
            .and_then(|v| v.as_array())
            .map(|arr| arr.len())
            .unwrap_or(0);
        
        let total_chars = data.get("total_characters")
            .and_then(|v| v.as_u64())
            .unwrap_or(1) as f64;
        
        if suspicious_count == 0 {
            99.0 // Maximum possible score - never 100%
        } else {
            let clean_ratio = (total_chars - suspicious_count as f64) / total_chars;
            (clean_ratio * 98.0).max(10.0) // Cap at 98% max, minimum 10%
        }
    });

    view! {
        <div class="w-full">
            // Tab Navigation
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex space-x-8" aria-label="Security Analysis Tabs">
                    <button
                        class=move || format!(
                            "py-2 px-1 border-b-2 font-medium text-sm transition-colors {}",
                            if active_tab.get() == SecurityTab::Overview {
                                "border-blue-500 text-blue-600"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }
                        )
                        on:click=move |_| set_active_tab.set(SecurityTab::Overview)
                    >
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            "Overview"
                        </div>
                    </button>
                    
                    <button
                        class=move || format!(
                            "py-2 px-1 border-b-2 font-medium text-sm transition-colors {}",
                            if active_tab.get() == SecurityTab::BadCharacters {
                                "border-blue-500 text-blue-600"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }
                        )
                        on:click=move |_| set_active_tab.set(SecurityTab::BadCharacters)
                    >
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            "Bad Characters"
                        </div>
                    </button>
                    
                    <button
                        class=move || format!(
                            "py-2 px-1 border-b-2 font-medium text-sm transition-colors {}",
                            if active_tab.get() == SecurityTab::HomoglyphAttacks {
                                "border-blue-500 text-blue-600"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }
                        )
                        on:click=move |_| set_active_tab.set(SecurityTab::HomoglyphAttacks)
                    >
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            "Homoglyph Attacks"
                        </div>
                    </button>
                    
                    <button
                        class=move || format!(
                            "py-2 px-1 border-b-2 font-medium text-sm transition-colors {}",
                            if active_tab.get() == SecurityTab::VisualSpoofing {
                                "border-blue-500 text-blue-600"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }
                        )
                        on:click=move |_| set_active_tab.set(SecurityTab::VisualSpoofing)
                    >
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                            </svg>
                            "Visual Spoofing"
                        </div>
                    </button>
                </nav>
            </div>

            // Tab Content
            <div class="min-h-96">
                <Show when=move || active_tab.get() == SecurityTab::Overview>
                    <OverviewTab security_score=security_score analysis_data=parsed_data />
                </Show>
                
                <Show when=move || active_tab.get() == SecurityTab::BadCharacters>
                    <BadCharactersTab analysis_data=parsed_data />
                </Show>
                
                <Show when=move || active_tab.get() == SecurityTab::HomoglyphAttacks>
                    <HomoglyphAttacksTab analysis_data=parsed_data />
                </Show>
                
                <Show when=move || active_tab.get() == SecurityTab::VisualSpoofing>
                    <VisualSpoofingTab analysis_data=parsed_data />
                </Show>
            </div>
        </div>
    }
}

#[component]
fn OverviewTab(
    security_score: Memo<f64>,
    analysis_data: Memo<Value>,
) -> impl IntoView {
    view! {
        <div class="space-y-6">
            // Security Score Card
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">"Security Analysis Score"</h3>
                        <p class="text-sm text-gray-600">"Comprehensive threat assessment"</p>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-blue-600">
                            {move || format!("{:.1}%", security_score.get())}
                        </div>
                        <div class="text-sm text-gray-500">"Clean"</div>
                    </div>
                </div>
                
                // Progress bar
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div 
                        class="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500"
                        style=move || format!("width: {}%", security_score.get())
                    ></div>
                </div>
                
                // Important disclaimer
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start gap-3">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <h4 class="font-medium text-yellow-800 mb-1">"Security Notice"</h4>
                            <p class="text-sm text-yellow-700">
                                "Complete security can never be guaranteed. This represents the best analysis possible with current tools. "
                                "New threats emerge constantly, and no scanner can detect 100% of all potential security issues."
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            // Call to Action
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">"Need More Comprehensive Analysis?"</h3>
                    <p class="text-gray-600 mb-4">
                        "For enterprise-grade security scanning with advanced threat detection, "
                        "consider upgrading to our professional solutions."
                    </p>
                    <a 
                        href="https://badcharacterscanner.com/future"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        "Explore Enterprise Solutions"
                    </a>
                </div>
            </div>

            // Quick Stats
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-gray-800">
                                {move || {
                                    let data = analysis_data.get();
                                    data.get("total_characters")
                                        .and_then(|v| v.as_u64())
                                        .unwrap_or(0)
                                        .to_string()
                                }}
                            </div>
                            <div class="text-sm text-gray-600">"Characters Analyzed"</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-gray-800">
                                {move || {
                                    let data = analysis_data.get();
                                    data.get("suspicious_characters")
                                        .and_then(|v| v.as_array())
                                        .map(|arr| arr.len())
                                        .unwrap_or(0)
                                        .to_string()
                                }}
                            </div>
                            <div class="text-sm text-gray-600">"Issues Found"</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.586-3.586a2 2 0 00-2.828 0l-3 3a2 2 0 002.828 2.828l.106-.106a2 2 0 00.106-2.828l3-3z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-gray-800">
                                {move || {
                                    let data = analysis_data.get();
                                    data.get("security_analysis")
                                        .and_then(|sa| sa.get("risk_level"))
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("Low")
                                        .to_string()
                                }}
                            </div>
                            <div class="text-sm text-gray-600">"Risk Level"</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn BadCharactersTab(analysis_data: Memo<Value>) -> impl IntoView {
    view! {
        <div class="space-y-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">"Suspicious Characters Detected"</h3>

                <div class="space-y-4">
                    {move || {
                        let data = analysis_data.get();
                        let suspicious_chars = data.get("suspicious_characters")
                            .and_then(|v| v.as_array())
                            .cloned();

                        if suspicious_chars.as_ref().map_or(true, |chars| chars.is_empty()) {
                            view! {
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-800 mb-2">"No Suspicious Characters Found"</h4>
                                    <p class="text-gray-600">"Your text appears to be clean of problematic characters."</p>
                                </div>
                            }.into_view()
                        } else {
                            // Extract character data to avoid lifetime issues
                            let char_data_list: Vec<(String, u64, u64, Vec<String>)> = suspicious_chars.map(|chars| {
                                chars.iter().enumerate().filter_map(|(_index, char_data)| {
                                    let character = char_data.get("character")
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("?")
                                        .to_string();
                                    let position = char_data.get("position")
                                        .and_then(|v| v.as_u64())
                                        .unwrap_or(0);
                                    let codepoint = char_data.get("codepoint")
                                        .and_then(|v| v.as_u64())
                                        .unwrap_or(0);
                                    let reasons = char_data.get("suspicion_reasons")
                                        .and_then(|v| v.as_array())
                                        .map(|arr| arr.iter().filter_map(|r| r.as_str()).map(|s| s.to_string()).collect::<Vec<_>>())
                                        .unwrap_or_else(|| vec!["Unknown reason".to_string()]);

                                    Some((character, position, codepoint, reasons))
                                }).collect()
                            }).unwrap_or_default();

                            view! {
                                <div class="space-y-3">
                                    {char_data_list.into_iter().map(|(character, position, codepoint, reasons)| {
                                        view! {
                                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                                <div class="flex items-start gap-3">
                                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="flex items-center gap-2 mb-2">
                                                            <span class="font-mono text-lg bg-gray-100 px-2 py-1 rounded">
                                                                {if character.chars().all(|c| c.is_control() || c.is_whitespace()) {
                                                                    format!("[U+{:04X}]", codepoint)
                                                                } else {
                                                                    format!("'{}'", character)
                                                                }}
                                                            </span>
                                                            <span class="text-sm text-gray-600">
                                                                {format!("Position: {}", position)}
                                                            </span>
                                                        </div>
                                                        <div class="text-sm text-gray-700">
                                                            <strong>"Issues: "</strong>
                                                            {reasons.join(", ")}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }).collect::<Vec<_>>()}
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

#[component]
fn HomoglyphAttacksTab(analysis_data: Memo<Value>) -> impl IntoView {
    view! {
        <div class="space-y-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">"Homoglyph Attack Analysis"</h3>

                <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="font-medium text-blue-800 mb-2">"What are Homoglyph Attacks?"</h4>
                    <p class="text-sm text-blue-700">
                        "Homoglyph attacks use characters that look identical or very similar to legitimate characters "
                        "but have different Unicode code points. These can be used to create deceptive text that "
                        "appears normal but may bypass security filters or mislead users."
                    </p>
                </div>

                <div class="space-y-4">
                    {move || {
                        let data = analysis_data.get();
                        let attacks_data = data.get("security_analysis")
                            .and_then(|sa| sa.get("homograph_attacks"))
                            .and_then(|v| v.as_array())
                            .cloned();

                        if attacks_data.as_ref().map_or(true, |attacks| attacks.is_empty()) {
                            view! {
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-800 mb-2">"No Homoglyph Attacks Detected"</h4>
                                    <p class="text-gray-600">"Your text does not contain suspicious character substitutions."</p>
                                </div>
                            }.into_view()
                        } else {
                            let attack_descriptions: Vec<String> = attacks_data.map(|attacks| {
                                attacks.iter()
                                    .filter_map(|attack| attack.as_str())
                                    .map(|s| s.to_string())
                                    .collect()
                            }).unwrap_or_default();

                            view! {
                                <div class="space-y-3">
                                    {attack_descriptions.into_iter().map(|description| {
                                        view! {
                                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                                <div class="flex items-start gap-3">
                                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h5 class="font-medium text-orange-800 mb-1">"Potential Homoglyph Attack"</h5>
                                                        <p class="text-sm text-orange-700">{description}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }).collect::<Vec<_>>()}
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

#[component]
fn VisualSpoofingTab(analysis_data: Memo<Value>) -> impl IntoView {
    view! {
        <div class="space-y-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">"Visual Spoofing Detection"</h3>

                <div class="mb-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h4 class="font-medium text-purple-800 mb-2">"What is Visual Spoofing?"</h4>
                    <p class="text-sm text-purple-700">
                        "Visual spoofing involves using special Unicode characters, invisible characters, "
                        "or complex formatting to create text that appears different from its actual content. "
                        "This can be used to deceive users or bypass security systems."
                    </p>
                </div>

                <div class="space-y-4">
                    {move || {
                        let data = analysis_data.get();
                        let phishing_indicators = data.get("security_analysis")
                            .and_then(|sa| sa.get("phishing_indicators"))
                            .and_then(|v| v.as_array())
                            .cloned();

                        let steganography_potential = data.get("security_analysis")
                            .and_then(|sa| sa.get("steganography_potential"))
                            .and_then(|v| v.as_bool())
                            .unwrap_or(false);

                        let has_issues = phishing_indicators.as_ref().map_or(false, |pi| !pi.is_empty()) || steganography_potential;

                        if !has_issues {
                            view! {
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-800 mb-2">"No Visual Spoofing Detected"</h4>
                                    <p class="text-gray-600">"Your text does not contain suspicious visual spoofing patterns."</p>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-3">
                                    {if steganography_potential {
                                        view! {
                                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                                <div class="flex items-start gap-3">
                                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h5 class="font-medium text-red-800 mb-1">"Steganography Potential Detected"</h5>
                                                        <p class="text-sm text-red-700">
                                                            "The text contains patterns that could potentially be used for hiding information "
                                                            "or creating deceptive content through character manipulation."
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        }.into_view()
                                    } else {
                                        view! { <div></div> }.into_view()
                                    }}

                                    {
                                        let indicator_descriptions: Vec<String> = phishing_indicators.map(|indicators| {
                                            indicators.iter()
                                                .filter_map(|indicator| indicator.as_str())
                                                .map(|s| s.to_string())
                                                .collect()
                                        }).unwrap_or_default();

                                        indicator_descriptions.into_iter().map(|description| {
                                            view! {
                                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                                    <div class="flex items-start gap-3">
                                                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                        </div>
                                                        <div class="flex-1">
                                                            <h5 class="font-medium text-yellow-800 mb-1">"Phishing Indicator"</h5>
                                                            <p class="text-sm text-yellow-700">{description}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        }).collect::<Vec<_>>()
                                    }
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}
